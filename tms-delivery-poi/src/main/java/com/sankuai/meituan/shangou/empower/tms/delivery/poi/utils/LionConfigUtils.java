package com.sankuai.meituan.shangou.empower.tms.delivery.poi.utils;

import com.dianping.lion.client.Lion;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static com.dianping.lion.client.Lion.getConfigRepository;

@Slf4j
public class LionConfigUtils {

	public static boolean isGloryStore(long tenantId, long storeId) {
		if (isGloryTenant(tenantId)) {
			return true;
		}

		return isGloryStore(storeId);
	}

	public static boolean needSendStoreCoordinateAddressWhenLaunching() {
		try {
			return Lion.getConfigRepository().getBooleanValue("glory.delivery.send.store.coordinate.address", true);
		} catch (Exception e) {
			log.error("parse lion[glory.delivery.send.store.coordinate.address] failed, will default to true");
			return true;
		}
	}

	private static boolean isGloryTenant(long tenantId) {
		try {
			return Optional.ofNullable(Lion.getConfigRepository().get("glory.tenantIds"))
					.filter(StringUtils::isNotBlank)
					.map(it -> JsonUtil.fromJson(it, new TypeReference<List<Long>>() {
					}))
					.filter(it -> it.contains(tenantId))
					.isPresent();
		} catch (Exception e) {
			log.error("parse lion[glory.tenantIds] failed, will default empty white list", e);
			return false;
		}
	}

	private static boolean isGloryStore(long storeId) {
		try {
			return Optional.ofNullable(Lion.getConfigRepository().get("glory.storeIds"))
					.filter(StringUtils::isNotBlank)
					.map(it -> JsonUtil.fromJson(it, new TypeReference<List<Long>>() {
					}))
					.filter(it -> it.contains(storeId))
					.isPresent();
		} catch (Exception e) {
			log.error("parse lion[glory.storeIds] failed, will default empty white list", e);
			return false;
		}
	}

	/**
	 * 歪马送酒租户id
	 */
	@SuppressWarnings({"UnstableApiUsage"})
	public static List<String> getDHTenantIdList(){
		String tenantIdStr = Lion.getConfigRepository().get("drunk.horse.tenant.id.list", "1000395");
		if(org.apache.commons.lang.StringUtils.isBlank(tenantIdStr)){
			return Collections.emptyList();
		}
		return Splitter.on(",").splitToList(tenantIdStr);
	}

	/**
	 * 是否是歪马租户
	 */
	public static Boolean checkIsDHTenant(Long tenantId) {
		if (Objects.isNull(tenantId)) {
			return false;
		}
		List<String> dhTenantIdList = getDHTenantIdList();
		return dhTenantIdList.contains(tenantId.toString());
	}


	public static Integer getPreOrderAssessAddMinus(){
		return Lion.getConfigRepository().getIntValue("pre.assess.add.minus", 0);
	}

	/**
	 * 获取门店操作流水查询时间范围，单位：秒
	 * @return
	 */
	public static Integer getStoreOpLogQueryTimeRange(){
		return Lion.getConfigRepository().getIntValue("store.op.log.query.time.range", 3);
	}


	public static boolean isSwitchToTokenDapUrl() {
		return Lion.getConfigRepository().getBooleanValue("switch.token.dap", false);
	}

	public static int getPartitionSize() {
		return Lion.getConfigRepository().getIntValue("rider.query.partition.size", 50);
	}


	/**
	 * 判断运单超时的小时数，当超过预计送达时间x小时，判定为超时
	 */
	public static Integer getDHHours4JudgeDeliveryOrderTimeoutForAllThird() {
		try {
			return ConfigUtilAdapter.getInt("dh_hours_to_judge_timeout_for_all_third", 4);
		} catch (Exception e) {
			log.error("MCC[dh_hours_to_judge_timeout] parse error", e);
			return 4;
		}
	}


	public static boolean isLocationUpstreamGrayStore(long poiId) {
		try {
			List<Long> grayPoiList = Lion.getConfigRepository().getList("location.upstream.gray", Long.class);
			//-1则全量
			if (CollectionUtils.isNotEmpty(grayPoiList) && grayPoiList.size() == 1 && grayPoiList.get(0).equals(-1L)) {
				return true;
			}
			return Optional.ofNullable(grayPoiList).orElse(Lists.newArrayList()).contains(poiId);
		} catch (Exception e) {
			log.error("isLocationUpstreamGrayStore error", e);
			return false;
		}

	}

	public static int getPushDownOpeningTimeMin() {
		return getConfigRepository().getIntValue("push.down.opening.min", 40);
	}

	public static String getReferenceOpenTime() {
		return getConfigRepository().get("reference.open.time", "09:00");
	}


}
