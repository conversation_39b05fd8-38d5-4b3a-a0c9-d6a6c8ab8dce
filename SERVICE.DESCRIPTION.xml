<?xml version="1.0" encoding="UTF-8"?>
<serviceCatalog
        xmlns="http://service.sankuai.com/1.0.0"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://service.sankuai.com/1.0.0
            http://pixel.sankuai.com/repository/releases/com/meituan/apidoc/servicecatalog/1.0.0/servicecatalog-1.0.0.xsd">

    <serviceDescs>
        <!--
            该示例描述的是使用 Thrift IDL 文件进行接口描述时应该如何编写文档。
            接入文档可以参考：https://km.sankuai.com/page/60349877
            <interfaceDesc> 部分的 <searchPath> 参数用于指定 <idlFile> IDL 文件中 include 文件的搜索目录在什么位置。
            <idlFile> 和 <searchPath> 参数的值都是相对代码仓库根目录的路径。
            可以将 <serviceDesc> 中 name, description, scenarios 等信息以 @ServiceDoc 的注解在代码中标注，也可以直接
            按下面这种方式进行编写。
        -->
        <!--
              该示例描述的是使用 Restful 服务框架的情况下如何进行文档编写。该类型的编写方式和普通 java 的一样，都是通过注解的方式在
              代码中进行相关文档的描述。接入文档可以参考：https://km.sankuai.com/page/60715770
              可以将 <serviceDesc> 中 name, description, scenarios 等信息以 @ServiceDoc 的注解在代码中标注，也可以直接
              按下面这种方式进行编写。
        -->
        <serviceDesc>
            <appkey>com.sankuai.shangou.qnh.orderapi</appkey>
            <name>服务文档示例</name>
            <description>服务编写文档示例</description>
            <scenarios>服务文档示例</scenarios>
            <interfaceDescs>
                <interfaceDesc>
                    <type>restful</type>
                    <!-- 替换为自己定义的Controller类名称 -->
                    <class>com.sankuai.shangou.qnh.orderapi.controller.pc.OrderFuseController</class>
                </interfaceDesc>
                <interfaceDesc>
                    <type>restful</type>
                    <class>
                        com.sankuai.shangou.qnh.orderapi.controller.pc.ChannelOrderController
                    </class>
                </interfaceDesc>
<!--                <interfaceDesc>-->
<!--                    <type>octo.thrift</type>-->
<!--                    &lt;!&ndash; 替换为自己定义的thrift文件名称 &ndash;&gt;-->
<!--                    <idlFile>qnh_order_api-sdk/src/main/thrift/XXX.thrift</idlFile>-->
<!--                    &lt;!&ndash;<searchPaths>&ndash;&gt;-->
<!--                    &lt;!&ndash;<searchPath>thrift-idl-service/src/main/resources/thrift/com/meituan/xlab/lib</searchPath>&ndash;&gt;-->
<!--                    &lt;!&ndash;</searchPaths>&ndash;&gt;-->
<!--                </interfaceDesc>-->
            </interfaceDescs>
        </serviceDesc>
    </serviceDescs>

</serviceCatalog>