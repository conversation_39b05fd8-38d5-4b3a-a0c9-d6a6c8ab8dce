package com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain;


import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.RiderLocationDataDO;

import java.util.List;
import java.util.Map;

public interface RiderLocationRepository {

    /**
     * 把骑手位置信息存到缓存中
     * @param riderLocationDetail 骑手位置信息
     */
    void save(RiderLocationDetail riderLocationDetail);

    /**
     * 把三方骑手位置信息存到缓存中
     */
    void saveThirdLocation(ThirdRiderPoint thirdRiderPoint);


    /**
     * 获取单个骑手的位置信息
     * @param riderAccountId 骑手的账号id
     * @return 骑手的位置信息
     */
    RiderLocationDetail getStaffRiderLocation(long riderAccountId);

    /**
     * 获取单个骑手的最近一次上报的位置信息 (可能是很久之前的位置)
     * @param riderAccountId
     * @return
     */
    RiderLocationDataDO getLatestStaffRiderLocation(long riderAccountId);

    /**
     * 批量获取骑手的位置信息
     * @param riderAccountIds 骑手账号id列表
     * @return Map<Long, RiderLocationDetail> key: 骑手账号id value: 骑手位置
     */
    Map<Long, RiderLocationDetail> getStaffRiderLocations(List<Long> riderAccountIds);

    /**
     * 批量获取骑手的位置信息
     * @return Map<Long, RiderLocationDetail> key: 骑手账号id value: 骑手位置
     */
    Map<ThirdRiderKey, ThirdRiderPoint> getThirdRiderLocations(List<String> thirdRiderKeys);


    /**
     * 把骑手位置信息批量增量同步到缓存
     */
    void batchIncrementSave(Long tenantId, Long storeId, Long channelOrderId, List<RiderLocationDetail> list);
}
