package com.sankuai.meituan.shangou.empower.tms.delivery.rider.utils.convert;

import com.google.common.collect.Lists;
import com.sankuai.meituan.shangou.dms.base.model.value.DeliveryChannel;
import com.sankuai.meituan.shangou.empower.rider.client.common.Status;
import com.sankuai.meituan.shangou.empower.rider.client.common.TPageInfo;
import com.sankuai.meituan.shangou.empower.rider.client.enums.RiderDeliveryStatusEnum;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto.*;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.response.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryOrderDO;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.PricingRouteInfoDO;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.json.AddressJson;
import com.sankuai.meituan.shangou.dms.base.model.value.Address;
import com.sankuai.meituan.shangou.dms.base.model.value.CoordinatePoint;
import com.sankuai.meituan.shangou.dms.base.model.value.Receiver;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.enums.RiderLocatingExceptionEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.utils.CoordinateUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.utils.DeliveryRiderMccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.utils.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.FailureCodeEnum.RIDER_ARRIVAL_LOCATION_NOT_EXIST;

/**
 * <AUTHOR>
 * @Date 2021/12/8
 * @Desc 门店骑手监控大屏response转换工具
 */
@Slf4j
public class RiderQueryResponseConvertUtils {
    public static StoreDeliveryMonitorResponse buildStoreDeliveryMonitorResponse(List<AccountInfo> riderAccountIdList,
                                                                                 Map<Long, RiderLocationDetail> riderLocationMap,
                                                                                 List<RiderDeliveryOrder> riderDeliveryOrders,
                                                                                 Map<Long, Optional<RiderLocatingExceptionDetail>> riderLocatingExceptionMap,
                                                                                 Map<ThirdRiderKey, ThirdRiderPoint> thirdRiderLocationMap,
                                                                                 List<DeliveryOrderDO> thirdDeliveryOrderDOS,
                                                                                 Map<Integer, DeliveryChannel> deliveryChannelMap) {
        StoreDeliveryMonitorResponse response = new StoreDeliveryMonitorResponse();

        List<TRiderDeliveryOrderMonitorInfo> deliveryOrderList = riderDeliveryOrders.stream()
                .map(riderDeliveryOrder -> RiderQueryResponseConvertUtils.translate2TRiderDeliveryOrderMonitorInfo(riderDeliveryOrder, deliveryChannelMap))
                .collect(Collectors.toList());

        List<TRiderDeliveryOrderMonitorInfo> thirdDeliveryOrderList = thirdDeliveryOrderDOS.stream()
                .map(thirdDeliveryOrderDO -> RiderQueryResponseConvertUtils.translate2TRiderDeliveryOrderMonitorInfo(thirdDeliveryOrderDO, deliveryChannelMap))
                .collect(Collectors.toList());

        List<TRiderMonitorInfo> tRiderMonitorInfoList = buildTRiderMonitorInfoList(riderAccountIdList, riderLocationMap, riderDeliveryOrders, riderLocatingExceptionMap, thirdRiderLocationMap, thirdDeliveryOrderDOS, deliveryChannelMap);

        deliveryOrderList.addAll(thirdDeliveryOrderList);
        response.setDeliveryOrderMonitorInfoList(deliveryOrderList);
        response.setRiderMonitorInfoList(tRiderMonitorInfoList);
        return response;
    }

    /**
     * 将 RiderDeliveryOrder 转为 TRiderDeliveryOrderMonitorInfo.
     *
     * @param deliveryOrder 运单
     * @return TRiderDeliveryOrderMonitorInfo
     */
    private static TRiderDeliveryOrderMonitorInfo translate2TRiderDeliveryOrderMonitorInfo(RiderDeliveryOrder deliveryOrder, Map<Integer, DeliveryChannel> deliveryChannelMap) {
        if (Objects.isNull(deliveryOrder)) {
            return null;
        }
        DeliveryChannel deliveryChannel = deliveryChannelMap.get(deliveryOrder.getDeliveryChannel());
        TRiderDeliveryOrderMonitorInfo riderDeliveryOrder = new TRiderDeliveryOrderMonitorInfo();
        riderDeliveryOrder.setDeliveryOrderId(deliveryOrder.getId());
        riderDeliveryOrder.setTenantId(deliveryOrder.getTenantId());
        riderDeliveryOrder.setStoreId(deliveryOrder.getStoreId());
        riderDeliveryOrder.setOrderId(deliveryOrder.getCustomerOrderKey().getOrderId());
        riderDeliveryOrder.setChannelOrderId(deliveryOrder.getCustomerOrderKey().getChannelOrderId());
        riderDeliveryOrder.setOrderBizTypeCode(deliveryOrder.getCustomerOrderKey().getOrderBizType());
        riderDeliveryOrder.setEstimatedDeliveryTime(TimeUtil.toMilliSeconds(deliveryOrder.getTimeline().getEstimatedDeliveryTime()));
        riderDeliveryOrder.setEstimatedDeliveryEndTime(TimeUtil.toMilliSeconds(deliveryOrder.getTimeline().getEstimatedDeliveryEndTime()));
        riderDeliveryOrder.setDeliveryStatus(deliveryOrder.getStatus().getCode());
        riderDeliveryOrder.setRiderInfo(translate2TStaffRider(deliveryOrder.getRiderInfo()));
        riderDeliveryOrder.setReceiver(translate2TReceiver(deliveryOrder.getReceiver()));
        riderDeliveryOrder.setCreateTime(TimeUtil.toMilliSeconds(deliveryOrder.getTimeline().getCreateTime()));
        riderDeliveryOrder.setDaySeq(deliveryOrder.getCustomerOrderKey().getDaySeq());
        riderDeliveryOrder.setCurrentStatusTimeoutStamp(calCurrentStatusTimeOutStamp(deliveryOrder));
        riderDeliveryOrder.setEntireOrderTimeoutStamp(calEntireOderTimeOutStamp(deliveryOrder));
        if (Objects.nonNull(deliveryChannel)) {
            riderDeliveryOrder.setDeliveryPlatformCode(deliveryChannel.getDeliveryPlatFormCode());
            riderDeliveryOrder.setDeliveryChannelName(deliveryChannel.getCarrierName());
        }
        if (Objects.nonNull(deliveryOrder.getRiderDeliveryExtInfo()) && Objects.nonNull(deliveryOrder.getRiderDeliveryExtInfo().getAssessDeliveryTime())) {
            riderDeliveryOrder.setAssessDeliveryTime(deliveryOrder.getRiderDeliveryExtInfo().getAssessDeliveryTime());
        }
        riderDeliveryOrder.setPickDeliverySplitTag(Objects.nonNull(deliveryOrder.getRiderDeliveryExtInfo()) && Objects.equals(deliveryOrder.getRiderDeliveryExtInfo().getPickDeliverySplitTag(), true));
        return riderDeliveryOrder;
    }

    private static TRiderDeliveryOrderMonitorInfo translate2TRiderDeliveryOrderMonitorInfo(DeliveryOrderDO deliveryOrder, Map<Integer, DeliveryChannel> deliveryChannelMap) {
        if (Objects.isNull(deliveryOrder)) {
            return null;
        }
        DeliveryChannel deliveryChannel = deliveryChannelMap.get(deliveryOrder.getDeliveryChannel());
        TRiderDeliveryOrderMonitorInfo riderDeliveryOrder = new TRiderDeliveryOrderMonitorInfo();
        riderDeliveryOrder.setDeliveryOrderId(deliveryOrder.getId());
        riderDeliveryOrder.setTenantId(deliveryOrder.getTenantId());
        riderDeliveryOrder.setStoreId(deliveryOrder.getStoreId());
        riderDeliveryOrder.setOrderId(deliveryOrder.getOrderId());
        riderDeliveryOrder.setChannelOrderId(deliveryOrder.getChannelOrderId());
        riderDeliveryOrder.setOrderBizTypeCode(deliveryOrder.getOrderBizType());
        riderDeliveryOrder.setEstimatedDeliveryTime(TimeUtil.toMilliSeconds(deliveryOrder.getEstimatedDeliveryTime()));
        riderDeliveryOrder.setEstimatedDeliveryEndTime(TimeUtil.toMilliSeconds(deliveryOrder.getEstimatedDeliveryEndTime()));
        riderDeliveryOrder.setDeliveryStatus(deliveryOrder.getDeliveryStatus());
        riderDeliveryOrder.setRiderInfo(new TStaffRider(deliveryOrder.getRiderName(), deliveryOrder.getRiderPhone(), deliveryOrder.getRiderAccountId(), 1, Lists.newArrayList()));
        riderDeliveryOrder.setReceiver(translate2TReceiver(deliveryOrder));
        riderDeliveryOrder.setCreateTime(TimeUtil.toMilliSeconds(deliveryOrder.getCreateTime()));
        riderDeliveryOrder.setDaySeq(deliveryOrder.getDaySeq());
        riderDeliveryOrder.setCurrentStatusTimeoutStamp(calCurrentStatusTimeOutStamp(deliveryOrder));
        riderDeliveryOrder.setEntireOrderTimeoutStamp(calEntireOderTimeOutStamp(deliveryOrder));
        if (Objects.nonNull(deliveryChannel)) {
            riderDeliveryOrder.setDeliveryPlatformCode(deliveryChannel.getDeliveryPlatFormCode());
            riderDeliveryOrder.setDeliveryChannelName(deliveryChannel.getCarrierName());
        }
        return riderDeliveryOrder;
    }

    private static TStaffRider translate2TStaffRider(StaffRider riderInfo) {
        if (riderInfo == null) {
            return null;
        }

        return new TStaffRider(riderInfo.getRiderName(), riderInfo.getRiderPhone(), riderInfo.getRiderAccountId(), null, null);
    }

    /**
     * 将 Receiver 转为 TReceiver.
     *
     * @param receiver 收货人
     * @return TReceiver
     */
    private static TReceiver translate2TReceiver(Receiver receiver) {
        TReceiver tReceiver = new TReceiver();
        if (receiver == null) {
            return tReceiver;
        }
        tReceiver.setReceiverName(receiver.getReceiverName());
        tReceiver.setReceiverPhone(receiver.getReceiverPhone());
        tReceiver.setReceiverPrivacyPhone(receiver.getReceiverPrivacyPhone());
        Address receiverAddress = receiver.getReceiverAddress();
        if (receiverAddress == null) {
            return tReceiver;
        }
        tReceiver.setAddressDetail(receiverAddress.getAddressDetail());
        // 返回上游统一采用 火星坐标系
        tReceiver.setCoordinateType(CoordinateTypeEnum.MARS.getCode());
        CoordinatePoint coordinatePoint = receiverAddress.getCoordinatePoint();
        if (receiverAddress.getCoordinateType() == CoordinateTypeEnum.BAIDU && coordinatePoint != null) {
            coordinatePoint = CoordinateUtil.translateFromBaiduToMars(coordinatePoint);
        }
        tReceiver.setLongitude(Optional.ofNullable(coordinatePoint).map(CoordinatePoint::getLongitude).orElse(null));
        tReceiver.setLatitude(Optional.ofNullable(coordinatePoint).map(CoordinatePoint::getLatitude).orElse(null));
        return tReceiver;
    }

    private static TReceiver translate2TReceiver(DeliveryOrderDO deliveryOrderDO) {
        TReceiver tReceiver = new TReceiver();
        if (deliveryOrderDO == null) {
            return tReceiver;
        }
        tReceiver.setReceiverName(deliveryOrderDO.getReceiverName());
        tReceiver.setReceiverPhone(deliveryOrderDO.getReceiverPhone());
        tReceiver.setReceiverPrivacyPhone(deliveryOrderDO.getReceiverPrivacyPhone());
        try {
            AddressJson addressJson = JsonUtil.fromJson(deliveryOrderDO.getReceiverAddress(), AddressJson.class);
            if (addressJson == null) {
                return tReceiver;
            }
            tReceiver.setAddressDetail(addressJson.getAddressDetail());
            // 返回上游统一采用 火星坐标系
            tReceiver.setCoordinateType(CoordinateTypeEnum.MARS.getCode());
            String longitude = addressJson.getLongitude();
            String latitude = addressJson.getLatitude();
            if (addressJson.getCoordinateType() == CoordinateTypeEnum.BAIDU.getCode() && Objects.nonNull(addressJson.getLatitude()) && Objects.nonNull(addressJson.getLongitude())) {
                CoordinatePoint coordinatePoint = CoordinateUtil.translateFromBaiduToMars(new CoordinatePoint(addressJson.getLongitude(), addressJson.getLatitude()));
                longitude = coordinatePoint.getLongitude();
                latitude = coordinatePoint.getLatitude();
            }
            tReceiver.setLongitude(longitude);
            tReceiver.setLatitude(latitude);
        } catch (Exception e) {
            log.error("translate2TReceiver error, set AddressJson error", e);
        }
        return tReceiver;
    }

    /**
     * 将账号信息、订单信息、骑手位置信息组装成骑手监控信息
     *
     * @param riderAccountList    骑手账号信息
     * @param riderLocationMap    骑手位置信息
     * @param riderDeliveryOrders 订单信息
     * @return List<TRiderMonitorInfo> 骑手监控信息
     */
    public static List<TRiderMonitorInfo> buildTRiderMonitorInfoList(List<AccountInfo> riderAccountList,
                                                                     Map<Long, RiderLocationDetail> riderLocationMap,
                                                                     List<RiderDeliveryOrder> riderDeliveryOrders,
                                                                     Map<Long, Optional<RiderLocatingExceptionDetail>> riderLocatingExceptionMap,
                                                                     Map<ThirdRiderKey, ThirdRiderPoint> thirdRiderLocationMap,
                                                                     List<DeliveryOrderDO> thirdDeliveryOrderDOS,
                                                                     Map<Integer, DeliveryChannel> deliveryChannelMap) {

        Map<Long, List<TRiderDeliveryOrderMonitorInfo>> riderRelatedOrderMap = getRiderRelatedOrderMap(riderAccountList, riderDeliveryOrders, deliveryChannelMap);
        List<TRiderMonitorInfo> tRiderMonitorInfos = riderAccountList.stream().map(rider -> {
            TRiderMonitorInfo tRiderMonitorInfo = new TRiderMonitorInfo();
            tRiderMonitorInfo.setOrderInfoList(riderRelatedOrderMap.get(rider.getAccountId()));
            tRiderMonitorInfo.setStaffRider(new TStaffRider(rider.getUserName(), rider.getPhone(), rider.getAccountId(),
                    rider.getAccountType(), rider.getRoleIdList()));
            tRiderMonitorInfo.setLocation(translateLocation(rider.getAccountId(), riderLocationMap));
            tRiderMonitorInfo.setLocatingExceptionType(translateLocatingException(rider.getAccountId(), riderLocationMap, riderLocatingExceptionMap));
            return tRiderMonitorInfo;
        }).collect(Collectors.toList());


        //三方骑手只能按姓名+电话聚合
        List<TRiderMonitorInfo> thirdTRiderMonitorList = getThirdRiderMonitorInfos(thirdDeliveryOrderDOS, thirdRiderLocationMap, deliveryChannelMap);

        tRiderMonitorInfos.addAll(thirdTRiderMonitorList);
        return tRiderMonitorInfos;
    }

    private static List<TRiderMonitorInfo> getThirdRiderMonitorInfos(List<DeliveryOrderDO> thirdDeliveryOrderDOS, Map<ThirdRiderKey, ThirdRiderPoint> thirdRiderLocationMap, Map<Integer, DeliveryChannel> deliveryChannelMap) {
        try {
            Map<ThirdRiderKey, List<DeliveryOrderDO>> riderKeyAndThirdRiderOrderMap = thirdDeliveryOrderDOS
                    .stream()
                    //三方配送
                    .filter(riderDeliveryOrder -> !Objects.equals(riderDeliveryOrder.getDeliveryChannel(), DeliveryChannelEnum.MERCHANT_DELIVERY.getCode()))
                    .filter(order -> order.getDeliveryStatus() == RiderDeliveryStatusEnum.RIDER_ASSIGNED.getCode()
                            || order.getDeliveryStatus() == RiderDeliveryStatusEnum.RIDER_TAKEN_GOODS.getCode())
                    .filter(order -> StringUtils.isNotBlank(order.getRiderPhone()) && StringUtils.isNotBlank(order.getRiderName()))
                    .collect(Collectors.groupingBy(order -> new ThirdRiderKey(order.getRiderName(), order.getRiderPhone())));

            return riderKeyAndThirdRiderOrderMap
                    .entrySet()
                    .stream()
                    .map(
                            entry -> {
                                TRiderMonitorInfo tRiderMonitorInfo = new TRiderMonitorInfo();
                                tRiderMonitorInfo.setOrderInfoList(entry.getValue().stream().map(deliveryOrderDO -> RiderQueryResponseConvertUtils.translate2TRiderDeliveryOrderMonitorInfo(deliveryOrderDO, deliveryChannelMap)).collect(Collectors.toList()));
                                tRiderMonitorInfo.setStaffRider(new TStaffRider(entry.getKey().getName(), entry.getKey().getPhone(), 0L,
                                        0, Lists.newArrayList()));
                                tRiderMonitorInfo.setLocation(translateLocation(entry.getKey(), thirdRiderLocationMap));
                                tRiderMonitorInfo.setLocatingExceptionType(null);
                                return tRiderMonitorInfo;
                            }
                    ).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("getThirdRiderMonitorInfos error");
            return Lists.newArrayList();
        }
    }

    /**
     * 构建骑手到刻位置信息查询响应
     *
     * @param riderArrivalLocation 骑手到刻位置信息(位置+距离)
     * @return 骑手到刻位置信息查询响应
     */
    public static QueryRiderArrivalLocationResponse buildRiderArrivalLocationResponse(RiderArrivalLocation riderArrivalLocation) {
        if (riderArrivalLocation == null) {
            return new QueryRiderArrivalLocationResponse(new Status(RIDER_ARRIVAL_LOCATION_NOT_EXIST.getCode(), RIDER_ARRIVAL_LOCATION_NOT_EXIST.getMessage()));
        }

        TRiderArrivalLocation tRiderArrivalLocation = new TRiderArrivalLocation();
        tRiderArrivalLocation.setLocationCoordinate(new TLocationCoordinate(riderArrivalLocation.getLongitude(),riderArrivalLocation.getLatitude()));

        tRiderArrivalLocation.setNavigationDistance(riderArrivalLocation.getNavigationDistance());
        tRiderArrivalLocation.setLinearDistance(riderArrivalLocation.getLineDistance());

        return new QueryRiderArrivalLocationResponse(tRiderArrivalLocation);
    }

    /**
     * 构造StoreRiderAccountListResponse
     * @param accountInfoList 骑手账号信息列表
     * @return StoreRiderAccountListResponse
     */
    public static StoreRiderAccountListResponse buildStoreRiderAccountListResponse(List<AccountInfo> accountInfoList) {
        StoreRiderAccountListResponse response = new StoreRiderAccountListResponse();
        if (CollectionUtils.isEmpty(accountInfoList)) {
            response.setRiderList(Collections.emptyList());
            return response;
        }

        List<TStaffRider> riderList = accountInfoList.stream()
                .map(accountInfo -> new TStaffRider(accountInfo.getUserName(), accountInfo.getPhone(),
                        accountInfo.getAccountId(), accountInfo.getAccountType(), accountInfo.getRoleIdList()))
                .collect(Collectors.toList());

        response.setRiderList(riderList);

        return response;
    }

    public static PageQueryDeliveryExceptionListResponse buildPageQueryDeliveryExceptionListResponse(PageResult<RiderDeliveryException> riderDeliveryExceptionPageResult) {

        TPageInfo pageInfo = new TPageInfo(riderDeliveryExceptionPageResult.getPage(), riderDeliveryExceptionPageResult.getPageSize(),
                Long.valueOf(riderDeliveryExceptionPageResult.getTotal()).intValue());

        if (riderDeliveryExceptionPageResult.getTotal() == 0 || CollectionUtils.isEmpty(riderDeliveryExceptionPageResult.getInfo())) {
            return new PageQueryDeliveryExceptionListResponse(pageInfo, Collections.emptyList());
        }

        List<TRiderDeliveryException> tRiderDeliveryExceptionList = riderDeliveryExceptionPageResult.getInfo().stream()
                .map(RiderQueryResponseConvertUtils::transform2TRiderDeliveryException)
                .collect(Collectors.toList());

        return new PageQueryDeliveryExceptionListResponse(pageInfo, tRiderDeliveryExceptionList);
    }

    public static DeliveryExceptionResponse buildQueryDeliveryExceptionByChannelOrderIdResponse(List<RiderDeliveryException> riderDeliveryExceptions) {
        if (CollectionUtils.isEmpty(riderDeliveryExceptions)) {
            return new DeliveryExceptionResponse(Collections.emptyList());
        }

        List<TRiderDeliveryException> tRiderDeliveryExceptionList = riderDeliveryExceptions.stream()
                .map(RiderQueryResponseConvertUtils::transform2TRiderDeliveryException)
                .collect(Collectors.toList());

        return new DeliveryExceptionResponse(tRiderDeliveryExceptionList);
    }

    public static void fillPricingRouteInfo(List<TRiderDeliveryOrder> tRiderDeliveryOrderList, List<PricingRouteInfoDO> pricingRouteInfoDOS) {
        if (CollectionUtils.isEmpty(pricingRouteInfoDOS)) {
            return;
        }

        Map<Long, TPricingRouteInfo> pricingRouteInfoMap = pricingRouteInfoDOS.stream()
                .map(RiderQueryResponseConvertUtils::transform2TPricingRouteInfo)
                .collect(Collectors.toMap(TPricingRouteInfo::getDeliveryOrderId, Function.identity(), (k1, k2) -> k2));

        if (CollectionUtils.isNotEmpty(tRiderDeliveryOrderList)) {
            tRiderDeliveryOrderList.forEach(tRiderDeliveryOrder -> {
                tRiderDeliveryOrder.setPricingRouteInfo(pricingRouteInfoMap.get(tRiderDeliveryOrder.getDeliveryOrderId()));
            });
        }
    }

    private static TPricingRouteInfo transform2TPricingRouteInfo(PricingRouteInfoDO pricingRouteInfoDO) {
        TPricingRouteInfo tPricingRouteInfo = new TPricingRouteInfo();
        tPricingRouteInfo.setRouteId(pricingRouteInfoDO.getRouteId());
        tPricingRouteInfo.setDeliveryOrderId(pricingRouteInfoDO.getDeliveryOrderId());
        tPricingRouteInfo.setPolyline(translate2TLocationCoordinateList(pricingRouteInfoDO.getPolyline()));
        tPricingRouteInfo.setOrigin(translate2TLocationCoordinate(pricingRouteInfoDO.getOrigin()));
        tPricingRouteInfo.setDestination(translate2TLocationCoordinate(pricingRouteInfoDO.getDestination()));
        if (Objects.nonNull(pricingRouteInfoDO.getDistance())) {
            tPricingRouteInfo.setDistance(pricingRouteInfoDO.getDistance().intValue());
        }
        if(Objects.nonNull(pricingRouteInfoDO.getDuration())) {
            tPricingRouteInfo.setDuration(pricingRouteInfoDO.getDuration().intValue());
        }

        return tPricingRouteInfo;
    }

    private static TRiderDeliveryException transform2TRiderDeliveryException(RiderDeliveryException riderDeliveryException) {
        if (riderDeliveryException == null) {
            return null;
        }
        TRiderDeliveryException tRiderDeliveryException = new TRiderDeliveryException();

        //设置补偿渠道订单id
        String channelOrderId = riderDeliveryException.getChannelOrderId();
        tRiderDeliveryException.setChannelOrderIdSeq(channelOrderId);
        if(NumberUtils.isCreatable(channelOrderId)){
            tRiderDeliveryException.setChannelOrderId(Long.valueOf(channelOrderId));
        }
        tRiderDeliveryException.setCreateTime(TimeUtil.toMilliSeconds(riderDeliveryException.getCreateTime()));
        tRiderDeliveryException.setDaySeq(riderDeliveryException.getDaySeq());
        tRiderDeliveryException.setDeliveryOrderId(riderDeliveryException.getDeliveryOrderId());
        tRiderDeliveryException.setExceptionSubType(riderDeliveryException.getExceptionSubType());
        tRiderDeliveryException.setExceptionType(riderDeliveryException.getExceptionType());
        tRiderDeliveryException.setOrderBizType(riderDeliveryException.getOrderBizType());
        tRiderDeliveryException.setTenantId(riderDeliveryException.getTenantId());
        tRiderDeliveryException.setStoreId(riderDeliveryException.getStoreId());
        tRiderDeliveryException.setRiderAccountId(riderDeliveryException.getRiderAccountId());
        tRiderDeliveryException.setRiderAccountName(riderDeliveryException.getRiderAccountName());
        tRiderDeliveryException.setPayTime(TimeUtil.toMilliSeconds(riderDeliveryException.getPayTime()));

        RiderDeliveryExceptionEnum exceptionEnum = RiderDeliveryExceptionEnum.enumOf(riderDeliveryException.getExceptionType(), riderDeliveryException.getExceptionSubType());

        if (exceptionEnum != null) {
            tRiderDeliveryException.setExceptionTypeDesc(exceptionEnum.getExceptionDesc());
            tRiderDeliveryException.setExceptionSubTypeDesc(exceptionEnum.getExceptionSubDesc());
        }

        if (riderDeliveryException.getExceptionDescription() != null) {
            tRiderDeliveryException.setUserRealAddress(riderDeliveryException.getExceptionDescription().getUserRealAddress());
            tRiderDeliveryException.setPicUrls(riderDeliveryException.getExceptionDescription().getPicUrls());
            tRiderDeliveryException.setComment(riderDeliveryException.getExceptionDescription().getComment());
            tRiderDeliveryException.setModifiedAddress(riderDeliveryException.getExceptionDescription().getModifiedAddress());
        }


        return tRiderDeliveryException;

    }

    /**
     * 将运单挂到对应的骑手上
     *
     * @param riderAccountIdList  骑手账号列表
     * @param riderDeliveryOrders 门店运单列表
     * @return 骑手-运单列表 的映射关系
     */
    private static Map<Long, List<TRiderDeliveryOrderMonitorInfo>> getRiderRelatedOrderMap(List<AccountInfo> riderAccountIdList,
                                                                                           List<RiderDeliveryOrder> riderDeliveryOrders,
                                                                                           Map<Integer, DeliveryChannel> deliveryChannelMap) {
        //获取已分配骑手的运单列表
        List<RiderDeliveryOrder> relatedRiderOrderList = riderDeliveryOrders.stream()
                .filter(riderDeliveryOrder -> {
                    DeliveryChannel deliveryChannel = deliveryChannelMap.get(riderDeliveryOrder.getDeliveryChannel());
                    return deliveryChannel != null && Objects.equals(deliveryChannel.getDeliveryPlatFormCode(), DeliveryPlatformEnum.MERCHANT_SELF_DELIVERY.getCode());
                })
                .filter(order -> order.getStatus().getCode() == RiderDeliveryStatusEnum.RIDER_ASSIGNED.getCode()
                        || order.getStatus().getCode() == RiderDeliveryStatusEnum.RIDER_TAKEN_GOODS.getCode())
                .filter(order -> Objects.nonNull(order.getRiderInfo()))
                .collect(Collectors.toList());

        //将骑手-运单列表Map化
        Map<Long, List<TRiderDeliveryOrderMonitorInfo>> riderRelatedOrderMap = riderAccountIdList.stream()
                .collect(Collectors.toMap(AccountInfo::getAccountId, value -> new ArrayList<>(), (k1, k2) -> k1));
        for (RiderDeliveryOrder order : relatedRiderOrderList) {
            Long riderAccountId = order.getRiderInfo().getRiderAccountId();
            if (riderRelatedOrderMap.containsKey(riderAccountId)) {
                riderRelatedOrderMap.get(riderAccountId).add(translate2TRiderDeliveryOrderMonitorInfo(order, deliveryChannelMap));
            }
        }

        return riderRelatedOrderMap;
    }

    private static TLocationCoordinate translateLocation(Long accountId, Map<Long, RiderLocationDetail> riderLocationMap) {
        if (riderLocationMap.containsKey(accountId)) {
            RiderLocationDetail riderLocationDetail = riderLocationMap.get(accountId);
            return new TLocationCoordinate(riderLocationDetail.getLongitude(), riderLocationDetail.getLatitude());
        }

        return null;
    }

    private static TLocationCoordinate translateLocation(ThirdRiderKey thirdRiderKey, Map<ThirdRiderKey, ThirdRiderPoint> thirdRiderLocationMap) {
        if (thirdRiderLocationMap.containsKey(thirdRiderKey)) {
            ThirdRiderPoint thirdRiderPoint = thirdRiderLocationMap.get(thirdRiderKey);
            return new TLocationCoordinate(thirdRiderPoint.getPoint().getLongitude(), thirdRiderPoint.getPoint().getLatitude());
        }

        return null;
    }

    /**
     * 组装定位异常信息:
     * 查到位置时异常为null
     * 没有查到位置但是查到异常，异常取查到的异常
     * 没有查到位置也没有查到异常，异常取未知
     *
     * @param accountId                 骑手账号id
     * @param riderLocationMap          骑手位置map
     * @param riderLocatingExceptionMap 骑手定位异常map
     * @return
     */
    private static Integer translateLocatingException(Long accountId, Map<Long, RiderLocationDetail> riderLocationMap,
                                                      Map<Long, Optional<RiderLocatingExceptionDetail>> riderLocatingExceptionMap) {
        if (riderLocationMap.containsKey(accountId)) {
            return null;
        } else {
            Optional<RiderLocatingExceptionDetail> riderLocatingExceptionDetail = riderLocatingExceptionMap.getOrDefault(accountId, Optional.empty());
            return riderLocatingExceptionDetail
                    .map(exceptionDetail -> exceptionDetail.getRiderLocatingExceptionEnum().getValue())
                    .orElseGet(RiderLocatingExceptionEnum.UNKNOWN_EXCEPTION::getValue);
        }
    }

    private static Long calCurrentStatusTimeOutStamp(RiderDeliveryOrder order) {
        return calEndTime(getStartTime(order), getDuration(order));
    }

    private static Long calEntireOderTimeOutStamp(RiderDeliveryOrder order) {
        // 实时单
        if (Objects.isNull(order.getCustomerOrderKey().getReserved()) || !order.getCustomerOrderKey().getReserved()) {
            return calEndTime(order.getTimeline().getEstimatedDeliveryEndTime(), DeliveryRiderMccConfigUtils.getInTimeOrderDeliveryTimeOutDuration2B());
        }

        // 预约单
        return calEndTime(order.getTimeline().getEstimatedDeliveryEndTime(), DeliveryRiderMccConfigUtils.getBookingOrderDeliveryTimeOutDuration2B());
    }


    /**
     * 计算运单超时的时间戳
     *
     * @param startTime 计算运单超时的开始时间点
     * @param duration  运单超时配置时长
     * @return 运单超时的时间戳
     */
    private static Long calEndTime(LocalDateTime startTime, int duration) {
        if (startTime == null) {
            return null;
        }

        // 开始时间是2000年以前则认为是无效的开始时间
        if (startTime.isBefore(LocalDateTime.of(2000, 1, 1, 0, 0))) {
            return null;
        }

        return TimeUtil.toMilliSeconds(startTime.plusMinutes(duration));
    }


    private static Long calCurrentStatusTimeOutStamp(DeliveryOrderDO order) {
        return calEndTime(getStartTime(order), getDuration(order));
    }

    private static Long calEntireOderTimeOutStamp(DeliveryOrderDO order) {
        // 实时单
        if (Objects.isNull(order.getReserved()) || !Objects.equals(1, order.getReserved())) {
            return calEndTime(order.getEstimatedDeliveryEndTime(), DeliveryRiderMccConfigUtils.getInTimeOrderDeliveryTimeOutDuration2B());
        }

        // 预约单
        return calEndTime(order.getEstimatedDeliveryEndTime(), DeliveryRiderMccConfigUtils.getBookingOrderDeliveryTimeOutDuration2B());
    }

    /**
     * 获取计算运单超时的开始时间点
     *
     * @param order 运单
     * @return 计算运单超时的开始时间点
     */
    private static LocalDateTime getStartTime(DeliveryOrderDO order) {
        switch (DeliveryStatusEnum.valueOf(order.getDeliveryStatus())) {
            case WAITING_TO_ASSIGN_RIDER:
            case RIDER_ASSIGNED:
                return order.getCreateTime();
            default: {
                return order.getEstimatedDeliveryEndTime();
            }
        }
    }


    /**
     * 获取计算运单超时的开始时间点
     *
     * @param order 运单
     * @return 计算运单超时的开始时间点
     */
    private static LocalDateTime getStartTime(RiderDeliveryOrder order) {
        switch (order.getStatus()) {
            case WAITING_TO_ASSIGN_RIDER:
            case RIDER_ASSIGNED:
                return order.getTimeline().getCreateTime();
            default: {
                return order.getTimeline().getEstimatedDeliveryEndTime();
            }
        }
    }

    /**
     * 获取运单超时配置时长
     *
     * @param order 运单
     * @return 超时配置时长 单位：分钟
     */
    private static int getDuration(RiderDeliveryOrder order) {
        switch (order.getStatus()) {
            case WAITING_TO_ASSIGN_RIDER:
                return DeliveryRiderMccConfigUtils.getDHRiderAssignTimeoutMinutes();
            case RIDER_ASSIGNED:
                return DeliveryRiderMccConfigUtils.getDHRiderTakenGoodsTimeoutMinutes();
            default: {
                // 实时单
                if (Objects.isNull(order.getCustomerOrderKey().getReserved()) || !order.getCustomerOrderKey().getReserved()) {
                    return DeliveryRiderMccConfigUtils.getInTimeOrderDeliveryTimeOutDuration2B();
                }

                // 预约单
                return DeliveryRiderMccConfigUtils.getBookingOrderDeliveryTimeOutDuration2B();
            }
        }

    }

    private static int getDuration(DeliveryOrderDO order) {
        switch (DeliveryStatusEnum.valueOf(order.getDeliveryStatus())) {
            case WAITING_TO_ASSIGN_RIDER:
                return DeliveryRiderMccConfigUtils.getDHRiderAssignTimeoutMinutes();
            case RIDER_ASSIGNED:
                return DeliveryRiderMccConfigUtils.getDHRiderTakenGoodsTimeoutMinutes();
            default: {
                // 实时单
                if (Objects.isNull(order.getReserved()) || !Objects.equals(1, order.getReserved())) {
                    return DeliveryRiderMccConfigUtils.getInTimeOrderDeliveryTimeOutDuration2B();
                }

                // 预约单
                return DeliveryRiderMccConfigUtils.getBookingOrderDeliveryTimeOutDuration2B();
            }
        }

    }

    private static List<TLocationCoordinate> translate2TLocationCoordinateList(String polyline) {
        if (StringUtils.isBlank(polyline)) {
            return Collections.emptyList();
        }
        String[] coordinationList = polyline.split(",");
        List<TLocationCoordinate> tCoordinationList = new ArrayList<>();
        for (int i = 0; i < coordinationList.length; i = i + 2) {
            tCoordinationList.add(new TLocationCoordinate(coordinationList[i], coordinationList[i + 1]));
        }

        return tCoordinationList;
    }

    private static TLocationCoordinate translate2TLocationCoordinate(String coordinateStr) {
        if (StringUtils.isBlank(coordinateStr)) {
            return null;
        }
        String[] coordinationList = coordinateStr.split(",");
        if (coordinationList.length == 2) {
            return new TLocationCoordinate(coordinationList[0], coordinationList[1]);
        }

        log.error("解析经纬度点失败, coordinateStr: {}", coordinateStr);
        return null;
    }
}
