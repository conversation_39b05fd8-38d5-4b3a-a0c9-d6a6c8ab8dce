package com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class RiderLocationDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 骑手账号id，默认0
     */
    private Long riderAccountId;

    /**
     * 骑手姓名
     */
    private String riderName;

    /**
     * 骑手手机号码
     */
    private String riderPhone;

    //经度
    private String longitude;

    //纬度
    private String latitude;

    //定位结果来源
    private String provider;

    //定位结果精确度
    private String accuracy;

    //方向信息
    private String bearing;

    //速度信息
    private String speed;

    //时间信息
    private String time;

    private String os;

    //牵牛花app版本
    private String appVersion;

    // 骑手设备id
    private String uuid;

    /**
     * 时间戳
     */
    public long getLongTypeTime() {
        try {
            return Long.parseLong(time);
        } catch (NumberFormatException e) {
            return 0;
        }
    }

}
