package com.sankuai.meituan.shangou.empower.tms.delivery.rider.application;

import com.dianping.cat.Cat;
import com.meituan.mtrace.thread.pool.ExecutorServiceTraceWrapper;

import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.RiderDeliveryStatisticDO;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.tenant.PoiBaseInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.tenant.TenantSystemClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.application.resp.DeliveryRiderStatistics;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.application.resp.DeliveryStoreStatistics;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.application.resp.RiderDurationDeliveryStatistics;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.service.DeliveryCapacityLoadMonitorService;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.enums.PushMessageTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.task.exception.CommonRuntimeException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 该接口专门处理骑手运单统计维度的信息查询
 */
@Service
@Slf4j
public class RiderDeliveryStatisticsApplicationService {

    /**
     * 查询运单的分页大小
     */
    private static final int pageSize = 100;

    /**
     * 订单那边的兜底逻辑：用户在下单时，会有预计送达时间，订单那边在预计送达时间后4个小时后会将没完成的订单自动完成，所以这里只需要查过去5个小时内创建的运单
     */
    private static final Integer queryDuration = 5;

    /**
     * 从数据库能查询的最大运单数据量（当前业务还远未达到），达到该值将触发cat告警，提醒研发调整查询方案，比如减少门店数量，或者不走mysql，而改走es查询
     */
    private static final Integer maxCatAlertNum = 10000;

    /**
     * 达到该值直接抛异常，保证系统可用性
     */
    private static final Integer throwExceptionNum = 15000;

    /**
     * 防止循环查询次数过多，限制最大查询次数
     */
    private static final Integer maxSize = 10;

    /**
     * 属于履约中的状态列表
     */
    private static List<DeliveryStatusEnum> fulfilStatusList = Lists.list(DeliveryStatusEnum.WAITING_TO_ASSIGN_RIDER,
            DeliveryStatusEnum.RIDER_ASSIGNED, DeliveryStatusEnum.RIDER_TAKEN_GOODS);

    /**
     *  日期格式
     */
    private static final DateTimeFormatter yyyyMMdd = DateTimeFormatter.ofPattern("yyyyMMdd");

    @Resource
    private RiderDeliveryOrderRepository riderDeliveryOrderRepository;

    @Resource
    private RiderDeliveryStatisticsRepository riderDeliveryStatisticsRepository;

    @Resource
    private IDeliveryCapacityLoadRepository deliveryCapacityLoadRepository;

    @Resource
    private DeliveryCapacityLoadMonitorService deliveryCapacityLoadMonitorService;

    @Resource
    private TenantSystemClient tenantSystemClient;

    public List<DeliveryRiderStatistics> queryRiderStatistics(Long tenantId, List<Long> storeIds, List<Long> riderAccountIds) {
        return ParamExchanger.riderStatistics(riderAccountIds, this.queryRiderDeliveryList(tenantId, storeIds, riderAccountIds));
    }

    public List<DeliveryStoreStatistics> queryStoreStatistics(Long tenantId, List<Long> storeIds) {
        return ParamExchanger.storeStatistics(storeIds, this.queryRiderDeliveryList(tenantId, storeIds, null));
    }


    public List<RiderDurationDeliveryStatistics> queryDurationsStatistics(Long tenantId, Long riderAccountId,
                                                                                        List<StatisticDuration> durations) {

        return durations.stream().limit(maxSize)
                .map(duration -> this.durationDeliveryStatistics(tenantId, riderAccountId, duration))
                .collect(Collectors.toList());
    }

    public Set<Long> pushOrderOverloadMessage(Long tenantId) {
        List<PoiBaseInfo> poiList = tenantSystemClient.queryTenantStoreList(tenantId);
        List<Long> poiIds = poiList.stream().map(PoiBaseInfo::getPoiId).collect(Collectors.toList());
        HashSet<Long> pushStoreSet = new HashSet<>();

        int batchSize = 30;
        int index = 0;
        while (index < poiIds.size()) {
            List<Long> poiSubList = poiIds.subList(index, Math.min(index + batchSize, poiList.size()));

            //计算每个门店的运力负载
            StoreDeliveryCapacityLoadInfo storeDeliveryCapacityLoadInfo = deliveryCapacityLoadMonitorService.calStoreDeliveryCapacityLoadInfo(tenantId, poiSubList);

            //计算需要推送的门店以及推送的消息类型
            Map<Long, PushMessageTypeEnum> pushMessageTypeMap = deliveryCapacityLoadMonitorService.pushXmMessage(tenantId, poiSubList, storeDeliveryCapacityLoadInfo);

            //更新门店运力负载记录
            deliveryCapacityLoadRepository.setStoreDeliveryCapacityRecord(deliveryCapacityLoadMonitorService.buildDeliveringCapacityLoadRecordMap(poiSubList,
                    storeDeliveryCapacityLoadInfo, pushMessageTypeMap));

            //记录推送了哪些门店
            pushStoreSet.addAll(pushMessageTypeMap.keySet());

            index = index + batchSize;
        }

        return pushStoreSet;
    }

    /**
     * 分页查询所有满足条件的运单列表，先查满足条件的总数，然后用多线程并发分页查询，
     * 为避免从查询总数到分页查询期间满足条件的运单状态发生变更，
     * 从而导致两个分页查询的线程返回的结果包含同相同运单，所以在最终组装返回结果的时候，需要根据运单的主键id进行去重
     */
    private List<RiderDeliveryOrder> queryRiderDeliveryList(Long tenantId, List<Long> storeIds, List<Long> riderAccountIds) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime start = now.minusHours(queryDuration);
        //1、查询满足条件的运单总数
        long total = riderDeliveryOrderRepository.countRiderDeliveryOrder(tenantId, fulfilStatusList, start, now, storeIds, riderAccountIds);
        if (total == 0) {
            return Collections.emptyList();
        }
        //2、满足条件的运单数量检测，防止数量过大，影响系统稳定性
        this.riderDeliveryOrderCountCheck(total);

        //3、多线程分页查询运单
        List<Future<Map<Long, RiderDeliveryOrder>>> futures = new ArrayList<>();
        long totalPage = (total - 1)/pageSize + 1;
        for (int page = 1; page <= totalPage; page++) {
            PageRequest pageRequest = new PageRequest(page, pageSize);
            futures.add(ThreadPoolUtil.getInstance().submit(() -> {
                List<RiderDeliveryOrder> orderList = riderDeliveryOrderRepository.queryRiderDeliveryOrderByPage(
                        pageRequest, tenantId, fulfilStatusList,
                        start, now, storeIds, riderAccountIds);
                return Optional.ofNullable(orderList).orElse(Collections.emptyList()).stream().collect(
                        Collectors.toMap(RiderDeliveryOrder::getId, Function.identity()));
            }));
        }

        //4、组装返回结果，map的key为riderDeliveryOrder的主键id，防止并发导致不同线程查询的结果包含相同运单
        Map<Long, RiderDeliveryOrder> resultMap = new HashMap<>();
        try {
            for (Future<Map<Long, RiderDeliveryOrder>> future : futures) {
                Map<Long, RiderDeliveryOrder> tmpMap = Optional.ofNullable(future.get()).orElse(Collections.emptyMap());
                for (Long id : tmpMap.keySet()) {
                    if (!resultMap.containsKey(id)) {
                        resultMap.put(id, tmpMap.get(id));
                    } else if (resultMap.get(id).canStatusBeReachedFromCurrent(tmpMap.get(id).getStatus())) {
                        //相同运单，则使用最新的运单状态
                        resultMap.put(id, tmpMap.get(id));
                    }
                }
            }
        } catch (Exception e) {
            log.error("queryRiderDeliveryList error, msg:{}", e.getMessage(), e);
            throw new CommonRuntimeException(e.getMessage(), e);
        }
        return new ArrayList<>(resultMap.values());
    }

    //运单数量检测
    private void riderDeliveryOrderCountCheck(long total) {
        if (total > throwExceptionNum) {
            log.info("riderDeliveryOrderRepository.countRiderDeliveryOrder alert count:{}", total);
            throw new CommonRuntimeException(String.format("delivery order exception count : [%s] > [%s]", total, throwExceptionNum));
        }
        if (total > maxCatAlertNum) {
            log.info("riderDeliveryOrderRepository.countRiderDeliveryOrder exception count:{}", total);
            Cat.logEvent("rider delivery alert count", String.valueOf(total));
        }
    }

    /**
     * 背景: 配送数据到岗时间为t+1 提前点送达数据到岗时间为t+2
     * 查询指定时间内的骑手配送数据统计,有四种情况:
     * 1.如果只查询昨天的数据 且昨天的数据未到岗 ==> 返回的时间范围为[begin,end],返回指标为null
     * 2.数据查询范围包含昨天,且昨天数据未到岗 ==> 返回的时间范围为[begin,昨天-1],返回的指标在0的基础上累加
     * 3.数据查询范围包含昨天,且昨天数据已到岗 ==> 返回的时间范围为[begin,昨天],返回的指标在0的基础上累加
     * 4.数据查询范围不包含昨天 ==> 返回的时间范围为[begin,end],返回的指标在0的基础上累加
     */
    private RiderDurationDeliveryStatistics durationDeliveryStatistics(Long tenantId, Long accountId,
                                                                      StatisticDuration duration) {
        //前置校验: 如果只查询昨天的数据 且昨天的数据未到岗 直接返回空指标
        LocalDate yesterday = LocalDate.now(ZoneId.of("+8")).minusDays(1);
        if (duration.getBeginDate().isEqual(duration.getEndDate()) && duration.getBeginDate().equals(yesterday)
                && !checkDataIsReady(Long.parseLong(duration.getBeginDate().format(yyyyMMdd)))) {
            return RiderDurationDeliveryStatistics.newEmptyDeliveryStatistics(tenantId, accountId,
                    duration.getBeginDate().format(yyyyMMdd), duration.getEndDate().format(yyyyMMdd));
        }

        //时间范围前置处理
        StatisticDuration finalDuration = getActuallyDuration(duration);

        //查询范围内的配送数据
        List<RiderDeliveryStatisticDO> deliveryStatisticDOS = queryByAccountIdAndDuration(tenantId, accountId, finalDuration);

        //计算配送指标
        return ParamExchanger.calDurationDeliveryStatistics(tenantId, accountId, finalDuration, deliveryStatisticDOS);
    }


    /**
     * duration前置处理
     * 若duration endDate晚于昨天 需要将endDate设置为昨天
     * 若duration endDate等于昨天，则需要判断昨天的数据是否到岗 若数据未到岗则把结束时间设置成前天
     * 这样做主要是为了告诉上游服务 昨天的数据未到岗 所以最晚只能查到前天的数据
     */
    private StatisticDuration getActuallyDuration(StatisticDuration originalDuration) {
        LocalDate yesterday = LocalDate.now(ZoneId.of("+8")).minusDays(1);
        LocalDate beginDate = originalDuration.getBeginDate();
        LocalDate endDate = originalDuration.getEndDate();

        //最晚只能查昨天的数据
        if (endDate.isAfter(yesterday)) {
            endDate = yesterday;
        }

        if (endDate.equals(yesterday) && !checkDataIsReady(Long.parseLong(yesterday.format(yyyyMMdd)))) {
            return new StatisticDuration(beginDate, yesterday.minusDays(1));
        }

        return new StatisticDuration(beginDate, endDate);
    }

    /**
     * check数据是否到岗 若当天所有骑手都没有数据 判定为数据未到岗
     */
    private boolean checkDataIsReady(Long dt) {
        return riderDeliveryStatisticsRepository.checkDataIsReadyByDt(dt);
    }

    /**
     * 查询骑手一段时间内的配送数据列表(每条记录表示一天的配送数据)
     */
    private List<RiderDeliveryStatisticDO> queryByAccountIdAndDuration(Long tenantId, Long riderAccountId,
                                                                 StatisticDuration duration) {

        if (duration.getBeginDate().isAfter(duration.getEndDate())) {
            return Collections.emptyList();
        }

        String beginDateStr = duration.getBeginDate().format(yyyyMMdd);
        String endDateStr = duration.getEndDate().format(yyyyMMdd);

        return riderDeliveryStatisticsRepository.queryByAccountIdAndDuration(tenantId, riderAccountId,
                Long.valueOf(beginDateStr), Long.valueOf(endDateStr));

    }

    public static class ParamExchanger {


        public static List<DeliveryRiderStatistics> riderStatistics(List<Long> riderAccountIds,
                                                                    List<RiderDeliveryOrder> orderList) {

            //防御式编程，这里RIDER_TAKEN_GOODS状态下，不应该存在没有riderAccountId或者riderAccountId为0的运单
            Map<Long, List<RiderDeliveryOrder>> groupByRiderMap = orderList.stream()
                    .filter(order -> Objects.equals(order.getStatus().getCode(), DeliveryStatusEnum.RIDER_TAKEN_GOODS.getCode()))
                    //这里是兼容历史订单可能没有存储实付金额，对于没有存储实付金额的订单，都不过滤
                    .filter(order -> order.getRiderDeliveryExtInfo() == null  || order.getRiderDeliveryExtInfo().getActualPayAmt() == null || order.checkPayAmtBiggerThan(200))
                    .filter(order -> order.getRiderInfo() != null && order.getRiderInfo().getRiderAccountId() != null
                            && order.getRiderInfo().getRiderAccountId() > 0)
                    .collect(Collectors.groupingBy(order -> order.getRiderInfo().getRiderAccountId()));

            //指标组： Pair 存储：配送中（骑手已取货）订单量 + 配送中（骑手已取货）考核超时订单量
            Map<Long, Pair<Integer, Integer>> riderMap = groupByRiderMap.entrySet().stream().collect(Collectors.toMap(
                    Map.Entry::getKey, entry -> entry.getValue().stream().map(order ->
                                    Pair.of(1, order.checkDeliveryFulfillTimeout() ? 1 : 0))
                            .reduce(Pair.of(0, 0), (pair1, pair2) -> Pair.of(pair1.getLeft() + pair2.getLeft(),
                                    pair1.getRight() + pair2.getRight()))));

            Map<Long, Pair<Integer, Integer>> riderMapV2 = groupByRiderMap.entrySet().stream().collect(Collectors.toMap(
                    Map.Entry::getKey, entry -> entry.getValue().stream().map(order ->
                                    Pair.of(1, order.checkDeliveryFulfillTimeoutV2() ? 1 : 0))
                            .reduce(Pair.of(0, 0), (pair1, pair2) -> Pair.of(pair1.getLeft() + pair2.getLeft(),
                                    pair1.getRight() + pair2.getRight()))));

            Map<Long, List<RiderDeliveryOrder>> acceptedOrderGroupByRiderMap = orderList.stream()
                    .filter(order -> Objects.equals(order.getStatus().getCode(), DeliveryStatusEnum.RIDER_ASSIGNED.getCode()))
                    //这里是兼容历史订单可能没有存储实付金额，对于没有存储实付金额的订单，都不过滤
                    .filter(order -> order.getRiderDeliveryExtInfo() == null  || order.getRiderDeliveryExtInfo().getActualPayAmt() == null || order.checkPayAmtBiggerThan(200))
                    .filter(order -> order.getRiderInfo() != null && order.getRiderInfo().getRiderAccountId() != null
                            && order.getRiderInfo().getRiderAccountId() > 0)
                    .collect(Collectors.groupingBy(order -> order.getRiderInfo().getRiderAccountId()));

            //指标组： Pair 存储：拣货中（骑手已接单）订单量 + 拣货中（骑手已接单）考核超时订单量
            Map<Long, Pair<Integer, Integer>> riderPickingOrderMap = acceptedOrderGroupByRiderMap.entrySet().stream().collect(Collectors.toMap(
                    Map.Entry::getKey, entry -> entry.getValue().stream().map(order ->
                                    Pair.of(1, order.checkDeliveryFulfillTimeout() ? 1 : 0))
                            .reduce(Pair.of(0, 0), (pair1, pair2) -> Pair.of(pair1.getLeft() + pair2.getLeft(),
                                    pair1.getRight() + pair2.getRight()))));

            Map<Long, Pair<Integer, Integer>> riderPickingOrderMapV2 =
                    acceptedOrderGroupByRiderMap.entrySet().stream().collect(Collectors.toMap(
                    Map.Entry::getKey, entry -> entry.getValue().stream().map(order ->
                                    Pair.of(1, order.checkDeliveryFulfillTimeoutV2() ? 1 : 0))
                            .reduce(Pair.of(0, 0), (pair1, pair2) -> Pair.of(pair1.getLeft() + pair2.getLeft(),
                                    pair1.getRight() + pair2.getRight()))));

            //指标汇总
            return riderAccountIds.stream().map(id -> new DeliveryRiderStatistics(id,
                    riderMap.getOrDefault(id, Pair.of(0, 0)).getLeft(),
                    riderMap.getOrDefault(id, Pair.of(0, 0)).getRight(),
                    riderPickingOrderMap.getOrDefault(id, Pair.of(0, 0)).getLeft(),
                    riderPickingOrderMap.getOrDefault(id, Pair.of(0, 0)).getRight(),
                    riderPickingOrderMapV2.getOrDefault(id, Pair.of(0, 0)).getLeft(),
                    riderMapV2.getOrDefault(id, Pair.of(0, 0)).getLeft())).collect(Collectors.toList());
        }

        public static List<DeliveryStoreStatistics> storeStatistics(List<Long> storeIds, List<RiderDeliveryOrder> orderList) {

            //FIXME: 整体实现怪怪的，找时间重构下
            //这里是兼容历史订单可能没有存储实付金额，对于没有存储实付金额的订单，都不过滤
            Map<Long, List<RiderDeliveryOrder>> groupByStoreMap = orderList.stream()
                    .filter(order -> order.getRiderDeliveryExtInfo() == null  || order.getRiderDeliveryExtInfo().getActualPayAmt() == null || order.checkPayAmtBiggerThan(200))
                    .collect(Collectors.groupingBy(RiderDeliveryOrder::getStoreId));

            //指标组一：待领取订单量 + 待领取超时订单量
            Future<Map<Long, Pair<Integer, Integer>>> waitFuture = buildStatisticsFuture(groupByStoreMap,
                    DeliveryStatusEnum.WAITING_TO_ASSIGN_RIDER);

            Future<Map<Long, Pair<Integer, Integer>>> waitFutureV2 = buildStatisticsFutureV2(groupByStoreMap,
                    DeliveryStatusEnum.WAITING_TO_ASSIGN_RIDER);

            //指标组二：拣货中（骑手已接单）订单量 + 拣货中（骑手已接单）超时订单量
            Future<Map<Long, Pair<Integer, Integer>>> assignedFuture = buildStatisticsFuture(groupByStoreMap,
                    DeliveryStatusEnum.RIDER_ASSIGNED);

            Future<Map<Long, Pair<Integer, Integer>>> assignedFutureV2 = buildStatisticsFutureV2(groupByStoreMap,
                    DeliveryStatusEnum.RIDER_ASSIGNED);

            //指标组三：配送中（骑手已取货）订单量 + 配送中（骑手已取货）超时订单量
            Future<Map<Long, Pair<Integer, Integer>>> deliveringFuture = buildStatisticsFuture(groupByStoreMap,
                    DeliveryStatusEnum.RIDER_TAKEN_GOODS);

            Future<Map<Long, Pair<Integer, Integer>>> deliveringFutureV2 = buildStatisticsFutureV2(groupByStoreMap,
                    DeliveryStatusEnum.RIDER_TAKEN_GOODS);

            //单独指标一：履约中的订单数
            Future<Map<Long, Integer>> fulfillingFuture = ThreadPoolUtil.getInstance().submit(
                    () -> groupByStoreMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey,
                    entry -> entry.getValue().size())));

            //单独指标二：履约中的骑手数
            Future<Map<Long, Integer>> riderFuture = ThreadPoolUtil.getInstance().submit(
                    () -> groupByStoreMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey,
                    entry -> entry.getValue().stream().filter(order -> order.getRiderInfo() != null
                                    && order.getRiderInfo().getRiderAccountId() != null
                                    && order.getRiderInfo().getRiderAccountId() > 0)
                            .map(order -> order.getRiderInfo().getRiderAccountId()).collect(Collectors.toSet()).size())));
            //指标汇总
            try {
                return buildStoreStatistics(storeIds,
                        waitFuture.get(), waitFutureV2.get(), assignedFuture.get(), assignedFutureV2.get(), deliveringFuture.get(), deliveringFutureV2.get(), fulfillingFuture.get(),
                        riderFuture.get());
            } catch (Exception e) {
                log.error("storeStatistics future get error, msg:{}", e.getMessage(), e);
                throw new CommonRuntimeException(e);
            }

        }


        /**
         * 计算一段时间内的骑手配送指标
         * 由于提前点送达指标的到岗时间是t+2,所以这里需要特殊处理提前点送达指标的初始值,分为两种情况:
         * 1.若查不到配送记录 说明这段时间内骑手没有配送 ==> 提前点送达的指标应该初始化为0 而非null
         * 2.若查到了配送记录 这时候提前点送达的指标有可能未到岗 ==> 提前点送达的指标应该初始化为null
         */
        public static RiderDurationDeliveryStatistics calDurationDeliveryStatistics(Long tenantId, Long accountId,
                                                                                    StatisticDuration statisticDuration,
                                                                                    List<RiderDeliveryStatisticDO> riderDeliveryStatisticDOS) {

            Integer deliveredCount = 0, cancelBeforeDelivered = 0, timeoutCount = 0, deliveredIn25Mins = 0, deliveredExcludeBooking = 0,
                    deliveredTimeout = 0;
            Long deliveryDuration = 0L;

            Integer earlyClickDelivered = CollectionUtils.isEmpty(riderDeliveryStatisticDOS) ? 0 : null;

            //如果没有查到记录,说明这段时间骑手没有配送过，所以初始化为0而不是null
            Integer riskControlOrdCnt = CollectionUtils.isEmpty(riderDeliveryStatisticDOS) ? 0 : null;

            for (RiderDeliveryStatisticDO deliveryStatisticDO : riderDeliveryStatisticDOS) {
                deliveredCount += deliveryStatisticDO.getDelivered();
                cancelBeforeDelivered += deliveryStatisticDO.getCancelBeforeDelivered();
                timeoutCount += deliveryStatisticDO.getTimeout();
                deliveredIn25Mins += deliveryStatisticDO.getDeliveredIn25Mins();
                deliveryDuration += deliveryStatisticDO.getDeliveryDuration();
                deliveredExcludeBooking += deliveryStatisticDO.getDeliveredExcludeBooking();
                deliveredTimeout += deliveryStatisticDO.getDeliveredTimeout() != null ? deliveryStatisticDO.getDeliveredTimeout() : 0;
                if (Objects.nonNull(deliveryStatisticDO.getEarlyClick())) {
                    earlyClickDelivered = Objects.isNull(earlyClickDelivered) ? deliveryStatisticDO.getEarlyClick() :
                            earlyClickDelivered + deliveryStatisticDO.getEarlyClick();
                }

                //因为这个字段是后加的，所以之前的数据中这个字段的值是null,这里需要兼容一下
                // 1.如果这段时间内,所有值都是null,则相加的结果为null
                // 2.如果这段时间内,既有null又有值,则相加的结果为有值的结果相加
                // 3.如果这段时间内,所有记录都有值,则相加的结果为所有有值的结果相加
                riskControlOrdCnt = addValue(riskControlOrdCnt, deliveryStatisticDO.getRiskControl());
            }

            return RiderDurationDeliveryStatistics.builder()
                    .tenantId(tenantId)
                    .riderAccountId(accountId)
                    .deliveredCount(deliveredCount)
                    .cancelBeforeDeliveredCount(cancelBeforeDelivered)
                    .earlyClickDeliveryCount(earlyClickDelivered)
                    .riskControlCount(riskControlOrdCnt)
                    .avgFulfillDuration(Double.valueOf(divide(deliveryDuration.toString(), deliveredExcludeBooking.toString())))
                    .timeoutCount(timeoutCount)
                    .timeoutRate(calPercent(timeoutCount.toString(), deliveredCount.toString()))
                    .deliveredRateIn25min(calPercent(deliveredIn25Mins.toString(), deliveredExcludeBooking.toString()))
                    .etaOvertimeOrdNumV2(deliveredTimeout)
                    .etaOvertimeRatioV2(calPercent(BigDecimal.valueOf(deliveredTimeout), BigDecimal.valueOf(deliveredCount)))
                    .beginDate(statisticDuration.getBeginDate().format(yyyyMMdd))
                    .endDate(statisticDuration.getEndDate().format(yyyyMMdd))
                    .build();


        }

        private static String calPercent(BigDecimal partitionNumBigDecimal, BigDecimal totalNumBigDecimal) {
            if (partitionNumBigDecimal == null || totalNumBigDecimal == null) {
                return null;
            }
            if (totalNumBigDecimal.equals(BigDecimal.ZERO) && partitionNumBigDecimal.equals(BigDecimal.ZERO)) {
                return BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP).toString();
            }

            if (totalNumBigDecimal.equals(BigDecimal.ZERO) && !partitionNumBigDecimal.equals(BigDecimal.ZERO)) {
                return null;
            }

            BigDecimal result = partitionNumBigDecimal.multiply(new BigDecimal(100))
                    .divide(totalNumBigDecimal, 2, RoundingMode.HALF_UP);

            return result.toPlainString();
        }

        private static Integer addValue(Integer val1, Integer val2) {
            if (val1 == null && val2 == null) {
                return null;
            }

            if (val1 == null) {
                return val2;
            }

            if (val2 == null) {
                return val1;
            }

            return val1 + val2;
        }

        /**
         * 通过异步线程分组统计各组指标，每组指标目前包含总订单量和超时订单量两个指标
         *
         * @return
         */
        private static Future<Map<Long, Pair<Integer, Integer>>> buildStatisticsFuture(Map<Long, List<RiderDeliveryOrder>> groupByStoreMap,
                                                                                       DeliveryStatusEnum statusEnum) {
            return ThreadPoolUtil.getInstance().submit(() -> groupByStoreMap.entrySet().stream().collect(
                    Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().stream()
                            .filter(order -> Objects.equals(statusEnum.getCode(), order.getStatus().getCode()))
                            .map(order -> Pair.of(1, order.checkDeliveryFulfillTimeout() ? 1 : 0))
                            .reduce(Pair.of(0, 0), (pair1, pair2) -> Pair.of(
                                    pair1.getLeft() + pair2.getLeft(), pair1.getRight() + pair2.getRight())))));
        }

        private static Future<Map<Long, Pair<Integer, Integer>>> buildStatisticsFutureV2(Map<Long, List<RiderDeliveryOrder>> groupByStoreMap,
                                                                                         DeliveryStatusEnum statusEnum) {
            return ThreadPoolUtil.getInstance().submit(() -> groupByStoreMap.entrySet().stream().collect(
                    Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().stream()
                            .filter(order -> Objects.equals(statusEnum.getCode(), order.getStatus().getCode()))
                            .map(order -> Pair.of(1, order.checkDeliveryFulfillTimeoutV2() ? 1 : 0))
                            .reduce(Pair.of(0, 0), (pair1, pair2) -> Pair.of(
                                    pair1.getLeft() + pair2.getLeft(), pair1.getRight() + pair2.getRight())))));
        }

        private static List<DeliveryStoreStatistics> buildStoreStatistics(List<Long> storeIds,
                                                                          Map<Long, Pair<Integer, Integer>> waitMap,
                                                                          Map<Long, Pair<Integer, Integer>> waitMapV2,
                                                                          Map<Long, Pair<Integer, Integer>> assignedMap,
                                                                          Map<Long, Pair<Integer, Integer>> assignedMapV2,
                                                                          Map<Long, Pair<Integer, Integer>> deliveringMap,
                                                                          Map<Long, Pair<Integer, Integer>> deliveringMapV2,
                                                                          Map<Long, Integer> fulfillingMap,
                                                                          Map<Long, Integer> riderMap) {

            return storeIds.stream().map(id -> new DeliveryStoreStatistics(id,
                    waitMap.getOrDefault(id, Pair.of(0, 0)).getLeft(),
                    waitMap.getOrDefault(id, Pair.of(0, 0)).getRight(),
                    waitMapV2.getOrDefault(id, Pair.of(0, 0)).getRight(),
                    assignedMap.getOrDefault(id, Pair.of(0, 0)).getLeft(),
                    assignedMap.getOrDefault(id, Pair.of(0, 0)).getRight(),
                    assignedMapV2.getOrDefault(id, Pair.of(0, 0)).getRight(),
                    deliveringMap.getOrDefault(id, Pair.of(0, 0)).getLeft(),
                    deliveringMap.getOrDefault(id, Pair.of(0, 0)).getRight(),
                    deliveringMapV2.getOrDefault(id, Pair.of(0, 0)).getRight(),
                    fulfillingMap.getOrDefault(id, 0),
                    riderMap.getOrDefault(id, 0))).collect(Collectors.toList());
        }

        /**
         * 计算百分比 单位:% 结果保留两位小数
         */
        private static String calPercent(String partition, String total) {
            BigDecimal partitionBigDecimal = new BigDecimal(partition);
            BigDecimal totalBigDecimal = new BigDecimal(total);
            if (totalBigDecimal.equals(BigDecimal.ZERO)) {
                return BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP).toString();
            }
            return partitionBigDecimal.multiply(new BigDecimal(100))
                    .divide(totalBigDecimal, 2, RoundingMode.HALF_UP)
                    .toString();
        }

        /**
         * 相除计算 结果保留6位小数
         */
        private static String divide(String partition, String total) {
            BigDecimal partitionBigDecimal = new BigDecimal(partition);
            BigDecimal totalBigDecimal = new BigDecimal(total);
            if (totalBigDecimal.equals(BigDecimal.ZERO)) {
                return BigDecimal.ZERO.setScale(6, RoundingMode.HALF_UP).toString();
            }
            return partitionBigDecimal.divide(totalBigDecimal, 6, RoundingMode.HALF_UP)
                    .toString();
        }

    }

    private static class ThreadPoolUtil {
        private final static int CORE_POOL_SIZE = 10;

        private final static int MAX_POOL_SIZE = 20;
        private final static long KEEP_ALIVE_TIME = 60;

        //等待队列不能设置太长，避免请求长时间等待，因而线程池的拒绝策略是CallerRunsPolicy
        private final static int MAX_QUEUE_SIZE = 100;

        private static final ExecutorService deliveryStatisticsThreadPool;

        static {
            ThreadFactory namedThreadFactory = (Runnable r) -> new Thread(r, "Rider_Delivery_Statistics_ThreadPooL_" + r.hashCode());
            deliveryStatisticsThreadPool = new ExecutorServiceTraceWrapper(new ThreadPoolExecutor(CORE_POOL_SIZE, MAX_POOL_SIZE,
                    KEEP_ALIVE_TIME, TimeUnit.SECONDS, new ArrayBlockingQueue<>(MAX_QUEUE_SIZE),
                    namedThreadFactory, new ThreadPoolExecutor.CallerRunsPolicy()));
        }

        public static ExecutorService getInstance() {
            return deliveryStatisticsThreadPool;
        }
    }
}
