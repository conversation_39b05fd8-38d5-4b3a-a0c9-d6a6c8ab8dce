package com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class RiderDeliveryException {

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 门店id
     */
    private Long storeId;

    /**
     * 运单id
     */
    private Long deliveryOrderId;

    /**
     * 渠道订单id
     */
    private String channelOrderId;

    /**
     * 订单业务类型
     */
    private Integer orderBizType;

    /**
     * 日流水号
     */
    private Integer daySeq;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 骑手姓名
     */
    private String riderAccountName;

    /**
     * 骑手账号id
     */
    private Long riderAccountId;

    /**
     * 异常类型
     */
    private Integer exceptionType;

    /**
     * 异常二级类型
     */
    private Integer exceptionSubType;

    /**
     * 支付时间
     */
    private LocalDateTime payTime;

    /**
     * 异常描述信息（包含照片、备注）
     */
    private ExceptionDescription exceptionDescription;

    @Data
    @AllArgsConstructor
    public static class ExceptionDescription {
        /**
         * 照片Urls
         */
        private List<String> picUrls;

        /**
         * 用户真实地址
         */
        private String userRealAddress;


        /**
         * 修改后的地址
         */
        private String modifiedAddress;

        /**
         * 备注
         */
        private String comment;
    }
}
