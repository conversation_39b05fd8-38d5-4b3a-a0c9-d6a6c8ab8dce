package com.sankuai.shangou.logistics.sdms.domain.entity.config.expression.interval;

import com.fasterxml.jackson.annotation.JsonValue;
import com.google.common.collect.Sets;

import java.util.stream.Stream;

public enum IntervalTypeEnum {
    // (,]
    LEFT_OPEN(1, "左开右闭"),
    // [,)
    RIGHT_OPEN(2, "左闭右开"),
    // ()
    ALL_OPEN(3, "左开右开"),
    // [,]
    ALL_CLOSE(4, "左闭右闭"),
    // !=
    NOT_EQUAL(5, "不等于"),
    // ==
    EQUAL(6, "等于"),

    ANY(0, "任何数值");


    private int type;
    private String desc;

    IntervalTypeEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static IntervalTypeEnum of(int type) {

        return Stream.of(IntervalTypeEnum.values()).filter(e -> e.type == type).findFirst().orElse(null);
    }

    public boolean isRangeInterval() {
        return Sets.newHashSet(LEFT_OPEN, RIGHT_OPEN, ALL_OPEN, ALL_CLOSE).contains(this);
    }

    public boolean isRightOpen() {
        return Sets.newHashSet(RIGHT_OPEN).contains(this);
    }

    @JsonValue
    public int getType() {

        return type;
    }
}
