package com.sankuai.shangou.logistics.sdms.domain.entity.config.expression.interval;


import com.sankuai.shangou.logistics.sdms.domain.utils.IListUtils;
import lombok.Getter;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Getter
public class Interval {

    private IntervalTypeEnum intervalType;

    private List<IntervalNumber> values;


    /**
     * 小数位数
     */
    private Integer scale;

    public boolean match(BigDecimal bigDecimal) {
        if (Objects.isNull(bigDecimal)) {

            throw new IllegalArgumentException("param is null");
        }
        if (IntervalTypeEnum.ANY.equals(intervalType)) {
            return true;
        }

        if (Objects.nonNull(scale) && scale.compareTo(bigDecimal.scale()) < 0) {
            // 小数位不符合
            return false;
        }

        IntervalNumber firstIntervalNumber = values.get(0);
        switch (intervalType) {
            case EQUAL: {
                return firstIntervalNumber.isEqualTo(bigDecimal);
            }
            case NOT_EQUAL: {
                return !firstIntervalNumber.isEqualTo(bigDecimal);
            }
            default: {
                break;
            }
        }

        IntervalNumber secondIntervalNumber = values.get(1);
        boolean leftOpen = firstIntervalNumber.isLessThan(bigDecimal);
        boolean rightOpen = secondIntervalNumber.isGreaterThan(bigDecimal);
        boolean leftClose = firstIntervalNumber.isLessThan(bigDecimal) || firstIntervalNumber.isEqualTo(bigDecimal);
        boolean rightClose = secondIntervalNumber.isGreaterThan(bigDecimal) || secondIntervalNumber.isEqualTo(bigDecimal);
        switch (intervalType) {
            case ALL_OPEN: {
                return leftOpen && rightOpen;
            }
            case ALL_CLOSE: {
                return leftClose && rightClose;
            }
            case LEFT_OPEN: {
                return leftOpen && rightClose;
            }
            case RIGHT_OPEN: {
                return leftClose && rightOpen;
            }
            default:
                throw new IllegalArgumentException("no intervalType found!");
        }
    }

    public List<String> values() {

        return getValues().stream().map(IntervalNumber::getValue).collect(Collectors.toList());
    }

    public void values(List<String> values) {

        this.values = values.stream().map(IntervalNumber::new).collect(Collectors.toList());
    }

    // 反序列化int转化为IntervalType使用，勿删
    public void setIntervalType(int type) {

        this.intervalType = IntervalTypeEnum.of(type);
    }

    public void setValues(List<IntervalNumber> values) {
        this.values = values;
    }

    public void setScale(Integer scale) {
        this.scale = scale;
    }


    public BigDecimal rangeNumber() {
        if (!this.getIntervalType().isRangeInterval()) {
            throw new IllegalArgumentException("不是范围区间");
        }
        IntervalNumber intervalNumber1 = this.getValues().get(0);
        IntervalNumber intervalNumber2 = this.getValues().get(1);
        ;
        if (!intervalNumber1.isNotInfinity() || !intervalNumber2.isNotInfinity()) {
            throw new IllegalArgumentException("存在无尽数，无法比较");
        }
        return intervalNumber2.convertToBigDecimal().subtract(intervalNumber1.convertToBigDecimal());
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Interval interval = (Interval) o;
        return intervalType == interval.intervalType && IListUtils.collectionEqualsIgnoreOrderAndDuplicate(values, interval.values);
    }

    @Override
    public int hashCode() {
        return com.google.common.base.Objects.hashCode(intervalType, values);
    }
}
