package com.sankuai.shangou.logistics.sdms.domain.utils;

import com.dianping.lion.client.Lion;
import org.apache.commons.lang3.tuple.Pair;

import java.time.*;
import java.time.chrono.ChronoZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.Temporal;
import java.util.Objects;
import java.util.Optional;


/**
 * <AUTHOR>
 * @date 2022/2/16
 * @email jianglilin02@meituan
 */
public class TimeUtils {
    public static final Long UNIX_TIME_HOUR = 3600L;
    public static final Long UNIX_TIME_MINUTE = 60L;
    public static final Long UNIX_TIME_DAY = 24 * UNIX_TIME_HOUR;
    public static final DateTimeFormatter SALARY_RESULT_DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy年MM月dd日 " +
            "HH:mm");
    public static final DateTimeFormatter DX_TIME_FORMATTER = DateTimeFormatter.ofPattern("HH:mm");
    public static final DateTimeFormatter DEFAULT_DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    public static final DateTimeFormatter DEFAULT_DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    public static final String DEFAULT_INVALID_DATE_TEXT = "0000-00-00";

    private TimeUtils() {
    }

    public static LocalDateTime getEpochTime() {
        return Instant.EPOCH.atOffset(ZoneOffset.UTC).toLocalDateTime();
    }

    public static LocalDateTime fromMilliSeconds(Long milliseconds) {
        if (milliseconds == null) {
            return null;
        }
        return Instant.ofEpochMilli(milliseconds).atZone(ZoneId.systemDefault()).toLocalDateTime();
    }

    public static boolean isSameMonth(LocalDate scheduleDate, LocalDateTime localDateTime) {
        return Objects.equals(scheduleDate.getMonth(), localDateTime.getMonth());
    }

    public static boolean isFirstDayOfMonth(LocalDate localDate) {
        return Objects.equals(localDate.getDayOfMonth(), 1);
    }

    public static LocalDate fromMilliSecondsToLocalDate(Long milliseconds) {
        if (milliseconds == null) {
            return null;
        }
        return Instant.ofEpochMilli(milliseconds).atZone(ZoneId.systemDefault()).toLocalDate();
    }


    public static LocalDateTime fromSeconds(Long seconds) {
        if (seconds == null) {
            return null;
        }
        return Instant.ofEpochSecond(seconds).atZone(ZoneId.systemDefault()).toLocalDateTime();
    }

    public static Long toMilliSeconds(LocalDateTime time) {
        if (time == null) {
            return null;
        }

        return time.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }

    public static Optional<Integer> toSeconds(LocalDateTime time) {
        return Optional.ofNullable(time)
                .map(it -> it.atZone(ZoneId.systemDefault()))
                .map(ChronoZonedDateTime::toEpochSecond)
                .map(Math::toIntExact);
    }

    public static String format(LocalDateTime localDateTime, DateTimeFormatter dateTimeFormatter) {
        if (localDateTime == null) {
            return DEFAULT_INVALID_DATE_TEXT;
        } else {
            return localDateTime.format(dateTimeFormatter);
        }
    }

    public static String format(LocalDate localDate, DateTimeFormatter dateTimeFormatter) {
        if (localDate == null) {
            return DEFAULT_INVALID_DATE_TEXT;
        } else {
            return localDate.format(dateTimeFormatter);
        }
    }

    public static String format(LocalDate localDate) {
        return format(localDate, DEFAULT_DATE_FORMATTER);
    }

    public static LocalDate fromDefaultFormat(String format) {
        return LocalDate.parse(format, DEFAULT_DATE_FORMATTER);
    }

    public static long secondsOfTheDay(LocalDateTime localDateTime) {
        int hour = localDateTime.getHour();
        int minute = localDateTime.getMinute();
        int second = localDateTime.getSecond();
        return hour * UNIX_TIME_HOUR + minute * UNIX_TIME_MINUTE + second;
    }

    public static LocalTime fromFormat(String format) {
        return LocalTime.parse(format, DX_TIME_FORMATTER);
    }

    public static String toFormat(LocalTime localTime) {
        if (localTime == null) {
            return null;
        }
        return localTime.format(DX_TIME_FORMATTER);
    }

    public static Long toMilliSeconds(LocalDate date) {
        if (date == null) {
            return null;
        }

        return date.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }

    public static Boolean isInDuration(LocalDateTime baseTime, LocalDateTime durationBegin, LocalDateTime durationEnd) {
        return TimeUtils.isAfterOrEquals(baseTime, durationBegin) && TimeUtils.isBeforeOrEquals(baseTime, durationEnd);
    }

    /**
     * 将begin和end转换成LocalDateTime 当end早于或等于begin时,end转换成下一天的时间
     * @param date 日期信息
     * @param begin 开始时间
     * @param end 结束时间
     * @return 转换后的时间 Pair.Left:beginLocalDateTime Pair.Right:endLocalDateTime
     */
    public static Pair<LocalDateTime/*begin*/, LocalDateTime/*end*/> convertLocalTime2LocalDateTime(LocalDate date, LocalTime begin, LocalTime end){
        LocalDateTime localDateTimeBegin = LocalDateTime.of(date, begin);
        LocalDateTime localDateTimeEnd;
        if (isBeforeOrEquals(end, begin)) {
            localDateTimeEnd = LocalDateTime.of(date.plusDays(1), end);
        } else {
            localDateTimeEnd = LocalDateTime.of(date, end);
        }

        return Pair.of(localDateTimeBegin, localDateTimeEnd);
    }

    /**
     * 将begin和end转换成LocalDateTime，转换后的[beginLocalDateTime,endLocalDateTime]时间范围包含baseTime
     * @param baseTime 基线时间
     * @param begin 开始时间
     * @param end  结束时间
     * @return 转成LocalDateTime格式的begin和end Pair.Left:beginLocalDateTime Pair.Right:endLocalDateTime
     */
    public static Pair<LocalDateTime/*begin*/, LocalDateTime/*end*/> convertLocalTime2LocalDateTime(LocalDateTime baseTime, LocalTime begin, LocalTime end){
        LocalDateTime localDateTimeBegin;
        LocalDateTime localDateTimeEnd;
        if (begin.isAfter(baseTime.toLocalTime())) {
            localDateTimeBegin = LocalDateTime.of(baseTime.toLocalDate().minusDays(1), begin);
        } else {
            localDateTimeBegin = LocalDateTime.of(baseTime.toLocalDate(), begin);
        }

        if (end.isBefore(baseTime.toLocalTime())) {
            localDateTimeEnd = LocalDateTime.of(baseTime.toLocalDate().plusDays(1), end);
        } else {
            localDateTimeEnd = LocalDateTime.of(baseTime.toLocalDate(), end);
        }

        return Pair.of(localDateTimeBegin, localDateTimeEnd);
    }

    public static boolean isBeforeOrEquals(LocalTime origin, LocalTime other) {
        boolean isEquals = origin.equals(other);
        boolean isBefore = origin.isBefore(other);
        return isEquals || isBefore;
    }

    public static boolean isAfterOrEquals(LocalTime origin, LocalTime other) {
        boolean isEquals = origin.equals(other);
        boolean isAfter = origin.isAfter(other);
        return isEquals || isAfter;
    }


    public static boolean isBeforeOrEquals(LocalDate origin, LocalDate other) {
        boolean isEquals = origin.equals(other);
        boolean isBefore = origin.isBefore(other);
        return isEquals || isBefore;
    }

    public static boolean isAfterOrEquals(LocalDate origin, LocalDate other) {
        boolean isEquals = origin.equals(other);
        boolean isAfter = origin.isAfter(other);
        return isEquals || isAfter;
    }

    public static boolean isBeforeOrEquals(LocalDateTime origin, LocalDateTime other) {
        boolean isEquals = origin.equals(other);
        boolean isBefore = origin.isBefore(other);
        return isEquals || isBefore;
    }

    public static boolean isAfterOrEquals(LocalDateTime origin, LocalDateTime other) {
        boolean isEquals = origin.equals(other);
        boolean isAfter = origin.isAfter(other);
        return isEquals || isAfter;
    }
    /**
     * 在同一天里，localTime是否在start和end的范围内
     * @param start 起始时间
     * @param end 结束时间
     * @param localTime 需要判断的时间
     * @return 是否在范围内
     */
    public static boolean isInRangeInOneDay(LocalTime start, LocalTime end, LocalTime localTime) {
        boolean startBeforeOrEquals = TimeUtils.isBeforeOrEquals(start,localTime);
        boolean endAfterOrEquals = TimeUtils.isAfterOrEquals(end, localTime);
        return startBeforeOrEquals && endAfterOrEquals;
    }

    /**
     * 在同一天里，localTime是否在start和end的范围内(开区间,不包含相等的情况)
     * @param start 起始时间
     * @param end 结束时间
     * @param localTime 需要判断的时间
     * @return 是否在范围内
     */
    public static boolean isInOpenRangeInOneDay(LocalTime start, LocalTime end, LocalTime localTime) {
        boolean startBefore = start.isBefore(localTime);
        boolean endAfter = end.isAfter(localTime);
        return startBefore && endAfter;
    }

    /**
     * 在同一天和下一天内，localTime是否在start和end的范围内
     * @param start 起始时间
     * @param end 结束时间
     * @param localTime 需要判断的时间
     * @return 是否在范围内
     */
    public static boolean isInOpenRangeInOneDayAndNextDay(LocalTime start, LocalTime end, LocalTime localTime) {
        //时段是否跨天
        boolean isWorkCrossDay = TimeUtils.isAfterOrEquals(start, end);
        if (isWorkCrossDay) {
            //如果跨天，拆分为两段进行判断
            boolean inFirstSpilt = TimeUtils.isInOpenRangeInOneDay(start, LocalTime.MAX, localTime);
            boolean inSecondSpilt = TimeUtils.isInOpenRangeInOneDay(LocalTime.MIN, end, localTime);
            return inFirstSpilt || inSecondSpilt;
        } else {
            //非跨天，简单判断即可
            return TimeUtils.isInRangeInOneDay(start, end, localTime);
        }
    }

    /**
     * 在同一天和下一天内，localTime是否在start和end的范围内
     * @param start 起始时间
     * @param end 结束时间
     * @param localTime 需要判断的时间
     * @return 是否在范围内
     */
    public static boolean isInRangeInOneDayAndNextDay(LocalTime start, LocalTime end, LocalTime localTime) {
        //时段是否跨天
        boolean isWorkCrossDay = TimeUtils.isAfterOrEquals(start, end);
        if (isWorkCrossDay) {
            //如果跨天，拆分为两段进行判断
            boolean inFirstSpilt = TimeUtils.isInRangeInOneDay(start, LocalTime.MAX, localTime);
            boolean inSecondSpilt = TimeUtils.isInRangeInOneDay(LocalTime.MIN, end, localTime);
            return inFirstSpilt || inSecondSpilt;
        } else {
            //非跨天，简单判断即可
            return TimeUtils.isInRangeInOneDay(start, end, localTime);
        }
    }

    @Deprecated
    public static boolean isNotOverLap(Pair<LocalTime,LocalTime> duration1, Pair<LocalTime,LocalTime> duration2) {
        LocalTime s1 = duration1.getLeft();
        LocalTime e1 = duration1.getRight();

        LocalTime s2 = duration2.getLeft();
        LocalTime e2 = duration2.getRight();

        //时间不重合分两种情况[s1,e1] [s2,e2]：
        //1.s1在[s2,e2]内 或者 e1在[s2,e2]内
        //2.s2在[s1,e1]内 或者 e2在[s1,e1]内
        boolean isMore1 = TimeUtils.isInOpenRangeInOneDayAndNextDay(s2, e2, s1);
        boolean isLess1 = TimeUtils.isInOpenRangeInOneDayAndNextDay(s2, e2, e1);

        boolean isMore2 = TimeUtils.isInOpenRangeInOneDayAndNextDay(s1, e1, s2);
        boolean isLess2 = TimeUtils.isInOpenRangeInOneDayAndNextDay(s1, e1, e2);

        return  !(isMore1 || isLess1 || isMore2 || isLess2);
    }

    @Deprecated
    public static boolean isPeriodOverlapping(Pair<LocalTime,LocalTime> duration1, Pair<LocalTime,LocalTime> duration2) {
        LocalTime firstStart = duration1.getLeft();
        LocalTime firstEnd = duration1.getRight();
        LocalTime secondStart = duration2.getLeft();
        LocalTime secondEnd = duration2.getRight();

        boolean isFirstCrossDay = firstEnd.isBefore(firstStart);
        boolean isSecondCrossDay = secondEnd.isBefore(secondStart);

        // 如果两个时间区间都跨天，必然会重叠
        if (isFirstCrossDay && isSecondCrossDay) {
            return true;
        }

        LocalTime maxStart = null;
        LocalTime minEnd = null;
        if (isFirstCrossDay) {
            maxStart = secondStart;
        } else if (isSecondCrossDay) {
            maxStart = firstStart;
        } else {
            maxStart = firstStart.isAfter(secondStart) ? firstStart : secondStart;
        }
        minEnd = firstEnd.isBefore(secondEnd) ? firstEnd : secondEnd;

        // 如果两个时间段，最大开始时间 < 最小结束时间，则时间段重复
        return maxStart.isBefore(minEnd);
    }

    public static boolean checkIsNotOverLap2(Pair<LocalTime,LocalTime> duration1, Pair<LocalTime,LocalTime> duration2){
        if (Lion.getConfigRepository().getBooleanValue("schedule.overlap.check.fallback", false)) {
            return true;
        }

        LocalTime aStart = duration1.getKey();
        LocalTime aEnd = duration1.getValue();
        LocalTime bStart = duration2.getKey();
        LocalTime bEnd = duration2.getValue();

        if(aEnd.isBefore(aStart)){

            return checkIsNotOverLap2(Pair.of(aStart,LocalTime.MAX), Pair.of(bStart,bEnd))
                    && checkIsNotOverLap2(Pair.of(LocalTime.MIN,aEnd),Pair.of(bStart,bEnd));

        }

        if(bEnd.isBefore(bStart)){
            return checkIsNotOverLap2(Pair.of(aStart,aEnd),Pair.of(bStart,LocalTime.MAX))
                    && checkIsNotOverLap2(Pair.of(aStart,aEnd),Pair.of(LocalTime.MIN,bEnd));
        }

        // 如果a时间段在b时间段之前
        if (aEnd.isBefore(bStart) || aStart.isAfter(bEnd)) {
            //无交集
            return true;
        }
        // 如果a时间段在b时间段之后
        if (bEnd.isBefore(aStart) || bStart.isAfter(aEnd)) {
            //无交集
            return true;
        }

        // 有交集
        return false;
    }

    /**
     * 找到重合的时间段，如果没有重合，则返回Optional.empty()
     * @return
     */
    public static Optional<Pair<LocalDateTime, LocalDateTime>> findOverlapTime(Pair<LocalDateTime, LocalDateTime> period1, Pair<LocalDateTime, LocalDateTime> period2) {
        LocalDateTime start1 = period1.getLeft();
        LocalDateTime end1 = period1.getRight();
        LocalDateTime start2 = period2.getLeft();
        LocalDateTime end2 = period2.getRight();

        LocalDateTime overlapStart = start1.isBefore(start2) ? start2 : start1;
        LocalDateTime overlapEnd = end1.isBefore(end2) ? end1 : end2;

        if (overlapStart.isBefore(overlapEnd)) {
            return Optional.of(Pair.of(overlapStart, overlapEnd));
        } else {
            return Optional.empty();
        }
    }

    public static LocalDateTime getMonth(LocalDateTime date){

        if(Objects.isNull(date)){
            return null;
        }

        return LocalDateTime.of(date.getYear(),date.getMonth(),1,0,0,0);
    }

    public static LocalDate getMonth(LocalDate date){

        if(Objects.isNull(date)){
            return null;
        }

        return LocalDate.of(date.getYear(),date.getMonth(),1);

    }

    /**
     * 计算两个时间之间的间隔
     */
    public static long between(Temporal startTimeInclude, Temporal endTimeExclude, ChronoUnit unit) {
        return unit.between(startTimeInclude, endTimeExclude);
    }
}
