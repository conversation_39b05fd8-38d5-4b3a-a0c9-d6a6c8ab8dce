package com.sankuai.shangou.logistics.sdms.domain.entity.limit;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.dianping.cat.Cat;
import com.dianping.lion.Environment;
import com.dianping.lion.client.ConfigRepository;
import com.dianping.lion.client.Lion;
import com.meituan.service.mobile.mtthrift.generic.GenericService;
import com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy;
import com.sankuai.shangou.commons.exception.common.exceptions.BizException;
import com.sankuai.shangou.commons.exception.common.exceptions.SystemException;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.logistics.sdms.domain.utils.LionUtils;
import com.sankuai.shangou.logistics.sdms.sdk.limit.constant.LimitTypeEnum;
import com.sankuai.shangou.logistics.sdms.sdk.limit.dto.LimitItemDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/10/31 17:00
 **/
@SuppressWarnings("all")
@Slf4j
@Component
public class LimitItemClientProxyHelper implements InitializingBean {

    @Value("${app.name}")
    private String appKey;

    private Map<String, ThriftClientProxy> clientProxyMap = new HashMap<>();
    @Override
    public void afterPropertiesSet() throws Exception {
        updateThriftClientMap();
        ConfigRepository configRepository = Lion.getConfigRepository(Environment.getAppName());
        configRepository.addConfigListener(configEvent -> {
            if (Objects.equals(LimitItemClientProxyConfig.CONFIG_KEY, configEvent.getKey())){
                updateThriftClientMap();
            }
        });
    }

    private void updateThriftClientMap() {
        List<LimitItemClientProxyConfig> configList = LimitItemClientProxyConfig.getConfigList();
        for (LimitItemClientProxyConfig config : configList) {
            String key = buildMapKey(config);
            if (clientProxyMap.containsKey(key)) {
                continue;
            }

            try {
                ThriftClientProxy clientProxy = new ThriftClientProxy();
                //声明
                clientProxy.setAppKey(appKey);
                clientProxy.setRemoteAppkey(config.getRemoteAppKey());
                clientProxy.setGenericServiceName(config.getGenericServiceName());
                clientProxy.setFilterByServiceName(true);
                clientProxy.setGeneric("json-simple");
                clientProxy.afterPropertiesSet();
                clientProxyMap.put(key, clientProxy);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        log.info("updateThriftClientList success, limitItemQueryProxyConfigList size:{}", clientProxyMap.size());
    }

    private String buildMapKey(LimitItemClientProxyConfig config) {
        return config.getRemoteAppKey() + "-" + config.getGenericServiceName();
    }

    /**
     * 获取设备未领取限制接单的限制项
     *
     * @param tenantId      租户id
     * @param accountIdList 账号id List
     * @return 限制项目
     */

    private List<LimitItemDTO> getEquipmentUnGetLimitItemList(Long tenantId, List<Long> accountIdList, Long storeId, GenericService genericService) {
        //参数不能为空
        if (Objects.isNull(tenantId) || CollectionUtils.isEmpty(accountIdList) || Objects.isNull(genericService)) {
            log.error("参数不能为空, tenantId:{}, accountIdList:{}, genericService:{}", tenantId, accountIdList, genericService);
            return Collections.emptyList();
        }

        //使用
        List<String> paramTypes = new ArrayList<>();
        paramTypes.add("java.lang.Long");
        paramTypes.add("java.lang.List");
        paramTypes.add("java.lang.Integer");
        paramTypes.add("java.lang.Long");

        //分割账号，每次调用最多200个账号
        List<List<Long>> partition = ListUtils.partition(accountIdList, 200);
        List<LimitItemDTO> resultList = new ArrayList<>();
        partition.forEach(accountIds -> {
            List<String> paramValues = new ArrayList<>();
            paramValues.add(tenantId.toString());
            paramValues.add(JSON.toJSONString(accountIds));
            paramValues.add(String.valueOf(LimitTypeEnum.EQUIPMENT_UN_GET.getCode()));
            paramValues.add(Optional.ofNullable(storeId).map(id -> id.toString()).orElse(null));

            try {
                //调用服务端方法
                log.info("调用设备未领取限制接单的限制项三方接口开始,  paramValues: {}", paramValues);
                String result = genericService.$invoke("queryNecessaryEquipment", paramTypes, paramValues);
                log.info("调用设备未领取限制接单的限制项三方接口结束, paramValues: {}, result:{}", paramValues, result);

                ////反序列化
                TResult<List<LimitItemDTO>> resp = JSON.parseObject(result, new TypeReference<TResult<List<LimitItemDTO>>>() {

                });


                if (resp == null || !resp.isSuccess()) {
                    log.error("调用设备未领取限制接单的限制项三方接口失败, resp: {}", resp);
                } else {
                    JSONObject jsonObject = JSON.parseObject(result);
                    JSONArray data = jsonObject.getJSONArray("data");
                    if (data != null) {
                        data.forEach(item -> {
                            JSONObject itemJsonObject = (JSONObject) item;
                            //{"monthValue":6,"year":2025,"month":"JUNE","dayOfMonth":26,"dayOfWeek":"THURSDAY","dayOfYear":177,"hour":14,"minute":32,"second":59,"nano":0,"chronology":{"calendarType":"iso8601","id":"ISO"}}
                            JSONObject limitStartDate = itemJsonObject.getJSONObject("limitStartDate");
                            LimitItemDTO limitItemDTO = jsonObject.parseObject(item.toString(), LimitItemDTO.class);
                            if (limitStartDate != null) {
                                LocalDateTime localDateTime = LocalDateTime.of(
                                        limitStartDate.getIntValue("year"),
                                        limitStartDate.getIntValue("monthValue"),
                                        limitStartDate.getIntValue("dayOfMonth"),
                                        limitStartDate.getIntValue("hour"),
                                        limitStartDate.getIntValue("minute"),
                                        limitStartDate.getIntValue("second")
                                );
                                limitItemDTO.setLimitStartDate(localDateTime);
                            }
                            resultList.add(limitItemDTO);
                        });
                    }
                }
            } catch (Exception e) {
                log.error("调用设备未领取限制接单的限制项三方接口失败", e);
            }
        });

        return resultList;

    }

    public List<LimitItemDTO> queryLimitItemListByAccountId(Long tenantId, Long accountId, Long storeId) {

        List<LimitItemClientProxyConfig> configList = LimitItemClientProxyConfig.getConfigList();

        List<String> tenantLimitItems = LionUtils.getTenantLimitItems(tenantId);
        return configList
                .stream()
                .sorted(Comparator.comparingInt(LimitItemClientProxyConfig::getOrder))
                .filter(config -> tenantLimitItems.contains(config.getLimitTypeEnum().name()))
                .filter(config -> !Objects.equals(config.getIsDegraded(), true))
                .map(config -> {
                    try {
                        ThriftClientProxy clientProxy = clientProxyMap.get(buildMapKey(config));

                        //注意GenericService是com.meituan.service.mobile.mtthrift.generic.GenericService
                        GenericService genericService = (GenericService) clientProxy.getObject();

                        if (genericService == null) {
                            return null;
                        }

                        //查看设备未领取限制接单
                        if (Objects.equals(config.getLimitTypeEnum(), LimitTypeEnum.EQUIPMENT_UN_GET)) {
                            List<LimitItemDTO> result = getEquipmentUnGetLimitItemList(tenantId, Collections.singletonList(accountId), storeId, genericService);
                            if (CollectionUtils.isEmpty(result)) {
                                return null;
                            } else {
                                return result.get(0);
                            }
                        }

                        //使用
                        List<String> paramTypes = new ArrayList<>();
                        paramTypes.add("java.lang.Long");
                        paramTypes.add("java.lang.Long");
                        paramTypes.add("java.lang.Integer");

                        List<String> paramValues = new ArrayList<>();
                        paramValues.add(tenantId.toString());
                        paramValues.add(accountId.toString());
                        paramValues.add(String.valueOf(config.getLimitTypeEnum().getCode()));

                        //调用服务端方法
                        log.info("start invoke genericService queryLimitItem, limitType: {},  paramValues: {}, appKey: {}",
                                config.getLimitTypeEnum(), paramValues, clientProxy.getClientInfo().getRemoteAppkey());
                        String result = genericService.$invoke("queryLimitItem", paramTypes, paramValues);
                        log.info("end invoke genericService queryLimitItem, limitType: {}, paramValues: {}, result:{}",
                                config.getLimitTypeEnum(), paramValues, result);

                        //反序列化
                        TResult<LimitItemDTO> resp = JSON.parseObject(result, new TypeReference<TResult<LimitItemDTO>>(){});
                        if (resp == null || !resp.isSuccess()) {
                            log.error("genericService invoke fail, resp: {}", resp);
                            throw new BizException("查询单个限制项失败");
                        }
                        return resp.getData();
                    } catch (Exception e) {
                        Cat.logEvent("LIMIT_ACCEPT_ORDER", "SINGLE_RIDER_INVOKE_FAIL");
                        log.error("genericService invoke error", e);
                        throw new RuntimeException(e);
                    }
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    public Map<Long, List<LimitItemDTO>> queryLimitItemListByAccountIdList(Long tenantId, List<Long> accountIdList, Long storeId) {
        List<LimitItemClientProxyConfig> configList = LimitItemClientProxyConfig.getConfigList();
        List<String> tenantLimitItems = LionUtils.getTenantLimitItems(tenantId);
        return configList
                .stream()
                .sorted(Comparator.comparingInt(LimitItemClientProxyConfig::getOrder))
                .filter(config -> tenantLimitItems.contains(config.getLimitTypeEnum().name()))
                .filter(config -> !Objects.equals(config.getIsDegraded(), true))
                .map(config -> {
                    try {
                        ThriftClientProxy clientProxy = clientProxyMap.get(buildMapKey(config));

                        //注意GenericService是com.meituan.service.mobile.mtthrift.generic.GenericService
                        GenericService genericService = (GenericService) clientProxy.getObject();

                        if (genericService == null) {
                            log.error("genericService is null");
                            throw new SystemException("genericService is null");
                        }

                        //查看设备未领取限制接单
                        if (Objects.equals(config.getLimitTypeEnum(), LimitTypeEnum.EQUIPMENT_UN_GET)) {
                            return getEquipmentUnGetLimitItemList(tenantId, accountIdList, storeId, genericService);
                        }

                        //使用
                        List<String> paramTypes = new ArrayList<>();
                        paramTypes.add("java.lang.Long");
                        paramTypes.add("java.util.List");
                        paramTypes.add("java.lang.Integer");

                        List<String> paramValues = new ArrayList<>();
                        paramValues.add(tenantId.toString());
                        paramValues.add(JSON.toJSONString(accountIdList));
                        paramValues.add(String.valueOf(config.getLimitTypeEnum().getCode()));

                        //调用服务端方法
                        log.info("start invoke genericService batchQueryLimitItemList, paramValues: {}, appKey: {}", paramValues,
                                clientProxy.getClientInfo().getAppkey());
                        String result = genericService.$invoke("batchQueryLimitItemList", paramTypes, paramValues);
                        log.info("end invoke genericService batchQueryLimitItemList, paramValues: {}, result:{}", paramValues, result);

                        //反序列化
                        TResult<List<LimitItemDTO>> resp = JSON.parseObject(result, new TypeReference<TResult<List<LimitItemDTO>>>(){});
                        if (resp == null || !resp.isSuccess()) {
                            log.error("genericService invoke fail, resp: {}", resp);
                            throw new BizException("查询单个限制项失败");
                        }
                        return resp.getData();
                    } catch (Exception e) {
                        Cat.logEvent("LIMIT_ACCEPT_ORDER", "BATCH_RIDER_INVOKE_FAIL");
                        log.error("genericService invoke error", e);
                        throw new RuntimeException(e);
                    }
                })
                .filter(Objects::nonNull)
                .flatMap(Collection::stream)
                .collect(Collectors.groupingBy(LimitItemDTO::getAccountId));
    }
}
