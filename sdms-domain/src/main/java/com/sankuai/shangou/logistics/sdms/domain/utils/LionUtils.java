package com.sankuai.shangou.logistics.sdms.domain.utils;

import com.dianping.lion.client.Lion;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025-07-24
 * @email <EMAIL>
 */
public class LionUtils {

    private static final Map<String, String> INIT_MAP = new HashMap<String, String>() {{
        put("1000395", "SAFETY_TRAINING,NEW_EMPLOYEE_TRAINING,EQUIPMENT_UN_GET");
    }};

    public static List<String> getTenantLimitItems(long tenantId) {
        Map<String, String> tenantLimitItem = Lion.getConfigRepository().getMap("tenant.limit.item", String.class, INIT_MAP);
        if (MapUtils.isEmpty(tenantLimitItem)) {
            return Lists.newArrayList();
        }
        String limitItemStr = tenantLimitItem.get(String.valueOf(tenantId));
        if (StringUtils.isBlank(limitItemStr)) {
            return Lists.newArrayList();
        }
        return Lists.newArrayList(limitItemStr.split(","));
    }

}
