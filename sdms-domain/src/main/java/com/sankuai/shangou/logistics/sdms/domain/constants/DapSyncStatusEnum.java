package com.sankuai.shangou.logistics.sdms.domain.constants;

/**
 * <AUTHOR>
 * @date 2023-08-29
 * @email <EMAIL>
 */
public enum DapSyncStatusEnum {

    NOT_SYNC(0, "未同步至青云"),
    SYNCED(1, "已同步至青云")
    ;
    private int code;
    private String desc;

    DapSyncStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static DapSyncStatusEnum valueOfCode(int code) {
        for (DapSyncStatusEnum obj : DapSyncStatusEnum.values()) {
            if (java.util.Objects.equals(obj.code, code)) {
                return obj;
            }
        }
        return null;
    }

    public static DapSyncStatusEnum valueOfDesc(String desc) {
        for (DapSyncStatusEnum obj : DapSyncStatusEnum.values()) {
            if (java.util.Objects.equals(obj.desc, desc)) {
                return obj;
            }
        }
        return null;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
