package com.sankuai.shangou.logistics.sdms.domain.external;

import com.sankuai.shangou.logistics.sdms.domain.entity.rider.RiderDeliveryOrderInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/7/9 10:47
 **/
public interface RiderDeliveryQueryClient {
    /**
     * 查询骑手进行中的运单
     * @param tenantId 租户id
     * @param storeId 门店id
     * @param accountId 骑手账号id
     * @return 骑手进行中的运单
     */
    List<RiderDeliveryOrderInfo> queryRiderInProgressOrder(Long tenantId, Long storeId, Long accountId);
}
