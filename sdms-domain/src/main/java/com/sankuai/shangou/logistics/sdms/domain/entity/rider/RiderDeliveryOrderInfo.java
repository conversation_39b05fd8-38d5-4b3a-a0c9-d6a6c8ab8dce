package com.sankuai.shangou.logistics.sdms.domain.entity.rider;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024/7/9 10:54
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RiderDeliveryOrderInfo {
    private Long tenantId;

    private Long storeId;

    private Long accountId;

    private Long deliveryOrderId;

    private Integer deliveryStatus;

    private Long orderId;

    private String channelOrderId;

    private Integer orderBizType;

}
