package com.sankuai.shangou.logistics.sdms.domain.entity.limit;

import com.alibaba.fastjson.JSON;
import com.dianping.lion.client.Lion;
import com.google.common.collect.Lists;
import com.sankuai.shangou.logistics.sdms.sdk.limit.constant.LimitTypeEnum;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/10/31 17:01
 **/
@Data
@Slf4j
public class LimitItemClientProxyConfig {
    /**
     * 泛化调用配置
     */
    private String remoteAppKey;

    private String genericServiceName;

    private Integer order;

    private Boolean isDegraded;

    private LimitTypeEnum limitTypeEnum;

    public static final String CONFIG_KEY = "limit.accept.order.item.proxy.config";

    public static List<LimitItemClientProxyConfig> getConfigList() {
        String s = Lion.getConfigRepository().get(CONFIG_KEY);
        if (StringUtils.isBlank(s)) {
            log.info("getLimitItemClientProxyConfigList is null");
            return Lists.newArrayList();
        }
        return JSON.parseArray(s, LimitItemClientProxyConfig.class);
    }
}
