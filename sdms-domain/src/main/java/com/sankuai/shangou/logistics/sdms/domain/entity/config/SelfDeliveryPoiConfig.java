package com.sankuai.shangou.logistics.sdms.domain.entity.config;

import com.sankuai.shangou.logistics.sdms.domain.constants.DapSyncStatusEnum;
import com.sankuai.shangou.logistics.sdms.domain.entity.common.Operator;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023-08-28
 * @email <EMAIL>
 */
@Getter
@ToString
@EqualsAndHashCode
public class SelfDeliveryPoiConfig {

    private Long id;

    private Long tenantId;

    private Long poiId;

    private Boolean enableTurnDelivery;

    public SelfDeliveryPoiConfig(Long id, Long tenantId, Long poiId, Boolean enableTurnDelivery) {
        this.id = id;
        this.tenantId = tenantId;
        this.poiId = poiId;
        this.enableTurnDelivery = enableTurnDelivery;
    }
}
