package com.sankuai.shangou.logistics.sdms.domain.entity.yoda;

import com.sankuai.shangou.logistics.sdms.sdk.verify.enums.CollectContentTypeEnum;
import com.sankuai.shangou.logistics.sdms.sdk.verify.enums.PunishTypeEnum;
import lombok.Data;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/7/9 15:46
 **/
@Data
public class YodaIdentityTaskConfig {
    private Long id;

    private Long tenantId;

    private Integer storeOperationMode;

    private List<Integer> cityIds;

    private List<Long> storeIds;

    private Integer collectMode;

    private List<Integer> collectContentTypeList;

    private Integer collectPeriod;

    private Integer maxCollectTime;

    private Integer taskValidPeriod;

    private Integer collectInterval;

    private LocalTime periodBeginTime;

    private LocalTime periodEndTime;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    private Map<CollectContentTypeEnum, PunishTypeEnum> punishConfigMap;
    /**
     * 岗位列表
     */
    private List<String> positionList;
    /**
     * 任务即将过期提醒时间（单位分钟）
     */
    private Integer taskNearExpiration;
    /**
     * 二级配置-不管控配置
     */
    private List<VerifyNoControlInfo> noControl;
}
