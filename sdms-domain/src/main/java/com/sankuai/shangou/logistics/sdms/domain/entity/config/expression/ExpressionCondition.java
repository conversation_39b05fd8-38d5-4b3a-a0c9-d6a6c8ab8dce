package com.sankuai.shangou.logistics.sdms.domain.entity.config.expression;

import com.sankuai.shangou.logistics.sdms.domain.entity.config.expression.interval.Interval;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024-07-09
 * @email <EMAIL>
 */
@Data
public class ExpressionCondition {

    private String name;

    private String identifier;

    private String formula;

    private Interval interval;

}
