package com.sankuai.shangou.logistics.sdms.domain.utils;

import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2022/3/7
 * @email jianglilin02@meituan
 */
public class IListUtils {

    private IListUtils() {

    }

    public static <T,R> Stream<T> nullSafeStream(Collection<T> originList) {
        return Optional
                .ofNullable(originList)
                .orElse(Lists.newArrayList())
                .stream();
    }


    public static <T,R> List<R> mapTo(Collection<T> originList, Function<T,R> mapFunction) {
        return Optional
                .ofNullable(originList)
                .orElse(Lists.newArrayList())
                .stream()
                .map(mapFunction)
                .collect(Collectors.toList());
    }

    public static <T,R> List<R> mapToAndDistinct(List<T> originList, Function<T,R> mapFunction) {
        return Optional
                .ofNullable(originList)
                .orElse(Lists.newArrayList())
                .stream()
                .map(mapFunction)
                .distinct()
                .collect(Collectors.toList());
    }

    public static <T, K, V> Map<K, V> nullSafeAndOverrideCollectToMap(Collection<T> originList,
                                                                      Function<? super T, ? extends K> keyMapper,
                                                                      Function<? super T, ? extends V> valueMapper) {
        return Optional
                .ofNullable(originList)
                .orElse(Lists.newArrayList())
                .stream()
                .collect(
                        Collectors.toMap(
                                keyMapper,
                                valueMapper,
                                (older, newer) -> newer
                        )
                );
    }

    public static <T, K, V> Map<K, List<V>> nullSafeCollectToListMap(List<T> originList,
                                                           Function<? super T, ? extends K> keyMapper,
                                                           Function<? super T, List<V>> valueMapper) {
        return Optional
                .ofNullable(originList)
                .orElse(Lists.newArrayList())
                .stream()
                .collect(
                        Collectors.toMap(
                                keyMapper,
                                valueMapper,
                                (older, newer) -> {
                                    older.addAll(newer);
                                    return older;
                                }
                        )
                );
    }

    public static <T, K> Map<K, List<T>> nullSafeGroupBy(List<T> originList,
                                                         Function<? super T, ? extends K> classifier) {
        return Optional
                .ofNullable(originList)
                .orElse(Lists.newArrayList())
                .stream()
                .collect(Collectors.groupingBy(classifier));
    }

    /**
     * 判断两个list是否相同，忽略元素的顺序和重复
     * e.g:
     * - 忽略顺序: [1,2,3] 与 [3,2,1] 相同
     * - 忽略重复: [1,1,2] 与 [1,2,2] 相同
     */
    public static Boolean collectionEqualsIgnoreOrderAndDuplicate(Collection<?> list1, Collection<?> list2) {
        //都是空，那么是相等的
        if (CollectionUtils.isEmpty(list1) && CollectionUtils.isEmpty(list2)) {
            return true;
        }
        //只有一边是空的，那么不相等
        if (CollectionUtils.isEmpty(list1) || CollectionUtils.isEmpty(list2)) {
            return false;
        }

        return list1.containsAll(list2) && list2.containsAll(list1);
    }


    public static <T> List<T> nullSafeFilterNullElement(List<T> originList) {
        return Optional
                .ofNullable(originList)
                .orElse(Lists.newArrayList())
                .stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    public static <T> List<T> nullSafeFilterElement(List<T> originList, Predicate<? super T> predicate) {
        return Optional
                .ofNullable(originList)
                .orElse(Lists.newArrayList())
                .stream()
                .filter(predicate)
                .collect(Collectors.toList());
    }

    public static <T,R> List<R> nullSafeFilterThenMap(List<T> originList, Predicate<? super T> predicate, Function<T,R> mapFunction) {
        return Optional
                .ofNullable(originList)
                .orElse(Lists.newArrayList())
                .stream()
                .filter(predicate)
                .map(mapFunction)
                .collect(Collectors.toList());
    }

    public static <T,R> Boolean nullSafeAllMatch(List<T> originList, Predicate<? super T> predicate) {
        return Optional
                .ofNullable(originList)
                .orElse(Lists.newArrayList())
                .stream()
                .allMatch(predicate);
    }
}
