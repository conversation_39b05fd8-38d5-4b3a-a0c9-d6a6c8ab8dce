package com.sankuai.shangou.logistics.sdms.domain.constants;

/**
 * <AUTHOR>
 * @since 2024/7/12 14:45
 **/
public enum VerifyTypeEnum {
    Helmet(1, "头盔"),

    DRESSING(2, "着装"),

    SAME_PERSON(3, "同人比对")
    ;

    private int code;

    private String desc;

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    VerifyTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static VerifyTypeEnum enumOf(int code) {
        for (VerifyTypeEnum val : values()) {
            if (code == val.getCode()) {
                return val;
            }
        }

        throw new IllegalArgumentException("不识别的采集任务类型");
    }
}
