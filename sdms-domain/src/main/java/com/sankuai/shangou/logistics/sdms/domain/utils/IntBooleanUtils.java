package com.sankuai.shangou.logistics.sdms.domain.utils;


import com.sankuai.shangou.commons.exception.common.exceptions.BizException;
import com.sankuai.shangou.commons.thrift.publisher.exception.ShangouBizException;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023-08-29
 * @email <EMAIL>
 */
public class IntBooleanUtils {

    private IntBooleanUtils() {

    }

    private static final int FALSE_INT = 0;

    private static final int TRUE_INT = 1;

    public static boolean toBool(int boolInt) {
        if (Objects.equals(boolInt, FALSE_INT)) {
            return false;
        } else if (Objects.equals(boolInt, TRUE_INT)) {
            return true;
        }
        throw new ShangouBizException("int转换bool错误");
    }

}
