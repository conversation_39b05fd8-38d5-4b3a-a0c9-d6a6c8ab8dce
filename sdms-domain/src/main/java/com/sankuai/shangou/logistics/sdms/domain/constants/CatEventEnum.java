package com.sankuai.shangou.logistics.sdms.domain.constants;

/**
 * <AUTHOR>
 * @date 2023-08-31
 * @email <EMAIL>
 */
public enum CatEventEnum {

    DAP_TURN_DELIVERY_ERROR("DapDelivery", "turnDeliveryError"),

    SEND_MESSAGE_ERROR("SEND_MESSAGE", "ERROR"),

    VERIFY_RESULT_CHECK_NOT_PASS("VERIFY_RESULT", "NOT_PASS"),

    VERIFY_RESULT_CHECK_ERROR("VERIFY_RESULT", "ERROR"),

    VERIFY_ASSIGN_FAIL("VERIFY_ASSIGN", "LATEST_TASK_NOT_COMPLETE")
    ;


    private String type;
    private String name;

    CatEventEnum(String type, String name) {
        this.type = type;
        this.name = name;
    }


    public static CatEventEnum valueOfType(String type) {
        for (CatEventEnum obj : CatEventEnum.values()) {
            if (java.util.Objects.equals(obj.type, type)) {
                return obj;
            }
        }
        return null;
    }

    public static CatEventEnum valueOfName(String name) {
        for (CatEventEnum obj : CatEventEnum.values()) {
            if (java.util.Objects.equals(obj.name, name)) {
                return obj;
            }
        }
        return null;
    }

    public String getType() {
        return type;
    }

    public String getName() {
        return name;
    }
}
