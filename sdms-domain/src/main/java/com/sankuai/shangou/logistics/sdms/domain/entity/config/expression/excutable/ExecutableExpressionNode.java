package com.sankuai.shangou.logistics.sdms.domain.entity.config.expression.excutable;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.lang.Nullable;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

@Getter
@Setter
@NoArgsConstructor
public class ExecutableExpressionNode {
    private String condition;
    private String formula;
    private List<ExecutableExpressionNode> subs;

    public ExecutableExpressionNode(String condition, String formula) {
        this.condition = condition;
        this.formula = formula;
    }

    public void addSubNode(ExecutableExpressionNode node) {
        if (this.subs == null) {
            this.subs = new ArrayList<>();
        }
        this.subs.add(node);
    }

    public boolean hasSubs() {
        return subs != null && subs.size() > 0;
    }

    public String toString() {
        StringBuilder buffer = new StringBuilder(50);
        print(buffer, "", "");
        return buffer.toString();
    }

    private void print(StringBuilder buffer, String prefix, String childrenPrefix) {
        buffer.append(prefix);
        buffer.append(this.condition);
        if (StringUtils.isNotEmpty(this.formula)) {
            buffer.append("—————").append(this.formula);
        }
        buffer.append('\n');

        if (this.hasSubs()) {
            for (Iterator<ExecutableExpressionNode> it = this.subs.iterator(); it.hasNext(); ) {
                ExecutableExpressionNode node = it.next();
                if (it.hasNext()) {
                    node.print(buffer, childrenPrefix + "├── ", childrenPrefix + "│   ");
                } else {
                    node.print(buffer, childrenPrefix + "└── ", childrenPrefix + "    ");
                }
            }
        }
    }

}
