package com.sankuai.shangou.logistics.sdms.domain.repository;

import com.sankuai.shangou.logistics.sdms.domain.entity.yoda.YodaIdentityTaskConfig;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2024/7/9 15:45
 **/
public interface YodaIdentityTaskConfigRepository {
    Optional<YodaIdentityTaskConfig> getHitConfig(Long tenantId, Long storeId);

    Optional<YodaIdentityTaskConfig> selectByPrimaryKey(Long id);
    List<YodaIdentityTaskConfig> selectByPrimaryKeys(Collection<Long> ids);
}
