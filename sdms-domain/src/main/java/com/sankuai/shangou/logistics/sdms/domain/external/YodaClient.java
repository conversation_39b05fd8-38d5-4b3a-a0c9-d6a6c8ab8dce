package com.sankuai.shangou.logistics.sdms.domain.external;

import com.sankuai.shangou.logistics.sdms.domain.constants.VerifyTypeEnum;
import com.sankuai.shangou.logistics.sdms.domain.entity.yoda.YodaVerifyResult;
import org.apache.commons.lang3.tuple.Pair;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/7/12 11:33
 **/
public interface YodaClient {
    String authorize(String userAgent, String uuid, String ip,
                            String version, Long userAccountId, String userPhoneNum,
                            String userName, String userIdNum, List<VerifyTypeEnum> verifyTypeEnums,
                            int platform);

    Boolean result(String requestCode, String responseCode, Long riderAccountId);

    YodaVerifyResult queryFaceVerifyData(String requestCode);

    Pair<Boolean, String> verifySamePerson(String userAgent, String uuid, String ip,
                                           Long userAccountId, String userName,
                                           String userIdNum, String faceUrl);

    String queryFaceImageBase64(String requestCode);
}
