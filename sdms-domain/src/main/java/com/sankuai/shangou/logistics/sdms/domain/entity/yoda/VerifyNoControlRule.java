package com.sankuai.shangou.logistics.sdms.domain.entity.yoda;

import com.sankuai.shangou.logistics.sdms.sdk.verify.enums.CollectContentTypeEnum;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description 不管控规则
 * @date 2025-03-24
 */
@Data
public class VerifyNoControlRule {
    /**
     * 不管控采集类型
     */
    private List<CollectContentTypeEnum> collectionTypes;
    /**
     * 装备领取时间（天）
     */
    private Integer pickupTime;

}
