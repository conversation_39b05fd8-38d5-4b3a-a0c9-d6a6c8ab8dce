<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>xframe-starter-parent</artifactId>
        <groupId>com.meituan.xframe</groupId>
        <!--XFrame产品版本：https://km.sankuai.com/custom/onecloud/page/133516477-->
        <version>*******</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <name>qnh_order_api</name>

    <groupId>com.sankuai.shangou.qnh</groupId>
    <artifactId>order-api</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>war</packaging>

    <properties>
        <maven.deploy.skip>true</maven.deploy.skip>
        <stock-biz-version>4.5.1</stock-biz-version>
        <xmd-log4j2.version>2.0.4</xmd-log4j2.version>
        <xmd-common-log4j2.version>2.0.4</xmd-common-log4j2.version>
        <log4j2.version>2.17.1</log4j2.version>
        <log4j2-1.2-api.version>2.17.1</log4j2-1.2-api.version>
        <ocms.channel-client.version>2.27.17</ocms.channel-client.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.dianping.zebra</groupId>
                <artifactId>zebra-calcite</artifactId>
                <version>3.3.2</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.zebra</groupId>
                <artifactId>zebra-tool</artifactId>
                <version>3.3.2</version>
            </dependency>
            <dependency>
                <groupId>com.meituan.xframe</groupId>
                <artifactId>xframe-dependencies-analytics</artifactId>
                <version>0.0.46</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.meituan</groupId>
                <artifactId>poros-high-level-client</artifactId>
                <version>0.9.22_ES7</version>
            </dependency>
            <dependency>
                <groupId>com.meituan.shangou.saas</groupId>
                <artifactId>reco_store_saas_order_platform_common</artifactId>
                <version>1.9.44</version>
            </dependency>
            <dependency>
                <groupId>com.meituan.shangou.linz</groupId>
                <artifactId>linz-boot</artifactId>
                <version>1.0.0</version>
            </dependency>
            <dependency>
                <artifactId>poi-ooxml-schemas</artifactId>
                <groupId>org.apache.poi</groupId>
                <version>3.17</version>
            </dependency>

            <dependency>
                <groupId>com.meituan.shangou.saas</groupId>
                <artifactId>reco_store_saas_order_management_client</artifactId>
                <version>1.6.87</version>
            </dependency>
            <dependency>
                <groupId>org.powermock</groupId>
                <artifactId>powermock-module-junit4</artifactId>
                <version>2.0.2</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.powermock</groupId>
                <artifactId>powermock-api-mockito2</artifactId>
                <version>2.0.2</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>com.github.jsonzou</groupId>
                <artifactId>jmockdata</artifactId>
                <version>4.3.0</version>
            </dependency>
            <dependency>
                <groupId>com.google.code.gson</groupId>
                <artifactId>gson</artifactId>
                <version>2.8.6</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.meituan.shangou.empower.sgshopmgmt</groupId>
                <artifactId>reco_store_saas_product_biz-client</artifactId>
                <version>1.8.4</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.shangou</groupId>
                <artifactId>shangou-exception-common</artifactId>
                <version>2.8.6</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.shangou.sgxsupply.wxmall</groupId>
                <artifactId>bizmanagement-client</artifactId>
                <version>2.18.7</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-collections4</artifactId>
                <version>4.4</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-compress</artifactId>
                <version>1.19</version>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.inf</groupId>
            <artifactId>xmd-log4j2</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.inf</groupId>
            <artifactId>xmd-common-log4j2</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.log</groupId>
            <artifactId>scribe-log4j2</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.xframe</groupId>
            <artifactId>thrift-xframe-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.xframe</groupId>
            <artifactId>web-xframe-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.rhino</groupId>
            <artifactId>rhino-client</artifactId>
        </dependency>
        <!--复制e api-->
        <!--token 服务-->
        <dependency>
            <groupId>com.sankuai.conch.certify</groupId>
            <artifactId>tokenAccessSdk</artifactId>
        </dependency>
        <!--密文加解密-->
        <dependency>
            <groupId>com.meituan.service.inf</groupId>
            <artifactId>kms-pangolin-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan</groupId>
            <artifactId>mtconfig-client</artifactId>
        </dependency>

        <!-- 采购client -->
        <dependency>
            <groupId>com.sankuai.meituan.reco.supplychain.purchase</groupId>
            <artifactId>reco-supplychain-purchase-client</artifactId>
            <version>2.16.7</version>
            <exclusions>
                <exclusion>
                    <artifactId>mtconfig-client</artifactId>
                    <groupId>com.sankuai.meituan</groupId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.sankuai.sgxsupply.purchase</groupId>
            <artifactId>sgxsupply-purchase-client</artifactId>
            <version>1.0</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>netty</artifactId>
                    <groupId>io.netty</groupId>
                </exclusion>
            </exclusions>
        </dependency>


        <!-- 歪马B端项目 -->
        <dependency>
            <groupId>com.sankuai.shangou.sgxsupply.wxmall</groupId>
            <artifactId>bizmanagement-client</artifactId>
            <version>2.18.7</version>
            <exclusions>
                <exclusion>
                    <groupId>com.sankuai.shangou</groupId>
                    <artifactId>bizmanagement-client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.dianping.zebra</groupId>
            <artifactId>zebra-api</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>kms-java-client</artifactId>
                    <groupId>com.meituan.service.inf</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.sankuai.shangou.empower</groupId>
            <artifactId>infrastructure-im-client</artifactId>
            <version>1.0.2</version>
        </dependency>

        <dependency>
            <groupId>com.dianping.squirrel</groupId>
            <artifactId>squirrel-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.dianping.lion</groupId>
            <artifactId>lion-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.meituan.mafka</groupId>
            <artifactId>mafka-client_2.10</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.codehaus.jackson</groupId>
                    <artifactId>jackson</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.netty</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-tcnative-boringssl-static</artifactId>
            <version>2.0.51.Final</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.shangou.logistics</groupId>
            <artifactId>handle-unit-api</artifactId>
            <version>1.0.0</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.reco.store.management</groupId>
            <artifactId>empower-task-service-idl</artifactId>
            <version>3.54.40</version>
            <exclusions>
                <exclusion>
                    <groupId>javax.validation</groupId>
                    <artifactId>validation-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.hibernate.validator</groupId>
                    <artifactId>hibernate-validator</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>aws-java-sdk-core</artifactId>
                    <groupId>com.amazonaws</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.reco.pickselect</groupId>
            <artifactId>pick-select-service-idl</artifactId>
            <version>6.2.3</version>
            <exclusions>
                <exclusion>
                    <artifactId>rhino-client</artifactId>
                    <groupId>com.dianping.rhino</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.sankuai.sgdata</groupId>
            <artifactId>query-api-sdk</artifactId>
            <version>1.0.3</version>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j-over-slf4j</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.sankuai.shangou</groupId>
            <artifactId>dmp-sdk</artifactId>
            <version>1.0.15-fusion-v2-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>thrift-xframe-boot-starter</artifactId>
                    <groupId>com.meituan.xframe</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>zebra-xframe-boot-starter</artifactId>
                    <groupId>com.meituan.xframe</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.waimai.service.order</groupId>
            <artifactId>waimai_service_order_clientassembly</artifactId>
            <version>4.179.0</version>
            <exclusions>
                <exclusion>
                    <groupId>com.meituan.mafka</groupId>
                    <artifactId>mafka-client_2.9</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>waimai_set_router-client</artifactId>
                    <groupId>com.sankuai.meituan.waimai</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>jline</groupId>
            <artifactId>jline</artifactId>
            <version>2.14.5</version>
        </dependency>

        <dependency>
            <groupId>com.meituan.mtrace</groupId>
            <artifactId>mtrace-http</artifactId>
        </dependency>

        <dependency>
            <groupId>com.taobao.tair</groupId>
            <artifactId>tair3-client</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>mtconfig-client</artifactId>
                    <groupId>com.sankuai.meituan</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.sankuai.octo</groupId>
            <artifactId>mns-invoker</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.octo</groupId>
            <artifactId>idl-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.octo</groupId>
            <artifactId>idl-sgagent</artifactId>
        </dependency>
        <!--store-saas-common-->
        <dependency>
            <groupId>com.sankuai.meituan.shangou</groupId>
            <artifactId>store-saas-common</artifactId>
            <version>2.1.4</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>asm</groupId>
                    <artifactId>asm-all</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>poi-ooxml-schemas</artifactId>
                    <groupId>org.apache.poi</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>eagle-restclient</artifactId>
                    <groupId>com.sankuai.meituan</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>netty</artifactId>
                    <groupId>io.netty</groupId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.xmlbeans</groupId>
                    <artifactId>xmlbeans</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-compress</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.poi</groupId>
                    <artifactId>poi-ooxml</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.poi</groupId>
                    <artifactId>poi</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.poi</groupId>
                    <artifactId>poi-scratchpad</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.poi</groupId>
                    <artifactId>poi-ooxml-schemas</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>elasticsearch</artifactId>
                    <groupId>org.elasticsearch</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.shangou.saas.crm</groupId>
            <artifactId>store-saas-data-client</artifactId>
            <version>2.3.1</version>
        </dependency>

        <!--会员服务mif-->
        <dependency>
            <groupId>com.sankuai.meituan.shangou.saas.crm</groupId>
            <artifactId>store-saas-mif-client</artifactId>
            <version>1.22</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan</groupId>
            <artifactId>mtgis-remote-service</artifactId>
            <version>1.0.24.19</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan</groupId>
            <artifactId>mt-service-http</artifactId>
            <version>1.3.20</version>
        </dependency>

        <!--赋能商品服务依赖-->
        <dependency>
            <groupId>com.sankuai.meituan.shangou.platform</groupId>
            <artifactId>shangou_empower_product_client</artifactId>
            <version>2.8.3</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.shangou.empower.sgshopmgmt</groupId>
            <artifactId>reco_store_saas_product_biz-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.shangou</groupId>
            <artifactId>reco_store_saas_message_management_client</artifactId>
            <version>1.2.6</version>
        </dependency>

        <!--配送服务-->
        <dependency>
            <groupId>com.meituan.shangou.saas</groupId>
            <artifactId>reco_store_saas_delivery_service-client</artifactId>
            <version>1.2.2</version>
        </dependency>

        <dependency>
            <groupId>com.meituan.rc.yoda</groupId>
            <artifactId>yoda-client</artifactId>
            <version>1.2.2</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.shangou</groupId>
            <artifactId>shangou-exception-common</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.reco.pickselect</groupId>
            <artifactId>pick-select-storage-bin-client</artifactId>
            <version>3.26.11</version>
            <exclusions>
                <exclusion>
                    <artifactId>elasticsearch-rest-high-level-client</artifactId>
                    <groupId>org.elasticsearch.client</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.reco.pickselect</groupId>
            <artifactId>pick-select-logic-idl</artifactId>
            <version>1.10.0</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.reco.pickselect</groupId>
            <artifactId>pick-select-storage-bin-idl</artifactId>
            <version>4.0.1</version>
            <exclusions>
                <exclusion>
                    <groupId>com.sankuai.meituan.reco.store.management</groupId>
                    <artifactId>stock-biz-commons</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.meituan.servicecatalog</groupId>
            <artifactId>api-annotations</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.reco.store</groupId>
            <artifactId>store-saas-infrastructure-shield-common</artifactId>
            <version>1.0.5</version>
            <exclusions>
                <exclusion>
                    <artifactId>xstream</artifactId>
                    <groupId>com.thoughtworks.xstream</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-cloud-starter-feign</artifactId>
                    <groupId>org.springframework.cloud</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-cloud-starter-eureka</artifactId>
                    <groupId>org.springframework.cloud</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan.shangou.empower</groupId>
            <artifactId>reco_store_saas_auth_client</artifactId>
            <version>1.6.15</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan.shangou.empower</groupId>
            <artifactId>reco_store_sac_client</artifactId>
            <version>2.3.4</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.security</groupId>
            <artifactId>sec-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.cip.crane</groupId>
            <artifactId>crane-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.service.inf</groupId>
            <artifactId>kms-java-client</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.meituan.service.mobile</groupId>
                    <artifactId>mtthrift</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.meituan.service.inf</groupId>
            <artifactId>kms-tls-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.oceanus.http</groupId>
            <artifactId>oceanus-http</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.shangou.saas</groupId>
            <artifactId>reco_store_saas_tenant_client</artifactId>
            <version>3.5.21</version>
        </dependency>
        <!-- 打印服务新包 -->
        <dependency>
            <groupId>com.meituan.shangou.saas</groupId>
            <artifactId>qnh_fulfill_print_client</artifactId>
            <version>1.0.6</version>
        </dependency>
        <dependency>
            <groupId>com.meituan.shangou.saas</groupId>
            <artifactId>reco_store_saas_tenant_high_level_client</artifactId>
            <version>3.3.3</version>
        </dependency>
<!--        &lt;!&ndash; 订单服务 &ndash;&gt;-->
<!--        <dependency>-->
<!--            <groupId>com.meituan.shangou.saas</groupId>-->
<!--            <artifactId>reco_store_saas_order_management_client</artifactId>-->
<!--            <version>1.5.77</version>-->
<!--            <exclusions>-->
<!--                <exclusion>-->
<!--                    <groupId>*</groupId>-->
<!--                    <artifactId>*</artifactId>-->
<!--                </exclusion>-->
<!--            </exclusions>-->
<!--        </dependency>-->
        <!-- 对账服务 -->
        <dependency>
            <groupId>com.sankuai.meituan.shangou.empower.settlement</groupId>
            <artifactId>reco_shopmgmt_settlement_service-client</artifactId>
            <version>1.0.217</version>
            <exclusions>
                <exclusion>
                    <groupId>org.hibernate</groupId>
                    <artifactId>hibernate-validator</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>javax.validation</groupId>
                    <artifactId>validation-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--支付服务-->
        <dependency>
            <groupId>com.sankuai.meituan.shangou.empower</groupId>
            <artifactId>reco_store_saas_payment_client</artifactId>
            <version>1.0.2</version>
            <exclusions>
                <exclusion>
                    <artifactId>zookeeper</artifactId>
                    <groupId>org.apache.zookeeper</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- 售后服务 -->
        <dependency>
            <groupId>com.meituan.shangou.saas</groupId>
            <artifactId>reco_store_saas_order_biz_client</artifactId>
            <version>2.3.47</version>
            <exclusions>
                <exclusion>
                    <groupId>org.hibernate.validator</groupId>
                    <artifactId>hibernate-validator</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.hibernate</groupId>
                    <artifactId>hibernate-validator</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>javax.validation</groupId>
                    <artifactId>validation-api</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>pick-select-common</artifactId>
                    <groupId>com.meituan.reco.pickselect</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.apache.ant</groupId>
            <artifactId>ant</artifactId>
            <version>1.9.7</version>
        </dependency>

        <!--ocms-->
        <dependency>
            <groupId>com.sankuai.meituan.shangou.empower.ocms</groupId>
            <artifactId>reco_shopmgmt_ocms_service-client</artifactId>
            <version>3.24.27</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.shangou.empower.ocms</groupId>
            <artifactId>reco_shopmgmt_ocms_channel_service-client</artifactId>
            <version>${ocms.channel-client.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.dianping.rhino</groupId>
                    <artifactId>rhino-client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan.shangou.sgshopcrm</groupId>
            <artifactId>reco_store_saas_price_management-client</artifactId>
            <version>1.5.3</version>
            <exclusions>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>

        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.shangou.linz</groupId>
            <artifactId>linz-boot</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan.reco.store</groupId>
            <artifactId>store-management-receipt-idl</artifactId>
            <version>2.4.2</version>
            <exclusions>
                <exclusion>
                    <groupId>org.hibernate.validator</groupId>
                    <artifactId>hibernate-validator</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>aws-java-sdk-core</artifactId>
                    <groupId>com.amazonaws</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.reco.pickselect</groupId>
            <artifactId>pick-select-ebase-idl</artifactId>
            <version>2.13.39</version>
            <exclusions>
                <exclusion>
                    <groupId>org.hibernate.validator</groupId>
                    <artifactId>hibernate-validator</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan.shangou.empower.sgshopmgmt</groupId>
            <artifactId>productplatform-sdk</artifactId>
            <version>1.8.19</version>
            <exclusions>
                <exclusion>
                    <groupId>org.hibernate.validator</groupId>
                    <artifactId>hibernate-validator</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan.reco.sgfulfillment</groupId>
            <artifactId>reco_fulfillment_tms-rider-delivery-client</artifactId>
            <version>2.2.91</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan.reco.sgfulfillment</groupId>
            <artifactId>reco_fulfillment_tms-delivery-client</artifactId>
            <version>2.2.83-fusion-v2-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan.reco.sgfnqnh.promotion</groupId>
            <artifactId>qnh-promotion-client</artifactId>
            <version>1.0.7</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.it.sso</groupId>
            <artifactId>sso-java-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.shangou</groupId>
            <artifactId>shangou-supplychain-api</artifactId>
            <version>2.3.2</version>
        </dependency>
        <dependency>
            <groupId>com.meituan.service.mobile</groupId>
            <artifactId>mtthrift</artifactId>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-module-junit4</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-api-mockito2</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.github.jsonzou</groupId>
            <artifactId>jmockdata</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.xframe</groupId>
            <artifactId>config-xframe-boot-autoconfigure</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.reco.pickselect</groupId>
            <artifactId>pick-select-common</artifactId>
            <version>3.10.6</version>
            <exclusions>
                <exclusion>
                    <artifactId>zookeeper</artifactId>
                    <groupId>org.apache.zookeeper</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>zebra-api</artifactId>
                    <groupId>com.dianping.zebra</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mafka-client_2.10</artifactId>
                    <groupId>com.meituan.mafka</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.sgfulfillment.comment</groupId>
            <artifactId>fulfillment-comment-client</artifactId>
            <version>1.0.8</version>
        </dependency>

        <!--添加重试策略-->
        <dependency>
            <groupId>org.springframework.retry</groupId>
            <artifactId>spring-retry</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>4.1.1</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>4.1.1</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml-schemas</artifactId>
            <version>4.1.1</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-scratchpad</artifactId>
            <version>4.1.1</version>
        </dependency>
        <dependency>
            <groupId>org.apache.xmlbeans</groupId>
            <artifactId>xmlbeans</artifactId>
            <version>3.0.2</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-compress</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper</artifactId>
            <version>5.1.2</version>
        </dependency>
        <dependency>
            <groupId>com.thoughtworks.xstream</groupId>
            <artifactId>xstream</artifactId>
            <version>1.4.18</version>
        </dependency>
        <dependency>
            <groupId>com.meituan.shangou.empower</groupId>
            <artifactId>uwmsplatform-client</artifactId>
            <version>3.0.18</version>
        </dependency>
        <dependency>
            <groupId>com.meituan.shangou.empower</groupId>
            <artifactId>uwmsplatform-idl</artifactId>
            <version>3.0.18</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan.waimai</groupId>
            <artifactId>waimai-thrift-tools</artifactId>
            <version>1.9.6.0.5</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.shangou</groupId>
            <artifactId>shangou-thrift-augment</artifactId>
            <version>2.6.1</version>
        </dependency>

        <dependency>
            <groupId>com.meituan.shangou.goodscenter</groupId>
            <artifactId>goods_center_client</artifactId>
            <version>1.1.16</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.shangou.logistics</groupId>
            <artifactId>oio-api</artifactId>
            <version>1.1.4</version>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.qnh.ofc</groupId>
            <artifactId>qnh_ofc_transfer-client</artifactId>
            <version>1.0.5</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.shangou.qnh.ofc</groupId>
            <artifactId>qnh_ofc_ofw-client</artifactId>
            <version>1.0.6</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.sgfnqnh.finance</groupId>
            <artifactId>reco_sgfnqnh_finance_tax-client</artifactId>
            <version>1.0.1</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.shangou.xsupply</groupId>
            <artifactId>product_management-client</artifactId>
            <version>2.0.0</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.reco.store.management</groupId>
            <artifactId>wms-api</artifactId>
            <version>3.55.8</version>
        </dependency>
        <dependency>
            <groupId>com.meituan.shangou.saas</groupId>
            <artifactId>reco_store_saas_order_platform_client</artifactId>
            <version>1.9.136</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.shangou.qnh.fulfill</groupId>
            <artifactId>pick-select-query-idl</artifactId>
            <version>1.0.38-drunk-horse-fusion-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.shangou.infra</groupId>
            <artifactId>osw-api</artifactId>
            <version>1.0.56</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan.shangou.xsupply</groupId>
            <artifactId>price-management-client</artifactId>
            <version>1.0.27</version>
        </dependency>
        <!-- 歪马B端项目 -->
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <compilerArgument>-parameters</compilerArgument>
                    <encoding>UTF-8</encoding>
                    <source>1.8</source>
                    <target>1.8</target>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <distributionManagement>
        <repository>
            <id>meituan-nexus-releases</id>
            <name>Meituan Nexus Repository</name>
            <url>http://maven.sankuai.com/nexus/content/repositories/releases/</url>
        </repository>
        <snapshotRepository>
            <id>meituan-nexus-snapshots</id>
            <name>Meituan Nexus Repository</name>
            <url>http://maven.sankuai.com/nexus/content/repositories/snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

    <profiles>
        <profile>
            <id>dev</id>
            <build>
                <resources>
                    <resource>
                        <directory>src/main/profiles/dev</directory>
                    </resource>
                    <resource>
                        <filtering>true</filtering>
                        <directory>src/main/resources</directory>
                    </resource>
                </resources>
            </build>
            <properties>
                <active-profile>dev</active-profile>
            </properties>
        </profile>
        <profile>
            <id>local</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <build>
                <resources>
                    <resource>
                        <directory>src/main/profiles/test</directory>
                    </resource>
                    <resource>
                        <filtering>true</filtering>
                        <directory>src/main/resources</directory>
                    </resource>
                </resources>
            </build>
            <properties>
                <active-profile>test</active-profile>
            </properties>
        </profile>
        <profile>
            <id>test</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <build>
                <resources>
                    <resource>
                        <directory>src/main/profiles/test</directory>
                    </resource>
                    <resource>
                        <filtering>true</filtering>
                        <directory>src/main/resources</directory>
                    </resource>
                </resources>
            </build>
            <properties>
                <active-profile>test</active-profile>
            </properties>
        </profile>
        <profile>
            <id>staging</id>
            <build>
                <resources>
                    <resource>
                        <directory>src/main/profiles/staging</directory>
                    </resource>
                    <resource>
                        <filtering>true</filtering>
                        <directory>src/main/resources</directory>
                    </resource>
                </resources>
            </build>
            <properties>
                <active-profile>staging</active-profile>
            </properties>
        </profile>
        <profile>
            <id>prod</id>
            <build>
                <resources>
                    <resource>
                        <directory>src/main/profiles/prod</directory>
                    </resource>
                    <resource>
                        <filtering>true</filtering>
                        <directory>src/main/resources</directory>
                    </resource>
                </resources>
            </build>
            <properties>
                <active-profile>prod</active-profile>
            </properties>
        </profile>
    </profiles>
</project>
