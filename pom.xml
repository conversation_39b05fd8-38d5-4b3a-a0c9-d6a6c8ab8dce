<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>xframe-starter-parent</artifactId>
        <groupId>com.meituan.xframe</groupId>
        <!--XFrame产品版本：https://km.sankuai.com/custom/onecloud/page/133516477-->
        <version>2.6.12.1</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <env-suffix>-SNAPSHOT</env-suffix>
    </properties>

    <name>ofapp</name>

    <groupId>com.sankuai.shangou.supplychain</groupId>
    <artifactId>ofapp</artifactId>
    <version>1.0.0</version>
    <packaging>pom</packaging>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.sankuai.shangou.supplychain</groupId>
                <artifactId>ofapp-api</artifactId>
                <version>1.0.0</version>
            </dependency>
        </dependencies>
    </dependencyManagement>


    <profiles>
        <profile>
            <id>local</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <env-suffix>-SNAPSHOT</env-suffix>
            </properties>
        </profile>
        <profile>
            <id>dev</id>
            <properties>
                <env-suffix>-SNAPSHOT</env-suffix>
            </properties>
        </profile>
        <profile>
            <id>test</id>
            <properties>
                <env-suffix>-SNAPSHOT</env-suffix>
            </properties>
        </profile>
        <profile>
            <id>staging</id>
            <properties>
                <env-suffix/>
            </properties>
            <distributionManagement>
                <repository>
                    <id>meituan-nexus-releases</id>
                    <name>Meituan Nexus Repository</name>
                    <url>http://pixel.sankuai.com/repository/releases</url>
                </repository>
                <snapshotRepository>
                    <id>meituan-nexus-snapshots</id>
                    <name>Meituan Nexus Repository</name>
                    <url>http://pixel.sankuai.com/repository/snapshots</url>
                </snapshotRepository>
            </distributionManagement>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <env-suffix/>
            </properties>
            <distributionManagement>
                <repository>
                    <id>meituan-nexus-releases</id>
                    <name>Meituan Nexus Repository</name>
                    <url>http://pixel.sankuai.com/repository/releases</url>
                </repository>
                <snapshotRepository>
                    <id>meituan-nexus-snapshots</id>
                    <name>Meituan Nexus Repository</name>
                    <url>http://pixel.sankuai.com/repository/snapshots</url>
                </snapshotRepository>
            </distributionManagement>
        </profile>
    </profiles>

    <modules>
        <module>ofapp-api</module>
    </modules>
</project>