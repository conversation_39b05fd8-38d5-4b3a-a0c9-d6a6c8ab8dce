<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:util="http://www.springframework.org/schema/util"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd
        http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util.xsd">

    <bean id="tmsDeliveryClientThriftPoolConfig" class="com.meituan.service.mobile.mtthrift.client.pool.MTThriftPoolConfig">
        <property name="normalSize" value="2"/> <!-- 最大连接数，不建议设置太大 -->
        <property name="initialSize" value="1"/> <!-- 初始连接数，可以与最大连接数保持一致 -->
    </bean>

    <bean id="deliveryOperationThriftServiceClient" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="tmsDeliveryClientThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.DeliveryOperationThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('tms_delivery_thrift_timeout','10000')}"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.sgfulfillment.tms"/>
        <property name="remoteServerPort" value="8411"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="deliveryConfigurationThriftServiceClient" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="tmsDeliveryClientThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.DeliveryConfigurationThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('tms_delivery_thrift_timeout','10000')}"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.sgfulfillment.tms"/>
        <property name="remoteServerPort" value="8411"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="deliveryCallbackThriftServiceClient" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="tmsDeliveryClientThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.callback.DeliveryCallbackThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('tms_delivery_thrift_timeout','10000')}"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.sgfulfillment.tms"/>
        <property name="remoteServerPort" value="8411"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="deliveryChannelThriftServiceClient" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="tmsDeliveryClientThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.deliverychannel.DeliveryChannelThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('tms_delivery_thrift_timeout','10000')}"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.sgfulfillment.tms"/>
        <property name="remoteServerPort" value="8411"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="riderDeliveryTaskThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="tmsDeliveryClientThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.shangou.empower.rider.client.thrift.task.RiderDeliveryTaskThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('tms_delivery_thrift_timeout','10000')}"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.sgfulfillment.tms"/>
        <property name="remoteServerPort" value="8411"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/>
    </bean>

</beans>
