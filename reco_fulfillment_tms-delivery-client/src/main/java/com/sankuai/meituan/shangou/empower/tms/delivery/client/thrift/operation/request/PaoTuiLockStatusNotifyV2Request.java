package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@TypeDoc(
        description = "跑腿锁单2.0 解锁通知请求体",
        authors = {
                "huangmingjun"
        }
)
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
public class PaoTuiLockStatusNotifyV2Request {
    @FieldDoc(
            description = "渠道订单id",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public String channelOrderId;


    @FieldDoc (
            description = "是否可发配送 0:可发配送   1:不可发配送",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(2)
    public Integer deliveryAvailable;

}
