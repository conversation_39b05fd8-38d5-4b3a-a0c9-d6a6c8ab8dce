package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.launchpoint.ImmediateOrderDeliveryLaunchPointEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.dto.DeliveryChannelLaunchPointDto;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.dto.StoreAggDeliveryConfigDto;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.ParamCheckUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;


@TypeDoc(
		description = "修改门店/仓库聚合配送配置请求体"
)
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
public class StoreAggDeliveryConfigModifyRequest {

	@FieldDoc(
			description = "租户id",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(1)
	private Long tenantId;

	@FieldDoc(
			description = "门店id",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(2)
	private Long storeId;

	@FieldDoc(
			description = "操作员id",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(3)
	private Long operatorId;

	@FieldDoc(
			description = "操作员姓名",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(4)
	private String operatorName;

	@FieldDoc(
			description = "门店聚合配送配置",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(5)
	private StoreAggDeliveryConfigDto storeAggDeliveryConfigDto;

	public String validate() {
		if (tenantId == null || tenantId <= 0L) {
			return "租户id不合法";
		}
		if (storeId == null || storeId <= 0L) {
			return "门店id不合法";
		}
		if (storeAggDeliveryConfigDto == null) {
			return "门店聚合配送参数不能为空";
		}
		if (validateDeliveryChannelLaunchPoints(storeAggDeliveryConfigDto)) {
			return "门店聚合配送参数不合法";
		}
		return null;
	}

	/**
	 * @description: 校验修改聚合配送渠道及发单节点信息
	 * @param: storeAggDeliveryConfigDto
	 * @return boolean 如果校验通过，返回false
	*/
	private boolean validateDeliveryChannelLaunchPoints(StoreAggDeliveryConfigDto storeAggDeliveryConfigDto) {
		if (CollectionUtils.isEmpty(storeAggDeliveryConfigDto.getDeliveryChannelLaunchPoints())) {
			return false;
		}

		for (DeliveryChannelLaunchPointDto launchPoint: storeAggDeliveryConfigDto.getDeliveryChannelLaunchPoints()) {
			if (ImmediateOrderDeliveryLaunchPointEnum.enumOf(launchPoint.getDeliveryLaunchPoint()) == null) {
				return true;
			}

			if (!ParamCheckUtils.checkDeliveryLaunchDelayMinutes(launchPoint.getDeliveryLaunchDelayMinutes())
					|| !ParamCheckUtils.checkDeliveryLaunchDelayMinutes(launchPoint.getBookingOrderDeliveryLaunchMinutes())) {
				return true;
			}
		}
		return false;
	}
}
