package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

/**
 * 查询配送异常订单号的请求.
 *
 * <AUTHOR>
 * @since 2021/4/16 11:17
 */
@TypeDoc(
        description = "查询配送异常订单号的请求",
        authors = {
                "liyang176"
        }
)
@AllArgsConstructor
@NoArgsConstructor
@ThriftStruct
public class QueryDeliveryExceptionOrderRequest {

    @FieldDoc(
            description = "赋能门店 ID",
            requiredness = Requiredness.REQUIRED
    )
    private Long empowerStoreId;

    @FieldDoc(
            description = "租户 ID",
            requiredness = Requiredness.REQUIRED
    )
    private Long tenantId;

    @ThriftField(1)
    public Long getEmpowerStoreId() {
        return empowerStoreId;
    }

    @ThriftField
    public void setEmpowerStoreId(Long empowerStoreId) {
        this.empowerStoreId = empowerStoreId;
    }

    @ThriftField(2)
    public Long getTenantId() {
        return tenantId;
    }

    @ThriftField
    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public String validate() {
        if (empowerStoreId == null || empowerStoreId <= 0L) {
            return "赋能门店ID不合法";
        }

        return null;
    }
}
