package com.sankuai.meituan.shangou.empower.tms.delivery.client.utils;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.node.ObjectNode;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/4
 */
@Slf4j
public class JsonUtil {

	private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper()
			.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
			.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false)
			.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false)
			.setSerializationInclusion(JsonInclude.Include.NON_NULL)
			.findAndRegisterModules();

	public static String toJson(Object object) {
		if (object == null) {
			return null;
		}

		try {
			return OBJECT_MAPPER.writeValueAsString(object);
		} catch (Exception e) {
			log.error("将Java对象转换成Json串出错！", e);
			throw new JsonParseException("将Java对象转换成Json串出错", e);
		}
	}

	public static <T> T fromJson(String json, Class<T> clazz) {
		if (StringUtils.isBlank(json)) {
			return null;
		}

		try {
			return OBJECT_MAPPER.readValue(json, clazz);
		} catch (Exception e) {
			log.error("将Json串转换成Java对象出错 json:{}", json, e);
			throw new JsonParseException("将Json串转换成Java对象出错", e);
		}
	}

	public static <T> T fromJson(String json, TypeReference<T> typeRef) {
		if (StringUtils.isBlank(json)) {
			return null;
		}

		try {
			return OBJECT_MAPPER.readValue(json, typeRef);
		} catch (Exception e) {
			log.error("将Json串转换成Java对象出错 json:{}", json, e);
			throw new JsonParseException("将Json串转换成Java对象出错", e);
		}
	}

	public static JsonNode toJsonNode(String json) {
		if (StringUtils.isBlank(json)) {
			return null;
		}

		try {
			return OBJECT_MAPPER.readTree(json);
		} catch (Exception e) {
			log.error("将Json串转换成JsonNode出错！", e);
			throw new JsonParseException("将Json串转换成JsonNode出错", e);
		}
	}

	private static class JsonParseException extends RuntimeException {
		public JsonParseException(String message, Throwable cause) {
			super(message, cause);
		}
	}

	public static ObjectNode generateObjectNode() {
		return OBJECT_MAPPER.createObjectNode();
	}

}
