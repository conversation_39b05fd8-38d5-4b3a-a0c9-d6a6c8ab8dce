package com.sankuai.meituan.shangou.empower.tms.delivery.client.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

import java.util.HashMap;
import java.util.Map;

/**
 * 运单事件枚举
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/8
 */
public enum DeliveryEventEnum {

	/**
	 * 发起配送
	 */
	LAUNCH_DELIVERY(1, DeliveryStatusEnum.DELIVERY_LAUNCHED),

	/**
	 * 配送平台接单
	 */
	DELIVERY_PLATFORM_ACCEPT_DELIVERY_ORDER(10, DeliveryStatusEnum.WAITING_TO_ASSIGN_RIDER),

	/**
	 * 骑手接单
	 */
	RIDER_ASSIGN(20, DeliveryStatusEnum.RIDER_ASSIGNED),

	/**
	 * 骑手到店
	 */
	RIDER_ARRIVE_SHOP(30, DeliveryStatusEnum.RIDER_ARRIVED_SHOP),

	/**
	 * 骑手开始配送
	 */
	RIDER_START_DELIVERY(40, DeliveryStatusEnum.RIDER_TAKEN_GOODS),

	/**
	 * 商家开始配送
	 */
	MERCHANT_START_DELIVERY(41, DeliveryStatusEnum.MERCHANT_DELIVERING),

	/**
	 * 骑手送达
	 */
	RIDER_FINISH_DELIVERY(50, DeliveryStatusEnum.DELIVERY_DONE),

	/**
	 * 订单完成触发的兜底完成
	 */
	FINISH_BY_ORDER_DONE(51, DeliveryStatusEnum.DELIVERY_DONE, false),

	/**
	 * 配送取消
	 */
	DELIVERY_CANCEL(60, DeliveryStatusEnum.DELIVERY_CANCELLED),

	/**
	 * 特殊情况下订单无法被取消时的兜底Crane任务取消
	 */
	CANCEL_BY_CRANE_TASK(61, DeliveryStatusEnum.DELIVERY_CANCELLED, false),

	/**
	 * 配送失败
	 */
	DELIVERY_FAIL(70, DeliveryStatusEnum.DELIVERY_FAILED),

	/**
	 * 配送拒单
	 */
	DELIVERY_REJECT(110, DeliveryStatusEnum.DELIVERY_REJECTED),

	/**
	 * 配送异常
	 */
	DELIVERY_EXCEPTION(120, null),

	/**
	 * 骑手改派
	 */
	RIDER_CHANGE(130, null),

	/**
	 * 主动取消配送失败
	 */
	DELIVERY_CANCEL_FAIL(140, null),

	/**
	 * 运单状态锁定
	 */
	DELIVERY_STATUS_LOCK(1111,  null),

	/**
	 * 运单状态解锁
	 */
	DELIVERY_STATUS_UNLOCK(2222, null);

	private static final Map<Integer, DeliveryEventEnum> CODE_ENUM_MAP = new HashMap<>();
	private static final Map<DeliveryStatusEnum, DeliveryEventEnum> STATUS_EVENT_MAP = new HashMap<>();
	private final int code;
	private final DeliveryStatusEnum targetStatus;
	private final boolean canStatusBackToEvent;

	static {
		for (DeliveryEventEnum each : values()) {
			CODE_ENUM_MAP.put(each.getCode(), each);
			if (each.getTargetStatus() != null && each.canStatusBackToEvent) {
				STATUS_EVENT_MAP.put(each.getTargetStatus(), each);
			}
		}
	}

	DeliveryEventEnum(int code, DeliveryStatusEnum targetStatus) {
		this.code = code;
		this.targetStatus = targetStatus;
		this.canStatusBackToEvent = true;
	}

	DeliveryEventEnum(int code, DeliveryStatusEnum targetStatus, boolean canStatusBackToEvent) {
		this.code = code;
		this.targetStatus = targetStatus;
		this.canStatusBackToEvent = canStatusBackToEvent;
	}

	@JsonValue
	public int getCode() {
		return code;
	}

	public DeliveryStatusEnum getTargetStatus() {
		return targetStatus;
	}

	public static DeliveryEventEnum getEventByStatus(DeliveryStatusEnum targetStatus) {
		if (targetStatus == null) {
			return null;
		}
		return STATUS_EVENT_MAP.get(targetStatus);
	}

	@JsonCreator
	public static DeliveryEventEnum valueOf(Integer code) {
		return CODE_ENUM_MAP.get(code);
	}
}
