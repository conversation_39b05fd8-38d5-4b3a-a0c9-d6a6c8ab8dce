package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.common.Status;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.AggDeliveryPlatformConfig;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.DeliveryOrderInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.TDeliveryDetail;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
public class QueryDeliveryOrderInfoResponse {

    @FieldDoc(
            description = "执行状态",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public Status status;

    @FieldDoc(
            description = "配送信息",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(2)
    public List<DeliveryOrderInfo> deliveryOrderInfoList;

    @FieldDoc(
            description = "麦芽田配送详情链接",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(3)
    public AggDeliveryPlatformConfig maltAggDeliveryPlatformConfig;

    @FieldDoc(
            description = "青云配送详情链接",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(4)
    public Map<String,String> dapAggDeliveryPlatformConfig;
}


