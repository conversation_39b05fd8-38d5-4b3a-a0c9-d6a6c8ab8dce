package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@TypeDoc(
        description = "根据子类型查询异常订单号的请求",
        authors = {
                "goulei02"
        }
)
@AllArgsConstructor
@NoArgsConstructor
@ThriftStruct
@Data
@Builder
public class QueryDeliveryExceptionOrderBySubTypeRequest {

    @FieldDoc(
            description = "子类型，0全部/10未接单/20未到店/30未取货/40配送超时/50系统异常",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public Integer subType;

    @FieldDoc(
            description = "赋能门店 ID",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(2)
    public Long empowerStoreId;

    @FieldDoc(
            description = "租户 ID",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(3)
    public Long tenantId;
}
