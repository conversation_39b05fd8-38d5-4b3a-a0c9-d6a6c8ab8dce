package com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.launchpoint;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

import java.util.HashMap;
import java.util.Map;

/**
 * 预订单配送发起时间点枚举
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/7/8
 */
public enum BookingOrderDeliveryLaunchPointEnum {
	/**
	 * 在预计送达时间前x分钟发起配送
	 */
	BEFORE_DELIVERY(1),

	/**
	 * 拣货完成后X分钟发起配送
	 */
	PICK_DONE(2),

	/**
	 * 订单支付完成后X分钟发起配送
	 */
	ORDER_PAID(3),
	;

	private static final Map<Integer, BookingOrderDeliveryLaunchPointEnum> CODE_ENUM_MAP = new HashMap<>();
	private final int code;

	static {
		for (BookingOrderDeliveryLaunchPointEnum each : values()) {
			CODE_ENUM_MAP.put(each.getCode(), each);
		}
	}

	BookingOrderDeliveryLaunchPointEnum(int code) {
		this.code = code;
	}

	@JsonValue
	public int getCode() {
		return code;
	}

	@JsonCreator
	public static BookingOrderDeliveryLaunchPointEnum enumOf(Integer code) {
		return CODE_ENUM_MAP.get(code);
	}

	public ImmediateOrderDeliveryLaunchPointEnum bookingToImmediate(){
		if(this == ORDER_PAID){
			return ImmediateOrderDeliveryLaunchPointEnum.ORDER_PAID;
		}else if(this == PICK_DONE){
			return ImmediateOrderDeliveryLaunchPointEnum.PICK_DONE;
		}
		return ImmediateOrderDeliveryLaunchPointEnum.MERCHANT_ACCEPT;
	}
}
