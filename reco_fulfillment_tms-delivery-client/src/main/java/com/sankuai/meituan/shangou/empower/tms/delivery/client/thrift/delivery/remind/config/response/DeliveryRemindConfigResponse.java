package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.delivery.remind.config.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.common.Status;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.delivery.remind.config.dto.TDeliveryRemindConfig;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 配送提醒配置的返回体.
 *
 * <AUTHOR>
 * @since 2021/10/8 14:57
 */
@TypeDoc(
        description = "配送提醒配置的返回体",
        authors = {
                "liyang176"
        }
)
@ThriftStruct
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DeliveryRemindConfigResponse {

    @FieldDoc(
            description = "执行状态",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public Status status = Status.SUCCESS;

    @FieldDoc(
            description = "门店的配送提醒配置"
    )
    @ThriftField(2)
    public TDeliveryRemindConfig config;

    public DeliveryRemindConfigResponse(Status status) {
        this.status = status;
    }

    public DeliveryRemindConfigResponse(TDeliveryRemindConfig config) {
        this.status = Status.SUCCESS;
        this.config = config;
    }
}
