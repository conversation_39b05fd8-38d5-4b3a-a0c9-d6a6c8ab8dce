package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.response;

import java.util.List;
import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created by liuyonggao on 2021/1/6.
 */

@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
public class TStoreConfig {


    @FieldDoc(
            description = "租户Id",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public Long tenantId;

    @FieldDoc(
            description = "门店id",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(2)
    public Long storeId;

    @FieldDoc(
            description = "电话",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(3)
    public String contactPhone;

    @FieldDoc(
            description = "自动发配送时间点，1-商家接单，2-拣货完成",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(4)
    public Integer autoLaunchPoint;

    @FieldDoc(
            description = "自动发配送延时，单位（分钟）",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(5)
    public Integer autoLaunchDelayMinutes;

    @FieldDoc(
            description = "发配送方式，1-自动发配送，2-手动发配送",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(6)
    public Integer launchPattern;

    @FieldDoc(
            description = "门店名称",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(7)
    public String storeName;

    @FieldDoc(
            description = "发配送规则，1-低价优先",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(8)
    public Integer launchRule;

    @FieldDoc(
            description = "可用的发配送规则，如：低价优先",
            requiredness = Requiredness.NONE
    )
    @ThriftField(9)
    public List<TLaunchRule> availableLaunchRules;

    @FieldDoc(
            description = "预约单-自动发配送时间点，1-预计送达前，2-拣货完成后",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(10)
    public Integer bookingOrderAutoLaunchPoint;

    @FieldDoc(
            description = "预约单 自动发配送分钟数，单位（分钟）",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(11)
    public Integer bookingOrderAutoLaunchMinutes;
    @FieldDoc(
            description = "三方聚合运力平台配置",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(12)
    public List<TAggDeliveryPlatformConfig> aggPlatformConfigs;

    @FieldDoc(
            description = "上次聚合运力平台配置",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(13)
    public List<TAggDeliveryPlatformConfig> lastPlatformConfigs;



}
