package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.common.Status;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.common.TPageInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.TDeliveryDetail;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/8/28 20:22
 **/
@ThriftStruct
@NoArgsConstructor
@AllArgsConstructor
@Data
public class PageQueryDeliveryInfoResponse {

    @FieldDoc(
            description = "响应状态",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(value = 1, requiredness = ThriftField.Requiredness.REQUIRED)
    public Status status = Status.SUCCESS;

    @FieldDoc(
            description = "分页信息",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(2)
    public TPageInfo pageInfo;

    @FieldDoc(
            description = "配送信息",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(3)
    public List<TDeliveryDetail> TDeliveryDetails;

}
