package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.callback.request.SelfDeliveryCallbackReq;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.request.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.request.orderplatform.DeliveryManualLaunchRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.response.*;

/**
 * 配送操作服务，提供发起配送，转自配送等能力
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/10/22
 */
@InterfaceDoc(
		displayName = "配送操作服务",
		type = "octo.thrift.annotation",
		scenarios = "配送操作服务，提供预发配送，发起配送，转自配送等能力",
		description = "配送操作服务，提供预发配送，发起配送，转自配送等能力",
		authors = {
				"hedong07"
		}
)
@ThriftService
public interface DeliveryOperationThriftService {

	@MethodDoc(
			displayName = "预发三方配送",
			description = "预发三方配送，查询可配送渠道，提供配送费用的信息",
			parameters = {
					@ParamDoc(
							name = "request",
							description = "预发三方配送请求",
							type = DeliveryLaunchRequest.class,
							requiredness = Requiredness.REQUIRED
					)
			},
			returnValueDescription = "预发三方配送请求操作结果",
			restExampleResponseData = "{\"status\":{\"code\":0, \"msg\":\"\"}}",
			example = "暂无"
	)
	@ThriftMethod
	DeliveryPreLaunchResponse preLaunchDelivery(DeliveryPreLaunchRequest request);

	@MethodDoc(
			displayName = "发起三方配送",
			description = "覆盖商家手动发起三方配送，重发三方配送等场景",
			parameters = {
					@ParamDoc(
							name = "request",
							description = "发起三方配送请求",
							type = DeliveryLaunchRequest.class,
							requiredness = Requiredness.REQUIRED
					)
			},
			returnValueDescription = "发起三方配送操作结果",
			restExampleResponseData = "{\"status\":{\"code\":0, \"msg\":\"\"}}",
			example = "暂无"
	)
	@ThriftMethod
	DeliveryLaunchResponse launchDelivery(DeliveryLaunchRequest request);

	@MethodDoc(
			displayName = "平台配送转三方配送",
			description = "平台配送转三方配送",
			parameters = {
					@ParamDoc(
							name = "request",
							description = "平台配送转三方配送请求",
							type = TurnToThirdDeliveryRequest.class,
							requiredness = Requiredness.REQUIRED
					)
			},
			returnValueDescription = "平台配送转三方配送操作结果",
			restExampleResponseData = "{\"status\":{\"code\":0, \"msg\":\"\"}}",
			example = "暂无"
	)
	@ThriftMethod
	TurnToThirdDeliveryResponse turnToThirdDelivery(TurnToThirdDeliveryRequest request);

	@MethodDoc(
			displayName = "平台配送转商家自配送",
			description = "平台配送转商家自配送",
			parameters = {
					@ParamDoc(
							name = "request",
							description = "平台配送转商家自配送请求",
							type = TurnToMerchantSelfDeliveryRequest.class,
							requiredness = Requiredness.REQUIRED
					)
			},
			returnValueDescription = "平台配送转商家自配送送操作结果",
			restExampleResponseData = "{\"status\":{\"code\":0, \"msg\":\"\"}}",
			example = "暂无"
	)
	@ThriftMethod
	TurnToMerchantSelfDeliveryResponse turnToMerchantSelfDelivery(TurnToMerchantSelfDeliveryRequest request);


	@MethodDoc(
			displayName = "歪马三方配送转商家自配送",
			description = "歪马三方配送转商家自配送",
			parameters = {
					@ParamDoc(
							name = "request",
							description = "歪马三方配送转商家自配送请求",
							type = TurnToMerchantSelfDeliveryRequest.class,
							requiredness = Requiredness.REQUIRED
					)
			},
			returnValueDescription = "歪马三方配送转商家自配送操作结果",
			restExampleResponseData = "{\"status\":{\"code\":0, \"msg\":\"\"}}",
			example = "暂无"
	)
	@ThriftMethod
	TurnToMerchantSelfDeliveryResponse turnToMerchantSelfDeliveryForDrunkHorse(DrunkHorseTurnToMerchantSelfDeliveryRequest request);

	@MethodDoc(
			displayName = "取消配送",
			description = "取消配送的场景",
			parameters = {
					@ParamDoc(
							name = "request",
							description = "取消配送请求",
							type = DeliveryCancelRequest.class,
							requiredness = Requiredness.REQUIRED
					)
			},
			returnValueDescription = "取消配送的发起结果",
			restExampleResponseData = "{\"status\":{\"code\":0, \"msg\":\"\"}}",
			example = "暂无"
	)
	@ThriftMethod
	DeliveryCancelResponse cancelDelivery(DeliveryCancelRequest request);

	@MethodDoc(
			displayName = "发送黑盒渠道三方配送",
			description = "发送黑盒渠道配送三方配送，重发三方配送等场景",
			parameters = {
					@ParamDoc(
							name = "request",
							description = "发送黑盒渠道配送三方配送",
							type = DeliveryLaunchRequest.class,
							requiredness = Requiredness.REQUIRED
					)
			},
			returnValueDescription = "发送黑盒渠道配送三方配送操作结果",
			restExampleResponseData = "{\"status\":{\"code\":0, \"msg\":\"\"}}",
			example = "暂无"
	)
	@ThriftMethod
	DeliveryLaunchResponse launchWithOutChannelDelivery(DLaunchWithOutChannelReq request);

	@MethodDoc(
			displayName = "查询订单配送URL",
			description = "查询订单配送URL",
			parameters = {
					@ParamDoc(
							name = "request",
							description = "查询订单配送URL",
							type = DeliveryOrderUrlRequest.class,
							requiredness = Requiredness.REQUIRED
					)
			},
			returnValueDescription = "查询订单配送URL",
			restExampleResponseData = "{\"status\":{\"code\":0, \"msg\":\"\"}}",
			example = "暂无"
	)
	@ThriftMethod
	DeliveryOrderUrlResponse queryOrderDeliveryUrl(DeliveryOrderUrlRequest request);

	@MethodDoc(
			displayName = "转三方配送",
			description = "转三方配送",
			parameters = {
					@ParamDoc(
							name = "request",
							description = "转三方配送请求",
							type = TurnToAggregationDeliveryRequest.class,
							requiredness = Requiredness.REQUIRED
					)
			},
			returnValueDescription = "转三方配送操作结果",
			restExampleResponseData = "{\"status\":{\"code\":0, \"msg\":\"\"}}",
			example = "暂无"
	)
	@ThriftMethod
	TurnToAggregationDeliveryResponse turnToAggregationDelivery(TurnToAggregationDeliveryRequest request);

	@MethodDoc(
			displayName = "手动发起配送",
			description = "手动发起配送场景",
			parameters = {
					@ParamDoc(
							name = "request",
							description = "手动发起配送请求",
							type = DeliveryLaunchRequest.class,
							requiredness = Requiredness.REQUIRED
					)
			},
			returnValueDescription = "手动发起配送结果",
			restExampleResponseData = "{\"status\":{\"code\":0, \"msg\":\"\"}}",
			example = "暂无"
	)
	@ThriftMethod
	DeliveryLaunchResponse manualLaunchDelivery(DeliveryManualLaunchRequest request);

	@MethodDoc(
			displayName = "跑腿锁单结束兜底发聚合配送",
			description = "跑腿锁单结束兜底发聚合配送",
			parameters = {
					@ParamDoc(
							name = "request",
							description = "跑腿锁单结束兜底发聚合配送",
							type = SelfDeliveryCallbackReq.class,
							requiredness = Requiredness.REQUIRED
					)
			},
			returnValueDescription = "跑腿锁单结束兜底发聚合配送",
			restExampleResponseData = "{\"status\":{\"code\":0, \"msg\":\"\"}}",
			example = "暂无"
	)
	@ThriftMethod
	PaoTuiLockStatusNotifyResponse paoTuiLockStatusNotify(PaoTuiLockStatusNotifyRequest req);



	@MethodDoc(
			displayName = "跑腿锁单2.0解锁",
			description = "跑腿锁单2.0解锁",
			parameters = {
					@ParamDoc(
							name = "request",
							description = "跑腿锁单2.0解锁",
							type = SelfDeliveryCallbackReq.class,
							requiredness = Requiredness.REQUIRED
					)
			},
			returnValueDescription = "跑腿锁单2.0解锁",
			restExampleResponseData = "{\"status\":{\"code\":0, \"msg\":\"\"}}",
			example = "暂无"
	)
	@ThriftMethod
	PaoTuiLockStatusNotifyResponse paoTuiLockStatusNotifyV2(PaoTuiLockStatusNotifyV2Request req);


	@MethodDoc(
			displayName = "查询聚合配送订单详情链接",
			description = "查询聚合配送订单详情链接",
			parameters = {
					@ParamDoc(
							name = "request",
							description = "查询聚合配送订单详情链接",
							type = QueryAggOrderDetailLinkRequest.class,
							requiredness = Requiredness.REQUIRED
					)
			},
			returnValueDescription = "查询聚合配送订单详情链接请求",
			restExampleResponseData = "{\"status\":{\"code\":0, \"msg\":\"\"}}",
			example = "暂无"
	)
	@ThriftMethod
	QueryAggLinkResponse queryAggOrderDetailLink(QueryAggOrderDetailLinkRequest request);

	@MethodDoc(
			displayName = "查询聚合平台门店配置链接",
			description = "查询聚合平台门店配置链接",
			parameters = {
					@ParamDoc(
							name = "request",
							description = "查询聚合平台门店配置链接",
							type = QueryAggStoreSettingsLinkRequest.class,
							requiredness = Requiredness.REQUIRED
					)
			},
			returnValueDescription = "查询聚合平台门店配置链接请求",
			restExampleResponseData = "{\"status\":{\"code\":0, \"msg\":\"\"}}",
			example = "暂无"
	)
	@ThriftMethod
	QueryAggLinkResponse queryAggStoreSettingsLink(QueryAggStoreSettingsLinkRequest request);

	@MethodDoc(
			displayName = "查询聚合平台链接凭证",
			description = "查询聚合平台链接凭证",
			parameters = {
					@ParamDoc(
							name = "request",
							description = "查询聚合平台链接凭证",
							type = QueryAggLinkAuthRequest.class,
							requiredness = Requiredness.REQUIRED
					)
			},
			returnValueDescription = "查询聚合平台链接凭证请求",
			restExampleResponseData = "{\"status\":{\"code\":0, \"msg\":\"\"}}",
			example = "暂无"
	)
	@ThriftMethod
	QueryAggLinkAuthResponse queryAggLinkAuthInfo(QueryAggLinkAuthRequest request);
}
