package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: 聚合配送渠道配送节点
 * @Author: zhangjian155
 * @Date: 2022/10/11 15:56
 */
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
@Builder
public class DeliveryChannelLaunchPointDto {
    @FieldDoc(
            description = "渠道名称"
    )
    @ThriftField(1)
    private Integer channelType;

    @FieldDoc(
            description = "配送发单节点"
    )
    @ThriftField(2)
    private Integer deliveryLaunchPoint;

    @FieldDoc(
            description = "立即单自动呼叫骑手延迟时间"
    )
    @ThriftField(3)
    private Integer deliveryLaunchDelayMinutes;

    @FieldDoc(
            description = "预约单自动呼叫骑手延迟时间"
    )
    @ThriftField(4)
    private Integer bookingOrderDeliveryLaunchMinutes;
}
