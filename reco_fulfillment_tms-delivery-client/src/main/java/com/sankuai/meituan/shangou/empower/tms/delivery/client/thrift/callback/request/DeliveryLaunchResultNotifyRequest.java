package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.callback.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * 回调发起配送结果请求体
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/1/7
 */
@TypeDoc(
		description = "回调发起配送结果请求体",
		authors = {
				"hedong07"
		}
)
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
@SuppressWarnings("DuplicatedCode")
public class DeliveryLaunchResultNotifyRequest {

	@FieldDoc(
			description = "闪购赋能订单号",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(1)
	public String orderId;

	@FieldDoc(
			description = "闪购中台门店id",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(2)
	public String storeId;

	@FieldDoc(
			description = "包裹id",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(3)
	public Long pkgId;

	@FieldDoc(
			description = "发单是否成功",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(4)
	public Boolean isLaunchSuccess;

	@FieldDoc(
			description = "发单失败原因code",
			requiredness = Requiredness.OPTIONAL
	)
	@ThriftField(5)
	public Integer launchFailReasonCode;

	@FieldDoc(
			description = "发单失败原因描述",
			requiredness = Requiredness.OPTIONAL
	)
	@ThriftField(6)
	public String launchFailReasonDesc;

	@FieldDoc(
			description = "配送渠道id",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(7)
	public String deliveryChannelId;

	@FieldDoc(
			description = "配送服务包",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(8)
	public String servicePackage;

	@FieldDoc(
			description = "配送距离，单位：米",
			requiredness = Requiredness.OPTIONAL
	)
	@ThriftField(9)
	public Long distance;

	@FieldDoc(
			description = "配送费用，单位：元",
			requiredness = Requiredness.OPTIONAL
	)
	@ThriftField(10)
	public Double deliveryFee;

	public String validate() {
		if (!StringUtils.isNumeric(orderId)) {
			return "闪购赋能订单号非法";
		}

		if (!StringUtils.isNumeric(storeId)) {
			return "闪购中台门店id非法";
		}

		if (pkgId == null) {
			return "包裹id不能为空";
		}

		if (isLaunchSuccess == null) {
			return "发单是否成功不能为空";
		}

		if (!isLaunchSuccess && launchFailReasonCode == null) {
			return "发单失败时，发单失败原因code不能为空";
		}

		if (!isLaunchSuccess && StringUtils.isEmpty(launchFailReasonDesc)) {
			return "发单失败时，发单失败原因描述不能为空";
		}

		if (!StringUtils.isNumeric(deliveryChannelId)) {
			return "配送渠道id非法";
		}

		if (StringUtils.isEmpty(servicePackage)) {
			return "服务包非法";
		}

		return null;
	}
}
