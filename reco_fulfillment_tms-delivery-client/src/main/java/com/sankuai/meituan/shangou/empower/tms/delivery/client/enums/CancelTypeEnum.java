package com.sankuai.meituan.shangou.empower.tms.delivery.client.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/7/13
 */
public enum CancelTypeEnum {
	/**
	 * 主动取消
	 */
	ACTIVE_CANCEL(1),

	/**
	 * 被动取消
	 */
	PASSIVE_CANCEL(2),

	/**
	 * 未知原因取消
	 * 例：聚合运力平台兜底查询三方配送渠道侧，查询到取消状态，此时无法得知取消原因
	 */
	UNKNOWN_CANCEL(3);

	private static final Map<Integer, CancelTypeEnum> CODE_ENUM_MAP = new HashMap<>();
	private final int code;

	static {
		for (CancelTypeEnum each : values()) {
			CODE_ENUM_MAP.put(each.getCode(), each);
		}
	}

	CancelTypeEnum(int code) {
		this.code = code;
	}

	public int getCode() {
		return code;
	}

	public static CancelTypeEnum enumOf(Integer code) {
		return CODE_ENUM_MAP.get(code);
	}
}
