package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.common.Status;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.dto.BatchStoreDeliveryConfigDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


/**
 * <AUTHOR>
 */
@TypeDoc(
		description = "批量查询门店/仓库配送配置返回体"
)
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
public class StoreDeliveryConfigBatchQueryResponse {

	@FieldDoc(
			description = "执行状态",
			requiredness = Requiredness.OPTIONAL
	)
	@ThriftField(1)
	public Status status;

	@FieldDoc(
			description = "返回结果",
			requiredness = Requiredness.OPTIONAL
	)
	@ThriftField(2)
	public List<BatchStoreDeliveryConfigDto> batchStoreDeliveryConfigDtoList;
}
