package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@TypeDoc(
        description = "操作项"
)
@NoArgsConstructor
@AllArgsConstructor
@Data
@ThriftStruct
public class DeliveryOperateItem {

    @FieldDoc(
            description = "可操作列表",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(1)
    public List<Integer> operateItemList = new ArrayList<>();
}
