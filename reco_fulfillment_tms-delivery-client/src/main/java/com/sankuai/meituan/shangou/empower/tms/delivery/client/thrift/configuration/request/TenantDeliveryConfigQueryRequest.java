package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/11/22
 */
@TypeDoc(
		description = "查询租户配送配置请求体",
		authors = {"hedong07"}
)
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
public class TenantDeliveryConfigQueryRequest {

	@FieldDoc(
			description = "租户id",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(1)
	public Long tenantId;

	public String validate() {
		if (tenantId == null || tenantId <= 0L) {
			return "租户ID不合法";
		}

		return null;
	}
}
