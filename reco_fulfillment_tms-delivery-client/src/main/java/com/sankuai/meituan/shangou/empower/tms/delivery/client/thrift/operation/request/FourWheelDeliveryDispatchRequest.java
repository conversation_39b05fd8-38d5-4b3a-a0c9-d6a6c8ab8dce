package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
public class FourWheelDeliveryDispatchRequest {

    @ThriftField(1)
    public Long orderId;

    @ThriftField(2)
    public Integer deliveryType;

    @ThriftField(3)
    public Long tenantId;

    @ThriftField(4)
    public Long accountId;

    @ThriftField(5)
    public String accountName;

    @ThriftField(6)
    public String appId;

    @ThriftField(7)
    public List<FourWheelDeliveryDispatchDto> vehicleDetailList;


}
