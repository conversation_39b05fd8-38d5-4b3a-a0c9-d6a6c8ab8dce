package com.sankuai.meituan.shangou.empower.tms.delivery.client.enums;

/**
 * 外部系统调用配送场景枚举
 */
public enum OuterDeliveryLaunchSceneEnum {

    /**
     * 其他场景
     */
    OTHER(0),
    /**
     * 订单中心场景
     */
    ORDER_CENTER_PLATFORM(1),

    ;
    private final int code;
    OuterDeliveryLaunchSceneEnum(int code){
        this.code=code;
    }

    public int getCode() {
        return code;
    }

    public static OuterDeliveryLaunchSceneEnum toEnum(int code){
        OuterDeliveryLaunchSceneEnum[] data=values();
        for (OuterDeliveryLaunchSceneEnum launchSceneEnum : data){
            if(launchSceneEnum.getCode()==code){
                return launchSceneEnum;
            }
        }
        return OuterDeliveryLaunchSceneEnum.OTHER;
    }

}
