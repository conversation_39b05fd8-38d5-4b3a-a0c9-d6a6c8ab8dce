package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;


@ThriftStruct
@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class DeliveryConfigUpdateTimeRequest {

    @FieldDoc(
            description = "租户ID",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public Long tenantId;

    @FieldDoc(
            description = "门店ID",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(2)
    public Long storeId;

}
