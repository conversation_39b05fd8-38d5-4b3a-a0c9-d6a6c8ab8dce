package com.sankuai.meituan.shangou.empower.tms.delivery.client.utils;

import com.google.common.base.CaseFormat;
import com.google.common.collect.Maps;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.common.MaltFarmSignArg;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;

import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;

/**
 * <AUTHOR>
 * @since 2021/04/06 21:12
 */
@Slf4j
public class MaltFarmSignUtils {

    public static String generateSignature(Map<String, Object> request, String appSecret) {
        TreeMap<String, Object> sortMap = new TreeMap<>(request);
        StringBuilder resultBuilder = new StringBuilder(appSecret);
        Set<Map.Entry<String, Object>> entries = sortMap.entrySet();
        for (Map.Entry<String, Object> entry : entries) {
            resultBuilder.append(entry.getKey())
                    .append(entry.getValue() != null ? entry.getValue().toString() : StringUtils.EMPTY);
        }
        resultBuilder.append(appSecret);
        return DigestUtils.md5Hex(resultBuilder.toString()).toUpperCase();
    }

    public static String generateSignatureFromRequest(Object request, String appSecret) {
        Map<String, Object> signArgMap = generateSignArgMap(request);
        return generateSignature(signArgMap, appSecret);
    }

    private static Map<String, Object> generateSignArgMap(Object request) {
        Map<String, Object> signMap = Maps.newHashMap();
        Field[] fields = request.getClass().getFields();

        for (Field field : fields) {
            Annotation[] declaredAnnotations = field.getDeclaredAnnotations();
            for (Annotation declaredAnnotation : declaredAnnotations) {
                if (declaredAnnotation instanceof MaltFarmSignArg) {
                    try {
                        boolean originAccessible = field.isAccessible();
                        try {
                            field.setAccessible(true);
                            signMap.put(CaseFormat.LOWER_CAMEL.to(CaseFormat.LOWER_UNDERSCORE, field.getName()), field.get(request));
                        } finally {
                            field.setAccessible(originAccessible);
                        }
                    } catch (Exception e) {
                        log.error("解析麦芽田签名参数异常", e);
                        throw new IllegalArgumentException("解析麦芽田签名参数异常", e);
                    }
                    break;
                }
            }
        }

        return signMap;
    }
}
