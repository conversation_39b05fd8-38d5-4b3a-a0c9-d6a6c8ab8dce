package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.request.OuterDeliveryInfoQueryRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.request.OuterRiderTrackQueryRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.response.OuterQueryDeliveryInfoResponse;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.response.OuterQueryRiderTrackDynamicInfoResponse;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.response.OuterQueryRiderTrackImmutableInfoResponse;

/**
 * <AUTHOR>
 */
@InterfaceDoc(
        displayName = "外部查询配送",
        type = "octo.thrift.annotation",
        scenarios = "外部查询配送",
        description = "外部查询配送"
)
@ThriftService
public interface OuterQueryDeliveryInfoThriftService {

    @MethodDoc(
            displayName = "开放平台查询骑手轨迹静态信息",
            description = "开放平台查询骑手轨迹静态信息",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "开放平台查询骑手轨迹静态信息",
                            type = OuterRiderTrackQueryRequest.class,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            returnValueDescription = "开放平台查询骑手轨迹静态信息",
            restExampleResponseData = "{\"status\":{\"code\":0, \"msg\":\"\"}}",
            example = "暂无"
    )
    @ThriftMethod
    OuterQueryRiderTrackImmutableInfoResponse getDeliveryImmutableInfo(OuterRiderTrackQueryRequest outerRiderTrackQueryRequest);

    @MethodDoc(
            displayName = "开放平台查询骑手轨迹动态信息",
            description = "开放平台查询骑手轨迹动态信息",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "开放平台查询骑手轨迹动态信息",
                            type = OuterRiderTrackQueryRequest.class,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            returnValueDescription = "开放平台查询骑手轨迹动态信息",
            restExampleResponseData = "{\"status\":{\"code\":0, \"msg\":\"\"}}",
            example = "暂无"
    )
    @ThriftMethod
    OuterQueryRiderTrackDynamicInfoResponse getDeliveryDynamicInfo(OuterRiderTrackQueryRequest outerRiderTrackQueryRequest);

    @MethodDoc(
            displayName = "开放平台查询配送信息",
            description = "开放平台查询配送信息",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "开放平台查询配送信息",
                            type = OuterDeliveryInfoQueryRequest.class,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            returnValueDescription = "开放平台查询配送信息",
            restExampleResponseData = "{\"status\":{\"code\":0, \"msg\":\"\"}}",
            example = "暂无"
    )
    @ThriftMethod
    OuterQueryDeliveryInfoResponse queryDeliveryInfo(OuterDeliveryInfoQueryRequest outerDeliveryInfoQueryRequest);
}
