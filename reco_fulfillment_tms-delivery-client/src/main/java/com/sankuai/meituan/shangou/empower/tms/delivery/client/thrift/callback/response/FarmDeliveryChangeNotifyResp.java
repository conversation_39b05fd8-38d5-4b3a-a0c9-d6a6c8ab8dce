package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.callback.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.common.Status;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2021/4/14
 * @email jianglilin02@meituan
 */
@Data
@ThriftStruct
@AllArgsConstructor
@NoArgsConstructor
public class FarmDeliveryChangeNotifyResp {

    @FieldDoc(
            description = "执行状态",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public Status status;

}
