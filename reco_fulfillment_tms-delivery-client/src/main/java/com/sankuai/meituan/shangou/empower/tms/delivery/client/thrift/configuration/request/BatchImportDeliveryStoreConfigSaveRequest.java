package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.ConfigModuleEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.dto.DeliveryStoreConfigDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 */
@TypeDoc(
		description = "批量导入门店配送配置，保存租户配送配置请求体"
)
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
public class BatchImportDeliveryStoreConfigSaveRequest {

	@FieldDoc(
			description = "租户id",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(1)
	public Long tenantId;

	@FieldDoc(
			description = "门店id",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(2)
	public Long storeId;

	@FieldDoc(
			description = "修改模块,1表示外部渠道开通或者修改，2表示配送规则修改",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(3)
	public Integer module;

	@FieldDoc(
			description = "聚合运力配置信息",
			requiredness = Requiredness.OPTIONAL
	)
	@ThriftField(4)
	public List<DeliveryStoreConfigDto> deliveryStoreConfigDtoList;

	@FieldDoc(
			description = "操作员id",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(5)
	private Long operatorId;

	@FieldDoc(
			description = "操作员姓名",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(6)
	private String operatorName;

	public String validate() {
		if (tenantId == null || tenantId <= 0L) {
			return "租户ID不合法";
		}

		if (storeId == null || storeId <= 0L) {
			return "门店ID不合法";
		}

		if (module == null) {
			return "操作模块不能为空";
		}

		if (module != ConfigModuleEnum.PLATFORM.getValue()) {
			return "操作模块当前仅支持传入3";
		}

		if (CollectionUtils.isEmpty(deliveryStoreConfigDtoList)) {
			return "操作内容不能为空";
		}

		return null;
	}
}
