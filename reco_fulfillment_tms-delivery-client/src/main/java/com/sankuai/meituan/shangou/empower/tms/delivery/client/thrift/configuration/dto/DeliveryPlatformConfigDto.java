package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * @Description: 配送平台配置
 * @Author: zhangjian155
 * @Date: 2022/12/6 17:14
 */
@ThriftStruct
@Data
@NoArgsConstructor
public class DeliveryPlatformConfigDto {
    @FieldDoc(
            description = "配送平台code",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(1)
    public Integer platformCode;

    @FieldDoc(
            description = "当前开通状态标识 1-已开通 0-未开通",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(2)
    public Integer openFlag;

    @FieldDoc(
            description = "三方运力平台配送appKey",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(3)
    public String appKey;

    @FieldDoc(
            description = "三方运力平台配SecretKey",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(4)
    public String appSecretKey;

    @FieldDoc(
            description = "三方运力平台配送详情跳转链接",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(5)
    public String redirectUrl = StringUtils.EMPTY;
}
