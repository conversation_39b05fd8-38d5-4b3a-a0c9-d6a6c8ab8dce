package com.sankuai.meituan.shangou.empower.tms.delivery.client.enums;

public enum RiderDeliveryExceptionEnum {

    //联系不上用户
    CUSTOMER_PHONE_NUMBER_IS_EMPTY(1, 1, "联系不上顾客", "客户电话为空号"),
    CUSTOMER_PHONE_NUMBER_IS_WRONG(1, 2, "联系不上顾客", "客户电话有误"),
    CUSTOMER_PHONE_IS_TURN_OFF(1, 3, "联系不上顾客", "客户电话关机"),
    CUSTOMER_DOSE_NOT_ANSWER_THE_PHONE(1, 4, "联系不上顾客", "多次拨通但客户未接听/拒接"),
    CAN_NOT_CONTACT_CUSTOMER_OTHER(1, 5, "联系不上顾客", "其他"),

    //顾客真实地址与系统不符
    ADDRESS_IS_WRONG_500(2, 1, "顾客真实地址与系统不符", "500米及以内"),
    ADDRESS_IS_WRONG_500_1000(2, 2, "顾客真实地址与系统不符", "500米-1公里"),
    ADDRESS_IS_WRONG_1000_1500(2, 3, "顾客真实地址与系统不符", "1公里-1.5公里"),
    ADDRESS_IS_WRONG_MORE_THAN_2000(2, 4, "顾客真实地址与系统不符", "2公里及以上"),
    ADDRESS_IS_WRONG_1500_2000(2, 5, "顾客真实地址与系统不符", "1.5公里-2公里"),


    //顾客修改地址
    CUSTOMER_CHANGE_ADDRESS_500(3, 1, "顾客修改地址", "500米及以内"),
    CUSTOMER_CHANGE_ADDRESS_500_1000(3, 2, "顾客修改地址", "500米-1公里"),
    CUSTOMER_CHANGE_ADDRESS_1000_1500(3, 3, "顾客修改地址", "1公里-1.5公里"),
    CUSTOMER_CHANGE_ADDRESS_MORE_THAN_2000(3, 4, "顾客修改地址", "2公里及以上"),
    CUSTOMER_CHANGE_ADDRESS_1500_2000(3, 5, "顾客修改地址", "1.5公里-2公里"),

    //车辆故障
    ELECTRIC_CAR_OUT_OF_POWER(4, 1, "车辆故障", "电动车没电"),
    ELECTRIC_CAR_FLAT_TIRE_OR_FLAT_AIR(4, 2, "车辆故障", "电动车故障(爆胎、没气等)"),
    VEHICLE_BROKE_DOWN_OTHER(4, 3, "车辆故障", "其他"),


    //骑手发生意外
    RIDER_OCCUR_ACCIDENT(5, 0, "骑手发生意外", ""),

    //商品破损
    PRODUCT_IS_DAMAGE(6, 0, "商品破损", ""),

    //其他
    OTHER(7, 0, "其他", "");

    private int exceptionType;
    private int exceptionSubType;
    private String exceptionDesc;
    private String exceptionSubDesc;

    RiderDeliveryExceptionEnum(int exceptionType, int exceptionSubType, String exceptionDesc, String exceptionSubDesc) {
        this.exceptionType = exceptionType;
        this.exceptionSubType = exceptionSubType;
        this.exceptionDesc = exceptionDesc;
        this.exceptionSubDesc = exceptionSubDesc;
    }

    public int getExceptionType() {
        return exceptionType;
    }

    public int getExceptionSubType() {
        return exceptionSubType;
    }

    public String getExceptionDesc() {
        return exceptionDesc;
    }

    public String getExceptionSubDesc() {
        return exceptionSubDesc;
    }

    public static RiderDeliveryExceptionEnum enumOf(int exceptionType, int exceptionSubType) {
        for (RiderDeliveryExceptionEnum value : values()) {
            if (exceptionType == value.getExceptionType() && exceptionSubType == value.getExceptionSubType()) {
                return value;
            }
        }

        return null;
    }

    public static boolean cannotConnectCustomer(int exceptionType) {
        return exceptionType == 1;
    }
}
