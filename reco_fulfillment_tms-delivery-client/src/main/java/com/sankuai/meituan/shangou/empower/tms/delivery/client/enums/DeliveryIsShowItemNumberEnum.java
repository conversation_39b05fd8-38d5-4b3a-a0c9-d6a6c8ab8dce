package com.sankuai.meituan.shangou.empower.tms.delivery.client.enums;

import java.util.HashMap;
import java.util.Map;


/**
 * 门店商品是否展示货号并替换商品名称
 * <AUTHOR>
 * Date 2023/9/11 10:21 AM
 * Description
 */
public enum DeliveryIsShowItemNumberEnum {

    NO(0),


    YES(1);

    private static final Map<Integer, DeliveryIsShowItemNumberEnum> CODE_ENUM_MAP = new HashMap<>();
    private final int code;

    static {
        for (DeliveryIsShowItemNumberEnum each : values()) {
            CODE_ENUM_MAP.put(each.code, each);
        }
    }

    DeliveryIsShowItemNumberEnum(int code) {
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public static DeliveryIsShowItemNumberEnum enumOf(Integer code) {
        return CODE_ENUM_MAP.get(code);
    }
}
