package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.common.Status;

import java.util.HashMap;
import java.util.Map;

@TypeDoc(
        description = "配送单URL结构体",
        authors = {
                "tangjianpei"
        }
)
@ThriftStruct
public class DeliveryOrderUrlResponse {

        @FieldDoc(
                description = "执行状态",
                requiredness = Requiredness.REQUIRED
        )
        @ThriftField(1)
        public Status status;

        @FieldDoc(
                description = "URL映射",
                requiredness = Requiredness.REQUIRED
        )
        @ThriftField(2)
        public Map<String,String> orderUrlMap=new HashMap<>();

        public Status getStatus() {
                return status;
        }

        public void setStatus(Status status) {
                this.status = status;
        }

        public Map<String, String> getOrderUrlMap() {
                return orderUrlMap;
        }

        public void setOrderUrlMap(Map<String, String> orderUrlMap) {
                this.orderUrlMap = orderUrlMap;
        }

        @Override
        public String toString() {
                return "DeliveryOrderUrlResponse{" +
                        "status=" + status +
                        ", orderUrlMap=" + orderUrlMap +
                        '}';
        }
}
