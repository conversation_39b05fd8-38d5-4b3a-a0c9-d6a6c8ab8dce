package com.sankuai.meituan.shangou.empower.tms.delivery.client.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * 是否可发配送枚举
 * 0-可发配送
 * 1-不可发配送
 * <AUTHOR>
 * @date 2025-03-20
 */
@Getter
public enum DeliveryAvailableEnum {
     YES(0),
     NO(1);

    private final int value;

    DeliveryAvailableEnum(int value) {
        this.value = value;
    }

    public static DeliveryAvailableEnum findOf(Integer value) {
        for (DeliveryAvailableEnum deliveryAvailableEnum : DeliveryAvailableEnum.values()) {
            if (deliveryAvailableEnum.getValue() == value) {
                return deliveryAvailableEnum;
            }
        }
        return null;
    }
}
