package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.common.Status;

/**
 * 发起三方配送响应体
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/10/22
 */
@TypeDoc(
		description = "发起三方配送响应体",
		authors = {
				"hedong07"
		}
)
@ThriftStruct
public class DeliveryLaunchResponse {

	@FieldDoc(
			description = "执行状态",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(1)
	public Status status;

	public DeliveryLaunchResponse() {
	}

	public DeliveryLaunchResponse(Status status) {
		this.status = status;
	}

	public Status getStatus() {
		return status;
	}

	public void setStatus(Status status) {
		this.status = status;
	}

	@Override
	public String toString() {
		return "DeliveryLaunchResponse{" +
				"status=" + status +
				'}';
	}
}
