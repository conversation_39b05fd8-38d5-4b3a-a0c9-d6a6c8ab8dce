package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
public class OFCDeliveryCancelRequest {

    @ThriftField(1)
    public Long tenantId;

    @ThriftField(2)
    public Long warehouseId;

    @ThriftField(3)
    public Long orderId;

    @ThriftField(4)
    public Long fulfillmentOrderId;

}
