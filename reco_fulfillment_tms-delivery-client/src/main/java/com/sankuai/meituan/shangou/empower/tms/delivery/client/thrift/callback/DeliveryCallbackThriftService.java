package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.callback;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.callback.request.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.callback.response.*;

/**
 * 配送回调服务
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/1/7
 */
@InterfaceDoc(
		displayName = "配送回调服务",
		type = "octo.thrift.annotation",
		scenarios = "配送回调服务，提供异步发起结果回调，异步取消结果回调，配送进度变更回调等能力",
		description = "配送回调服务，提供异步发起结果回调，异步取消结果回调，配送进度变更回调等能力",
		authors = {
				"hedong07"
		}
)
@ThriftService
public interface DeliveryCallbackThriftService {

	@MethodDoc(
			displayName = "聚合运力异常通知",
			description = "聚合运力异常通知",
			parameters = {
					@ParamDoc(
							name = "request",
							description = "配送变更回调请求",
							type = DeliveryChangeNotifyRequest.class,
							requiredness = Requiredness.REQUIRED
					)
			},
			returnValueDescription = "聚合运力异常通知",
			restExampleResponseData = "{\"status\":{\"code\":0, \"msg\":\"\"}}",
			example = "暂无"
	)
	@ThriftMethod
	AggDeliveryFailCallbackResponse notifyAggDeliveryFailResult(AggDeliveryFailReasonNotifyRequest request);

	@MethodDoc(
			displayName = "麦芽田状态变更通知",
			description = "麦芽田状态变更通知",
			parameters = {
					@ParamDoc(
							name = "request",
							description = "配送变更回调请求",
							type = DeliveryChangeNotifyRequest.class,
							requiredness = Requiredness.REQUIRED
					)
			},
			returnValueDescription = "聚合运力异常通知",
			restExampleResponseData = "{\"status\":{\"code\":0, \"msg\":\"\"}}",
			example = "暂无"
	)
	@ThriftMethod
	FarmDeliveryChangeNotifyResp farmNotifyDeliveryChange(FarmDeliveryChangeNotifyReq req);

	@MethodDoc(
			displayName = "自配送变更通知",
			description = "自配送变更通知",
			parameters = {
					@ParamDoc(
							name = "request",
							description = "自配送变更回调请求",
							type = SelfDeliveryCallbackReq.class,
							requiredness = Requiredness.REQUIRED
					)
			},
			returnValueDescription = "自配送变更通知",
			restExampleResponseData = "{\"status\":{\"code\":0, \"msg\":\"\"}}",
			example = "暂无"
	)
	@ThriftMethod
	SelfDeliveryCallbackResp selfDeliveryCallback(SelfDeliveryCallbackReq req);

	@MethodDoc(
			displayName = "饿了么切换自配送变更通知",
			description = "饿了么切换自配送变更通知",
			parameters = {
					@ParamDoc(
							name = "request",
							description = "自配送变更回调请求",
							type = SelfDeliveryCallbackReq.class,
							requiredness = Requiredness.REQUIRED
					)
			},
			returnValueDescription = "自配送变更通知",
			restExampleResponseData = "{\"status\":{\"code\":0, \"msg\":\"\"}}",
			example = "暂无"
	)
	@ThriftMethod
	ElmSwitchSelfDeliveryCallbackResp elmSwitchSelfDeliveryCallback(ElmSwitchSelfDeliveryCallbackReq req);
}
