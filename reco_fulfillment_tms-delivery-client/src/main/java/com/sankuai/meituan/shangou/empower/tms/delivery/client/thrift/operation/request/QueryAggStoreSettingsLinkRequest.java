package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @date 2025-01-09
 */
@TypeDoc(
        description = "查询聚合配送门店设置链接请求"
)
@Data
@ThriftStruct
public class QueryAggStoreSettingsLinkRequest extends QueryAggLinkBaseRequest{

    @FieldDoc(
            description = "配送平台",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(6)
    private Integer platformCode;

    @FieldDoc(
            description = "eToken",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(7)
    private String eToken;
}
