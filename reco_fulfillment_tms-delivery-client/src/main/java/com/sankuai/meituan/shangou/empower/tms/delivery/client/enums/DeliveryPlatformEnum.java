package com.sankuai.meituan.shangou.empower.tms.delivery.client.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * 配送平台枚举：
 * 0、自建配送平台(已对接海葵、蜂鸟，提供顺序发配送机制)
 * 1、聚合运力平台(已对接达达、顺丰，提供低价优先机制)，到家配送部门提供基础服务，TMS->ULAP->LDP->3PL
 * 2、麦芽田平台(对接多个配送平台，提供多种配送机制)
 * 3、自营配送(商家自己配送)
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/6/24
 */
@SuppressWarnings("SpellCheckingInspection")
public enum DeliveryPlatformEnum {

	/**
	 * 自建配送平台(已对接海葵、蜂鸟，提供顺序发配送机制)
	 */
	SELF_BUILT_DELIVERY_PLATFORM(0, "自建配送平台"),

	/**
	 * 聚合运力平台(已对接达达、顺丰，提供低价优先机制)，到家配送部门提供基础服务，TMS->ULAP->LDP->3PL
	 */
	AGGREGATION_DELIVERY_PLATFORM(1, "聚合运力平台"),

	/**
	 * 麦芽田平台(对接多个配送平台，提供多种配送机制)
	 */
	MALT_FARM_DELIVERY_PLATFORM(2, "麦芽田平台"),

	/**
	 * 自营配送(商家自己配送，牵牛花提供骑手端基础功能)
	 */
	MERCHANT_SELF_DELIVERY(3, "商家自配送"),

	/**
	 * 青云聚信平台(对接多个配送平台，提供多种配送机制)
	 */
	DAP_DELIVERY_PLATFORM(4, "青云聚信平台"),

	/**
	 * 平台配送（三方渠道自己的配送平台）
	 */
	ORDER_CHANNEL_DELIVERY_PLATFORM(5, "平台配送"),
	/**
	 * 订单自配送(三方平台自配送)
	 */
	OTHER_SELF_DELIVERY_PLATFORM(6, "订单自配送")
	;

	private final int code;
	private final String desc;
	private static final Map<Integer, DeliveryPlatformEnum> CODE_ENUM_MAP = new HashMap<>();

	static {
		for (DeliveryPlatformEnum each : values()) {
			CODE_ENUM_MAP.put(each.code, each);
		}
	}

	DeliveryPlatformEnum(int code, String desc) {
		this.code = code;
		this.desc = desc;
	}

	public int getCode() {
		return code;
	}

	public String getDesc() {
		return desc;
	}

	public static DeliveryPlatformEnum enumOf(Integer code) {
		return CODE_ENUM_MAP.get(code);
	}
}
