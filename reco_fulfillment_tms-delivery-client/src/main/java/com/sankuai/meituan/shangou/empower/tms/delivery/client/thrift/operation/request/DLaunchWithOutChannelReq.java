package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 发起三方配送请求体
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/04/15
 */
@TypeDoc(
		description = "发起三方配送请求体",
		authors = {
				"hedong07"
		}
)
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
@SuppressWarnings("DuplicatedCode")
public class DLaunchWithOutChannelReq {

	@FieldDoc(
			description = "租户id",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(1)
	public Long tenantId;

	@FieldDoc(
			description = "门店id",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(2)
	public Long storeId;

	@FieldDoc(
			description = "赋能统一订单号",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(3)
	public Long orderId;

	@FieldDoc(
			description = "操作人id",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(4)
	public Long operatorId;

	public String validate() {
		if (tenantId == null || tenantId <= 0L) {
			return "租户id不合法";
		}

		if (storeId == null || storeId <= 0L) {
			return "门店id不合法";
		}

		if (orderId == null || orderId <= 0L) {
			return "赋能订单id不合法";
		}

		if (operatorId == null || operatorId <= 0L) {
			return "操作人id不合法";
		}

		return null;
	}
}
