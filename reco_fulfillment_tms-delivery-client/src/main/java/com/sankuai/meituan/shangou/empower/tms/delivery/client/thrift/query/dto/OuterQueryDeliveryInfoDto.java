package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 开放平台查询骑手轨迹动态信息
 * <AUTHOR>
 */
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
public class OuterQueryDeliveryInfoDto {

    public static final OuterQueryDeliveryInfoDto EMPTY = new OuterQueryDeliveryInfoDto();

    @FieldDoc(
            description = "赋能订单号",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(1)
    public Long orderId;

    @FieldDoc(
            description = "配送状态code",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(2)
    public Integer deliveryStatusCode;

    @FieldDoc(
            description = "承运商code",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(3)
    public Integer deliveryChannelCode;

    @FieldDoc(
            description = "承运商名称",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(4)
    public String deliveryChannelDesc;

    @FieldDoc(
            description = "订单配送距离（单位米）",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(5)
    public Long deliveryDistance;

    @FieldDoc(
            description = "订单配送费（单位分）",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(6)
    public Integer deliveryFee;

    @FieldDoc(
            description = "骑手信息",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(7)
    public RiderInfoDto riderInfoDto;

    @FieldDoc(
            description = "骑手实际到店时间戳",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(8)
    public Long riderArrivedStoreTime;

    @FieldDoc(
            description = "骑手实际取货时间戳",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(9)
    public Long riderTakeGoodsTime;

    @FieldDoc(
            description = "配送取消原因code",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(10)
    public Integer cancelReasonCode;

    @FieldDoc(
            description = "配送取消原因描述",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(11)
    public String cancelReasonDesc;

}
