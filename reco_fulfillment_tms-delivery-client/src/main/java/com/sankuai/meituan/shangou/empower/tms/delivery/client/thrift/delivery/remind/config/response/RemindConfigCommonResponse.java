package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.delivery.remind.config.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.common.Status;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 配送提醒配置通用响应体.
 *
 * <AUTHOR>
 * @since 2021/10/8 16:26
 */
@TypeDoc(
        description = "配送提醒配置通用响应体",
        authors = {
                "liyang176"
        }
)
@ThriftStruct
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RemindConfigCommonResponse {

    @FieldDoc(
            description = "执行状态",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public Status status;
}
