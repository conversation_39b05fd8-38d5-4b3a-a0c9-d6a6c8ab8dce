package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.common.Status;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.TDeliveryRedirectDetail;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/1/5
 */
@TypeDoc(
        description = "查询聚合配送跳转信息响应体",
        authors = {
                "qianteng"
        }
)
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
public class QueryAggDeliveryRedirectModuleResponse {

    @FieldDoc(
            description = "执行状态",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public Status status;

    @FieldDoc(
            description = "配送信息",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(2)
    public List<TDeliveryRedirectDetail> tDeliveryRedirectDetails;

    public static QueryAggDeliveryRedirectModuleResponse emptyResponse() {
        return new QueryAggDeliveryRedirectModuleResponse(Status.SUCCESS, new ArrayList<TDeliveryRedirectDetail>());
    }
}
