package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/11/20
 */
@TypeDoc(
		description = "订单渠道配送范围同步请求体",
		authors = {
				"hedong07"
		}
)
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
public class ChannelDeliveryRangeSyncRequest {

	@FieldDoc(
			description = "订单渠道id",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(1)
	public Integer channelId;

	@FieldDoc(
			description = "最小订单金额，起送价",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(2)
	public Integer minOrderPrice;

	@FieldDoc(
			description = "配送费",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(3)
	public Integer deliveryFee;

	@FieldDoc(
			description = "配送时长",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(4)
	public Integer deliveryTime;

	public String validate() {
		if (channelId == null) {
			return "未知渠道编码";
		}

		switch (channelId) {
			case 100:
				if (minOrderPrice < 0) {
					return "美团渠道最小订单金额不合法";
				}
				break;

			default:
				return "暂不支持渠道编码：" + channelId;
		}

		return null;
	}
}
