package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.common.Status;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * 预发三方配送响应体
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/1/4
 */
@TypeDoc(
		description = "预发三方配送响应体",
		authors = {
				"hedong07"
		}
)
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
public class DeliveryPreLaunchResponse {

	@FieldDoc(
			description = "执行状态",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(1)
	public Status status;

	@FieldDoc(
			description = "配送渠道列表",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(2)
	public List<DeliveryChannelPreLaunchResponse> deliveryChannelList;

	public DeliveryPreLaunchResponse(Status status) {
		this.status = status;
		this.deliveryChannelList = new ArrayList<>();
	}

	public DeliveryPreLaunchResponse(List<DeliveryChannelPreLaunchResponse> deliveryChannelList) {
		this.status = Status.SUCCESS;
		this.deliveryChannelList = deliveryChannelList;
	}
}
