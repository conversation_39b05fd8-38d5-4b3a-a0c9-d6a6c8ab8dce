package com.sankuai.meituan.shangou.empower.tms.delivery.client.enums;

import org.apache.commons.lang3.ObjectUtils;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021/4/15
 * @email jianglilin02@meituan
 */
public enum FarmDeliveryChannelEnum {

    /**
     * 未知配送渠道
     * 场景：自动发单前，无法得知配送渠道
     */
    /**
     * 未知配送渠道
     */
    MALT_FARM(-2, "麦芽田配送"),

    FARM_DELIVERY_MEITUAN(20000, "美团配送"),

    FARM_DELIVERY_SHUNFENG(20001, "顺丰同城"),

    FARM_DELIVERY_DADA(20002, "达达快送"),

    FARM_DELIVERY_SHANSONG(20003, "闪送"),

    FARM_DELIVERY_UU(20004, "UU跑腿"),

    FARM_DELIVERY_DIANWODA(20005, "点我达"),

    FARM_DELIVERY_FENGNIAO(20006, "蜂鸟跑腿"),

    FARM_DELIVERY_FENGNIAO_KA(20023, "蜂鸟配送"),

    FARM_DELIVERY_PAOTUI(20024, "美团跑腿"),

    FARM_DELIVERY_UNKNOW(29999, "麦芽田-未知配送商"),
    ;

    private final int code;
    private final String desc;


    FarmDeliveryChannelEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static FarmDeliveryChannelEnum enumOf(int value) {
        for (FarmDeliveryChannelEnum each : values()) {
            if (each.getCode() == value) {
                return each;
            }
        }

        return null;
    }

    public static boolean isFarmDeliveryChannel(int deliveryChannelId) {
        return ObjectUtils.allNotNull(enumOf(deliveryChannelId));
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
