package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Builder
@Data
public class TDeliveryRedirectModule {

    @FieldDoc(
            description = "提示文案",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(1)
    private String title;

    @FieldDoc(
            description = "链接文案",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(2)
    private String urlText;

    @FieldDoc(
            description = "跳转url",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(3)
    private String url;
}
