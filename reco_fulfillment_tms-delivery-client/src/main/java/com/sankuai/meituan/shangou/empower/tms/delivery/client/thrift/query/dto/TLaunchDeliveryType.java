package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/1/7
 */
@TypeDoc(
        description = "配送可发起的操作类型",
        authors = {
                "钱腾"
        }
)
@Data
@ThriftStruct
public class TLaunchDeliveryType {

    @FieldDoc(
            description = "是否可重发配送",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public boolean canRetryLaunch;

    @FieldDoc(
            description = "是否可商家自行配送",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(2)
    public boolean canSelfDelivery;

    @FieldDoc(
            description = "是否可手动发三方配送",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(3)
    public boolean canManualLaunchThirdPart;

    @FieldDoc(
            description = "是否可异常转三方配送",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(4)
    public boolean canLaunchThirdPartWhenException;

    @FieldDoc(
            description = "是否可以取消配送",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(5)
    public boolean canCancel;

    @FieldDoc(
            description = "是否可以重发麦芽田配送",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(6)
    public boolean canRetryLaunchByMaltfarm;

    @FieldDoc(
            description = "是否可以重发麦芽田配送",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(7)
    public boolean canRetryLaunchByHaiKui;

    @FieldDoc(
            description = "是否可以重发青云聚信平台",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(8)
    public boolean canRetryLaunchByDap;
}
