package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryLaunchTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2021/3/4
 * @email jianglilin02@meituan
 */
@TypeDoc(
        description = "门店配送能力",
        authors = {
                "jianglilin02"
        }
)
@NoArgsConstructor
@AllArgsConstructor
@Data
@ThriftStruct
public class TDeliveryAbility {

    @FieldDoc(
            description = "是否有聚合运力",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(1)
    public Boolean hasAggrDeliveryChannel;
    @FieldDoc(
            description = "是否有三方运力",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(2)
    public Boolean hasThirdPartDeliveryChannel;
    @FieldDoc(
            description = "发配送类型，见deliveryLaunchTypeEnum",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(3)
    public Integer deliveryLaunchType;

    /**
     * 既没有对接老得三方运力，也没有对接聚合运力平台
     *
     * @return
     */
    public boolean hasNoOnlineDeliveryAbility() {
        return !hasAggrDeliveryChannel && !hasThirdPartDeliveryChannel;
    }

    public boolean canManualLaunchDelivery() {
        return hasAggrDeliveryChannel && DeliveryLaunchTypeEnum.MANUAL_LAUNCH_DELIVERY.equals(deliveryLaunchType);
    }
}
