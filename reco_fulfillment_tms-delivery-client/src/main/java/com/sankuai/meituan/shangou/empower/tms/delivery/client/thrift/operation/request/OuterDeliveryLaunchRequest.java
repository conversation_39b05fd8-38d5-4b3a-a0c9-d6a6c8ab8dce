package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.OuterDeliveryLaunchSceneEnum;

@TypeDoc(
        description = "发起三方配送请求体"
)
@ThriftStruct
public class OuterDeliveryLaunchRequest {

    @FieldDoc(
            description = "渠道门店id",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public long channelStoreId;

    @FieldDoc(
            description = "渠道订单号",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(2)
    public String channelOrderId;

    /**
     * @see OuterDeliveryLaunchSceneEnum
     */
    @FieldDoc(
            description = "外部调用场景枚举值",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(3)
    public int sceneEnumCode;

    public OuterDeliveryLaunchRequest() {
    }

    public OuterDeliveryLaunchRequest(long channelStoreId, String channelOrderId, int sceneEnumCode) {
        this.channelStoreId = channelStoreId;
        this.channelOrderId = channelOrderId;
        this.sceneEnumCode = sceneEnumCode;
    }

    public long getChannelStoreId() {
        return channelStoreId;
    }

    public void setChannelStoreId(long channelStoreId) {
        this.channelStoreId = channelStoreId;
    }

    public String getChannelOrderId() {
        return channelOrderId;
    }

    public void setChannelOrderId(String channelOrderId) {
        this.channelOrderId = channelOrderId;
    }

    public int getSceneEnumCode() {
        return sceneEnumCode;
    }

    public void setSceneEnumCode(int sceneEnumCode) {
        this.sceneEnumCode = sceneEnumCode;
    }

    @Override
    public String toString() {
        return "OuterDeliveryLaunchRequest{" +
                "channelStoreId=" + channelStoreId +
                ", channelOrderId='" + channelOrderId + '\'' +
                ", sceneEnumCode=" + sceneEnumCode +
                '}';
    }
}
