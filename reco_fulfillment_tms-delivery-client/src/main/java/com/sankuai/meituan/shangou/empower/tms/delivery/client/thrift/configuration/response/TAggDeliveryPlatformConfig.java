package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 聚合运力平台配置
 *
 * <AUTHOR>
 * @since 2021/04/04
 */
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
public class TAggDeliveryPlatformConfig {

    @FieldDoc(
            description = "平台code",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public Integer platformCode;

    @FieldDoc(
            description = "当前开通状态标识 1-已开通 2-未开通",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(2)
    public Integer openFlag;

    @FieldDoc(
            description = "三方运力平台配置跳转链接",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(3)
    public String configUrl;

    @FieldDoc(
            description = "三方运力平台配送详情跳转链接",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(4)
    public String redirectUrl;

    @FieldDoc(
            description = "三方运力平台配送appKey",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(5)
    public String appKey;

    @FieldDoc(
            description = "三方运力平台配SecretKey",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(6)
    public String appSecretKey;

    @FieldDoc(
            description = "渠道类型 美团 100，饿了么 200",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(7)
    public Integer channelType;



}
