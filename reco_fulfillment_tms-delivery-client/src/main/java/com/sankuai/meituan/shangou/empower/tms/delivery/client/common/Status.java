package com.sankuai.meituan.shangou.empower.tms.delivery.client.common;

import com.facebook.swift.codec.ThriftConstructor;
import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.FailureCodeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.ResponseCodeEnum;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/10/22
 */
@TypeDoc(
		description = "通用请求执行状态",
		authors = {
				"hedong07"
		}
)
@ThriftStruct
public class Status {

	public static final Status SUCCESS = new Status(ResponseCodeEnum.SUCCESS.getValue(), StringUtils.EMPTY);

	@FieldDoc(
			description = "状态码",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(1)
	public int code;

	@FieldDoc(
			description = "失败详情",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(2)
	public String msg;

	public Status() {
	}

	@ThriftConstructor
	public Status(int code, String msg) {
		this.code = code;
		this.msg = msg;
	}

	public int getCode() {
		return code;
	}

	public void setCode(int code) {
		this.code = code;
	}

	public String getMsg() {
		return msg;
	}

	public void setMsg(String msg) {
		this.msg = msg;
	}

	@Override
	public String toString() {
		return "Status{" +
				"code=" + code +
				", msg='" + msg + '\'' +
				'}';
	}

	public static Status from(FailureCodeEnum code) {
		Objects.requireNonNull(code, "code must not be null");
		return new Status(code.getCode(), code.getMessage());
	}

}
