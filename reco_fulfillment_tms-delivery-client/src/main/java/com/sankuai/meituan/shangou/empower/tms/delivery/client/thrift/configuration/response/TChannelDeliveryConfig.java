package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/11/22
 */
@TypeDoc(
		description = "渠道配送配置信息",
		authors = {"hedong07"}
)
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
public class TChannelDeliveryConfig {

	@FieldDoc(
			description = "配送渠道id",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(1)
	public Integer deliveryChannelId;

	@FieldDoc(
			description = "配送渠道名称",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(2)
	public String deliveryChannelName;

	@FieldDoc(
			description = "appKey",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(3)
	public String appKey;

	@FieldDoc(
			description = "secret",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(4)
	public String secret;

	@FieldDoc(
			description = "渠道状态",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(5)
	public Integer status;
}
