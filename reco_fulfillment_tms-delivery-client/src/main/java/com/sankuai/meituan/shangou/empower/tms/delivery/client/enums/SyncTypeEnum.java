package com.sankuai.meituan.shangou.empower.tms.delivery.client.enums;

/**
 * <AUTHOR>
 * @email LIUYON<PERSON><PERSON><PERSON>@meituan.com
 * @date 2021/1/7
 */
public enum SyncTypeEnum {
	/**
	 * 开通
	 */
	CREATE(10),

	/**
	 * 修改
	 */
	MODIFY(20),

	/**
	 * 删除（解绑）
	 */
	DELETE(30);

	private final int value;

	private SyncTypeEnum(int value) {
		this.value = value;
	}

	public int getValue() {
		return value;
	}

	public static SyncTypeEnum findByValue(int value) {
		for (SyncTypeEnum each : values()) {
			if (each.getValue() == value) {
				return each;
			}
		}

		return null;
	}
}
