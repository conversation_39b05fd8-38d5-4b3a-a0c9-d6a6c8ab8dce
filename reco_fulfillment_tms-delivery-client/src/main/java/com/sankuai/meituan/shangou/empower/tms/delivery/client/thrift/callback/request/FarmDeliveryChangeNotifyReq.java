package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.callback.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.common.MaltFarmSignArg;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2021/4/14
 * @email jianglilin02@meituan
 */
@TypeDoc(
        description = "麦芽田运力异常回调",
        authors = {
                "zengping02"
        }
)
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
public class FarmDeliveryChangeNotifyReq {
    @FieldDoc(
            description = "订单id"
    )
    @ThriftField(value = 1, requiredness = ThriftField.Requiredness.REQUIRED)
    @MaltFarmSignArg
    public String originId;

    @FieldDoc(
            description = "配送方单号"
    )
    @ThriftField(value = 3, requiredness = ThriftField.Requiredness.REQUIRED)
    @MaltFarmSignArg
    public String originLogisticNo;

    @FieldDoc(
            description = "时间戳"
    )
    @ThriftField(value = 4, requiredness = ThriftField.Requiredness.REQUIRED)
    @MaltFarmSignArg
    public Long timestamp;

    @FieldDoc(
            description = "配送方"
    )
    @ThriftField(value = 5, requiredness = ThriftField.Requiredness.REQUIRED)
    @MaltFarmSignArg
    public String logistic;

    @FieldDoc(
            description = "麦芽田单号"
    )
    @ThriftField(value = 6, requiredness = ThriftField.Requiredness.REQUIRED)
    @MaltFarmSignArg
    public String logisticNo;

    @FieldDoc(
            description = "配送方单号"
    )
    @ThriftField(value = 7, requiredness = ThriftField.Requiredness.REQUIRED)
    @MaltFarmSignArg
    public Integer status;

    @FieldDoc(
            description = "配送方单号"
    )
    @ThriftField(value = 8, requiredness = ThriftField.Requiredness.REQUIRED)
    @MaltFarmSignArg
    public String statusDesc;


    @FieldDoc(
            description = "配送方单号"
    )
    @ThriftField(value = 9, requiredness = ThriftField.Requiredness.REQUIRED)
    @MaltFarmSignArg
    public String riderName;

    @FieldDoc(
            description = "配送方单号"
    )
    @ThriftField(value = 10, requiredness = ThriftField.Requiredness.REQUIRED)
    @MaltFarmSignArg
    public String riderPhone;

    @FieldDoc(
            description = "配送方单号"
    )
    @ThriftField(value = 11, requiredness = ThriftField.Requiredness.REQUIRED)
    @MaltFarmSignArg
    public String content;

    @FieldDoc(
            description = "配送方单号"
    )
    @ThriftField(value = 12, requiredness = ThriftField.Requiredness.REQUIRED)
    @MaltFarmSignArg
    public String amount;

    @FieldDoc(
            description = "配送方单号"
    )
    @ThriftField(value = 13, requiredness = ThriftField.Requiredness.REQUIRED)
    @MaltFarmSignArg
    public String distance;

    @FieldDoc(
            description = "配送方单号"
    )
    @ThriftField(value = 14, requiredness = ThriftField.Requiredness.REQUIRED)
    @MaltFarmSignArg
    public String cancelAmount;

    @FieldDoc(
            description = "配送方单号"
    )
    @ThriftField(value = 15, requiredness = ThriftField.Requiredness.REQUIRED)
    @MaltFarmSignArg
    public String cancelReason;

    @FieldDoc(
            description = "配送方单号"
    )
    @ThriftField(value = 16, requiredness = ThriftField.Requiredness.REQUIRED)
    @MaltFarmSignArg
    public Integer cancelReasonCode;

    @FieldDoc(
            description = "签名"
    )
    @ThriftField(value = 17, requiredness = ThriftField.Requiredness.REQUIRED)
    public String sign;

    @FieldDoc(
            description = "平台code"
    )
    @ThriftField(value = 18, requiredness = ThriftField.Requiredness.REQUIRED)
    public Integer platformCode;

    @FieldDoc(
            description = "失败原因"
    )
    @ThriftField(value = 19, requiredness = ThriftField.Requiredness.REQUIRED)
    @MaltFarmSignArg
    public String failReasonMessage;

    @FieldDoc(
            description = "失败原因"
    )
    @ThriftField(value = 20, requiredness = ThriftField.Requiredness.REQUIRED)
    @MaltFarmSignArg
    public String failReasonCode;

    @FieldDoc(
            description = "配送小费，元"
    )
    @ThriftField(value = 21, requiredness = ThriftField.Requiredness.REQUIRED)
    @MaltFarmSignArg
    public String tipAmount;

    @FieldDoc(
            description = "骑手电话号码加密token"
    )
    @ThriftField(value = 22, requiredness = ThriftField.Requiredness.OPTIONAL)
    @MaltFarmSignArg
    public String riderPhoneToken;

    @FieldDoc(
            description = "订单渠道来源"
    )
    @ThriftField(value = 23, requiredness = ThriftField.Requiredness.OPTIONAL)
    @MaltFarmSignArg
    public Integer orderChannelType;

    @FieldDoc(
            description = "基础配送费"
    )
    @ThriftField(value = 24, requiredness = ThriftField.Requiredness.OPTIONAL)
    public String baseFee;

    @FieldDoc(
            description = "优惠费用"
    )
    @ThriftField(value = 25, requiredness = ThriftField.Requiredness.OPTIONAL)
    public String discountFee;

    @FieldDoc(
            description = "保价费用"
    )
    @ThriftField(value = 26, requiredness = ThriftField.Requiredness.OPTIONAL)
    public String insuredFee;

    @FieldDoc(
            description = "原始物流运单id"
    )
    @ThriftField(value = 27, requiredness = ThriftField.Requiredness.OPTIONAL)
    public String originWaybillNo;
}
