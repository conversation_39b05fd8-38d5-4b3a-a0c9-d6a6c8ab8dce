package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 渠道预发三方配送响应体
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/1/4
 */
@TypeDoc(
		description = "渠道预发三方配送响应体",
		authors = {
				"hedong07"
		}
)
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
public class DeliveryChannelPreLaunchResponse {
	@FieldDoc(
			description = "配送渠道编码",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(1)
	public Integer deliveryChannelId;

	@FieldDoc(
			description = "配送渠道名称",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(2)
	public String deliveryChannelName;

	@FieldDoc(
			description = "服务包编码",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(3)
	public String servicePackage;

	@FieldDoc(
			description = "是否可以发起配送",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(4)
	public Boolean available;

	@FieldDoc(
			description = "预发单失败原因",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(5)
	public String failReason;

	@FieldDoc(
			description = "预估配送费用，单位：元",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(6)
	public Double estimatedDeliveryFee;

	@FieldDoc(
			description = "优惠金额，单位：元"
	)
	@ThriftField(7)
	public Double discountAmount;
}
