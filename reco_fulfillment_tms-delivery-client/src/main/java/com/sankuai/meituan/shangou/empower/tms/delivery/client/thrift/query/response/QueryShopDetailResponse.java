package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.common.Status;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.ShopDetail;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.TDeliveryOrder;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@TypeDoc(
        description = "查询门店信息响应体",
        authors = {
                "yuanyu09"
        }
)
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
@Builder
public class QueryShopDetailResponse {
        @FieldDoc(
                description = "执行状态",
                requiredness = Requiredness.REQUIRED
        )
        @ThriftField(1)
        public Status status;

        @FieldDoc(
                description = "门店信息",
                requiredness = Requiredness.REQUIRED
        )
        @ThriftField(2)
        public ShopDetail shopDetails;
}
