package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.ConfigModuleEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.dto.DeliveryStoreConfigDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/9/3 18:13
 **/
@Data
@ThriftStruct
@AllArgsConstructor
@NoArgsConstructor
public class CreateAggregationDeliveryPoiRequest {

    @FieldDoc(
            description = "租户id",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public Long tenantId;

    @FieldDoc(
            description = "门店id",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(2)
    public Long storeId;

    @FieldDoc(
            description = "配送平台code",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(3)
    public Integer platformCode;

    @FieldDoc(
            description = "操作员id",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(4)
    private Long operatorId;

    @FieldDoc(
            description = "操作员姓名",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(5)
    private String operatorName;



    public String validate() {
        if (tenantId == null || tenantId <= 0L || storeId == null || storeId <= 0L) {
            return "租户门店ID不合法";
        }


        if (platformCode == null || DeliveryPlatformEnum.enumOf(platformCode) == null) {
            return "平台code不合法";
        }

        return null;
    }

}
