package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: 门店配置（渠道维度）
 * @Author: zhangjian155
 * @Date: 2022/12/6 19:07
 */
@ThriftStruct
@Data
@NoArgsConstructor
public class StoreChannelDeliveryConfigDto {

    @FieldDoc(
            description = "渠道名称"
    )
    @ThriftField(1)
    private Integer channelType;

    @FieldDoc(
            description = "配送发单节点"
    )
    @ThriftField(2)
    private Integer deliveryLaunchPoint;

    @FieldDoc(
            description = "配送平台设置"
    )
    @ThriftField(3)
    private DeliveryPlatformConfigDto deliveryPlatformConfig;

    @FieldDoc(
            description = "立即单自动呼叫时间"
    )
    @ThriftField(4)
    private Integer deliveryDelayLaunchMinutes;

    @FieldDoc(
            description = "预约单自动呼叫时间"
    )
    @ThriftField(5)
    private Integer bookingOrderDeliveryLaunchMinutes;
}
