package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.common.Status;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/1/6
 */
@TypeDoc(
		description = "租户配送配置修改响应体",
		authors = {"liuyonggao"}
)
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
public class ConfigCommonResponse {

	@FieldDoc(
			description = "执行状态",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(1)
	public Status status;

	@FieldDoc(
			description = "门店授权信息",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(2)
	public ShopAuthConfig shopAuthConfig;

	public ConfigCommonResponse (Status status) {
		this.status = status;
	}
}
