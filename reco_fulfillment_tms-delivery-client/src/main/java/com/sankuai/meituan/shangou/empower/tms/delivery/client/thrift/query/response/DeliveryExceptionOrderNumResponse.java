package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.response;

import java.util.ArrayList;
import java.util.List;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.common.Status;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.TOrderIdentifier;

import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

/**
 * 配送异常订单数量 Response.
 *
 * <AUTHOR>
 * @since 2021/4/19 10:58
 */
@TypeDoc(
        description = "配送异常订单数量 Response",
        authors = {
                "liyang176"
        }
)
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
public class DeliveryExceptionOrderNumResponse {

    @FieldDoc(
            description = "响应状态",
            requiredness = Requiredness.REQUIRED
    )
    private Status status = Status.SUCCESS;

    @FieldDoc(
            description = "配送异常订单数量"
    )
    private Integer orderNum = 0;

    @ThriftField(1)
    public Status getStatus() {
        return status;
    }

    @ThriftField
    public void setStatus(Status status) {
        this.status = status;
    }

    @ThriftField(2)
    public Integer getOrderNum() {
        return orderNum;
    }

    @ThriftField
    public void setOrderNum(Integer orderNum) {
        this.orderNum = orderNum;
    }
}
