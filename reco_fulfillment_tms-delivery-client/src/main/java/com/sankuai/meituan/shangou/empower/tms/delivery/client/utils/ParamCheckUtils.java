package com.sankuai.meituan.shangou.empower.tms.delivery.client.utils;

import lombok.extern.slf4j.Slf4j;

/**
 * 参数校验工具
 * <AUTHOR>
 */
@Slf4j
public class ParamCheckUtils {

    /**
     * 校验门店配送配置里延迟呼叫时间参数，返回true代表通过
    */
    public static boolean checkDeliveryLaunchDelayMinutes(Integer delayMinutes) {
        if (delayMinutes == null) {
            return true;
        }
        return delayMinutes >= 0 && delayMinutes <= 999;
    }

}
