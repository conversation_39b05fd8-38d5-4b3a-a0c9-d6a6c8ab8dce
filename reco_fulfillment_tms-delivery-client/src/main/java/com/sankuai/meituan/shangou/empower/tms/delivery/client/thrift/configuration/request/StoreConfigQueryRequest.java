package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.request;

import java.util.List;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/1/6
 */
@TypeDoc(
		description = "查询租户配送配置请求体",
		authors = {"liuyonggao"}
)
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
public class StoreConfigQueryRequest {

	@FieldDoc(
			description = "租户id",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(1)
	public Long tenantId;

	@FieldDoc(
			description = "门店id",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(2)
	public Long storeId;

	@FieldDoc(
			description = "聚合运力平台code:2麦芽田",
			requiredness = Requiredness.OPTIONAL
	)
	@ThriftField(3)
	public List<Integer> aggPlatformCodes;

	public String validate() {
		if (tenantId == null || tenantId <= 0L || storeId == null || storeId <= 0L) {
			return "租户门店ID不合法";
		}

		return null;
	}
}
