package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.callback.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@TypeDoc(
        description = "回调青云门店授权成功请求体",
        authors = {
                "yuanyu09"
        }
)
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
public class DapShopAuthNotifyRequest {
    @FieldDoc(
            description = "渠道门店id",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public String storeId;

}
