package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @date 2025-01-10
 */
@TypeDoc(description = "聚合链路授权信息")
@ThriftStruct
@Data
public class AggLinkAuthInfo {
    @FieldDoc(description = "配送订单ID")
    @ThriftField(value = 1, requiredness = ThriftField.Requiredness.OPTIONAL)
    private String deliveryOrderId;

    @FieldDoc(description = "门店/仓ID")
    @ThriftField(value = 2, requiredness = ThriftField.Requiredness.OPTIONAL)
    private Long poiId;

    @FieldDoc(description = "eToken")
    @ThriftField(value = 3, requiredness = ThriftField.Requiredness.OPTIONAL)
    private String eToken;

    @FieldDoc(description = "操作人ID")
    @ThriftField(value = 4, requiredness = ThriftField.Requiredness.OPTIONAL)
    private String operatorId;

    @FieldDoc(description = "操作人名称")
    @ThriftField(value = 5, requiredness = ThriftField.Requiredness.OPTIONAL)
    private String operatorName;

    @Override
    public String toString() {
        return JsonUtil.toJson(this);
    }
}
