package com.sankuai.meituan.shangou.empower.tms.delivery.client.enums;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/1/7
 */
public enum CancelMarkEnum {
	/**
	 * 默认
	 */
	DEFAULT(0),
	/**
	 * 取消中
	 */
	CANCELING(1);

	private final int value;

	private CancelMarkEnum(int value) {
		this.value = value;
	}

	public int getValue() {
		return value;
	}

	public static CancelMarkEnum findByValue(int value) {
		for (CancelMarkEnum each : values()) {
			if (each.getValue() == value) {
				return each;
			}
		}

		return null;
	}
}
