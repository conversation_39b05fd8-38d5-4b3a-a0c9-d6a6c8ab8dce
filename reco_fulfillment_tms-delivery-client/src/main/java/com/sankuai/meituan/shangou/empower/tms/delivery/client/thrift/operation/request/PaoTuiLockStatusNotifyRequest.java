package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@TypeDoc(
        description = "跑腿锁单结束通知请求体",
        authors = {
                "yuanyu09"
        }
)
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
public class PaoTuiLockStatusNotifyRequest {
    @FieldDoc(
            description = "渠道订单id",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public String channelOrderId;

}
