package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.common.Status;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.OriginWaybillDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024-08-08
 */
@TypeDoc(
        description = "批量查询订单原始配送单号响应体",
        authors = {
                "puxiaokang"
        }
)
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
public class BatchQueryOriginWaybillNoResponse {
    @FieldDoc(
            description = "执行状态",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public Status status = Status.SUCCESS;

    @FieldDoc(
            description = "订单原始运单列表: Key: orderId",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(2)
    public Map<Long, List<OriginWaybillDto>> resultMap;

    public BatchQueryOriginWaybillNoResponse(Map<Long, List<OriginWaybillDto>> resultMap) {
        this.resultMap = resultMap;
    }
}
