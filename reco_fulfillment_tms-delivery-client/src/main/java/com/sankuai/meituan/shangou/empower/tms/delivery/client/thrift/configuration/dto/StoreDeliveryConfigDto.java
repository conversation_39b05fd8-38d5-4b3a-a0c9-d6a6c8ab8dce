package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collections;
import java.util.List;
/**
 * @Description: 查询门店/仓库的配送设置返回体
 * @Author: zhangjian155
 * @Date: 2022/10/11 15:53
 */
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
@Builder
public class StoreDeliveryConfigDto {

    public static final StoreDeliveryConfigDto EMPTY =
            StoreDeliveryConfigDto.builder().storeChannelDeliveryConfigList(Collections.<StoreChannelDeliveryConfigDto>emptyList()).build();

    @FieldDoc(
            description = "渠道发配送节点配置"
    )
    @ThriftField(1)
    private List<StoreChannelDeliveryConfigDto> storeChannelDeliveryConfigList;
}
