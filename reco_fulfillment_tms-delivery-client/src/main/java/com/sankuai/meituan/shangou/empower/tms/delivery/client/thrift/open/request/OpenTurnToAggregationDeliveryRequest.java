package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.open.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

@TypeDoc(
        description = "平台配送转三方配送请求体"
)
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
public class OpenTurnToAggregationDeliveryRequest {

    @FieldDoc(
            description = "租户id",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public Long tenantId;

    @FieldDoc(
            description = "门店id",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(2)
    public Long poiId;

    @FieldDoc(
            description = "运单号",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(3)
    public String channelOrderId;

    @FieldDoc(
            description = "渠道号",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(4)
    public Integer channelId;

    @FieldDoc(
            description = "操作人id",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(5)
    public Long operatorId;

    @FieldDoc(
            description = "操作人Name",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(6)
    public String operatorName;

    @FieldDoc(
            description = "配送平台id",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(7)
    public Integer deliveryPlatformId;

    public void validate() {
        if (tenantId == null || tenantId <= 0L) {
            throw new IllegalArgumentException("租户id不合法") ;
        }

        if (poiId == null || poiId <= 0L) {
            throw new IllegalArgumentException("门店id不合法");
        }

        if (StringUtils.isBlank(channelOrderId)) {
            throw new IllegalArgumentException("订单id不合法");
        }

        if (deliveryPlatformId == null || deliveryPlatformId <= 0) {
            throw new IllegalArgumentException("配送渠道不合法");
        }

        if (operatorId == null || operatorId <= 0L) {
            throw new IllegalArgumentException("操作人id不合法");
        }
    }

}
