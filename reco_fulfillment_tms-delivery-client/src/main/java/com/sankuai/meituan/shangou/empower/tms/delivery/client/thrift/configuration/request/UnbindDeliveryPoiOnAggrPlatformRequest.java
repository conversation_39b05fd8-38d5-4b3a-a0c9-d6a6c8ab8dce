package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.request;

import org.apache.commons.lang3.StringUtils;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

/**
 * 解绑聚合运力平台上的配送门店 请求.
 *
 * <AUTHOR>
 * @since 2021/3/8 16:38
 */
@TypeDoc(
        description = "解绑聚合运力平台上的配送门店的请求"
)
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
public class UnbindDeliveryPoiOnAggrPlatformRequest {
    @FieldDoc(
            description = "租户id",
            requiredness = Requiredness.REQUIRED
    )
    private Long tenantId;

    @FieldDoc(
            description = "门店ID",
            requiredness = Requiredness.REQUIRED
    )
    private Long storeId;

    @FieldDoc(
            description = "配送商编码",
            requiredness = Requiredness.REQUIRED
    )
    private Integer deliveryChannelId;

    @FieldDoc(
            description = "配送商门店ID",
            requiredness = Requiredness.REQUIRED
    )
    private String deliveryChannelPoiId;

    @ThriftField(1)
    public Long getTenantId() {
        return tenantId;
    }

    @ThriftField
    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    @ThriftField(2)
    public Long getStoreId() {
        return storeId;
    }

    @ThriftField
    public void setStoreId(Long storeId) {
        this.storeId = storeId;
    }

    @ThriftField(3)
    public Integer getDeliveryChannelId() {
        return deliveryChannelId;
    }

    @ThriftField
    public void setDeliveryChannelId(Integer deliveryChannelId) {
        this.deliveryChannelId = deliveryChannelId;
    }

    @ThriftField(4)
    public String getDeliveryChannelPoiId() {
        return deliveryChannelPoiId;
    }

    @ThriftField
    public void setDeliveryChannelPoiId(String deliveryChannelPoiId) {
        this.deliveryChannelPoiId = deliveryChannelPoiId;
    }

    public String validate() {
        if (tenantId == null || tenantId <= 0L || storeId == null || storeId <= 0L) {
            return "租户门店ID不合法";
        }
        if (deliveryChannelId == null) {
            return "配送商编码不合法";
        }
        if (StringUtils.isBlank(deliveryChannelPoiId)) {
            return "配送商门店ID不合法";
        }
        return null;
    }
}
