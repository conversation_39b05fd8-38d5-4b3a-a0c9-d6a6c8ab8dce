package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.common.Status;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.TOrderIdentifier;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description 根据子类型查询异常订单 Response
 * @ClassName DeliveryExceptionOrderSubTypeCountResponse
 * <AUTHOR>
 * @Version 1.0
 * @Date 2022/1/4 2:26 下午
 */
@TypeDoc(
        description = "查询运单信息响应体",
        authors = {
                "goulei02"
        }
)
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
@Builder
public class DeliveryExceptionOrdersBySubTypeResponse {

    @FieldDoc(
            description = "响应状态",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(value = 1, requiredness = ThriftField.Requiredness.REQUIRED)
    public Status status = Status.SUCCESS;

    @FieldDoc(
            description = "订单标识列表"
    )
    @ThriftField(2)
    public List<TOrderIdentifier> orders = new ArrayList<>(0);
}
