package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.google.common.collect.ImmutableSet;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/11/22
 */
@TypeDoc(
		description = "修改渠道配送配置请求体",
		authors = {"hedong07"}
)
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
public class ChannelDeliveryConfigUpdateRequest {

	public static final int ENABLED = 1;
	public static final int DISABLED = 0;
	private static final Set<Integer> VALID_STATUS = ImmutableSet.of(ENABLED, DISABLED);

	@FieldDoc(
			description = "配送渠道id",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(1)
	public Integer deliveryChannelId;

	@FieldDoc(
			description = "appKey",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(2)
	public String appKey;

	@FieldDoc(
			description = "secret",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(3)
	public String secret;

	@FieldDoc(
			description = "状态",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(4)
	public Integer status;

	public String validate() {
		if (deliveryChannelId == null || deliveryChannelId <= 0L) {
			return "配送渠道ID不合法";
		}

		if (status == null || !VALID_STATUS.contains(status)) {
			return "状态不合法";
		}

		if (Objects.equals(status, ENABLED)) {
			if (StringUtils.isBlank(appKey)) {
				return "AppKey信息缺失";
			}

			if (StringUtils.isBlank(secret)) {
				return "Secret信息缺失";
			}
		}

		return null;
	}
}
