package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.common.Status;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.OuterQueryRiderTrackDynamicInfoDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 开放平台查询骑手轨迹动态信息
 * <AUTHOR>
 */
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
@Builder
public class OuterQueryRiderTrackDynamicInfoResponse {

    @FieldDoc(
            description = "执行状态",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public Status status;

    @FieldDoc(
            description = "执行状态",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(2)
    public OuterQueryRiderTrackDynamicInfoDto data;

}
