package com.sankuai.meituan.shangou.empower.tms.delivery.client.utils;


import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.*;

/**
 * <AUTHOR>
 * @description
 * @date 2025-01-10
 */
@Slf4j
public class DapSignUtils {
    /**
     * 加密方法入口
     */
    public static String generateSign(Map<String, Object> params, String secret) {
        if (MapUtils.isEmpty(params)) {
            return "";
        }
        String encodeString = getEncodeString(params, secret);
        return generateSign(encodeString);
    }

    private static String getEncodeString(Map<String, Object> params, String secret) {
        Iterator<String> keyIter = params.keySet().iterator();
        Set<String> sortedParams = new TreeSet<>();
        while (keyIter.hasNext()) {
            sortedParams.add(keyIter.next());
        }
        StringBuilder strB = new StringBuilder(secret);

        // 排除sign和空值参数
        for (String key : sortedParams) {
            if (key.equals("sign")) {
                continue;
            }
            String value = String.valueOf(params.get(key));
            if (value != null && !value.isEmpty()) {
                strB.append(key).append(value);
            }
        }
        return strB.toString();
    }

    private static String generateSign(String content) {
        return Objects.requireNonNull(sha1(content)).toLowerCase();
    }

    /**
     * SHA1 加密
     *
     * @param s 待加密的字符串
     * @return
     */
    private static String sha1(String s) {
        char[] hexDigits = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'};
        try {
            byte[] btInput = s.getBytes(StandardCharsets.UTF_8);
            // 获得 MessageDigest 对象
            MessageDigest mdInst = MessageDigest.getInstance("sha-1");
            // 使用指定的字节更新摘要
            mdInst.update(btInput);
            // 获得密文
            byte[] md = mdInst.digest();
            // 把密文转换成十六进制的字符串形式
            int j = md.length;
            char[] str = new char[j * 2];
            int k = 0;
            for (byte byte0 : md) {
                str[k++] = hexDigits[byte0 >>> 4 & 0xf];
                str[k++] = hexDigits[byte0 & 0xf];
            }
            return new String(str);
        } catch (Exception e) {
            log.info("sha1 error, exception:", e);
        }
        return null;
    }
}
