package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.common.Status;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description 异常订单子类型数量 Response
 * @ClassName DeliveryExceptionOrderSubTypeCountResponse
 * <AUTHOR>
 * @Version 1.0
 * @Date 2022/1/4 2:26 下午
 */
@TypeDoc(
        description = "异常订单子类型数量 Response",
        authors = {
                "goulei02"
        }
)
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
public class DeliveryExceptionOrderSubTypeCountResponse {

    @FieldDoc(
            description = "响应状态",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(value = 1, requiredness = ThriftField.Requiredness.REQUIRED)
    public Status status = Status.SUCCESS;

    @FieldDoc(
            description = "全部数量"
    )
    @ThriftField(value = 2)
    public Integer allSubTypeCount = 0;

    @FieldDoc(
            description = "未接单数量"
    )
    @ThriftField(value = 3)
    public Integer noRiderAcceptCount = 0;

    @FieldDoc(
            description = "未到店数量"
    )
    @ThriftField(value = 4)
    public Integer noArrivalStoreCount = 0;

    @FieldDoc(
            description = "未取货数量"
    )
    @ThriftField(value = 5)
    public Integer noRiderTakeGoodsCount = 0;

    @FieldDoc(
            description = "配送超时数量"
    )
    @ThriftField(value = 6)
    public Integer deliveryTimeoutCount = 0;

    @FieldDoc(
            description = "系统异常数量"
    )
    @ThriftField(value = 7)
    public Integer systemExceptionCount = 0;

    @FieldDoc(
            description = "异常上报数量"
    )
    @ThriftField(value = 8)
    public Integer reportExceptionCount = 0;

    @FieldDoc(
            description = "取货失败数量"
    )
    @ThriftField(value = 9)
    public Integer takeGoodsExceptionCount = 0;
}
