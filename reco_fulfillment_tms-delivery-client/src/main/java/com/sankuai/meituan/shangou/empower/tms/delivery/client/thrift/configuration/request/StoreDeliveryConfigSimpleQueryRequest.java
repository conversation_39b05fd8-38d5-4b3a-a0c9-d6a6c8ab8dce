package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

@TypeDoc(
		description = "批量查询租户下门店配送配置请求体"
)
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
public class StoreDeliveryConfigSimpleQueryRequest {

	@FieldDoc(
			description = "租户id",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(1)
	public Long tenantId;

	@FieldDoc(
			description = "门店id",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(2)
	public List<Long> storeIdList;

	public String validate() {
		if (tenantId == null || tenantId <= 0L) {
			return "租户门店ID不能为空";
		}
		if (CollectionUtils.isEmpty(storeIdList)) {
			return "门店ID不能为空";
		}
		return null;
	}
}
