package com.sankuai.meituan.shangou.empower.tms.delivery.client.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

import java.util.HashMap;
import java.util.Map;

/**
 * 经纬度坐标类型
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/3
 */
public enum CoordinateTypeEnum {

	/**
	 * 未知
	 */
	UNKNOWN(-1),

	/**
	 * 火星坐标系(高德地图/腾讯地图)
	 */
	MARS(0),

	/**
	 * 百度坐标系(百度地图)
	 */
	BAIDU(1);

	private static final Map<Integer, CoordinateTypeEnum> CODE_TO_ENUM_MAP = new HashMap<>();
	private final int code;

	static {
		for (CoordinateTypeEnum each : values()) {
			CODE_TO_ENUM_MAP.put(each.getCode(), each);
		}
	}

	CoordinateTypeEnum(int code) {
		this.code = code;
	}

	@JsonValue
	public int getCode() {
		return code;
	}

	@JsonCreator
	public static CoordinateTypeEnum valueOf(int code) {
		return CODE_TO_ENUM_MAP.get(code);
	}
}
