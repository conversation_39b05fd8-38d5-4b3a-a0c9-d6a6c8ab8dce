package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.common.Status;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 查询配送门店维度配置响应体
 *
 * <AUTHOR>
 * @date 2025/2/26
 */
@TypeDoc(
		description = "查询配送门店维度配置响应体",
		authors = {"jianglilin02"}
)
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
public class DeliveryDimensionPoiQueryResponse {

	@FieldDoc(
			description = "执行状态",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(1)
	private Status status;

	@FieldDoc(
			description = "配送门店维度配置信息",
			requiredness = Requiredness.OPTIONAL
	)
	@ThriftField(2)
	private DeliveryDimensionPoiDto deliveryDimensionPoi;

	/**
	 * 只传状态的构造函数
	 */
	public DeliveryDimensionPoiQueryResponse(Status status) {
		this.status = status;
	}

	/**
	 * 配送门店维度配置DTO
	 */
	@TypeDoc(
			description = "配送门店维度配置传输对象",
			authors = {"jianglilin02"}
	)
	@NoArgsConstructor
	@AllArgsConstructor
	@ThriftStruct
	@Data
	public static class DeliveryDimensionPoiDto {

		@FieldDoc(
				description = "主键ID",
				requiredness = Requiredness.REQUIRED
		)
		@ThriftField(1)
		private Long id;

		@FieldDoc(
				description = "租户ID",
				requiredness = Requiredness.REQUIRED
		)
		@ThriftField(2)
		private Long tenantId;

		@FieldDoc(
				description = "门店ID",
				requiredness = Requiredness.REQUIRED
		)
		@ThriftField(3)
		private Long storeId;

		@FieldDoc(
				description = "自送拣配作业模式：1-拣配分离，2-拣配一体，3-拣配一体支持手动分离",
				requiredness = Requiredness.OPTIONAL
		)
		@ThriftField(4)
		private Integer selfDeliveryMode;

		@FieldDoc(
				description = "跳转导航模式",
				requiredness = Requiredness.OPTIONAL
		)
		@ThriftField(5)
		private Integer internalNavigationMode;

		@FieldDoc(
				description = "剩余时长配置",
				requiredness = Requiredness.OPTIONAL
		)
		@ThriftField(6)
		private String assessTimeConfig;

		@FieldDoc(
				description = "确认送达操作配置",
				requiredness = Requiredness.OPTIONAL
		)
		@ThriftField(7)
		private String deliveryCompleteMode;

		@FieldDoc(
				description = "配送转骑手范围(角色id列表)",
				requiredness = Requiredness.OPTIONAL
		)
		@ThriftField(8)
		private List<Long> riderTransRoles;

		@FieldDoc(
				description = "已送达列表排序模式",
				requiredness = Requiredness.OPTIONAL
		)
		@ThriftField(9)
		private Integer completedSortMode;

		@FieldDoc(
				description = "配送提醒设置",
				requiredness = Requiredness.OPTIONAL
		)
		@ThriftField(10)
		private String deliveryRemindConfig;

		@FieldDoc(
				description = "创建时间",
				requiredness = Requiredness.OPTIONAL
		)
		@ThriftField(11)
		private String createdAt;

		@FieldDoc(
				description = "更新时间",
				requiredness = Requiredness.OPTIONAL
		)
		@ThriftField(12)
		private String updatedAt;
	}
}