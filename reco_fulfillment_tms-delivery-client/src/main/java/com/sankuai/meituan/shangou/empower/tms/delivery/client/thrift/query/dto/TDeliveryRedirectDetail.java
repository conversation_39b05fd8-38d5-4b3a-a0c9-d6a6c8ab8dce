package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
public class TDeliveryRedirectDetail {

    @FieldDoc(
            description = "牵牛花订单ID",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(1)
    private Long orderId;

    @FieldDoc(
            description = "聚合配送跳转信息",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(2)
    private TDeliveryRedirectModule TDeliveryRedirectModule;

}
