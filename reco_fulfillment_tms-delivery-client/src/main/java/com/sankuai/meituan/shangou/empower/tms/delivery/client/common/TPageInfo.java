package com.sankuai.meituan.shangou.empower.tms.delivery.client.common;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 分页信息.
 *
 * <AUTHOR>
 * @since 2023/7/12 19:28
 */
@TypeDoc(
        description = "分页信息.",
        authors = {
                "zhangjian155"
        }
)
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
@ThriftStruct
public class TPageInfo {

    @FieldDoc(
            description = "页码",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public Integer pageNum = 1;

    @FieldDoc(
            description = "分页大小",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(2)
    public Integer pageSize = 20;

    @FieldDoc(
            description = "总条数",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(3)
    public Integer total = 0;
}
