package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.callback.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@TypeDoc(
        description = "转自配送回调"
)
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
public class ElmSwitchSelfDeliveryCallbackReq {

    @FieldDoc(
            description = "渠道订单id"
    )
    @ThriftField(value = 1, requiredness = ThriftField.Requiredness.REQUIRED)
    public String channelOrderId;

    @FieldDoc(
            description = "是否可以切换自配送"
    )
    @ThriftField(value = 2, requiredness = ThriftField.Requiredness.REQUIRED)
    public Boolean canSelfDelivery;

    @FieldDoc(
            description = "是否可以二次呼叫平台配送"
    )
    @ThriftField(value = 3, requiredness = ThriftField.Requiredness.REQUIRED)
    public Boolean canSecondManualCall;
}
