package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description
 * @date 2025-01-13
 */
@TypeDoc(description = "查询聚合链路举证请求")
@ThriftStruct
@NoArgsConstructor
@AllArgsConstructor
@Data
public class QueryAggLinkAuthRequest {

    @FieldDoc(description = "租户ID")
    @ThriftField(value = 1, requiredness = ThriftField.Requiredness.REQUIRED)
    private String code;
}
