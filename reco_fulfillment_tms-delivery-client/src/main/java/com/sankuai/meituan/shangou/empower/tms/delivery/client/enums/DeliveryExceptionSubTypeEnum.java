package com.sankuai.meituan.shangou.empower.tms.delivery.client.enums;

import com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.collect.ImmutableList;
import com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.collect.Lists;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import org.apache.commons.collections.CollectionUtils;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description 配送异常子类型枚举
 * @ClassName DeliveryExceptionSubTypeEnum
 * <AUTHOR>
 * @Version 1.0
 * @Date 2021/12/31 4:58 下午
 */
public enum DeliveryExceptionSubTypeEnum {

    /**
     * 全部
     */
    ALL(0,
            Collections.<Integer>emptyList(),
            Collections.<Integer>emptyList(),
            Lists.newArrayList(DeliveryStatusEnum.INIT, DeliveryStatusEnum.DELIVERY_LAUNCHED
            , DeliveryStatusEnum.WAITING_TO_ASSIGN_RIDER, DeliveryStatusEnum.DELIVERY_REJECTED
            , DeliveryStatusEnum.DELIVERY_FAILED, DeliveryStatusEnum.DELIVERY_CANCELLED
            , DeliveryStatusEnum.RIDER_ASSIGNED, DeliveryStatusEnum.RIDER_ARRIVED_SHOP
            , DeliveryStatusEnum.RIDER_TAKEN_GOODS),
            "全部"),
    /**
     * 未接单
     */
    NO_RIDER_ACCEPT(10,
            ImmutableList.of(
                    DeliveryExceptionTypeEnum.LAUNCH_DELIVERY_FAILED_BY_SYSTEM_EXCEPTION.getCode(),
                    DeliveryExceptionTypeEnum.LAUNCH_DELIVERY_FAILED_BY_MERCHANT.getCode(),
                    DeliveryExceptionTypeEnum.DELIVERY_EXCEPTION_BY_LIMITED_DELIVERY_SERVICE_CAPACITY.getCode(),
                    DeliveryExceptionTypeEnum.DELIVERY_EXCEPTION_BY_RECEIVER.getCode(),
                    DeliveryExceptionTypeEnum.DELIVERY_EXCEPTION_BY_RIDER.getCode(),
                    DeliveryExceptionTypeEnum.DELIVERY_EXCEPTION_BY_SYSTEM.getCode(),
                    DeliveryExceptionTypeEnum.UNKNOWN.getCode()
            ),
            ImmutableList.of(
                    DeliveryExceptionCodeEnum.NO_EXCEPTION.getCode(),
                    DeliveryExceptionCodeEnum.NO_RIDE_ACCEPT.getCode(),
                    DeliveryExceptionCodeEnum.DELIVERY_TIME_OUT.getCode(),
                    DeliveryExceptionCodeEnum.CHANNEL_CANCEL.getCode(),
                    DeliveryExceptionCodeEnum.SEND_DELIVERY_FAIL.getCode(),
                    DeliveryExceptionCodeEnum.SEND_CANCEL_FAIL.getCode(),
                    DeliveryExceptionCodeEnum.ESTIMATED_TIME_OUT.getCode(),
                    DeliveryExceptionCodeEnum.SYNC_CREATE_FAIL.getCode(),
                    DeliveryExceptionCodeEnum.SYNC_CANCEL_FAIL.getCode(),
                    DeliveryExceptionCodeEnum.UNKNOWN.getCode(),
                    DeliveryExceptionCodeEnum.DELIVERY_STATUS_ROLLBACK.getCode()
            ),
            Lists.newArrayList(DeliveryStatusEnum.INIT, DeliveryStatusEnum.DELIVERY_LAUNCHED
            , DeliveryStatusEnum.WAITING_TO_ASSIGN_RIDER, DeliveryStatusEnum.DELIVERY_REJECTED
            , DeliveryStatusEnum.DELIVERY_FAILED, DeliveryStatusEnum.DELIVERY_CANCELLED),
            "未接单"),
    /**
     * 未到店
     */
    NO_ARRIVAL_STORE(20,
            Collections.<Integer>emptyList(),
            ImmutableList.of(
                    DeliveryExceptionCodeEnum.NO_ARRIVAL_STORE.getCode(),
                    DeliveryExceptionCodeEnum.DELIVERY_TIME_OUT.getCode(),
                    DeliveryExceptionCodeEnum.SEND_CANCEL_FAIL.getCode(),
                    DeliveryExceptionCodeEnum.ESTIMATED_TIME_OUT.getCode(),
                    DeliveryExceptionCodeEnum.SYNC_CREATE_FAIL.getCode(),
                    DeliveryExceptionCodeEnum.SYNC_CANCEL_FAIL.getCode(),
                    DeliveryExceptionCodeEnum.UNKNOWN.getCode()
            ),
            Lists.newArrayList(DeliveryStatusEnum.RIDER_ASSIGNED),
            "未到店"),
    /**
     * 未取货
     */
    NO_RIDER_TAKE_GOODS(30,
            Collections.<Integer>emptyList(),
            ImmutableList.of(
                    DeliveryExceptionCodeEnum.SEND_CANCEL_FAIL.getCode(),
                    DeliveryExceptionCodeEnum.ESTIMATED_TIME_OUT.getCode(),
                    DeliveryExceptionCodeEnum.SYNC_CREATE_FAIL.getCode(),
                    DeliveryExceptionCodeEnum.SYNC_CANCEL_FAIL.getCode(),
                    DeliveryExceptionCodeEnum.RIDER_PICK_TIME_OUT.getCode(),
                    DeliveryExceptionCodeEnum.UNKNOWN.getCode()
            ),
            Lists.newArrayList(DeliveryStatusEnum.RIDER_ARRIVED_SHOP),
            "未取货"),
    /**
     * 配送超时
     */
    DELIVERY_TIMEOUT(40,
            Collections.<Integer>emptyList(),
            ImmutableList.of(
                    DeliveryExceptionCodeEnum.ESTIMATED_TIME_OUT.getCode()
            ),
            Lists.newArrayList(DeliveryStatusEnum.RIDER_TAKEN_GOODS),
            "配送超时"),
    /**
     * 系统异常
     */
    SYSTEM_EXCEPTION(50,
            ImmutableList.of(
                    DeliveryExceptionTypeEnum.LAUNCH_DELIVERY_FAILED_BY_SYSTEM_EXCEPTION.getCode(),
                    DeliveryExceptionTypeEnum.DELIVERY_EXCEPTION_BY_SYSTEM.getCode()
            ),
            ImmutableList.of(
                    DeliveryExceptionCodeEnum.SYSTEM_EXCEPTION_DELIVERY_FAIL.getCode(),
                    DeliveryExceptionCodeEnum.OPEN_API_DELIVERY_EXCEPTION.getCode()
            ),
            Lists.newArrayList(DeliveryStatusEnum.INIT, DeliveryStatusEnum.DELIVERY_LAUNCHED
                    , DeliveryStatusEnum.WAITING_TO_ASSIGN_RIDER, DeliveryStatusEnum.DELIVERY_REJECTED
                    , DeliveryStatusEnum.DELIVERY_FAILED, DeliveryStatusEnum.DELIVERY_CANCELLED
                    , DeliveryStatusEnum.RIDER_ASSIGNED, DeliveryStatusEnum.RIDER_ARRIVED_SHOP
                    , DeliveryStatusEnum.RIDER_TAKEN_GOODS),
            "系统异常"),
    /**
     * 异常上报
     */
    REPORT_EXCEPTION(60,
            ImmutableList.of(
                    DeliveryExceptionTypeEnum.DELIVERY_EXCEPTION_BY_RIDER.getCode()
            ),
            ImmutableList.of(
                    DeliveryExceptionCodeEnum.SELF_DELIVERY.getCode(),
                    DeliveryExceptionCodeEnum.RIDER_REPORT_FAIL.getCode(),
                    DeliveryExceptionCodeEnum.RECALL_RIDER_FAIL.getCode(),
                    DeliveryExceptionCodeEnum.RECALL_SELF_RIDER_FAIL.getCode(),
                    DeliveryExceptionCodeEnum.DELIVERY_EXCEPTION_UPLOAD.getCode()
            ),
            Lists.newArrayList(DeliveryStatusEnum.INIT, DeliveryStatusEnum.DELIVERY_LAUNCHED
                    , DeliveryStatusEnum.WAITING_TO_ASSIGN_RIDER, DeliveryStatusEnum.DELIVERY_REJECTED
                    , DeliveryStatusEnum.DELIVERY_FAILED, DeliveryStatusEnum.DELIVERY_CANCELLED
                    , DeliveryStatusEnum.RIDER_ASSIGNED, DeliveryStatusEnum.RIDER_ARRIVED_SHOP
                    , DeliveryStatusEnum.RIDER_TAKEN_GOODS),
            "异常上报"),
    /**
     * 取货失败
     */
    TAKE_EXCEPTION(70,
            ImmutableList.of(
                    DeliveryExceptionTypeEnum.DELIVERY_EXCEPTION_BY_RIDER.getCode()
            ),
            ImmutableList.of(
                    DeliveryExceptionCodeEnum.RIDER_TAKE_FAIL.getCode(),
                    DeliveryExceptionCodeEnum.RIDER_TAKE_FAIL_AUDITING.getCode()
            ),
            Lists.newArrayList(DeliveryStatusEnum.INIT, DeliveryStatusEnum.DELIVERY_LAUNCHED
                    , DeliveryStatusEnum.WAITING_TO_ASSIGN_RIDER, DeliveryStatusEnum.DELIVERY_REJECTED
                    , DeliveryStatusEnum.DELIVERY_FAILED, DeliveryStatusEnum.DELIVERY_CANCELLED
                    , DeliveryStatusEnum.RIDER_ASSIGNED, DeliveryStatusEnum.RIDER_ARRIVED_SHOP
                    , DeliveryStatusEnum.RIDER_TAKEN_GOODS),
            "取货失败"),

    ;

    private final int code;

    /**
     * @see
     * package com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryExceptionTypeEnum
     */
    private final List<Integer> exceptionTypeList;

    /**
     * @see
     * package com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryExceptionCodeEnum
     */
    private final List<Integer> exceptionCodeList;
    private final List<DeliveryStatusEnum> deliveryStatusList;
    private final String desc;
    private static final Map<String, DeliveryExceptionSubTypeEnum> CODE_TO_ENUM_MAP = new HashMap<>();
    private static final Map<String, DeliveryExceptionSubTypeEnum> DELIVERY_STATUS_CODE_TO_ENUM_MAP = new HashMap<>();

    DeliveryExceptionSubTypeEnum(int code, List<Integer> exceptionTypeList, List<Integer> exceptionCodeList,
                                 List<DeliveryStatusEnum> deliveryStatusList, String desc) {
        this.code = code;
        this.desc = desc;
        this.exceptionTypeList = exceptionTypeList;
        this.exceptionCodeList = exceptionCodeList;
        this.deliveryStatusList = deliveryStatusList;
    }

    static {
        for (DeliveryExceptionSubTypeEnum each : values()) {
            CODE_TO_ENUM_MAP.put(String.valueOf(each.getCode()), each);
            if (each != ALL) {
                for (DeliveryStatusEnum deliveryStatusEnum : each.getDeliveryStatusList()) {
                    DELIVERY_STATUS_CODE_TO_ENUM_MAP.put(String.valueOf(deliveryStatusEnum.getCode()), each);
                }
            }
        }
    }

    @JsonValue
    public int getCode() {
        return code;
    }

    public List<Integer> getExceptionTypeList() {
        return exceptionTypeList;
    }

    public List<Integer> getExceptionCodeList() {
        return exceptionCodeList;
    }

    public List<DeliveryStatusEnum> getDeliveryStatusList() { return deliveryStatusList; }

    public String getDesc() {
        return desc;
    }

    @JsonCreator
    public static DeliveryExceptionSubTypeEnum valueOf(int code) {
        return CODE_TO_ENUM_MAP.get(String.valueOf(code));
    }

    public static DeliveryExceptionSubTypeEnum deliveryStatusCodeValueOf(int deliveryStatusCode) {
        return DELIVERY_STATUS_CODE_TO_ENUM_MAP.get(String.valueOf(deliveryStatusCode));
    }

    public boolean check(Integer exceptionType,
                         Integer exceptionCode,
                         int deliveryStatusCode){
        if (exceptionType == null || exceptionCode == null) {
            return false;
        }
        boolean typeCheck = CollectionUtils.isEmpty(exceptionTypeList) || exceptionTypeList.contains(exceptionType);
        boolean codeCheck = CollectionUtils.isEmpty(exceptionCodeList) || exceptionCodeList.contains(exceptionCode);
        DeliveryStatusEnum deliveryStatusEnum = DeliveryStatusEnum.valueOf(deliveryStatusCode);
        if(deliveryStatusEnum == null){
            return false;
        }
        return typeCheck && codeCheck && deliveryStatusList.contains(deliveryStatusEnum);
    }

    public static DeliveryExceptionSubTypeEnum deliveryStatusCodeValueOf(Integer exceptionType,
                                                                              Integer exceptionCode,
                                                                              int deliveryStatusCode) {
        for (DeliveryExceptionSubTypeEnum subTypeEnum : values()) {
            if(subTypeEnum.check(exceptionType, exceptionCode, deliveryStatusCode)){
                return subTypeEnum;
            }
        }
        return null;
    }

    public static DeliveryExceptionSubTypeEnum deliveryStatusCodeValueOfWithOutAll(Integer exceptionType,
                                                                         Integer exceptionCode,
                                                                         int deliveryStatusCode) {
        for (DeliveryExceptionSubTypeEnum subTypeEnum : values()) {
            if(subTypeEnum == ALL){
                continue;
            }
            if(subTypeEnum.check(exceptionType, exceptionCode, deliveryStatusCode)){
                return subTypeEnum;
            }
        }
        return null;
    }
}
