package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.delivery.remind.config.request;

import java.util.List;

import org.apache.commons.lang3.StringUtils;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.common.TOperator;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 保存门店配送提醒配置的请求体.
 *
 * <AUTHOR>
 * @since 2021/10/8 16:20
 */
@TypeDoc(
        description = "保存门店配送提醒配置的请求体",
        authors = {
                "liyang176"
        }
)
@ThriftStruct
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SaveStoreRemindConfigRequest {
    /**
     * 保存接收人的最大长度
     *
     */
    private static  final int LIMIT_LENGTH = 20;

    @FieldDoc(
            description = "租户 ID",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public Long tenantId;

    @FieldDoc(
            description = "门店 ID",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(2)
    public Long storeId;

    @FieldDoc(
            description = "提醒接收人",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(3)
    public List<String> recipients;

    @FieldDoc(
            description = "操作人"
    )
    @ThriftField(4)
    public TOperator operator;

    @FieldDoc(
            description = "歪马企业超时提醒接收人",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(5)
    public List<String> dhRecipients;

    public String validate() {
        if (tenantId == null || tenantId <= 0) {
            return "租户 ID 无效";
        }
        if (storeId == null || storeId <= 0) {
            return "门店 ID 无效";
        }
        if (recipients == null) {
            return "配送提醒接收人无效";
        } else {
            for (String recipient : recipients) {
                if (StringUtils.isEmpty(recipient)) {
                    return "提醒 mis 号无效";
                }
            }
        }
        if (recipients.size() > LIMIT_LENGTH) {
            return "配送提醒接收人超出人数限制，限制最多人数：" + LIMIT_LENGTH;
        }

        if (operator == null) {
            return "操作人无效";
        } else {
            String operatorCheck = operator.validate();
            if (operatorCheck != null) {
                return operatorCheck;
            }
        }

        if (dhRecipients == null) {
            return "歪马配送提醒接收人无效";
        } else {
            for (String recipient : dhRecipients) {
                if (StringUtils.isEmpty(recipient)) {
                    return "歪马提醒 mis 号无效";
                }
            }
        }

        if (dhRecipients.size() > LIMIT_LENGTH) {
            return "歪马配送提醒接收人超出人数限制，限制最多人数：" + LIMIT_LENGTH;
        }
        return null;
    }
}
