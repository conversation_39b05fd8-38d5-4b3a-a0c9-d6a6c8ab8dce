package com.sankuai.meituan.shangou.empower.tms.delivery.client.enums;

import org.apache.commons.lang3.StringUtils;

public enum PlatformSourceEnum {
    OMS(0, ""),
    OFC(1, "O"),
    ;

    private int code;
    /**
     * 前缀
     */
    private String prefixStr;

    PlatformSourceEnum(int code, String prefixStr) {
        this.code = code;
        this.prefixStr = prefixStr;
    }

    public int getCode() {
        return code;
    }

    public String getPrefixStr() {
        return prefixStr;
    }

    public static PlatformSourceEnum checkOrderIdSource(String orderId) {
        if (StringUtils.isEmpty(orderId)) {
            return OMS;
        }
        if (orderId.startsWith(OFC.getPrefixStr())) {
            return OFC;
        }
        return OMS;
    }

    public static String getOrderIdWithoutPrefix(String orderId) {
        PlatformSourceEnum platformSourceEnum = checkOrderIdSource(orderId);
        if (platformSourceEnum == OMS) {
            return orderId;
        }
        return StringUtils.removeStart(orderId, platformSourceEnum.getPrefixStr());
    }

    public String toPrefixOrderId(String orderId){
        return this.getPrefixStr()+orderId;
    }

    public static PlatformSourceEnum platformCodeToEnum(Integer code){
        if(code == null){
            return OMS;
        }
        for (PlatformSourceEnum platformSource : values()){
            if(platformSource.getCode() == code){
                return platformSource;
            }
        }
        return OMS;
    }

}
