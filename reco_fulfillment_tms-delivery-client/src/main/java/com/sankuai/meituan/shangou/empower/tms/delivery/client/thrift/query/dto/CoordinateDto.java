package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 坐标信息
 * <AUTHOR>
 */
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
@Builder
public class CoordinateDto {

    @FieldDoc(
            description = "经度",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(1)
    public String longitude;

    @FieldDoc(
            description = "纬度",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(2)
    public String latitude;

}
