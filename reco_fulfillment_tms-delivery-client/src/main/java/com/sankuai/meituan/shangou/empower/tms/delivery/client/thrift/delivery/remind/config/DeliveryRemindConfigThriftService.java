package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.delivery.remind.config;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.delivery.remind.config.request.QueryStoreRemindConfigRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.delivery.remind.config.request.SaveStoreRemindConfigRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.delivery.remind.config.response.DeliveryRemindConfigResponse;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.delivery.remind.config.response.RemindConfigCommonResponse;

/**
 * 配送业务提醒配置.
 *
 * <AUTHOR>
 * @since 2021/10/8 14:41
 */
@InterfaceDoc(
        displayName = "配送业务提醒配置",
        type = "octo.thrift.annotation",
        scenarios = "配送业务提醒配置服务，如配置门店配送超时后的提醒人",
        description = "配送业务提醒配置服务，如配置门店配送超时后的提醒人",
        authors = {
                "liyang176"
        }
)
@ThriftService
public interface DeliveryRemindConfigThriftService {

    @MethodDoc(
            displayName = "查询门店配送提醒配置",
            description = "查询门店配送提醒配置",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询门店配送提醒配置的请求",
                            type = QueryStoreRemindConfigRequest.class,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            returnValueDescription = "门店配送提醒配置",
            restExampleResponseData = "{}",
            example = "暂无"
    )
    @ThriftMethod
    DeliveryRemindConfigResponse queryStoreDeliveryRemindConfig(QueryStoreRemindConfigRequest request);

    @MethodDoc(
            displayName = "保存门店配送提醒配置",
            description = "保存门店配送提醒配置",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "保存门店配送提醒配置的请求",
                            type = SaveStoreRemindConfigRequest.class,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            returnValueDescription = "操作结果",
            restExampleResponseData = "{}",
            example = "暂无"
    )
    @ThriftMethod
    RemindConfigCommonResponse saveStoreDeliveryRemindConfig(SaveStoreRemindConfigRequest request);
}
