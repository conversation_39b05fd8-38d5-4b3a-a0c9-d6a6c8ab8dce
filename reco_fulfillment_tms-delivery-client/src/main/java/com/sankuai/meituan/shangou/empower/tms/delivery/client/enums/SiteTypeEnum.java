package com.sankuai.meituan.shangou.empower.tms.delivery.client.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @description 端类型
 * @date 2024-11-12
 */
@Getter
public enum SiteTypeEnum {
    /**
     * 移动端
     */
    APP("app"),
    /**
     * 电脑端
     */
    PC("pc");

    private final String code;

    SiteTypeEnum(String code) {
        this.code = code;
    }

    public static SiteTypeEnum of(String code) {
        for (SiteTypeEnum typeEnum : values()) {
            if (Objects.equals(typeEnum.getCode(), code)) {
                return typeEnum;
            }
        }
        return SiteTypeEnum.APP;
    }
}
