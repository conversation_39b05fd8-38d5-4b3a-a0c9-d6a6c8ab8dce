package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.TDeliveryDetail;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/1/5
 */
@TypeDoc(
        description = "查询配送信息响应体",
        authors = {
                "qianteng"
        }
)
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
public class QueryDeliveryInfoResponse {
    @FieldDoc(
            description = "配送信息",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public List<TDeliveryDetail> TDeliveryDetails;

    public static QueryDeliveryInfoResponse emptyResponse() {
        return new QueryDeliveryInfoResponse(new ArrayList<TDeliveryDetail>());
    }
}
