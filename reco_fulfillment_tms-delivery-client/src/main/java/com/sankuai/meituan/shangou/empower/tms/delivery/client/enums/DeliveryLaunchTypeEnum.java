package com.sankuai.meituan.shangou.empower.tms.delivery.client.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * 配送发起类型
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/1/4
 */
public enum DeliveryLaunchTypeEnum {

	/**
	 * 自动发配送
	 */
	AUTO_LAUNCH_DELIVERY(1),

	/**
	 * 手动发配送
	 */
	MANUAL_LAUNCH_DELIVERY(2);

	private static final Map<Integer, DeliveryLaunchTypeEnum> CODE_ENUM_MAP = new HashMap<>();
	private final int code;

	static {
		for (DeliveryLaunchTypeEnum each : values()) {
			CODE_ENUM_MAP.put(each.code, each);
		}
	}

	DeliveryLaunchTypeEnum(int code) {
		this.code = code;
	}

	public int getCode() {
		return code;
	}

	public static DeliveryLaunchTypeEnum enumOf(Integer code) {
		return CODE_ENUM_MAP.get(code);
	}
}
