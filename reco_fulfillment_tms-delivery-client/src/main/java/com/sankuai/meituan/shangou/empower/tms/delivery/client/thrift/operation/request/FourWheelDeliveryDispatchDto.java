package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
public class FourWheelDeliveryDispatchDto {

    @ThriftField(1)
    public Integer vehicleCode;

    @ThriftField(2)
    public String vehicleName;

    @ThriftField(3)
    public String vehicleFeeRange;

}
