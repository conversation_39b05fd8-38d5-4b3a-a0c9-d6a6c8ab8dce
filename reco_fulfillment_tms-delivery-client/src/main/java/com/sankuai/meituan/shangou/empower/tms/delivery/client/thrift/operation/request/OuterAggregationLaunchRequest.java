package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@TypeDoc(
        description = "发起三方配送"
)
@ThriftStruct
@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class OuterAggregationLaunchRequest {

    @FieldDoc(
            description = "租户ID",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public Long tenantId;

    @FieldDoc(
            description = "门店ID",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(2)
    public Long shopId;

    @FieldDoc(
            description = "赋能订单ID",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(3)
    public Long orderId;

}
