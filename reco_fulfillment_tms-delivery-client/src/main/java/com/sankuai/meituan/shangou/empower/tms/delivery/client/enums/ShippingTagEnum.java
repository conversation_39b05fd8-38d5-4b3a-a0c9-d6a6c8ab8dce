package com.sankuai.meituan.shangou.empower.tms.delivery.client.enums;

import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/3/7 11:22
 **/
public enum ShippingTagEnum {
    //大范围
    WIDER_SHIPPING_AREA(1, "WIDER_SHIPPING_AREA", "大范围");

    private int code;
    private String shippingCodePrefix;
    private String desc;

    public int getCode() {
        return code;
    }

    public String getShippingCodePrefix() {
        return shippingCodePrefix;
    }

    public String getDesc() {
        return desc;
    }

    ShippingTagEnum(int code, String shippingCodePrefix, String desc) {
        this.code = code;
        this.shippingCodePrefix = shippingCodePrefix;
        this.desc = desc;
    }

    public ShippingTagEnum enumOf(int code) {
        for (ShippingTagEnum tagEnum : values()) {
            if (tagEnum.getCode() == code) {
                return tagEnum;
            }
        }

        return null;
    }


    public ShippingTagEnum enumOf(String shippingCodePrefix) {
        for (ShippingTagEnum tagEnum : values()) {
            if (StringUtils.equals(tagEnum.getShippingCodePrefix(), shippingCodePrefix)) {
                return tagEnum;
            }
        }

        return null;
    }

    public static List<ShippingTagEnum> parseTagFromShippingCode(String shippingCode) {
        if (StringUtils.isBlank(shippingCode)) {
            return Collections.emptyList();
        }

        ArrayList<ShippingTagEnum> shippingTagEnumList = new ArrayList<>();
        for (ShippingTagEnum shippingTagEnum : ShippingTagEnum.values()) {
            if (shippingCode.contains(shippingTagEnum.getShippingCodePrefix())){
                shippingTagEnumList.add(shippingTagEnum);
            }
        }

        return shippingTagEnumList;
    }
}
