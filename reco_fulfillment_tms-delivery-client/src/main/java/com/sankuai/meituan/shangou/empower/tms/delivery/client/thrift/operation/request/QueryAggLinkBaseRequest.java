package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @date 2025-01-13
 */
@ThriftStruct
@Data
public class QueryAggLinkBaseRequest {
    @FieldDoc(
            description = "租户id",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    private Long tenantId;

    @FieldDoc(
            description = "门店id",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(2)
    private Long poiId;
    /**
     * @see com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.SiteTypeEnum
     */
    @FieldDoc(
            description = "设备类型",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(3)
    private String deviceType;

    @FieldDoc(
            description = "操作人账号",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(4)
    private String operatorAccount;

    @FieldDoc(
            description = "操作人姓名",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(5)
    private String operatorName;
}
