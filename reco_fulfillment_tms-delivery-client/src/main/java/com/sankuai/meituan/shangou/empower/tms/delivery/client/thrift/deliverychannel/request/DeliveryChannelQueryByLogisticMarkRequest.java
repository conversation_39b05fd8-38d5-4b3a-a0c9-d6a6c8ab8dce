package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.deliverychannel.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.Assert;

/**
 * 根据聚合配送平台承运商信息查询配送渠道信息请求体
 *
 * <AUTHOR>
 * @date 2023/4/6
 */
@TypeDoc(
		description = "根据聚合配送平台承运商信息查询配送渠道信息请求体",
		authors = {
				"zhangjian155"
		}
)
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
public class DeliveryChannelQueryByLogisticMarkRequest {

	@FieldDoc(
			description = "配送平台code",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(1)
	public Integer deliveryPlatFormCode;

	@FieldDoc(
			description = "订单渠道code",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(2)
	public Integer orderChannelCode;

	@FieldDoc(
			description = "聚合配送平台承运商信息",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(3)
	public String logisticMark;

	public void validate() {
		Assert.notNull(deliveryPlatFormCode, "deliveryPlatFormCode不能为null");
		Assert.notNull(orderChannelCode, "orderChannelCode不能为null");
		Assert.notNull(logisticMark, "logisticMark不能为null");
	}
}
