package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: 门店部分配送配置,当前用于批量导入门店配送配置场景，以及给订单侧提供主动查询门店配送配置的场景（抖音）
 * @Author: zhangjian155
 */
@ThriftStruct
@Data
@NoArgsConstructor
public class StoreSubDeliveryConfigDto {

    @FieldDoc(
            description = "渠道名称"
    )
    @ThriftField(1)
    private Integer channelType;

    @FieldDoc(
            description = "配送发单节点"
    )
    @ThriftField(2)
    private Integer deliveryLaunchPoint;

    @FieldDoc(
            description = "配送平台code"
    )
    @ThriftField(3)
    private Integer deliveryPlatformCode;

    @FieldDoc(
            description = "当前开通状态标识 1-已开通 0-未开通"
    )
    @ThriftField(4)
    public Integer openFlag;
}
