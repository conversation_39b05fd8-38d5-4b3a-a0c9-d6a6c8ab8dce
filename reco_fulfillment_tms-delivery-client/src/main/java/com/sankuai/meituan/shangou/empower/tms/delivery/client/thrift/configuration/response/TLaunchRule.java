package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 配送规则 Thrift 对象.
 *
 * <AUTHOR>
 * @since 2021/3/8 19:36
 */
@TypeDoc(
        description = "配送规则 Thrift 对象"
)
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@ToString
public class TLaunchRule {
    @FieldDoc(
            description = "配送规则编码"
    )
    private Integer code;

    @FieldDoc(
            description = "配送规则名称"
    )
    private String name;

    @ThriftField(1)
    public Integer getCode() {
        return code;
    }

    @ThriftField
    public void setCode(Integer code) {
        this.code = code;
    }

    @ThriftField(2)
    public String getName() {
        return name;
    }

    @ThriftField
    public void setName(String name) {
        this.name = name;
    }
}
