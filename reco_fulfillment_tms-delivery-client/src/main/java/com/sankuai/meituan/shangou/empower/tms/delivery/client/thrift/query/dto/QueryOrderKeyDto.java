package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024-08-08
 */
@TypeDoc(
        description = "请求订单信息",
        authors = {
                "puxiaokang"
        }
)
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
public class QueryOrderKeyDto {
    @FieldDoc(
            description = "渠道订单号",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public String viewOrderId;

    @FieldDoc(
            description = "赋能订单号",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(2)
    public Long orderId;

    @FieldDoc(
            description = "渠道类型",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(3)
    public Integer orderBizType;

    @FieldDoc(
            description = "门店id",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(4)
    public Long shopId;

    @FieldDoc(
            description = "仓id",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(5)
    public Long warehouseId;

    @FieldDoc(
            description = "转单门店/仓Id",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(6)
    public Long fulfillWareHouseId;

}
