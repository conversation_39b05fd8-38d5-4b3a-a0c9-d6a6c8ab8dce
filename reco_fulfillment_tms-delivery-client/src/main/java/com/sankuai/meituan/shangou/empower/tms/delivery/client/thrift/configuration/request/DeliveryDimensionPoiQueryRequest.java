package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 查询配送门店维度配置请求体
 *
 * <AUTHOR>
 * @date 2025/2/26
 */
@TypeDoc(
		description = "查询配送门店维度配置请求体",
		authors = {"jianglilin02"}
)
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
public class DeliveryDimensionPoiQueryRequest {

	@FieldDoc(
			description = "租户ID",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(1)
	private Long tenantId;

	@FieldDoc(
			description = "门店ID",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(2)
	private Long storeId;

	/**
	 * 参数校验
	 * 
	 * @return 错误信息，为null表示校验通过
	 */
	public String validate() {
		if (tenantId == null || tenantId <= 0L) {
			return "租户ID不合法";
		}
		if (storeId == null || storeId <= 0L) {
			return "门店ID不合法";
		}
		return null;
	}
}