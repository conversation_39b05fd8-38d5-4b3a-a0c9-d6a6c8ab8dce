package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
public class ShopAuthConfig {

    @FieldDoc(
            description = "请求结果",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public Integer code;

    @FieldDoc(
            description = "跳转url",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(2)
    public String dapLinkUrl;

    @FieldDoc(
            description = "是否授权",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(3)
    public Integer isAuthed;
}
