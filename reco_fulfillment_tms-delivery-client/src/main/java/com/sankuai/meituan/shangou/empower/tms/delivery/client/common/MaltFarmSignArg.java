package com.sankuai.meituan.shangou.empower.tms.delivery.client.common;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @date 2021/4/14
 * @email jianglilin02@meituan
 *
 * 标记麦芽田签名参数
 */
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
public @interface MaltFarmSignArg {
}
