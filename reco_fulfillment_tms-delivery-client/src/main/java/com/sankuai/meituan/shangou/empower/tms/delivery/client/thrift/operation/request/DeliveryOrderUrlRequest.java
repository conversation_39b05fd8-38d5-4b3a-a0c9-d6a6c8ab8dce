package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@TypeDoc(
        description = "配送单URL请求体",
        authors = {
                "tangjianpei"
        }
)
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
@SuppressWarnings("DuplicatedCode")
public class DeliveryOrderUrlRequest {

    @FieldDoc(
            description = "租户id",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public Long tenantId;

    @FieldDoc(
            description = "门店id",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(2)
    public Long storeId;

    @FieldDoc(
            description = "订单id列表",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(3)
    public List<Long> orderIdList;

    @FieldDoc(
            description = "履约订单id列表",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(4)
    public List<String> fulfillMarkIdList;

}
