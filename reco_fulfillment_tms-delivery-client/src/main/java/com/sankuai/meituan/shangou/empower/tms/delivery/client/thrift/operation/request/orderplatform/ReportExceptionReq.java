package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.request.orderplatform;

import com.facebook.swift.codec.ThriftConstructor;
import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;

import java.util.List;

/**
 * ReportExceptionReq
 *
 * <AUTHOR>
 * @since 2023/2/28
 */
@Data
@ThriftStruct
public class ReportExceptionReq {

    @FieldDoc(description = "租户id")
    @ThriftField(1)
    public Long tenantId;

    @FieldDoc(description = "门店id")
    @ThriftField(2)
    public Long storeId;

    @FieldDoc(description = "赋能订单id")
    @ThriftField(3)
    public Long empowerOrderId;

    @FieldDoc(description = "渠道号")
    @ThriftField(4)
    public Integer channelId;

    @FieldDoc(description = "图片url")
    @ThriftField(5)
    public List<String> pictureUrls;

    @FieldDoc(description = "操作人账号id")
    @ThriftField(6)
    public Long operatorAccountId;

    @FieldDoc(description = "操作人姓名")
    @ThriftField(7)
    public String operatorName;

    @FieldDoc(description = "请求端的appid")
    @ThriftField(8)
    public String requestAppId;

    @ThriftConstructor
    public ReportExceptionReq(Long tenantId, Long storeId, Long empowerOrderId, Integer channelId,
            List<String> pictureUrls, Long operatorAccountId, String operatorName, String requestAppId) {
        this.tenantId = tenantId;
        this.storeId = storeId;
        this.empowerOrderId = empowerOrderId;
        this.channelId = channelId;
        this.pictureUrls = pictureUrls;
        this.operatorAccountId = operatorAccountId;
        this.operatorName = operatorName;
        this.requestAppId = requestAppId;
    }

}
