package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collections;
import java.util.List;

/**
 * @Description: 简单查询门店/仓库的配送设置返回体
 * @Author: zhangjian155
 * @Date: 2022/10/11 15:53
 */
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
@Builder
public class SimpleStoreDeliveryConfigDto {

    public static final SimpleStoreDeliveryConfigDto EMPTY =
            SimpleStoreDeliveryConfigDto.builder().storeChannelDeliveryConfigList(Collections.<StoreSubDeliveryConfigDto>emptyList()).build();

    @FieldDoc(
            description = "门店ID"
    )
    @ThriftField(1)
    private Long storeId;

    @FieldDoc(
            description = "配送配置列表"
    )
    @ThriftField(2)
    private List<StoreSubDeliveryConfigDto> storeChannelDeliveryConfigList;
}
