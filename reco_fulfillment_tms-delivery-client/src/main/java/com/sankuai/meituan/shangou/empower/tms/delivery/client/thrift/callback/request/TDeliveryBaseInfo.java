package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.callback.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2021/1/6.
 */

@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
public class TDeliveryBaseInfo {


    @FieldDoc(
            description = "租户Id",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public Long tenantId;

    @FieldDoc(
            description = "门店id",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(2)
    public Long storeId;

    @FieldDoc(
            description = "电话",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(3)
    public String contactPhone;

    @FieldDoc(
            description = "自动发配送时间点，1-商家接单，2-拣货完成",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(4)
    public Integer autoLaunchPoint;

    @FieldDoc(
            description = "自动发配送延时，单位（分钟）",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(5)
    public Integer autoLaunchDelayMinutes;

    @FieldDoc(
            description = "发配送方式，1-自动发配送，2-手动发配送",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(6)
    public Integer launchPattern;



}
