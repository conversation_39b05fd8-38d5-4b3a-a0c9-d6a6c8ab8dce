package com.sankuai.meituan.shangou.empower.tms.delivery.client.enums;

import java.util.Objects;

public enum DeliveryOrderTransEnum {

    /**
     * 常规转单
     */
    NORMAL_TRANS(1),
    /**
     * 异常转单
     */
    DELIVERY_EXCEPTION_TRANS(2),
    ;
    private final int transType;

    private DeliveryOrderTransEnum(int transType){
        this.transType = transType;
    }

    public int getTransType() {
        return transType;
    }

    public static boolean isTrans(Integer transType) {
        return Objects.equals(transType, NORMAL_TRANS.getTransType()) || Objects.equals(transType, DELIVERY_EXCEPTION_TRANS.getTransType());
    }
}
