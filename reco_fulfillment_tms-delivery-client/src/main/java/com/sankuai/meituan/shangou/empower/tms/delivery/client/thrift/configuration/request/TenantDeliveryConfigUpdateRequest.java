package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/11/22
 */
@TypeDoc(
		description = "修改租户配送配置请求体",
		authors = {"hedong07"}
)
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
public class TenantDeliveryConfigUpdateRequest {

	@FieldDoc(
			description = "租户id",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(1)
	public Long tenantId;

	@FieldDoc(
			description = "渠道配送配置集合",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(2)
	public List<ChannelDeliveryConfigUpdateRequest> channelDeliveryConfigs;

	@FieldDoc(
			description = "操作员id",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(3)
	public Long operatorId;

	@FieldDoc(
			description = "操作员姓名",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(4)
	public String operatorName;

	public String validate() {
		if (tenantId == null || tenantId <= 0L) {
			return "租户id不合法";
		}

		if (CollectionUtils.isEmpty(channelDeliveryConfigs)) {
			return "无配送修改";
		}

		for (ChannelDeliveryConfigUpdateRequest each : channelDeliveryConfigs) {
			String errorMsg = each.validate();
			if (errorMsg != null) {
				return errorMsg;
			}
		}

		if (operatorId == null || operatorId <= 0L) {
			return "操作员id不合法";
		}

		if (StringUtils.isBlank(operatorName)) {
			return "操作员姓名不能为空";
		}

		return null;
	}
}
