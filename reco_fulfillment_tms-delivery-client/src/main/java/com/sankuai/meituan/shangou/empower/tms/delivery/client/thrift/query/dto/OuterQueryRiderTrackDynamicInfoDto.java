package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 开放平台查询骑手轨迹动态信息
 * <AUTHOR>
 */
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
public class OuterQueryRiderTrackDynamicInfoDto {

    public static final OuterQueryRiderTrackDynamicInfoDto EMPTY = new OuterQueryRiderTrackDynamicInfoDto();

    @FieldDoc(
            description = "配送状态code",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(1)
    public Integer deliveryStatusCode;

    @FieldDoc(
            description = "承运商code",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(2)
    public Integer deliveryChannelCode;

    @FieldDoc(
            description = "承运商名称",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(3)
    public String deliveryChannelDesc;

    @FieldDoc(
            description = "最近一次配送状态变更时间戳",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(4)
    public Long lastEventTime;

    @FieldDoc(
            description = "订单送达/配送完成时间戳",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(5)
    public Long deliveryDoneTime;

    @FieldDoc(
            description = "骑手信息",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(6)
    public RiderInfoDto riderInfoDto;

}
