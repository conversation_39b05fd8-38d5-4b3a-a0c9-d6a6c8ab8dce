package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/11/20
 */
@TypeDoc(
		description = "发起配送请求体",
		authors = {
				"hedong07"
		}
)
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
public class DeliveryRangeSyncRequest {

	@FieldDoc(
			description = "租户id",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(1)
	public Long tenantId;

	@FieldDoc(
			description = "门店id",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(2)
	public Long storeId;

	@FieldDoc(
			description = "需要同步的渠道及配送范围相关属性",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(3)
	public List<ChannelDeliveryRangeSyncRequest> channelDeliveryRangeSyncRequests;

	@FieldDoc(
			description = "操作人id",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(4)
	public Long operatorId;

	public String validate() {
		if (tenantId == null || tenantId <= 0L) {
			return "租户id不合法";
		}

		if (storeId == null || storeId <= 0L) {
			return "门店id不合法";
		}

		if (CollectionUtils.isEmpty(channelDeliveryRangeSyncRequests)) {
			return "待同步渠道不能为空";
		}

		for (ChannelDeliveryRangeSyncRequest each : channelDeliveryRangeSyncRequests) {
			String errorMsg = each.validate();
			if (errorMsg != null) {
				return errorMsg;
			}
		}

		if (operatorId == null || operatorId <= 0L) {
			return "操作人id不合法";
		}

		return null;
	}
}
