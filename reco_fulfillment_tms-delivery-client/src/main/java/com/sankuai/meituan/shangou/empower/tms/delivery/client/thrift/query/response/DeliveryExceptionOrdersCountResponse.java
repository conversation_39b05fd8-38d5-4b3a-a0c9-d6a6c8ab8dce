package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.common.Status;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.TOrderIdentifier;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;


@TypeDoc(
        description = "配送异常订单id的分页查询结果"
)
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
public class DeliveryExceptionOrdersCountResponse {

    @FieldDoc(
            description = "响应状态",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(value = 1, requiredness = ThriftField.Requiredness.REQUIRED)
    public Status status = Status.SUCCESS;


    @FieldDoc(
            description = "总数",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(value = 3, requiredness = ThriftField.Requiredness.REQUIRED)
    public Integer totalCount = 0;


}
