package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.request.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.request.QueryDeliveryExceptionCountRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.request.QueryDeliveryExceptionOrderBySubTypeRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.request.QueryDeliveryExceptionOrderRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.request.QueryDeliveryExceptionRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.request.QueryDeliveryInfoRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.request.QueryDeliveryOrderRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.response.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/1/5
 * @desc 配送查询服务
 */
@InterfaceDoc(
        displayName = "配送查询服务",
        type = "octo.thrift.annotation",
        scenarios = "配送查询服务",
        description = "配送查询服务",
        authors = {
                "qianteng"
        }
)
@ThriftService
public interface QueryDeliveryInfoThriftService {

        @MethodDoc(
                displayName = "查询订单配送信息",
                description = "查询订单配送信息",
                parameters = {
                        @ParamDoc(
                                name = "request",
                                description = "查询订单配送信息",
                                type = QueryDeliveryInfoRequest.class,
                                requiredness = Requiredness.REQUIRED
                        )
                },
                returnValueDescription = "订单配送信息结果",
                restExampleResponseData = "{}",
                example = "暂无"
        )
        @ThriftMethod
        QueryDeliveryInfoResponse queryDeliveryInfoByOrderKeys(QueryDeliveryInfoRequest req);

        @MethodDoc(
                displayName = "查询订单配送信息",
                description = "查询订单配送信息，批量查询大小限制为50，可动态调节",
                parameters = {
                        @ParamDoc(
                                name = "request",
                                description = "查询订单配送信息",
                                type = QueryActiveDeliveryInfoRequest.class,
                                requiredness = Requiredness.REQUIRED
                        )
                },
                returnValueDescription = "订单配送信息结果",
                restExampleResponseData = "{}",
                example = "暂无"
        )
        //本次pda拆分没有使用该接口，该接口没有经过测试，如需使用，请先测试
        @ThriftMethod
        QueryActiveDeliveryInfoResponse queryActiveDeliveryInfoByOrderKeys(QueryActiveDeliveryInfoRequest req);

        @MethodDoc(
                displayName = "查询配送异常的订单id",
                description = "查询配送异常的订单id",
                parameters = {
                        @ParamDoc(
                                name = "request",
                                description = "查询配送异常订单id请求",
                                type = QueryDeliveryExceptionOrderRequest.class,
                                requiredness = Requiredness.REQUIRED
                        )
                },
                returnValueDescription = "查询配送异常的订单id结果",
                restExampleResponseData = "{}",
                example = "暂无"
        )
        @ThriftMethod
        DeliveryExceptionOrdersResponse queryDeliveryExceptionOrders(QueryDeliveryExceptionOrderRequest request);

        @MethodDoc(
                displayName = "查询配送异常的订单数量",
                description = "查询配送异常的订单数量",
                parameters = {
                        @ParamDoc(
                                name = "request",
                                description = "查询配送异常订单的请求",
                                type = QueryDeliveryExceptionOrderRequest.class,
                                requiredness = Requiredness.REQUIRED
                        )
                },
                returnValueDescription = "查询配送异常的订单数量结果",
                restExampleResponseData = "{}",
                example = "暂无"
        )
        @ThriftMethod
        DeliveryExceptionOrderNumResponse countDeliveryExceptionOrder(QueryDeliveryExceptionOrderRequest request);

        @MethodDoc(
            displayName = "查询运单信息",
            description = "查询运单信息",
            parameters = {
                @ParamDoc(
                    name = "request",
                    description = "查询运单信息",
                    type = QueryDeliveryInfoRequest.class,
                    requiredness = Requiredness.REQUIRED
                )
            },
            returnValueDescription = "订单运单结果",
            restExampleResponseData = "{}",
            example = "暂无"
        )
        @ThriftMethod
        QueryDeliveryOrderResponse queryDeliveryOrderByOrderId(QueryDeliveryOrderRequest request);

        @MethodDoc(
                displayName = "查询运单信息为空时返回成功",
                description = "查询运单信息为空时返回成功",
                parameters = {
                        @ParamDoc(
                                name = "request",
                                description = "查询运单信息为空时返回成功",
                                type = QueryDeliveryInfoRequest.class,
                                requiredness = Requiredness.REQUIRED
                        )
                },
                returnValueDescription = "订单运单结果",
                restExampleResponseData = "{}",
                example = "暂无"
        )
        @ThriftMethod
        QueryDeliveryOrderResponse queryDeliveryOrderByOrderIdWithEmpty(QueryDeliveryOrderRequest request);

        @MethodDoc(
                displayName = "根据子类型查询异常的订单列表",
                description = "根据子类型查询异常的订单列表",
                parameters = {
                        @ParamDoc(
                                name = "request",
                                description = "根据子类型查询异常订单号的请求",
                                type = QueryDeliveryExceptionOrderBySubTypeRequest.class,
                                requiredness = Requiredness.REQUIRED
                        )
                },
                returnValueDescription = "根据子类型查询异常订单号结果",
                restExampleResponseData = "{}",
                example = "暂无"
        )
        @ThriftMethod
        DeliveryExceptionOrdersBySubTypeResponse queryDeliveryExceptionOrdersBySubType(QueryDeliveryExceptionOrderBySubTypeRequest request);


        @MethodDoc(
                displayName = "分页查询配送异常列表",
                description = "分页查询配送异常列表",
                parameters = {
                        @ParamDoc(
                                name = "request",
                                description = "分页查询配送异常列表",
                                type = QueryDeliveryExceptionOrderBySubTypeRequest.class,
                                requiredness = Requiredness.REQUIRED
                        )
                },
                returnValueDescription = "分页查询配送异常列表",
                restExampleResponseData = "{}",
                example = "暂无"
        )
        @ThriftMethod
        DeliveryExceptionOrdersResponse queryDeliveryExceptionOrdersByStoreIdList(QueryDeliveryExceptionRequest request);

        @MethodDoc(
                displayName = "配送异常总数量",
                description = "配送异常总数量",
                parameters = {
                        @ParamDoc(
                                name = "request",
                                description = "配送异常总数量",
                                type = QueryDeliveryExceptionOrderBySubTypeRequest.class,
                                requiredness = Requiredness.REQUIRED
                        )
                },
                returnValueDescription = "配送异常总数量",
                restExampleResponseData = "{}",
                example = "暂无"
        )
        @ThriftMethod
        DeliveryExceptionOrdersCountResponse queryDeliveryExceptionCounts(QueryDeliveryExceptionCountRequest request);

        @MethodDoc(
                displayName = "查询异常订单子类型数量",
                description = "查询异常订单子类型数量",
                parameters = {
                        @ParamDoc(
                                name = "request",
                                description = "查询配送异常订单的请求",
                                type = QueryDeliveryExceptionOrderRequest.class,
                                requiredness = Requiredness.REQUIRED
                        )
                },
                returnValueDescription = "查询异常订单子类型数量结果",
                restExampleResponseData = "{}",
                example = "暂无"
        )
        @ThriftMethod
        DeliveryExceptionOrderSubTypeCountResponse queryDeliveryExceptionOrderSubTypeCount(QueryDeliveryExceptionOrderRequest request);

        @MethodDoc(
                displayName = "查询运单信息",
                description = "查询运单信息",
                parameters = {
                        @ParamDoc(
                                name = "request",
                                description = "查询运单信息",
                                type = QueryDeliveryInfoRequest.class,
                                requiredness = Requiredness.REQUIRED
                        )
                },
                returnValueDescription = "订单运单结果",
                restExampleResponseData = "{}",
                example = "暂无"
        )
        @ThriftMethod
        QueryDeliveryOrderResponse queryActiveDeliveryOrderByOrderIdList(List<Long> orderList);

        @MethodDoc(
                displayName = "查询可操作项信息",
                description = "查询可操作项信息",
                parameters = {
                        @ParamDoc(
                                name = "request",
                                description = "查询可操作项信息",
                                type = QueryDeliveryOperateItemRequest.class,
                                requiredness = Requiredness.REQUIRED
                        )
                },
                returnValueDescription = "查询可操作项信息",
                restExampleResponseData = "{}",
                example = "暂无"
        )
        @ThriftMethod
        QueryDeliveryOperateItemResponse queryDeliveryOperateItem(QueryDeliveryOperateItemRequest request);

        @MethodDoc(
                displayName = "多门店查询配送异常的订单数量",
                description = "多门店查询配送异常的订单数量",
                parameters = {
                        @ParamDoc(
                                name = "request",
                                description = "多门店查询配送异常的订单数量的请求",
                                type = QueryDeliveryExceptionOrderByStoreIdsRequest.class,
                                requiredness = Requiredness.REQUIRED
                        )
                },
                returnValueDescription = "查多门店查询配送异常的订单数量结果",
                restExampleResponseData = "{}",
                example = "暂无"
        )
        @ThriftMethod
        DeliveryExceptionOrderNumResponse countDeliveryExceptionOrderByStoreIds(QueryDeliveryExceptionOrderByStoreIdsRequest request);

        @MethodDoc(
                displayName = "多门店查询异常订单子类型数量",
                description = "多门店查询异常订单子类型数量",
                parameters = {
                        @ParamDoc(
                                name = "request",
                                description = "多门店查询异常订单子类型数量的请求",
                                type = QueryDeliveryExceptionOrderByStoreIdsRequest.class,
                                requiredness = Requiredness.REQUIRED
                        )
                },
                returnValueDescription = "多门店查询异常订单子类型数量结果",
                restExampleResponseData = "{}",
                example = "暂无"
        )
        @ThriftMethod
        DeliveryExceptionOrderSubTypeCountResponse queryDeliveryExceptionOrderSubTypeCountByStoreIds(QueryDeliveryExceptionOrderByStoreIdsRequest request);

        @MethodDoc(
                displayName = "多门店根据子类型分页查询异常的订单列表",
                description = "多门店根据子类型分页查询异常的订单列表",
                parameters = {
                        @ParamDoc(
                                name = "request",
                                description = "多多门店根据子类型分页查询异常的订单列表的请求",
                                type = QueryDeliveryExceptionOrderBySubTypeAndStoreIdsRequest.class,
                                requiredness = Requiredness.REQUIRED
                        )
                },
                returnValueDescription = "多门店根据子类型分页查询异常的订单列表结果",
                restExampleResponseData = "{}",
                example = "暂无"
        )
        @ThriftMethod
        DeliveryExceptionOrdersBySubTypeAndStoreIdsResponse queryDeliveryExceptionOrdersBySubTypeAndStoreIds(QueryDeliveryExceptionOrderBySubTypeAndStoreIdsRequest request);


        @MethodDoc(
                displayName = "分页查询门店待配送的三方运单",
                description = "分页查询门店待配送的三方运单",
                parameters = {
                        @ParamDoc(
                                name = "request",
                                description = "分页查询门店待配送的三方运单",
                                type = QueryDeliveryExceptionOrderBySubTypeAndStoreIdsRequest.class,
                                requiredness = Requiredness.REQUIRED
                        )
                },
                returnValueDescription = "分页查询门店待配送的三方运单",
                restExampleResponseData = "{}",
                example = "暂无"
        )
        @ThriftMethod
        PageQueryDeliveryInfoResponse pageQueryThirdDeliveryOrderList(@Valid QueryDeliveryByStatusRequest request);

        @MethodDoc(
                displayName = "查询三方配送运单数量",
                description = "查询三方配送运单数量",
                parameters = {
                        @ParamDoc(
                                name = "request",
                                description = "查询三方配送运单数量",
                                type = QueryDeliveryExceptionOrderBySubTypeAndStoreIdsRequest.class,
                                requiredness = Requiredness.REQUIRED
                        )
                },
                returnValueDescription = "查询三方配送运单数量",
                restExampleResponseData = "{}",
                example = "暂无"
        )
        @ThriftMethod
        QueryThirdDeliveryOrderCountResponse queryThirdDeliveryOrderCount(Long tenantId, Long storeId);


        @MethodDoc(
                displayName = "查询配送异常或的运单",
                description = "查询配送异常或的运单",
                parameters = {
                        @ParamDoc(
                                name = "request",
                                description = "查询配送异常运单id请求",
                                type = QueryDeliveryExceptionOrderRequest.class,
                                requiredness = Requiredness.REQUIRED
                        )
                },
                returnValueDescription = "查询配送异常的运单id结果",
                restExampleResponseData = "{}",
                example = "暂无"
        )
        @ThriftMethod
        QueryDeliveryInfoResponse queryDeliveryExceptionOrCanceledOrders(QueryDeliveryExceptionOrderRequest request);

        @MethodDoc(
                displayName = "查询配送异常的运单数量",
                description = "查询配送异常的运单数量",
                parameters = {
                        @ParamDoc(
                                name = "request",
                                description = "查询配送异常运单的请求",
                                type = QueryDeliveryExceptionOrderRequest.class,
                                requiredness = Requiredness.REQUIRED
                        )
                },
                returnValueDescription = "查询配送异常的运单数量结果",
                restExampleResponseData = "{}",
                example = "暂无"
        )
        @ThriftMethod
        DeliveryExceptionOrderNumResponse countDeliveryExceptionOrCanceledOrder(QueryDeliveryExceptionOrderRequest request);


        @MethodDoc(
                displayName = "根据门店和租户查询门店信息",
                description = "根据门店和租户查询门店信息",
                parameters = {
                        @ParamDoc(
                                name = "request",
                                description = "根据门店和租户查询门店信息的请求",
                                type = QueryDeliveryExceptionOrderBySubTypeAndStoreIdsRequest.class,
                                requiredness = Requiredness.REQUIRED
                        )
                },
                returnValueDescription = "根据门店和租户查询门店信息结果",
                restExampleResponseData = "{}",
                example = "暂无"
        )
        @ThriftMethod
        QueryShopDetailResponse queryShopDetail(QueryShopDetailRequest request);


        @MethodDoc(
                displayName = "查询订单配送信息",
                description = "查询订单配送信息",
                parameters = {
                        @ParamDoc(
                                name = "request",
                                description = "查询订单配送信息",
                                type = QueryDeliveryOrderInfoRequest.class,
                                requiredness = Requiredness.REQUIRED
                        )
                },
                returnValueDescription = "订单配送信息结果",
                restExampleResponseData = "{}",
                example = "暂无"
        )
        @ThriftMethod
        QueryDeliveryOrderInfoResponse queryDeliveryInfo(QueryDeliveryOrderInfoRequest req);

        @MethodDoc(
                displayName = "批量查询订单原始配送单号",
                description = "批量查询订单原始配送单号",
                parameters = {
                        @ParamDoc(
                                name = "request",
                                description = "批量查询订单原始配送单号",
                                type = BatchQueryOriginWaybillNoRequest.class,
                                requiredness = Requiredness.REQUIRED
                        )
                },
                returnValueDescription = "批量查询订单原始配送单号结果集",
                restExampleResponseData = "{}",
                example = "暂无"
        )
        @ThriftMethod
        BatchQueryOriginWaybillNoResponse batchQueryOriginWaybillNo(BatchQueryOriginWaybillNoRequest req);


        @MethodDoc(
                displayName = "查询聚合配送跳转信息",
                description = "查询聚合配送跳转信息，批量查询大小限制为50，可动态调节",
                parameters = {
                        @ParamDoc(
                                name = "request",
                                description = "查询聚合配送跳转信息",
                                type = QueryAggDeliveryRedirectModuleRequest.class,
                                requiredness = Requiredness.REQUIRED
                        )
                },
                returnValueDescription = "查询聚合配送跳转信息",
                restExampleResponseData = "{}",
                example = "暂无"
        )
        @ThriftMethod
        QueryAggDeliveryRedirectModuleResponse queryAggDeliveryRedirectModule(QueryAggDeliveryRedirectModuleRequest req);

}
