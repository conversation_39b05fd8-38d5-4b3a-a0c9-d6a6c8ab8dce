package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.common.Status;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.DeliveryOperateItem;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

@TypeDoc(
        description = "查询配送可以操作的按钮项"
)
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
public class QueryDeliveryOperateItemResponse {

    @FieldDoc(
            description = "执行状态",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public Status status;

    @FieldDoc(
            description = "操作列表Map",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(2)
    public Map<Long, DeliveryOperateItem> operateItemMap = new HashMap<>();


}
