package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.request.OuterAggregationLaunchRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.request.OuterDeliveryLaunchRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.request.DeliveryConfigUpdateTimeRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.response.DeliveryLaunchResponse;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.response.DeliveryConfigUpdateTimeResponse;

@InterfaceDoc(
        displayName = "外部操作配送",
        type = "octo.thrift.annotation",
        scenarios = "配送操作服务，提供发起配送等能力",
        description = "配送操作服务，提供发起配送等能力"
)
@ThriftService
public interface OuterDeliveryOperationThriftService {

    @MethodDoc(
            displayName = "发起三方配送",
            description = "覆盖商家手动发起三方配送等场景",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "发起三方配送请求",
                            type = OuterDeliveryLaunchRequest.class,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            returnValueDescription = "发起三方配送操作结果",
            restExampleResponseData = "{\"status\":{\"code\":0, \"msg\":\"\"}}",
            example = "暂无"
    )
    @ThriftMethod
    DeliveryLaunchResponse launchDelivery(OuterDeliveryLaunchRequest request);

    @MethodDoc(
            displayName = "手动发起三方配送",
            description = "手动发起三方配送",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "发起三方配送请求",
                            type = OuterAggregationLaunchRequest.class,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            returnValueDescription = "发起三方配送操作结果",
            restExampleResponseData = "{\"status\":{\"code\":0, \"msg\":\"\"}}",
            example = "暂无"
    )
    @ThriftMethod
    DeliveryLaunchResponse launchAggregationDelivery(OuterAggregationLaunchRequest request);

    @MethodDoc(
            displayName = "手动补自配送运单",
            description = "手动补自配送运单",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "发起三方配送请求",
                            type = OuterAggregationLaunchRequest.class,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            returnValueDescription = "发起三方配送操作结果",
            restExampleResponseData = "{\"status\":{\"code\":0, \"msg\":\"\"}}",
            example = "暂无"
    )
    @ThriftMethod
    DeliveryLaunchResponse launchMerchantSelfDelivery(OuterAggregationLaunchRequest request);

    @MethodDoc(
            displayName = "手动补自配送运单（演示租户生成数据场景使用）",
            description = "手动补自配送运单（演示租户生成数据场景使用）",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "发起三方配送请求",
                            type = OuterAggregationLaunchRequest.class,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            returnValueDescription = "发起三方配送操作结果",
            restExampleResponseData = "{\"status\":{\"code\":0, \"msg\":\"\"}}",
            example = "暂无"
    )
    @ThriftMethod
    DeliveryLaunchResponse launchMerchantSelfDeliveryDemo(OuterAggregationLaunchRequest request);

    @MethodDoc(
            displayName = "获取配送设置更新时间",
            description = "获取配送设置更新时间",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "获取配送设置更新时间",
                            type = OuterAggregationLaunchRequest.class,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            returnValueDescription = "获取配送设置更新时间",
            restExampleResponseData = "{\"status\":{\"code\":0, \"msg\":\"\"}}",
            example = "暂无"
    )
    @ThriftMethod
    DeliveryConfigUpdateTimeResponse queryDeliveryConfigUpdateTime(DeliveryConfigUpdateTimeRequest updateTimeRequest);
}
