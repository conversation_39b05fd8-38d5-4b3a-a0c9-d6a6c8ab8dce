package com.sankuai.meituan.shangou.empower.tms.delivery.client.enums;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/10/22
 */
public enum ConfigStatusEnum {
	DISABLE(0),
	ENABLE(1);

	private final int value;

	private ConfigStatusEnum(int value) {
		this.value = value;
	}

	public int getValue() {
		return value;
	}

	public static ConfigStatusEnum findByValue(int value) {
		for (ConfigStatusEnum each : values()) {
			if (each.getValue() == value) {
				return each;
			}
		}

		return null;
	}
}
