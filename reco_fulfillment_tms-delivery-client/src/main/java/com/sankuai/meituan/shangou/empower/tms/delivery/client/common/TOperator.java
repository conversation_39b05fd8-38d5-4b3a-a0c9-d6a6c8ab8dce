package com.sankuai.meituan.shangou.empower.tms.delivery.client.common;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 操作人 DTO.
 *
 * <AUTHOR>
 * @since 2021/10/11 19:32
 */
@TypeDoc(
        description = "TMS 操作人 DTO",
        authors = {
                "liyang176"
        }
)
@ThriftStruct
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TOperator {

    @FieldDoc(
            description = "账号 ID"
    )
    @ThriftField(1)
    public Long accountId;

    @FieldDoc(
            description = "账号名"
    )
    @ThriftField(2)
    public String accountName;

    @FieldDoc(
            description = "员工 ID"
    )
    @ThriftField(3)
    public Long employeeId;

    public String validate() {
        if (accountId == null || accountId <= 0L) {
            return "账号 ID 无效";
        }
        return null;
    }
}
