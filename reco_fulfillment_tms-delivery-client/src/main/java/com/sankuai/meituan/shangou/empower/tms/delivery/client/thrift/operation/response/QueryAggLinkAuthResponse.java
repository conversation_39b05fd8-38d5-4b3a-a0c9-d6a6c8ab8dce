package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.common.Status;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @date 2025-01-13
 */
@TypeDoc(description = "聚合链路授权响应")
@ThriftStruct
@Data
public class QueryAggLinkAuthResponse {
    @FieldDoc(description = "执行状态")
    @ThriftField(value = 1, requiredness = ThriftField.Requiredness.REQUIRED)
    private Status status= Status.SUCCESS;

    @FieldDoc(description = "聚合链路授权信息")
    @ThriftField(2)
    private AggLinkAuthInfo data;
}
