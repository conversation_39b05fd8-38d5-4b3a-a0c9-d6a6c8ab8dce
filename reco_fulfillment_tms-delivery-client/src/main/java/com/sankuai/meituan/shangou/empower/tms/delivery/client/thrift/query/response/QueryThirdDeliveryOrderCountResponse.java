package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.common.Status;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2023/8/30 15:53
 **/

@ThriftStruct
@AllArgsConstructor
@NoArgsConstructor
public class QueryThirdDeliveryOrderCountResponse {
    @FieldDoc(description = "响应状态")
    public Status status = Status.SUCCESS;

    @FieldDoc(description = "等待平台接单运单数量")
    private Integer waitPlatformAcceptCount;

    @FieldDoc(description = "等待骑手接单运单数量")
    private Integer waitRiderAcceptCount;

    @FieldDoc(description = "骑手已接单运单数量")
    private Integer riderAcceptedCount;

    @FieldDoc(description = "等待骑手到店运单数量")
    private Integer waitRiderArriveShopCount;

    @FieldDoc(description = "骑手配送中运单数量")
    private Integer riderDeliveringCount;


    @ThriftField(1)
    public Status getStatus() {
        return this.status;
    }

    @ThriftField
    public void setStatus(Status status) {
        this.status = status;
    }

    @ThriftField(2)
    public Integer getWaitPlatformAcceptCount() {
        return this.waitPlatformAcceptCount;
    }

    @ThriftField
    public void setWaitPlatformAcceptCount(Integer waitPlatformAcceptCount) {
        this.waitPlatformAcceptCount = waitPlatformAcceptCount;
    }

    @ThriftField(3)
    public Integer getWaitRiderAcceptCount() {
        return this.waitRiderAcceptCount;
    }

    @ThriftField
    public void setWaitRiderAcceptCount(Integer waitRiderAcceptCount) {
        this.waitRiderAcceptCount = waitRiderAcceptCount;
    }

    @ThriftField(4)
    public Integer getRiderAcceptedCount() {
        return this.riderAcceptedCount;
    }

    @ThriftField
    public void setRiderAcceptedCount(Integer riderAcceptedCount) {
        this.riderAcceptedCount = riderAcceptedCount;
    }

    @ThriftField(5)
    public Integer getWaitRiderArriveShopCount() {
        return this.waitRiderArriveShopCount;
    }

    @ThriftField
    public void setWaitRiderArriveShopCount(Integer waitRiderArriveShopCount) {
        this.waitRiderArriveShopCount = waitRiderArriveShopCount;
    }

    @ThriftField(6)
    public Integer getRiderDeliveringCount() {
        return this.riderDeliveringCount;
    }

    @ThriftField
    public void setRiderDeliveringCount(Integer riderDeliveringCount) {
        this.riderDeliveringCount = riderDeliveringCount;
    }

}
