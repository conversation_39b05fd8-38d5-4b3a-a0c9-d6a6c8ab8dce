package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@TypeDoc(
        description = "查询异常运单总数"
)
@AllArgsConstructor
@NoArgsConstructor
@ThriftStruct
@Data
@Builder
public class QueryDeliveryExceptionCountRequest {


    @FieldDoc(
            description = "门店ID集合",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public List<Long> storeIdList;


    @FieldDoc(
            description = "渠道订单ID",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(2)
    public String viewOrderId;


    @FieldDoc(
            description = "租户ID",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(3)
    public Long tenantId;

}
