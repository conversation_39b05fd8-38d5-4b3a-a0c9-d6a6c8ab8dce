package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.response;

import java.util.List;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.common.Status;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 聚合运力平台配置
 *
 * <AUTHOR>
 * @since 2021/04/10
 */
@TypeDoc(
        description = "查询门店配送配置信息",
        authors = {
                "zengping02"
        }
)
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
public class BatchStoreConfigQueryResponse {

    @FieldDoc(
            description = "执行状态",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public Status status;

    @FieldDoc(
            description = "内部门店结果",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(2)
    public List<TStoreConfig> tStoreConfigs;


    public BatchStoreConfigQueryResponse(Status status) {
        this.status = status;
    }
}
