package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

/**
 * TMS-订单标识.
 *
 * <AUTHOR>
 * @since 2021/4/16 10:58
 */
@TypeDoc(
        description = "TMS-订单标识",
        authors = {
                "liyang176"
        }
)
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
public class TOrderIdentifier {

    @FieldDoc(
            description = "订单业务类型Code，参照 com.meituan.shangou.saas.order.platform.enums.OrderBizTypeEnum",
            requiredness = Requiredness.REQUIRED
    )
    private Integer orderBizTypeCode;

    @FieldDoc(
            description = "渠道订单号",
            requiredness = Requiredness.REQUIRED
    )
    private String channelOrderId;

    @FieldDoc(
            description = "运单状态",
            requiredness = Requiredness.OPTIONAL
    )
    private DeliveryStatusEnum deliveryStatus;

    @FieldDoc(
            description = "运单异常类型",
            requiredness = Requiredness.OPTIONAL
    )
    private Integer exceptionTypeCode;

    @FieldDoc(
            description = "运单异常码",
            requiredness = Requiredness.OPTIONAL
    )
    private Integer deliveryExceptionCode;

    @FieldDoc(
            description = "最后操作时间",
            requiredness = Requiredness.OPTIONAL
    )
    private String allowLatestAuditTime;

    @ThriftField(1)
    public Integer getOrderBizTypeCode() {
        return orderBizTypeCode;
    }

    @ThriftField
    public void setOrderBizTypeCode(Integer orderBizTypeCode) {
        this.orderBizTypeCode = orderBizTypeCode;
    }

    @ThriftField(2)
    public String getChannelOrderId() {
        return channelOrderId;
    }

    @ThriftField
    public void setChannelOrderId(String channelOrderId) {
        this.channelOrderId = channelOrderId;
    }

    @ThriftField(3)
    public DeliveryStatusEnum getDeliveryStatus() {
        return deliveryStatus;
    }

    @ThriftField
    public void setDeliveryStatus(DeliveryStatusEnum deliveryStatus) {
        this.deliveryStatus = deliveryStatus;
    }

    @ThriftField(4)
    public Integer getExceptionTypeCode() {
        return exceptionTypeCode;
    }

    @ThriftField
    public void setExceptionTypeCode(Integer exceptionTypeCode) {
        this.exceptionTypeCode = exceptionTypeCode;
    }

    @ThriftField(5)
    public Integer getDeliveryExceptionCode() {
        return deliveryExceptionCode;
    }

    @ThriftField
    public void setDeliveryExceptionCode(Integer deliveryExceptionCode) {
        this.deliveryExceptionCode = deliveryExceptionCode;
    }

    @ThriftField(6)
    public String getAllowLatestAuditTime() {
        return allowLatestAuditTime;
    }

    @ThriftField
    public void setAllowLatestAuditTime(String allowLatestAuditTime) {
        this.allowLatestAuditTime = allowLatestAuditTime;
    }
}
