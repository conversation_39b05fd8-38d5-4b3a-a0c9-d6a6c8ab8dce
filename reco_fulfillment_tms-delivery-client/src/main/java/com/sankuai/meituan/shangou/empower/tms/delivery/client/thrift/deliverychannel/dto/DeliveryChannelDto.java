package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.deliverychannel.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 配送渠道信息
 *
 * <AUTHOR>
 * @since 2023/4/6
 */
@TypeDoc(
        description = "配送渠道信息",
        authors = {
                "zhangjian155"
        }
)
@ThriftStruct
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DeliveryChannelDto {

    @FieldDoc(
            description = "承运商信息"
    )
    @ThriftField(1)
    public String logisticMark;

    @FieldDoc(
            description = "配送平台code"
    )
    @ThriftField(2)
    public Integer deliveryPlatFormCode;

    @FieldDoc(
            description = "牵牛花维护的承运商code"
    )
    @ThriftField(3)
    public Integer carrierCode;

    @FieldDoc(
            description = "牵牛花维护的承运商名称"
    )
    @ThriftField(4)
    public String carrierName;

    @FieldDoc(
            description = "订单渠道code"
    )
    @ThriftField(5)
    public Integer orderChannelCode;
}
