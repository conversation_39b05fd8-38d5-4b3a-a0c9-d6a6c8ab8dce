package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.QueryOrderDeliveryInfoKey;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/1/5
 */
@TypeDoc(
        description = "查询配送信息请求体",
        authors = {
                "钱腾"
        }
)
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
public class QueryActiveDeliveryInfoRequest {

    @FieldDoc(
            description = "赋能统一订单号",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public List<QueryOrderDeliveryInfoKey> orderKeys;

    @FieldDoc(
            description = "是否返回聚合配送详情URL",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(2)
    public boolean needAggUrl = false;

}
