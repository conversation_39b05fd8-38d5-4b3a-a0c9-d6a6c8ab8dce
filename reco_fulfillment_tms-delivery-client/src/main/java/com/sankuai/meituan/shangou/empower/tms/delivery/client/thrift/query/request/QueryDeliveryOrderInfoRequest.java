package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 */
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
public class QueryDeliveryOrderInfoRequest {

    @FieldDoc(
            description = "赋能订单id",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public List<Long> orderIdList;

    public String validate() {
        if (CollectionUtils.isEmpty(orderIdList)) {
            return "赋能门店ID不能为空";
        }
        return null;
    }
}
