package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 聚合运力配送平台配置
 *
 * <AUTHOR>
 * @since 2022/12/14
 */
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
public class DeliveryChannelPlatformDto {

    @FieldDoc(
            description = "平台code",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public Integer platformCode;

    @FieldDoc(
            description = "当前开通状态标识 1-已开通 2-未开通",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(2)
    public Integer openFlag;

    @FieldDoc(
            description = "渠道类型 美团 100，饿了么 200",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(3)
    public Integer channelType;
}
