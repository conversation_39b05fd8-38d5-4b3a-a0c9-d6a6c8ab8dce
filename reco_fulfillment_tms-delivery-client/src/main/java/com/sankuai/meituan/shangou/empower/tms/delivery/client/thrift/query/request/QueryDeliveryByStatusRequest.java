package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import javax.annotation.Nullable;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/1/5
 */
@TypeDoc(
        description = "查询配送信息请求体",
        authors = {
                "钱腾"
        }
)
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
public class QueryDeliveryByStatusRequest {

    @NotNull(message = "租户id不能为空")
    private Long tenantId;

    @NotNull(message = "门店id不能为空")
    private Long storeId;

    @NotEmpty(message = "运单状态不能为空")
    private List<Integer> statusList;

    private Boolean filterException;

    @Min(1)
    private int page;

    @Min(1)
    private int pageSize;


    @ThriftField(1)
    public Long getTenantId() {
        return this.tenantId;
    }

    @ThriftField
    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    @ThriftField(2)
    public Long getStoreId() {
        return this.storeId;
    }

    @ThriftField
    public void setStoreId(Long storeId) {
        this.storeId = storeId;
    }

    @ThriftField(3)
    public List<Integer> getStatusList() {
        return this.statusList;
    }

    @ThriftField
    public void setStatusList(List<Integer> statusList) {
        this.statusList = statusList;
    }

    @ThriftField(4)
    public Boolean getFilterException() {
        return filterException;
    }

    @ThriftField
    public void setFilterException(Boolean filterException) {
        this.filterException = filterException;
    }

    @ThriftField(5)
    public int getPage() {
        return this.page;
    }

    @ThriftField
    public void setPage(int page) {
        this.page = page;
    }

    @ThriftField(6)
    public int getPageSize() {
        return this.pageSize;
    }

    @ThriftField
    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }


}
