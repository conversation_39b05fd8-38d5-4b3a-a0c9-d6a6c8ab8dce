package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.QueryOrderKeyDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-08-08
 */
@TypeDoc(
        description = "批量查询订单原始配送单号请求体",
        authors = {
                "puxiaokang"
        }
)
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
public class BatchQueryOriginWaybillNoRequest {
    @FieldDoc(
            description = "租户id",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public Long tenantId;

    @FieldDoc(
            description = "订单集合",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(2)
    public List<QueryOrderKeyDto> orderList;

}
