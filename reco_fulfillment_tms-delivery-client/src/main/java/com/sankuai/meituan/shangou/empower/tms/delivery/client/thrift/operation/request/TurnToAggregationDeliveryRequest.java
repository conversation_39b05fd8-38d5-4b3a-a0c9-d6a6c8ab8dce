package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@TypeDoc(
        description = "平台配送转三方配送请求体"
)
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
public class TurnToAggregationDeliveryRequest {

    @FieldDoc(
            description = "租户id",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public Long tenantId;

    @FieldDoc(
            description = "门店id",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(2)
    public Long storeId;

    @FieldDoc(
            description = "赋能统一订单号",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(3)
    public Long orderId;

    @FieldDoc(
            description = "操作人id",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(4)
    public Long operatorId;

    @FieldDoc(
            description = "操作人Name",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(5)
    public String operatorName;

    @FieldDoc(
            description = "配送平台id",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(6)
    public Integer deliveryPlatformId;

    @FieldDoc(
            description = "骑手id",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(7)
    public Long newRiderAccountId;

    public String validate() {
        if (tenantId == null || tenantId <= 0L) {
            return "租户id不合法";
        }

        if (storeId == null || storeId <= 0L) {
            return "门店id不合法";
        }

        if (orderId == null || orderId <= 0L) {
            return "赋能订单id不合法";
        }

        if (deliveryPlatformId == null || deliveryPlatformId <= 0) {
            return "配送渠道不合法";
        }

        if (operatorId == null || operatorId <= 0L) {
            return "操作人id不合法";
        }

        return null;
    }

}
