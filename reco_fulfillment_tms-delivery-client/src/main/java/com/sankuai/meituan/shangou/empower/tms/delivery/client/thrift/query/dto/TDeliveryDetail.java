package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/1/5
 */
@TypeDoc(
        description = "配送运单信息",
        authors = {
                "钱腾"
        }
)
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ThriftStruct
public class TDeliveryDetail {

    @FieldDoc(
            description = "赋能统一订单号",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public Long bizOrderId;

    @FieldDoc(
            description = "配送状态",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(2)
    public Integer status;

    @FieldDoc(
            description = "配送运费，单位元",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(4)
    public Double deliveryFee;

    @FieldDoc(
            description = "配送距离",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(5)
    public Long deliveryDistance;

    @FieldDoc(
            description = "配送主体,0-平台配送、1-三方配送、2-商家自行配送",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(6)
    public Integer deliveryEntity;

    @FieldDoc(
            description = "配送承运渠道名称",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(7)
    public String deliveryChannelName;

    @FieldDoc(
            description = "配送骑手姓名",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(8)
    public String riderName;

    @FieldDoc(
            description = "配送骑手电话",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(9)
    public String riderPhone;

    @FieldDoc(
            description = "配送异常",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(10)
    public String deliveryException;

    @FieldDoc(
            description = "异常配送码，提供给上游，解析更具体的异常描述"
    )
    @ThriftField(11)
    public Integer deliveryExceptionCode;

    @FieldDoc(
            description = "配送异常类型"
    )
    @ThriftField(12)
    public Integer deliveryExceptionType;

    @FieldDoc(
            description = "订单可发配送类型",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(14)
    public TLaunchDeliveryType tLaunchDeliveryType;

    @FieldDoc(
            description = "开始等待分配骑手时间",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(15)
    public Long startWaitAssignRiderTime;


    @FieldDoc(
            description = "展示的取消状态，0-无，1-取消中。注意这里和cancelMark的区别，这个值还聚合了一些展示逻辑如判断退款，订单状态等",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(16)
    public Integer displayCancelStatus;

    @FieldDoc(
            description = "配送渠道枚举code",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(17)
    public Integer deliveryChannelCode;

    @FieldDoc(
            description = "运单状态变更时间",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(18)
    public Long deliveryStatusChangeTime;

    @FieldDoc(
            description = "第N次配送",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(19)
    public Integer deliveryCount;

    @FieldDoc(
            description = "创建时间",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(20)
    public Long createTime;

    @FieldDoc(
            description = "转换类型",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(21)
    public Integer transType;

    @FieldDoc(
            description = "小费",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(22)
    public Double tipFee;

    @FieldDoc(
            description = "最后操作时间",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(23)
    public String allowLatestAuditTime;

    @FieldDoc(
            description = "渠道订单id",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(24)
    public String channelOrderId;

    @FieldDoc(
            description = "订单类型",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(25)
    public Integer orderBizType;

    @FieldDoc(
            description = "配送平台id",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(26)
    public Integer platformCode;

    @FieldDoc(
            description = "配送平台描述",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(27)
    public String platformDesc;

        @FieldDoc(
            description = "履约单号",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(28)
    public Long fulfillOrderId;

    @FieldDoc(
            description = "平台来源",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(29)
    public Integer platformSource;

    @FieldDoc(
            description = "代收点",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(30)
    public String signPosition;

    @FieldDoc(
            description = "考核配送时间",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(31)
    public Long assessDeliveryTime;

    @FieldDoc(
            description = "原始物流运单列表",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(32)
    public List<OriginWaybillDto> waybillList;

    @FieldDoc(
            description = "是否四轮配送 1是 0否",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(33)
    public Integer isFourWheelDelivery;

}
