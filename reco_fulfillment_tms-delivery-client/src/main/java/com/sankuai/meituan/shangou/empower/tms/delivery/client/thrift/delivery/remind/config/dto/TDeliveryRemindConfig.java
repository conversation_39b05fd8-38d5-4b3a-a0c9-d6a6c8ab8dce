package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.delivery.remind.config.dto;

import java.util.List;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 配送提醒配置.
 *
 * <AUTHOR>
 * @since 2021/10/8 15:01
 */
@TypeDoc(
        description = "配送提醒配置",
        authors = {
                "liyang176"
        }
)
@ThriftStruct
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TDeliveryRemindConfig {

    @FieldDoc(
            description = "租户 ID"
    )
    @ThriftField(1)
    public Long tenantId;

    @FieldDoc(
            description = "门店 ID"
    )
    @ThriftField(2)
    public Long storeId;

    @FieldDoc(
            description = "提醒人列表"
    )
    @ThriftField(3)
    public List<String> recipients;

    @FieldDoc(
            description = "歪马提醒人列表"
    )
    @ThriftField(4)
    public List<String> dhRecipients;
}
