package com.sankuai.meituan.shangou.empower.tms.delivery.client.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

import java.util.HashMap;
import java.util.Map;

import static com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum.*;

/**
 * 配送渠道枚举
 * <p>
 * 00000 无
 * <p>
 * <p>
 * <p>
 * 10001 顺丰 10002 达达 10003 闪送 10004 蜂鸟 10005 UU跑腿 10006 快跑者 10007 极客快送 10008 点我达 10009 同达 10010 生活半径 10011 邻趣 10012 趣送 10013 快服务 10014 菜鸟新配盟 10015 商家自建配送 10016 风先生 10017 其他
 * 100000 美团 100001 UU跑腿 100002 裹小递 100003 闪送 100004 测试-某一个测试承运商 100005 顺丰同城 100006 快服务 100007 达达快送 100008 曹操跑腿 100009 爱跑腿 100010 快跑者 100011 大有配送 100012 送个东西 100013 靠谱送 100014 懒猪快送 100015 好急 100016 美团配送 100017 快狗打车 100018 快男跑腿 100019 上海食派士商贸发展有限公司 100020 美团跑腿
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/3
 */
@SuppressWarnings("SpellCheckingInspection")
public enum DeliveryChannelEnum {

	/* --------- 订单平台配送 --------- */
	ORDER_PLATFORM_DELIVERY(1, "平台配送", null),


	/* --------- 商家自行配送 --------- */
	MERCHANT_DELIVERY(2, "自营配送", MERCHANT_SELF_DELIVERY),


	/* --------- 自建平台 --------- */
	HAI_KUI_DELIVERY(900, "美团海葵", SELF_BUILT_DELIVERY_PLATFORM),
	FENG_NIAO_DELIVERY(800, "蜂鸟即配", SELF_BUILT_DELIVERY_PLATFORM),


	/* --------- 聚合运力平台 --------- */
	AGGREGATION_DELIVERY(-1, "聚合运力配送", AGGREGATION_DELIVERY_PLATFORM),
	AGGREGATE_DELIVERY_MEITUAN_TUAN(10000, "美团海葵", AGGREGATION_DELIVERY_PLATFORM),
	AGGREGATE_DELIVERY_SHUNFENG(10001, "顺丰同城", AGGREGATION_DELIVERY_PLATFORM),
	AGGREGATE_DELIVERY_DADA(10002, "达达快送", AGGREGATION_DELIVERY_PLATFORM),
	AGGREGATE_DELIVERY_FENGNIAO(10004, "蜂鸟即配", AGGREGATION_DELIVERY_PLATFORM),


	/* --------- 麦芽田平台 --------- */
	MALT_FARM(-2, "麦芽田配送", MALT_FARM_DELIVERY_PLATFORM),
	FARM_DELIVERY_MEITUAN(20000, "美团配送", MALT_FARM_DELIVERY_PLATFORM),
	FARM_DELIVERY_SHUNFENG(20001, "顺丰同城", MALT_FARM_DELIVERY_PLATFORM),
	FARM_DELIVERY_DADA(20002, "达达快送", MALT_FARM_DELIVERY_PLATFORM),
	FARM_DELIVERY_SHANSONG(20003, "闪送", MALT_FARM_DELIVERY_PLATFORM),
	FARM_DELIVERY_UU(20004, "UU跑腿", MALT_FARM_DELIVERY_PLATFORM),
	FARM_DELIVERY_DIANWODA(20005, "点我达", MALT_FARM_DELIVERY_PLATFORM),
	FARM_DELIVERY_FENGNIAO(20006, "蜂鸟跑腿", MALT_FARM_DELIVERY_PLATFORM),
	FARM_DELIVERY_FENGNIAO_KA(20023, "蜂鸟配送", MALT_FARM_DELIVERY_PLATFORM),
	FARM_DELIVERY_PAO_TUI(20024, "美团跑腿", MALT_FARM_DELIVERY_PLATFORM),
	FARM_DELIVERY_365(20025, "365跑腿", MALT_FARM_DELIVERY_PLATFORM),
	FARM_DELIVERY_GUOXIAODI(20026, "裹小递", MALT_FARM_DELIVERY_PLATFORM),
	FARM_DELIVERY_CAOSONG(20027, "曹操送", MALT_FARM_DELIVERY_PLATFORM),
	FARM_DELIVERY_KELOOP(20028, "快跑者", MALT_FARM_DELIVERY_PLATFORM),
	FARM_DELIVERY_BANGLA(20029, "帮啦跑腿", MALT_FARM_DELIVERY_PLATFORM),
	FARM_DELIVERY_CAOCAO(20030,"曹操跑腿",MALT_FARM_DELIVERY_PLATFORM),
	FARM_DELIVERY_FUWU(20031,"快服务",MALT_FARM_DELIVERY_PLATFORM),
	FARM_DELIVERY_IPAOTUI(20032,"爱跑腿",MALT_FARM_DELIVERY_PLATFORM),
	FARM_DELIVERY_WUKONG(20033,"悟空快跑",MALT_FARM_DELIVERY_PLATFORM),
	FARM_DELIVERY_HAOJI(20034,"好急",MALT_FARM_DELIVERY_PLATFORM),
	FARM_DELIVERY_MTBS(20035,"美团跑腿帮送",MALT_FARM_DELIVERY_PLATFORM),
	FARM_DELIVERY_SHUNFENGC(20036,"顺丰C",MALT_FARM_DELIVERY_PLATFORM),
	FARM_DELIVERY_LAIDA(20037, "来答配送", MALT_FARM_DELIVERY_PLATFORM),
	FARM_DELIVERY_SONGDONGXI(20038, "送个东西", MALT_FARM_DELIVERY_PLATFORM),
	FARM_DELIVERY_ZHONGLIBAN(20039, "中里办", MALT_FARM_DELIVERY_PLATFORM),
	FARM_DELIVERY_MERCHANT(29998, "商家自配", MALT_FARM_DELIVERY_PLATFORM),
	FARM_DELIVERY_UNKNOW(29999, "麦芽田-未知配送商", MALT_FARM_DELIVERY_PLATFORM),

	DAP_DELIVERY(-4,"青云聚信平台",DAP_DELIVERY_PLATFORM),
	DAP_DELIVERY_PAO_TUI(40000, "美团跑腿", DAP_DELIVERY_PLATFORM),
	DAP_DELIVERY_SHUNFENG(40001, "顺丰同城", DAP_DELIVERY_PLATFORM),
	DAP_DELIVERY_DADA(40002, "达达快送", DAP_DELIVERY_PLATFORM),
	DAP_DELIVERY_SHANSONG(40003, "闪送", DAP_DELIVERY_PLATFORM),
	DAP_DELIVERY_GUOXIAODI(40004, "裹小递", DAP_DELIVERY_PLATFORM),
	DAP_DELIVERY_UU(40005, "UU跑腿", DAP_DELIVERY_PLATFORM),
	DAP_DELIVERY_MEI_TUAN(40006, "美团跑腿零售", DAP_DELIVERY_PLATFORM),
	DAP_DELIVERY_SH_SHIPAIS(40007, "上海食派士商贸发展有限公司", DAP_DELIVERY_PLATFORM),
	DAP_DELIVERY_FUWU(40008, "快服务", DAP_DELIVERY_PLATFORM),
	DAP_DELIVERY_CAO_CAO(40009, "曹操跑腿", DAP_DELIVERY_PLATFORM),
	DAP_DELIVERY_IPAOTUI(40010, "爱跑腿", DAP_DELIVERY_PLATFORM),
	DAP_DELIVERY_QUICK_RUNNER(40011, "快跑者", DAP_DELIVERY_PLATFORM),
	DAP_DELIVERY_BIG(40012, "大有配送", DAP_DELIVERY_PLATFORM),
	DAP_DELIVERY_SEND_SOMETHING(40013, "送个东西", DAP_DELIVERY_PLATFORM),
	DAP_DELIVERY_KAO_PU(40014, "靠谱送", DAP_DELIVERY_PLATFORM),
	DAP_DELIVERY_LAZY_PIG(40015, "懒猪快送", DAP_DELIVERY_PLATFORM),
	DAP_DELIVERY_HAOJI(40016, "好急", DAP_DELIVERY_PLATFORM),
	DAP_DELIVERY_MEITUAN(40017, "美团配送", DAP_DELIVERY_PLATFORM),
	DAP_DELIVERY_FAST_DOG_TAXI(40018, "快狗打车", DAP_DELIVERY_PLATFORM),
	DAP_DELIVERY_FAST_MAN(40019, "快男跑腿", DAP_DELIVERY_PLATFORM),
	DAP_DELIVERY_NA_DOU_DA(40020, "哪都达", DAP_DELIVERY_PLATFORM),
	DAP_DELIVERY_UNKNOW(49999, "青云聚信平台-未知配送商", DAP_DELIVERY_PLATFORM),

	/* --------- 有赞平台配送 --------- */
	ORDER_CHANNEL_DELIVERY(-5, "平台配送", ORDER_CHANNEL_DELIVERY_PLATFORM),
	ORDER_CHANNEL_DELIVERY_MEITUAN(50000, "美团配送", ORDER_CHANNEL_DELIVERY_PLATFORM),
	ORDER_CHANNEL_DELIVERY_DADA(50001, "达达", ORDER_CHANNEL_DELIVERY_PLATFORM),
	ORDER_CHANNEL_DELIVERY_FENGNIAO(50002, "蜂鸟", ORDER_CHANNEL_DELIVERY_PLATFORM),
	ORDER_CHANNEL_DELIVERY_DIANWODA(50003, "点我达", ORDER_CHANNEL_DELIVERY_PLATFORM),
	ORDER_CHANNEL_DELIVERY_SHUNFENG(50004, "顺丰同城", ORDER_CHANNEL_DELIVERY_PLATFORM),
	ORDER_CHANNEL_DELIVERY_TCSY(50005, "同城上云", ORDER_CHANNEL_DELIVERY_PLATFORM),
	ORDER_CHANNEL_DELIVERY_UNKNOW(59999, "平台配送-未知配送商", ORDER_CHANNEL_DELIVERY_PLATFORM),

	/* --------- 订单自配送 --------- */
	OTHER_SELF_DELIVERY(-6, "订单自配送", OTHER_SELF_DELIVERY_PLATFORM),
	OTHER_SELF_DELIVERY_MNG(60000, "非牵牛花管理配送", OTHER_SELF_DELIVERY_PLATFORM)
	;


	private static final Map<Integer, DeliveryChannelEnum> CODE_TO_ENUM_MAP = new HashMap<>();
	private final int code;
	private final String name;
	private final DeliveryPlatformEnum deliveryPlatform;

	static {
		for (DeliveryChannelEnum each : values()) {
			CODE_TO_ENUM_MAP.put(each.getCode(), each);
		}
	}

	DeliveryChannelEnum(int code, String name, DeliveryPlatformEnum deliveryPlatform) {
		this.name = name;
		this.code = code;
		this.deliveryPlatform = deliveryPlatform;
	}

	@JsonValue
	public int getCode() {
		return code;
	}

	public String getName() {
		return name;
	}

	public DeliveryPlatformEnum getDeliveryPlatform() {
		return deliveryPlatform;
	}

	public boolean isSelfBuiltDelivery() {
		return deliveryPlatform == SELF_BUILT_DELIVERY_PLATFORM;
	}

	public boolean isAggregationDelivery() {
		return deliveryPlatform == AGGREGATION_DELIVERY_PLATFORM;
	}

	public boolean isMaltFarmDelivery() {
		return deliveryPlatform == MALT_FARM_DELIVERY_PLATFORM;
	}

	public boolean isMerchantSelfDelivery() {
		return deliveryPlatform == MERCHANT_SELF_DELIVERY;
	}

	public boolean isDapDelivery() {
		return deliveryPlatform == DAP_DELIVERY_PLATFORM;
	}

	@JsonCreator
	public static DeliveryChannelEnum valueOf(int code) {
		return CODE_TO_ENUM_MAP.get(code);
	}
}
