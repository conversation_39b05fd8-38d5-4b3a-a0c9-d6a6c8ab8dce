package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.common.Status;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @date 2025-01-09
 */
@TypeDoc(
        description = "查询聚合配送门店设置链接响应"
)
@ThriftStruct
@Data
public class QueryAggLinkResponse {

    @FieldDoc(
            description = "执行状态",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    private Status status = Status.SUCCESS;

    @FieldDoc(
            description = "执行状态",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(2)
    private String url;

    @FieldDoc(
            description = "标题",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(3)
    private String title;

    @FieldDoc(
            description = "文案",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(4)
    private String urlText;
}
