package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.ExtensionDoc;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.dto.response.TmsResponse;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.request.orderplatform.AuditExceptionReq;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.request.orderplatform.CallDeliveryReq;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.request.orderplatform.CancelDeliveryReq;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.request.orderplatform.ReportExceptionReq;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.request.orderplatform.UpdateTipReq;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.response.DeliveryOperationResultDto;
import org.apache.thrift.TException;

/**
 * 订单平台配送服务
 *
 * <AUTHOR>
 * @since 2023/3/1
 */
@InterfaceDoc(
        displayName = "订单平台配送操作服务",
        type = "octo.thrift.annotation",
        scenarios = "订单平台配送操作服务",
        description = "订单平台配送操作服务"
)
@ThriftService
public interface OrderPlatformDeliveryOperateThriftService {

    @MethodDoc(
            displayName = "更新小费",
            description = "更新小费",
            parameters = {
                    @ParamDoc(name = "req", description = "请求参数")
            },
            returnValueDescription = "验签结果",
            extensions = {
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "无须鉴权")
            }
    )
    @ThriftMethod
    TmsResponse<DeliveryOperationResultDto> updateTip(UpdateTipReq req) throws TException;

    @MethodDoc(
            displayName = "取消平台配送",
            description = "取消平台配送",
            parameters = {
                    @ParamDoc(name = "req", description = "请求参数")
            },
            returnValueDescription = "验签结果",
            extensions = {
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "无须鉴权")
            }
    )

    @ThriftMethod
    TmsResponse<DeliveryOperationResultDto> cancelDelivery(CancelDeliveryReq req) throws TException;

    @MethodDoc(
            displayName = "再次呼叫",
            description = "再次呼叫",
            parameters = {
                    @ParamDoc(name = "req", description = "请求参数")
            },
            returnValueDescription = "验签结果",
            extensions = {
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "无须鉴权")
            }
    )
    @ThriftMethod
    TmsResponse<DeliveryOperationResultDto> callDelivery(CallDeliveryReq req) throws TException;

    @MethodDoc(
            displayName = "上报异常",
            description = "上报异常",
            parameters = {
                    @ParamDoc(name = "req", description = "请求参数")
            },
            returnValueDescription = "验签结果",
            extensions = {
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "无须鉴权")
            }
    )
    @ThriftMethod
    TmsResponse<DeliveryOperationResultDto> reportException(ReportExceptionReq req) throws TException;

    @MethodDoc(
            displayName = "审核异常",
            description = "审核异常",
            parameters = {
                    @ParamDoc(name = "req", description = "请求参数")
            },
            returnValueDescription = "验签结果",
            extensions = {
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "无须鉴权")
            }
    )
    @ThriftMethod
    TmsResponse<DeliveryOperationResultDto> auditException(AuditExceptionReq req) throws TException;

}
