package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.callback.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * 回调取消配送结果请求体
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/1/7
 */
@TypeDoc(
		description = "回调取消配送结果请求体",
		authors = {
				"hedong07"
		}
)
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
@SuppressWarnings("DuplicatedCode")
public class DeliveryCancelResultNotifyRequest {

	@FieldDoc(
			description = "闪购赋能订单号",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(1)
	public String orderId;

	@FieldDoc(
			description = "闪购中台门店id",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(2)
	public String storeId;

	@FieldDoc(
			description = "包裹id",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(3)
	public Long pkgId;

	@FieldDoc(
			description = "取消是否成功",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(4)
	public Boolean isCancelSuccess;

	@FieldDoc(
			description = "取消失败原因code",
			requiredness = Requiredness.OPTIONAL
	)
	@ThriftField(5)
	public Integer cancelFailReasonCode;

	@FieldDoc(
			description = "取消失败原因描述",
			requiredness = Requiredness.OPTIONAL
	)
	@ThriftField(6)
	public String cancelFailReasonDesc;

	public String validate() {
		if (!StringUtils.isNumeric(orderId)) {
			return "闪购赋能订单号非法";
		}

		if (!StringUtils.isNumeric(storeId)) {
			return "闪购中台门店id非法";
		}

		if (pkgId == null) {
			return "包裹id不能为空";
		}

		if (isCancelSuccess == null) {
			return "取消是否成功不能为空";
		}

		if (!isCancelSuccess && cancelFailReasonCode == null) {
			return "取消失败时，取消失败原因code不能为空";
		}

		if (!isCancelSuccess && StringUtils.isEmpty(cancelFailReasonDesc)) {
			return "取消失败时，取消失败原因描述不能为空";
		}

		return null;
	}

}
