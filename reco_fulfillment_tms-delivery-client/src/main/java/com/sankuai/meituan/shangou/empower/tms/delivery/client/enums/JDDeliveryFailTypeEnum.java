package com.sankuai.meituan.shangou.empower.tms.delivery.client.enums;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.*;

public enum JDDeliveryFailTypeEnum {

    FACTORY_DELIVERY_REJECT("1","厂家直送拒收"),
    FACTORY_DELIVERY_RETURN_GOODS("2","厂家直送需退发货"),
    FACTORY_DELIVERY_RETURNED_GOODS("3","厂家直送已退发货"),
    WAIT_STORE_RECEIVE("10","等待商家确认收货"),
    STORE_PRODUCT_LACK("GTF_1","门店商品缺货"),
    STORE_DONT_PICK("GTF_2","商家未拣货"),
    STORE_DONT_OPEN("GTF_5","门店未营业"),
    WAIT_TO_MUCH("GTF_6","等待时间长"),
    MALICE_ORDER("GTF_7","恶意订单"),
    CANCEL_BY_DADA("GTF_8","达达客服取消"),
    DELIVERY_OUT_OF_DISTANCE("DTF_1","配送距离超过5公里"),
    CALL_USER_FAIL("DTF_2","联系收货人失败超过3次"),
    USER_CHANGE_TIME("DTF_3","客户要求改期配送"),
    USER_REJECT_WITHOUT_REASON("DTF_4","客户无理由拒收"),
    PRODUCT_QUALITY_PROBLEM("DTF_5","质量问题"),
    LACK_PRODUCT("DTF_6","缺件/少件"),
    USER_REJECT_BY_TIMEOUT("DTF_7","订单超时客户拒收"),
    OTHER("DTF_8","其他"),
    MALICE_BUY_ORDER("DTF_9","恶意刷单"),
    USER_CHANGE_ADDRESS("DTF_10","客户更改配送地址"),
    PACKAGE_NON_STANDARD("20066","包装不规范/雪糕类无保温袋"),

    ;

    private String code;
    private String desc;

    JDDeliveryFailTypeEnum(String code,String desc){
        this.code=code;
        this.desc=desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static JDDeliveryFailTypeEnum toEnum(String code){
        if(StringUtils.isEmpty(code)){
            return null;
        }
        for (JDDeliveryFailTypeEnum typeEnum : values()){
            if(typeEnum.getCode().equals(code)){
                return typeEnum;
            }
        }
        return null;
    }

    public static List<JDDeliveryFailTypeEnum> batchToEnum(List<String> codeList){
        if(CollectionUtils.isEmpty(codeList)){
            return Collections.emptyList();
        }
        List<JDDeliveryFailTypeEnum> enumList = new ArrayList<>();
        for (JDDeliveryFailTypeEnum typeEnum : values()){
            if(codeList.contains(typeEnum.getCode())){
                enumList.add(typeEnum);
            }
        }
        return enumList;
    }
}
