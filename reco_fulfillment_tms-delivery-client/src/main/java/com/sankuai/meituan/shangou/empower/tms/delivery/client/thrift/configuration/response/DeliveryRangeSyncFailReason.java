package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/11/20
 */
@TypeDoc(
		description = "配送范围同步失败原因",
		authors = {
				"hedong07"
		}
)
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
public class DeliveryRangeSyncFailReason {

	@FieldDoc(
			description = "订单渠道id",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(1)
	public Integer channelId;

	@FieldDoc(
			description = "失败原因",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(2)
	public String failReason;
}
