package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@TypeDoc(
        description = "查询配送操作请求体"
)
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
public class QueryDeliveryOperateItemRequest {

    @FieldDoc(
            description = "租户id",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(1)
    public Long tenantId;

    @FieldDoc(
            description = "门店id",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(2)
    public Long storeId;

    @FieldDoc(
            description = "赋能订单idList",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(3)
    public List<Long> orderIdList;

}
