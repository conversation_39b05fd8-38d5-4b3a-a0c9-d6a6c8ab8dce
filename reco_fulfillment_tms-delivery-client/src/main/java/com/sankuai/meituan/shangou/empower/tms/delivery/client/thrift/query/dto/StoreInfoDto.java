package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 开放平台查询骑手轨迹静态信息，门店相关信息
 * <AUTHOR>
 */
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
public class StoreInfoDto {

    @FieldDoc(
            description = "门店坐标",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(1)
    public CoordinateDto storeCoordinate;

}
