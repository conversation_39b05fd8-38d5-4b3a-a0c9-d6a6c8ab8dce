package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.delivery.remind.config.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 查询门店超时提醒配置的请求体.
 *
 * <AUTHOR>
 * @since 2021/10/8 14:46
 */
@TypeDoc(
        description = "查询门店超时提醒配置的请求体",
        authors = {
                "liyang176"
        }
)
@ThriftStruct
@Data
@NoArgsConstructor
@AllArgsConstructor
public class QueryStoreRemindConfigRequest {

    @FieldDoc(
            description = "租户 ID"
    )
    @ThriftField(1)
    public Long tenantId;

    @FieldDoc(
            description = "门店 ID"
    )
    @ThriftField(2)
    public Long storeId;

    public String validate() {
        if (tenantId == null || tenantId <= 0) {
            return "租户 ID 无效";
        }
        if (storeId == null || storeId <= 0) {
            return "门店 ID 无效";
        }
        return null;
    }

}
