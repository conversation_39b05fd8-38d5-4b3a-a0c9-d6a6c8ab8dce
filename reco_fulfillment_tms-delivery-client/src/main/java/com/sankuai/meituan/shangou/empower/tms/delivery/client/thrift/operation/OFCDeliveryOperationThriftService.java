package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.request.OFCDeliveryCancelRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.response.DeliveryCancelResponse;

@InterfaceDoc(
        displayName = "OFC操作配送",
        type = "octo.thrift.annotation",
        scenarios = "配送操作服务，提供发起配送等能力",
        description = "配送操作服务，提供发起配送等能力"
)
@ThriftService
public interface OFCDeliveryOperationThriftService {

    @MethodDoc(
            displayName = "取消三方配送",
            description = "取消三方配送",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "取消三方配送请求",
                            type = OFCDeliveryCancelRequest.class,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            returnValueDescription = "取消三方配送操作结果",
            restExampleResponseData = "{\"status\":{\"code\":0, \"msg\":\"\"}}",
            example = "暂无"
    )
    @ThriftMethod
    DeliveryCancelResponse cancelDelivery(OFCDeliveryCancelRequest request);

}
