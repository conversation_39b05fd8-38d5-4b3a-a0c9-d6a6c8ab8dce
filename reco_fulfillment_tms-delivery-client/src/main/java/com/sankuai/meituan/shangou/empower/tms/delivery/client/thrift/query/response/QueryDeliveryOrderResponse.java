package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.common.Status;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.TDeliveryDetail;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.TDeliveryOrder;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.assertj.core.util.Lists;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/5/17
 */
@TypeDoc(
	description = "查询运单信息响应体",
	authors = {
		"zhangyusen"
	}
)
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
public class QueryDeliveryOrderResponse {

	@FieldDoc(
		description = "执行状态",
		requiredness = Requiredness.REQUIRED
	)
	@ThriftField(1)
	public Status status;

	@FieldDoc(
		description = "运单信息",
		requiredness = Requiredness.REQUIRED
	)
	@ThriftField(2)
	public List<TDeliveryOrder> TDeliveryOrders;

	public static QueryDeliveryOrderResponse buildFailResponse(int code, String msg) {
		QueryDeliveryOrderResponse response = new QueryDeliveryOrderResponse();
		response.setTDeliveryOrders(new ArrayList<TDeliveryOrder>());
		response.setStatus(new Status(code, msg));
		return response;
	}
}
