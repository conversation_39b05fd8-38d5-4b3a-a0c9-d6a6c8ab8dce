package com.sankuai.meituan.shangou.empower.tms.delivery.client.enums;

import lombok.Getter;

@Getter
public enum DeliveryAsyncOutTypeEnum {

    LOCK_DELIVERY_STATUS(1, "锁定运单状态"),
    UNLOCK_DELIVERY_STATUS(2, "解锁运单状态"),
    REPORT_EXCEPTION(3, "骑手上报异常"),
    DELIVER_COMPLETE(4, "配送完成"),
    DELIVERY_ACCEPT_OR_RIDER_CHANGE(5, "配送骑手变更"),

    DELIVERY_CANCEL(6, "配送取消"),

    RIDER_POST_DELIVERY_PROOF_PHOTO(7, "骑手上报送达图片"),

    TRANS_TO_THIRD_PART_DELIVERY(8, "转三方配送"),

    TRANS_TO_SELF_DELIVERY(9, "转自配"),

    PICK_DELIVERY_SPLIT(10, "拣配分离"),

    TAKE_AWAY(11, "骑手取货"),

    DELIVERY_DONE_BY_ORDER_FINISH(12, "订单驱动的配送完成"),

    THIRD_PART_DELIVERY_ORDER_CHANGE(13, "三方配送运单变化"),

    PICK_DONE_IN_PICK_DELIVERY_SPLIT(14, "拣配分离模式下拣货完成")
    ;

    private int value;
    private String desc;


    DeliveryAsyncOutTypeEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static DeliveryAsyncOutTypeEnum getInstance(int value) {
        for (DeliveryAsyncOutTypeEnum type : DeliveryAsyncOutTypeEnum.values()) {
            if (type.getValue() == value) {
                return type;
            }
        }
        return null;
    }
}
