package com.sankuai.meituan.shangou.empower.tms.delivery.client.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

import java.util.HashMap;
import java.util.Map;

/**
 * 配送策略枚举
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/3
 */
public enum DeliveryStrategyEnum {

	/**
	 * 指定顺序轮询
	 */
	SEQUENTIAL_POLLING(1),

	/**
	 * 多平台抢单, 暂未实现
	 */
	MULTI_PLATFORM_GRABBING(2);

	private static final Map<Integer, DeliveryStrategyEnum> CODE_ENUM_MAP = new HashMap<>();
	private final int code;

	static {
		for (DeliveryStrategyEnum each : values()) {
			CODE_ENUM_MAP.put(each.getCode(), each);
		}
	}

	DeliveryStrategyEnum(int code) {
		this.code = code;
	}

	@JsonValue
	public int getCode() {
		return code;
	}

	@JsonCreator
	public static DeliveryStrategyEnum valueOf(Integer code) {
		return CODE_ENUM_MAP.get(code);
	}
}
