package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryExceptionSubTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.CollectionUtils;

import java.util.List;

@TypeDoc(
        description = "多门店根据子类型分页查询异常订单列表的请求",
        authors = {
                "zhangjian155"
        }
)
@AllArgsConstructor
@NoArgsConstructor
@ThriftStruct
@Data
@Builder
public class QueryDeliveryExceptionOrderBySubTypeAndStoreIdsRequest {

    @FieldDoc(
            description = "子类型，0全部/10未接单/20未到店/30未取货/40配送超时/50系统异常",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public Integer subType;

    @FieldDoc(
            description = "赋能门店ID列表",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(2)
    public List<Long> empowerStoreIds;

    @FieldDoc(
            description = "页码",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(3)
    public Integer pageNum;

    @FieldDoc(
            description = "分页大小",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(4)
    public Integer pageSize;

    public String validate() {
        if (subType == null || DeliveryExceptionSubTypeEnum.valueOf(subType) == null) {
            return "子类型不合法";
        }
        if (CollectionUtils.isEmpty(empowerStoreIds)) {
            return "赋能门店ID列表不能为空";
        }
        if (null == pageNum) {
            return "页码不能为空";
        }
        if (null == pageSize) {
            return "分页大小不能为空";
        }
        return null;
    }
}
