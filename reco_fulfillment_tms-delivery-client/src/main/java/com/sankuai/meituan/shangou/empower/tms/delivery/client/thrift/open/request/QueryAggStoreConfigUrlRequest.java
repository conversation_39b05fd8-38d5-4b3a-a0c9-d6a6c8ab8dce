package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.open.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.Assert;

/**
 * 根据聚合配送平台承运商信息查询配送渠道信息请求体
 *
 * <AUTHOR>
 * @date 2023/4/6
 */
@TypeDoc(
		description = "根据聚合配送平台承运商信息查询配送渠道信息请求体",
		authors = {
				"zhangjian155"
		}
)
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
public class QueryAggStoreConfigUrlRequest {

	@FieldDoc(
			description = "门店id",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(1)
	public Long tenantId;

	@FieldDoc(
			description = "门店id",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(2)
	public Long poiId;

	@FieldDoc(
			description = "订单渠道code",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(3)
	public Integer deliveryPlatform;


	@FieldDoc(
			description = "token",
			requiredness = Requiredness.OPTIONAL
	)
	@ThriftField(4)
	private String token;

	@FieldDoc(
			description = "端类型： 参考 SiteTypeEnum",
			requiredness = Requiredness.OPTIONAL
	)
	@ThriftField(5)
	private String deviceType;

	public void validate() {
		if (tenantId == null || tenantId <= 0L) {
			throw new IllegalArgumentException("租户id不合法") ;
		}

		if (poiId == null || poiId <= 0L) {
			throw new IllegalArgumentException("门店id不合法");
		}

		if (deliveryPlatform == null || deliveryPlatform <= 0L) {
			throw new IllegalArgumentException("配送平台不合法");
		}

	}

}
