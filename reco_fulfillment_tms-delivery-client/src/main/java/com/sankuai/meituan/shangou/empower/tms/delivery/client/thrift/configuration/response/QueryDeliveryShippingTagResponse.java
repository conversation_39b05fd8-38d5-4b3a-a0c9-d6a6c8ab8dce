package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.common.Status;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/3/7 16:29
 **/
@ThriftStruct
@Data
@AllArgsConstructor
@NoArgsConstructor
public class QueryDeliveryShippingTagResponse {
    @ThriftField(1)
    private Status status;

    @ThriftField(2)
    /**
     * @see com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.ShippingTagEnum
     */
    private List<Integer> shippingTagList;

}
