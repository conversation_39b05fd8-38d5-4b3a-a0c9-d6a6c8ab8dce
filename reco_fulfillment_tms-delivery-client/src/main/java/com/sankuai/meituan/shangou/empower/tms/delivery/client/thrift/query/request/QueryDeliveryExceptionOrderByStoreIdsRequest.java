package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 多门店查询配送异常订单数量的请求.
 *
 * <AUTHOR>
 * @since 2023/7/12 17:48
 */
@TypeDoc(
        description = "多门店查询配送异常订单数量的请求",
        authors = {
                "zhangjian155"
        }
)
@AllArgsConstructor
@NoArgsConstructor
@Data
@ThriftStruct
public class QueryDeliveryExceptionOrderByStoreIdsRequest {

    @FieldDoc(
            description = "赋能订单id列表",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    private List<Long> empowerStoreIds;

    public String validate() {
        if (CollectionUtils.isEmpty(empowerStoreIds)) {
            return "赋能门店ID列表不能为空";
        }
        return null;
    }
}
