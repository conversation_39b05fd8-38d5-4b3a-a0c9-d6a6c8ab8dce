package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
public class DeliveryConfigUpdateTimeDto {
    @FieldDoc(
            description = "更新时间，秒纬度单位",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(1)
    public Long updateTime;
}
