package com.sankuai.meituan.shangou.empower.tms.delivery.client.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @description
 * @date 2024-11-08
 */
@Getter
public enum DapCategoryEnum {

    FOOD(100, "美食"),
    DESSERT(101, "甜品奶茶"),
    BAKING(102, "烘焙蛋糕"),
    FLOWERS(103, "鲜花绿植"),
    FRUITS(104, "生鲜果蔬"),
    SUPERMARKET(105, "超市百货"),
    DRINKS(106, "酒水茶饮"),
    DIGITAL(107, "数码家电"),
    MEDICINE(108, "医药健康"),
    ;

    private final int code;
    private final String desc;

    DapCategoryEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
