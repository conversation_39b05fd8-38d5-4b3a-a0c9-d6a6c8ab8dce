package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.dto.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.ResponseCodeEnum;
import lombok.Data;

/**
 * TmsResponse
 *
 * <AUTHOR>
 * @since 2023/3/27
 */
@Data
@ThriftStruct
public class TmsResponse<T> {

    @ThriftField(1)
    int code;

    @ThriftField(2)
    String msg;

    @ThriftField(3)
    T data;

    public boolean isSuccess() {
        return code == 0;
    }

    public static <T> TmsResponse<T> succeed(T data) {
        TmsResponse<T> resp = new TmsResponse<>();
        resp.setCode(ResponseCodeEnum.SUCCESS.getValue());
        resp.setData(data);
        return resp;
    }

    public static <T> TmsResponse<T> fail(Throwable e) {
        return fail(e.getMessage());
    }

    public static <T> TmsResponse<T> fail(String message) {
        TmsResponse<T> resp = new TmsResponse<>();
        resp.setCode(ResponseCodeEnum.FAILED.getValue());
        resp.setMsg(message);
        return resp;
    }

    public static <T> TmsResponse<T> fail(ResponseCodeEnum responseCodeEnum,String message) {
        TmsResponse<T> resp = new TmsResponse<>();
        resp.setCode(responseCodeEnum.getValue());
        resp.setMsg(message);
        return resp;
    }

}
