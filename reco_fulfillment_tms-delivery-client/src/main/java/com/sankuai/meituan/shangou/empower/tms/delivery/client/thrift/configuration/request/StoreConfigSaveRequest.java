package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.ConfigModuleEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.response.TAggDeliveryPlatformConfig;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.response.TOuterDeliveryConfig;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.response.TStoreConfig;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/1/6
 */
@TypeDoc(
		description = "保存租户配送配置请求体",
		authors = {"liuyonggao"}
)
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
public class StoreConfigSaveRequest {

	@FieldDoc(
			description = "租户id",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(1)
	public Long tenantId;

	@FieldDoc(
			description = "门店id",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(2)
	public Long storeId;

	@FieldDoc(
			description = "修改模块,1表示外部渠道开通或者修改，2表示配送规则修改",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(3)
	public Integer module;

	@FieldDoc(
			description = "内部修改",
			requiredness = Requiredness.OPTIONAL
	)
	@ThriftField(4)
	public TStoreConfig tStoreConfig;


	@FieldDoc(
			description = "外部修改内容",
			requiredness = Requiredness.OPTIONAL
	)
	@ThriftField(5)
	public TOuterDeliveryConfig tOuterDeliveryConfig;

	@FieldDoc(
			description = "聚合运力配置信息",
			requiredness = Requiredness.OPTIONAL
	)
	@ThriftField(6)
	public TAggDeliveryPlatformConfig tAggDeliveryPlatformConfig;

	@FieldDoc(
			description = "操作人",
			requiredness = Requiredness.OPTIONAL
	)
	@ThriftField(7)
	public String operatorName;

	@FieldDoc(
			description = "操作人id",
			requiredness = Requiredness.OPTIONAL
	)
	@ThriftField(8)
	public Long operatorId;


	public String validate() {
		if (tenantId == null || tenantId <= 0L || storeId == null || storeId <= 0L) {
			return "租户门店ID不合法";
		}


		if (module != null) {
			if (module == ConfigModuleEnum.CHANNEL.getValue() && tOuterDeliveryConfig == null) {
				return "操作内容不能为空";

			} else if (module == ConfigModuleEnum.CONFIG.getValue() && tStoreConfig == null) {
				return "操作内容不能为空";
			}
			else if (module == ConfigModuleEnum.PLATFORM.getValue() && tAggDeliveryPlatformConfig == null) {
				return "操作内容不能为空";
			}
		} else {
			return "操作模块不能为空";
		}

		return null;
	}
}
