package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 骑手相关信息
 * <AUTHOR>
 */
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
public class RiderInfoDto {

    @FieldDoc(
            description = "骑手坐标",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(1)
    public CoordinateDto riderCoordinate;

    @FieldDoc(
            description = "骑手姓名",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(2)
    public String riderName;

    @FieldDoc(
            description = "骑手电话",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(3)
    public String riderPhone;

}
