package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2021/1/6.
 */

@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
public class TOuterDeliveryConfig {


    @FieldDoc(
            description = "配送渠道ID",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public Integer deliveryChannelId;

    @FieldDoc(
            description = "配送渠道名称",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(2)
    public String deliveryChannelName;


    @FieldDoc(
            description = "操作类型，20修改，10申请开通",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(3)
    public Integer operateType;


    @FieldDoc(
            description = "配送渠道商户I",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(4)
    public String deliveryChannelMerchantId;

    @FieldDoc(
            description = "配送渠道门店ID",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(5)
    public String deliveryChannelPoiId;

    @FieldDoc(
            description = "中台门店ID",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(6)
    public Long storeId;

    @FieldDoc(
            description = "当前开通状态标识 1-已开通 2-未开通",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(7)
    public Integer openFlag;

    @FieldDoc(
            description = "绑定配送渠道门店所需的扩展信息，JSON 字符串",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(8)
    public String deliveryChannelPoiExt;


}
