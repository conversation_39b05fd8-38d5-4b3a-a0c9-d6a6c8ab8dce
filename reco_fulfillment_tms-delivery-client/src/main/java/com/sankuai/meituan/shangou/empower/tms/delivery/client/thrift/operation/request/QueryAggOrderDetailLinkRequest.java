package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @date 2025-01-09
 */
@TypeDoc(
        description = "聚合订单详情页跳转链接查询请求"
)
@Data
@ThriftStruct
public class QueryAggOrderDetailLinkRequest extends QueryAggLinkBaseRequest {
    @FieldDoc(
            description = "订单id",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(6)
    private Long orderId;
}
