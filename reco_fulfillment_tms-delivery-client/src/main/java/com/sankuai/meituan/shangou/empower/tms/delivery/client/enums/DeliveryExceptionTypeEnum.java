package com.sankuai.meituan.shangou.empower.tms.delivery.client.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

import java.util.HashMap;
import java.util.Map;

/**
 * 配送异常类型枚举
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/3
 */
public enum DeliveryExceptionTypeEnum {

	/**
	 * 无异常
	 */
	NO_EXCEPTION(0),

	/**
	 * 系统原因导致发起配送失败
	 * <p>
	 * 常见子类：
	 * <ol>
	 *     <li>系统异常(3次自动重试仍失败)</li>
	 *     <li>appID不存在</li>
	 *     <li>签名验证错误</li>
	 *     <li>运单参数错误</li>
	 *     <li>其他</li>
	 * </ol>
	 */
	LAUNCH_DELIVERY_FAILED_BY_SYSTEM_EXCEPTION(1),

	/**
	 * 商户/门店原因导致发起配送失败
	 * <p>
	 * 常见子类：
	 * <ol>
	 *     <li>商户不存在</li>
	 *     <li>配送门店不存在</li>
	 *     <li>配送门店非营业状态</li>
	 *     <li>不在配送门店营业时间内</li>
	 *     <li>配送门店未开通所选服务包</li>
	 *     <li>配送门店不支持预约单</li>
	 *     <li>配送门店不支持营业时间外发预订单</li>
	 * </ol>
	 */
	LAUNCH_DELIVERY_FAILED_BY_MERCHANT(2),

	/**
	 * 配送服务能力原因导致异常
	 * <p>
	 * 常见子类：
	 * <ol>
	 *     <li>超出站点营业时间</li>
	 *     <li>送货地址超区</li>
	 *     <li>取货地址超区</li>
	 *     <li>预约时间超出范围</li>
	 * </ol>
	 */
	DELIVERY_EXCEPTION_BY_LIMITED_DELIVERY_SERVICE_CAPACITY(3),

	/**
	 * 用户原因导致异常
	 * <p>
	 * 常见子类：
	 * <ol>
	 *     <li>联系不上用户</li>
	 *     <li>用户地址原因</li>
	 *     <li>其他(顾客拒收/顾客要求延时配送)</li>
	 * </ol>
	 */
	DELIVERY_EXCEPTION_BY_RECEIVER(4),

	/**
	 * 运力/骑手原因导致异常
	 * <p>
	 * 常见子类：
	 * <ol>
	 *     <li>当前运力不足，无骑手接单</li>
	 *     <li>因骑手原因，运单被取消</li>
	 * </ol>
	 */
	DELIVERY_EXCEPTION_BY_RIDER(5),

	/**
	 * 系统原因导致异常
	 * <p>
	 * 常见子类：
	 * <ol>
	 *     <li>因系统原因，运单被取消</li>
	 * </ol>
	 */
	DELIVERY_EXCEPTION_BY_SYSTEM(6),

	/**
	 * 其他未知原因导致异常
	 */
	UNKNOWN(7),
	;

	private static final Map<Integer, DeliveryExceptionTypeEnum> CODE_TO_ENUM_MAP = new HashMap<>();
	private final int code;

	static {
		for (DeliveryExceptionTypeEnum each : values()) {
			CODE_TO_ENUM_MAP.put(each.getCode(), each);
		}
	}

	DeliveryExceptionTypeEnum(int code) {
		this.code = code;
	}

	@JsonValue
	public int getCode() {
		return code;
	}

	@JsonCreator
	public static DeliveryExceptionTypeEnum valueOf(int code) {
		return CODE_TO_ENUM_MAP.get(code);
	}
}
