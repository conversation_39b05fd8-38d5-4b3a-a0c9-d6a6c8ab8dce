package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.callback.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.common.Status;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@TypeDoc(
        description = "回调青云门店授权成功响应体",
        authors = {
                "yuanyu09"
        }
)
@Data
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
public class DapShopAuthNotifyResp {
    @FieldDoc(
            description = "执行状态",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public Status status;
}
