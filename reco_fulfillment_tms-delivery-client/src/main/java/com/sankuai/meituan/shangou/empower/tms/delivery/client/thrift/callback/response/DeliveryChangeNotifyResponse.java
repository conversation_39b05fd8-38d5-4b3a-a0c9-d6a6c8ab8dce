package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.callback.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.common.Status;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 回调配送进度变更消费响应体
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/1/7
 */
@TypeDoc(
		description = "回调配送进度变更消费响应体",
		authors = {
				"hedong07"
		}
)
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
public class DeliveryChangeNotifyResponse {

	@FieldDoc(
			description = "执行状态",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(1)
	public Status status;
}
