package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/1/6
 */
@TypeDoc(
        description = "查询配送信息的订单key",
        authors = {
                "钱腾"
        }
)
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
public class QueryOrderDeliveryInfoKey {

    @FieldDoc(
            description = "租户id",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public Long tenantId;

    @FieldDoc(
            description = "门店id",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(2)
    public Long storeId;

    @FieldDoc(
            description = "赋能统一订单号",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(3)
    public Long empowerOrderId;


    @FieldDoc(
            description = "订单状态",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(4)
    public Integer orderStatus;

    @FieldDoc(
            description = "订单来源：开放平台、中台",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(5)
    public Integer orderSource;

    @FieldDoc(
            description = "配送方式：自提or配送到家",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(6)
    public Integer deliveryMethod;

    @FieldDoc(
            description = "是否平台配送标记，1-非平台配送，0-平台配送",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(7)
    public Integer selfDeliveryMark;
}
