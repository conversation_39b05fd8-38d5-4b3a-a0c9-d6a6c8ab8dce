package com.sankuai.meituan.shangou.empower.tms.delivery.client.enums;

import com.google.common.base.Preconditions;

import java.text.MessageFormat;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/8
 */
public enum FailureCodeEnum {

	SUCCESS(0, "成功"),
	SYSTEM_ERROR(-1, "系统繁忙,请稍后重试"),

	INVALID_PARAM(200, "参数验证不通过"),
	ORDER_NOT_SUPPORT_ERROR(305, "订单不支持此项操作"),
	ORDER_STATUS_ERROR(310, "当前状态的订单无法进行此项操作"),

	DELIVERY_STATUS_LOCKED(1111, "运单状态被锁定"),

	CREATE_DELIVERY_ERROR(10000, "接入配送失败，请重试"),
	UNKNOWN_DELIVERY_CHANNEL(10001, "无法解析配送渠道"),
	CANCEL_ORDER_FAIL(10002, "无法进行配送后取消订单失败"),
	QUERY_ORDER_FAIL(10003, "无法查询订单信息"),
	OPERATOR_SHOP_PERMISSION_ERROR(10004, "操作员无门店{0}权限", 1),
	DELIVERY_REJECTED(10005, "配送拒单:{0}", 1),
	DELIVERY_NEED_RETRY(10006, "配送需要重试"),
	PART_SHOP_DELIVERY_CONFIG_ALREADY_EXIST(801, "部分门店配送配置已存在"),
	DELIVERY_CONFIG_NOT_EXIST(11001, "配送配置不存在"),
	DELETE_SHOP_DELIVERY_CONFIGS_FAIL(11002, "删除门店配送配置失败"),
	MODIFY_SHOP_DELIVERY_FAIL(11003, "配送配置不存在"),
	BATCH_IMPORT_SHOP_DELIVERY_CONFIG_ERROR(11004, "批量导入门店配送配置失败"),
	BATCH_IMPORT_CONFIG_VALIDATE_ERROR(11005, "导入文件校验失败"),
	NEW_DELIVERY_CONFIG_PARAM_MISS_ERROR(11006, "新增配送配置参数缺失"),
	NEW_SHOP_DELIVERY_CONFIG_PARAM_MISS_ERROR(11007, "新增门店配送配置参数缺失"),
	LAUNCH_DELIVERY_CONFIG_INVALID_ERROR(11008, "发起配送配置无效"),
	IMPORT_SHOP_DELIVERY_CONFIG_DUPLICATE_ERROR(11009, "门店{0}数据重复，请检查后重新上传提交。", 1),
	APP_KEY_CONFIG_ALREADY_ERROR(11010, "AppKey:{0}被占用，不可使用。", 1),
	IMPORT_EXCEL_ROW_LIMIT_ERROR(11011, "导入Excel文档记录数超过{0}条限制", 1),
	CANNOT_MODIFY_SHOP_DELIVERY_NOT_EXIST(11012, "待编辑的门店{0}的{1}配送渠道不存在", 2),
	TENANT_NOT_CONFIG_DELIVERY_CHANNEL(11014, "租户没有启用的配送渠道"),
	TENANT_SHOP_NOT_CONFIG_DELIVERY_CHANNEL_SHOP(11015, "租户门店未配置配送渠道门店"),
	SYNC_DELIVERY_RANGE_ERROR(11016, "同步配送范围失败"),
	CANNOT_GET_DELIVERY_RANGE_FROM_DELIVERY_CHANNEL(11017, "无法获取配送渠道配送范围"),
	UNKNOWN_CHANNEL(11018, "未知渠道编码:{0}", 1),

	INVALID_RECEIVER_ADDRESS(11019, "收货人地址信息不满足配送条件"),
	LAUNCH_DELIVERY_FAILED(11020, "{0}", 1),
	OTHER_SYSTEM_CALL_FAILED(11021, "{0}", 1),
	CANCEL_DELIVERY_FAILED(11022, "取消运单失败"),

	/**
	 * 接口被限流
	 */
	INTERFACE_LIMITED(11023, "系统繁忙，请10s后重试"),

	/**
	 * 配送发起并发冲突
	 */
	LAUNCH_DELIVERY_CONCURRENCY_CONFLICT(11024, "系统繁忙，请稍后重试"),
	NO_CHANNEL_SUPPORT(11025, "暂无支持渠道"),
	REPEAT_CANCEL_DELIVERY(11026, "运单已在取消中"),
	CANCEL_DELIVERY_CHANNEL(11027, "只能取消三方配送运单"),
	NO_CANCEL_DELIVERY(11028, "暂无可取消的配送单，请刷新"),

	UNSUPPORTED_CHANNEL(11029, "系统暂不支持【{0}】", 1),

	LOGISTIC_ORDER_EXISTED(11030, "已存在进行中的配送订单"),

	RIDER_CHANGE_SAME(11031, "改派前后骑手相同", 0),

	QUERY_CHANNEL_DELIVERY_RANGE_FAIL(11032, "查询三方配送范围失败，请稍后重试"),
	STORE_ADDRESS_INVALID(11033, "门店地址信息有误，无法查询到合理的坐标点"),
	IS_NOT_BOOKING_ORDER(11034, "订单类型错误，不是预订单"),

	/**
	 * 配送操作冲突
	 */
	RIDER_OPERATE_CONFLICT(11040, "被其他配送员操作，请刷新数据"),
	DELIVERY_STATUS_ERR(11041, "当前状态{0},无法操作", 1),
	DELIVERY_ORDER_NOT_EXIST(11042, "找不到配送记录"),
	DELIVERY_SHOP_CONFIG_ERR(11043, "门店配送配置错误"),
	DELIVERY_ORDER_STATUS_ERROR(11044, "当前状态的运单无法进行此项操作"),
	EXCEPTION_REPORT_TIMES_ERROR(11045, "异常上报次数超过最大限制"),

	DELIVERY_ORDER_ERROR(11046, "当前运单无法进行此项操作"),

	EXCEPTION_TYPE_ERROR(11047, "上报的异常类型不合法"),

	DELIVER_RIDER_ERROR(11048, "您不是当前运单的骑手,无法进行此项操作"),

	OFFLINE_PROMOTE_CREATE_DELIVERY_ERROR(11049, "注意：地推现提不会发起配送"),



	/**
	 * 门店类型错误
	 */
	NOT_PROPRIETARY_STORES(11050, "该门店不是自营门店"),
	QUERY_POI_FAILED(11051, "查询门店/仓信息失败"),


	/**
	 * 配送渠道异常
	 */
	PLATFORM_CREATE_SHOP_EXCEPTION(11060, "平台建店失败"),
	/**
	 * 麦芽田自动发配送失败
	 */
	PLATFORM_AUTO_SEND_EXCEPTION(11061, "平台自动发配送失败"),
	/**
	 * 关闭门店异常
	 */
	PLATFORM_CLOSE_SHOP_EXCEPTION(11062, "平台关店失败"),

	/**
	 * 查询平台链接异常
	 */
	QUERY_PLATFORM_LINK_EXCEPTION(11063, "查询平台链接异常"),

	/**
	 * 查询聚合配送发单节点异常，下发参数中的渠道信息和数据库中渠道信息不一致
	*/
	CHANNEL_NOT_EXIST_EXCEPTION(11064, "当前订单渠道已被删除，请刷新当前界面"),

    FAST_AUTH_EXCEPTION(11065, "快速授权失败"),

	QUERY_SHOP_AUTH_EXCEPTION(11066,"查询门店授权信息失败"),

	/**
	 * 骑手到刻位置查询
	 */
	RIDER_ARRIVAL_LOCATION_NOT_EXIST(11070, "骑手到刻位置不存在"),


	S3_GENERATE_URL_FAILED(11071, "S3链接生成失败"),

	ORDER_TRANS_SELF_ERROR(11072,"{0}",1),

	POI_BASIC_SETTINGS_INCOMPLETE(11073, "门店未填写地址、联系人、电话，无法开通牵牛花管理配送，请前往【牵牛花web-门店管理】补充完善后开通"),

	TENANT_NOT_SUPPORT_OPERATION(11074, "当前租户不支持此操作"),

	TURN_DELIVERY_TYPE_TIMES_EXCEED_LIMIT(11075, "转配送的次数超过最大限制"),

	TRANS_SELF_DELIVERY_OPERATE_OPERATE(11076, "当前已经是自营配送, 请勿重复操作"),

	ORDER_STATUS_IS_FINAL(11078, "当前订单已取消或已完成，无法进行此项操作，请重新刷新"),
	SYNC_TENANT_POI_FAILED(11079, "同步渠道门店失败"),
	TENANT_POI_DELIVERY_POI_LATITUDE_LONGITUDE_EMPTY(11080, "租户门店和渠道门店经纬度均为空"),

	MANUAL_LAUNCH_DELIVERY_FAILED(11081, "呼叫失败，请联系服务经理"),
	MANUAL_LAUNCH_DELIVERY_DELIVERY_ORDER_ALREADY_EXISTS(11082, "牵牛花已存在进行中配送单，请勿重复呼叫"),
	MANUAL_LAUNCH_DELIVERY_DOU_YIN_FAILED(11083, "{0}", 1),

	ALREADY_DELIVERY_DONE(11084, "配送已完成,请刷新页面"),
	DOU_YIN_DELIVERY_MERCHANT_PLATFORM_AUTO_CALL(11085, "抖音商家端开启了自动呼叫"),

	OUTER_QUERY_DELIVERY_ORDER_TYPE_NOT_SUPPORT(11090, "该笔订单暂不支持查看配送轨迹"),

	SYNC_PAO_TUI_DELIVERY_STATUS_FAILED(11091,"锁单后同步配送状态失败"),

	MANUAL_LAUNCH_DELIVERY_DELIVERY_CHANNEL_NOT_SUPPORT(11092, "渠道暂不支持重新呼叫骑手，呼叫失败"),

	MANUAL_LAUNCH_DELIVERY_OPEN_API_FAILED(11093,"{0}",1),

	ORDER_CHANGE_NOTIFY_AGG_PLATFORM_FAILED(11094,"订单信息变更同步聚合配送平台失败"),

	DOU_YIN_TURN_SELF_AND_AGG_DELIVERY_ERROR(11095,"平台缺失收件信息，正在重试，可通过商家端呼叫平台配送"),

	AGG_LINK_AUTH_FAIL(11096,"链接授权已失效"),

	SYNC_PAO_TUI_LOCK_STATUS_FAILED(11097,"跑腿锁单V2解锁状态变更后同步聚合配送平台失败"),

	LIMIT_ACCEPT_ORDER(20000102, "骑手已被限制接单,原因: %s"),
	ABN_ORDER_NOT_TURN_AGG(20000103, "有异常单不支持转三方！"),
	;

	private final int code;
	private final String messageTemplate;
	private final int variableCount;

	FailureCodeEnum(int code, String messageTemplate) {
		this.code = code;
		this.messageTemplate = messageTemplate;
		this.variableCount = 0;
	}

	FailureCodeEnum(int code, String messageTemplate, int variableCount) {
		this.code = code;
		this.messageTemplate = messageTemplate;
		this.variableCount = variableCount;
	}

	public int getCode() {
		return code;
	}

	public String getMessage(Object... params) {
		Preconditions.checkArgument(params.length == this.variableCount);
		return MessageFormat.format(messageTemplate, params);
	}
}
