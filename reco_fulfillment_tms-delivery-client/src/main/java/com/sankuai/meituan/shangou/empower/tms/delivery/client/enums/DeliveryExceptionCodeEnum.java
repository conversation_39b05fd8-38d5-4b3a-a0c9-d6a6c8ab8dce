package com.sankuai.meituan.shangou.empower.tms.delivery.client.enums;

import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotNull;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021/4/19
 * @email liuyonggao@meituan
 */
public enum DeliveryExceptionCodeEnum {
    NO_EXCEPTION(0, "无异常"),
    NO_RIDE_ACCEPT(1, "长时间无人接单"),

    NO_ARRIVAL_STORE(2, "接单后,长时间未到店"),

    DELIVERY_TIME_OUT(3, "接单后，配送超时未完成"),

    CHANNEL_CANCEL(4, "配送方取消配送"),

    SEND_DELIVERY_FAIL(5, "配送发单失败"),
    SEND_CANCEL_FAIL(6, "配送取消失败"),
    ESTIMATED_TIME_OUT(7, "订单超过预计送达时间未完成"),
    SYNC_CREATE_FAIL(10, "发起同步失败"),
    SYNC_CANCEL_FAIL(11, "发起取消失败"),
    RIDER_PICK_TIME_OUT(12, "骑手到店超时未取货"),

    /**
     * 配送状态回退
     */
    DELIVERY_STATUS_ROLLBACK(13, "配送状态回退"),

    /**
    * 平台配送异常，可以切换自配送
     */
    SELF_DELIVERY(14,"发生骑手异常上报，请及时查看处理"),

    /**
     * 异常上报
     */
    RIDER_REPORT_FAIL(15, "发生骑手异常上报，请及时进入配送详情审核"),

    /**
     * 取货失败待审核
     */
    RIDER_TAKE_FAIL_AUDITING(16, "请尽快与骑手联系"),

    /**
     * 取货失败
     */
    RIDER_TAKE_FAIL(17, "请尽快与骑手联系"),

    /**
     * 二次呼叫
     */
    RECALL_RIDER_FAIL(18, "发生骑手异常上报，请及时查看处理"),

    /**
     * 二次呼叫以及转自送
     */
    RECALL_SELF_RIDER_FAIL(19, "发生骑手异常上报，请及时查看处理"),

    /**
     * elm配送异常上报
     */
    DELIVERY_EXCEPTION_UPLOAD(20,"发生骑手异常上报，请及时查看处理"),

    OPEN_API_DELIVERY_EXCEPTION(21,"平台配送异常，请及时处理"),



    LACK_MONEY(98, "余额不足"),

    SYSTEM_EXCEPTION_DELIVERY_FAIL(100, "由于系统故障发配送失败，请自行配送或等待系统恢复"),

    UNKNOWN(10000, "未知"),


    ;

    private final int code;
    private final String desc;


    DeliveryExceptionCodeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static DeliveryExceptionCodeEnum enumOf(int value) {
        for (DeliveryExceptionCodeEnum each : values()) {
            if (each.getCode() == value) {
                return each;
            }
        }

        return UNKNOWN;
    }

    public static DeliveryExceptionCodeEnum codeStrOf(String value) {
        if(StringUtils.isEmpty(value)){
            return UNKNOWN;
        }
        for (DeliveryExceptionCodeEnum each : values()) {
            if (Objects.equals(String.valueOf(each.getCode()), value)) {
                return each;
            }
        }

        return UNKNOWN;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据运单状态判断异常类型
     */
    @NotNull
    public static DeliveryExceptionCodeEnum resolveByStatus(DeliveryStatusEnum deliveryStatus) {
        switch (deliveryStatus) {
            case INIT:
            case DELIVERY_LAUNCHED:
            case WAITING_TO_ASSIGN_RIDER:
                return NO_RIDE_ACCEPT;
            case RIDER_ASSIGNED:
                return NO_ARRIVAL_STORE;
            case RIDER_ARRIVED_SHOP:
                return RIDER_PICK_TIME_OUT;
            case RIDER_TAKEN_GOODS:
                return ESTIMATED_TIME_OUT;
            default:
                return NO_EXCEPTION;
        }
    }

}
