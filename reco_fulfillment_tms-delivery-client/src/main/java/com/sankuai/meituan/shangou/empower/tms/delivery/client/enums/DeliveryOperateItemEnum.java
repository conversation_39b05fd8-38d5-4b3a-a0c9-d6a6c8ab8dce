package com.sankuai.meituan.shangou.empower.tms.delivery.client.enums;

import java.util.HashMap;
import java.util.Map;

public enum DeliveryOperateItemEnum {

    TRANS_SELF_DELIVERY(1,"转自配送"),
    TRANS_MALT_FARM(2,"转麦芽田"),
    TRANS_DAP_DELIVERY(3,"转青云智送"),
    ADD_TIP_FEE(4,"加小费"),
    CANCEL_DELIVERY(5,"取消配送"),
    RECALL_DELIVERY(6,"再次呼叫"),
    REPORT_EXCEPTION(7,"异常上报"),
    AUDIT_EXCEPTION(8,"异常审核"),
    MANUAL_LAUNCH(9, "抖音平台配送手动呼叫配送"),
    EXCEPTION_RECALL_LAUNCH(10, "抖音平台配送异常重新呼叫配送"),
    TRANS_FOUR_WHEEL_DELIVERY(11, "转汽车配送"),
    APPEND_FOUR_WHEEL_DELIVERY_TYPE(12, "追加汽车配送车型")
    ;
    private Integer code;
    private String desc;

    private static Map<Integer,DeliveryOperateItemEnum> OPERATE_ITEM_MAP = new HashMap<>();
    static {
        for (DeliveryOperateItemEnum itemEnum : values()){
            OPERATE_ITEM_MAP.put(itemEnum.getCode(),itemEnum);
        }
    }

    DeliveryOperateItemEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public DeliveryOperateItemEnum codeToEnum(Integer code){
        if(code == null){
            return null;
        }
        return OPERATE_ITEM_MAP.get(code);
    }

}
