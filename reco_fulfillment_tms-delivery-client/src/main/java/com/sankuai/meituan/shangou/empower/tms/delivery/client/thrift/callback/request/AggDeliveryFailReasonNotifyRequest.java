package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.callback.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.common.MaltFarmSignArg;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2021/04/07
 */
@TypeDoc(
		description = "聚合运力异常回调(目前只有麦芽田)",
		authors = {
				"zengping02"
		}
)
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
public class AggDeliveryFailReasonNotifyRequest {

	@FieldDoc(
			description = "订单id"
	)
	@ThriftField(value = 1, requiredness = ThriftField.Requiredness.REQUIRED)
	@MaltFarmSignArg
	public String originId;

	@FieldDoc(
			description = "异常原因码"
	)
	@ThriftField(value = 2, requiredness = ThriftField.Requiredness.REQUIRED)
	@MaltFarmSignArg
	public Integer code;

	@FieldDoc(
			description = "异常原因描述"
	)
	@ThriftField(value = 3, requiredness = ThriftField.Requiredness.REQUIRED)
	@MaltFarmSignArg
	public String reason;

	@FieldDoc(
			description = "时间戳"
	)
	@ThriftField(value = 4, requiredness = ThriftField.Requiredness.REQUIRED)
	@MaltFarmSignArg
	public Long timestamp;

	@FieldDoc(
			description = "签名"
	)
	@ThriftField(value = 5, requiredness = ThriftField.Requiredness.REQUIRED)
	public String sign;

	@FieldDoc(
			description = "平台code"
	)
	@ThriftField(value = 6, requiredness = ThriftField.Requiredness.OPTIONAL)
	public Integer platformCode;
}
