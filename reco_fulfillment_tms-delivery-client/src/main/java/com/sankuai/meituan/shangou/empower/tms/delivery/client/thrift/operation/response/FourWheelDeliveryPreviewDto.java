package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
public class FourWheelDeliveryPreviewDto {

    @ThriftField(1)
    public String totalFeeRangeMin;

    @ThriftField(2)
    public String totalFeeRangeMax;

    @ThriftField(3)
    public Integer vehicleCode;

    @ThriftField(4)
    public String vehicleName;

    @ThriftField(5)
    public Boolean isDelivery;

    @ThriftField(6)
    public String vehicleFeeRange;

    @ThriftField(7)
    public String url;


}
