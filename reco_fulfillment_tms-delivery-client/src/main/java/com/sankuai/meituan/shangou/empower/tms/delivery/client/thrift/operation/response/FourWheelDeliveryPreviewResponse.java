package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
public class FourWheelDeliveryPreviewResponse {

    @ThriftField(1)
    public List<FourWheelDeliveryPreviewDto> previewList = new ArrayList<>();

}
