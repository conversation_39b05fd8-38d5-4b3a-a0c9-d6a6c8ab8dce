package com.sankuai.meituan.shangou.empower.tms.delivery.client.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * 牵牛花统一取消配送原因枚举
 * <AUTHOR>
 */

public enum DeliveryCancelReasonEnum {

    NO_CANCEL(0, "", ""),

    MERCHANT_CANCEL(1, "商家取消", ""),

    CARRIER_CANCEL(2, "配送方原因取消", ""),

    CUSTOMER_CANCEL(3, "顾客原因取消", ""),

    OTHER_CANCEL(99, "其他原因系统取消", "")
    ;

    private final int cancelCode;

    private final String cancelReason;

    private final String cancelDesc;

    private static final Map<Integer, DeliveryCancelReasonEnum> CODE_ENUM_MAP = new HashMap<>();

    static {
        for (DeliveryCancelReasonEnum each : values()) {
            CODE_ENUM_MAP.put(each.getCancelCode(), each);
        }
    }

    DeliveryCancelReasonEnum(int cancelCode, String cancelReason, String cancelDesc) {
        this.cancelCode = cancelCode;
        this.cancelReason = cancelReason;
        this.cancelDesc = cancelDesc;
    }

    public int getCancelCode() {
        return this.cancelCode;
    }

    public String getCancelReason() {
        return this.cancelReason;
    }

    public static DeliveryCancelReasonEnum enumOf(Integer code) {
        if (code == null) {
            return null;
        }
        return CODE_ENUM_MAP.get(code);
    }
}
