package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023/3/7 16:24
 **/
@ThriftStruct
@Data
public class QueryDeliveryShippingTagRequest {
    @ThriftField(1)
    private Long tenantId;

    @ThriftField(2)
    private Long storeId;

    @ThriftField(3)
    private Integer channelId;

    @ThriftField(4)
    private Double longitude;

    @ThriftField(5)
    private Double latitude;

    public String validate() {
        if (tenantId == null || tenantId <= 0L) {
            return "租户id不合法";
        }

        if (storeId == null || storeId <= 0L) {
            return "门店id不合法";
        }

        if (channelId == null || channelId <= 0L) {
            return "渠道id不合法";
        }

        if (latitude == null || longitude == null) {
            return "经纬度不合法";
        }

        return null;

    }
}
