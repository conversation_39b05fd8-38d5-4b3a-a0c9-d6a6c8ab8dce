package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/5/17
 */

@TypeDoc(
	description = "查询运单信息请求体",
	authors = {
		"zhangyusen03"
	}
)
@AllArgsConstructor
@NoArgsConstructor
@Data
@ThriftStruct
public class QueryDeliveryOrderRequest {

	@FieldDoc(
		description = "赋能订单id",
		requiredness = Requiredness.REQUIRED
	)
	@ThriftField(1)
	public Long orderId;

	@FieldDoc(
			description = "是否查询从库",
			requiredness = Requiredness.OPTIONAL
	)
	@ThriftField(2)
	public boolean masterOnly = true;

	public String validate() {
		if (orderId == null || orderId <= 0L) {
			return "赋能订单id不合法";
		}
		return null;
	}
}
