package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.request;

import java.util.List;

import org.apache.commons.collections.CollectionUtils;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 聚合运力平台配置
 *
 * <AUTHOR>
 * @since 2021/04/10
 */
@TypeDoc(
		description = "查询租户配送配置请求体",
		authors = {"zengping02"}
)
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
public class BatchStoreConfigQueryRequest {

	@FieldDoc(
			description = "租户id",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(1)
	public Long tenantId;

	@FieldDoc(
			description = "门店id",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(2)
	public List<Long> storeIdList;

	@FieldDoc(
			description = "门店id",
			requiredness = Requiredness.OPTIONAL
	)
	@ThriftField(3)
	public List<Integer> aggPlatformCodes;

	public String validate() {
		if (tenantId == null || tenantId <= 0L || CollectionUtils.isEmpty(storeIdList)) {
			return "租户门店ID不能为空";
		}

		return null;
	}
}
