package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.open.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023-08-29
 * @email <EMAIL>
 */
@Data
@ThriftStruct
@NoArgsConstructor
@AllArgsConstructor
public class TDeliveryPlatformUrl {

    @FieldDoc(
            description = "链接名称",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public String linkTitle;

    @FieldDoc(
            description = "url",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(2)
    public String linkUrl;

}
