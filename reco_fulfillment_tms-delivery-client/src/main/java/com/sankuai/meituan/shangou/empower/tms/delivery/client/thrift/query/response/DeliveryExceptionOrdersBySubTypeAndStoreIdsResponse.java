package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.common.Status;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.TOrderIdentifier;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.common.TPageInfo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * 多门店根据子类型分页查询异常订单Response
 */
@TypeDoc(
        description = "多门店根据子类型分页查询异常订单",
        authors = {
                "zhangjian155"
        }
)
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
@Builder
public class DeliveryExceptionOrdersBySubTypeAndStoreIdsResponse {

    @FieldDoc(
            description = "响应状态",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(value = 1, requiredness = ThriftField.Requiredness.REQUIRED)
    public Status status = Status.SUCCESS;

    @FieldDoc(
            description = "分页信息",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(value = 2, requiredness = ThriftField.Requiredness.REQUIRED)
    public TPageInfo tPageInfo;

    @FieldDoc(
            description = "订单标识列表"
    )
    @ThriftField(3)
    public List<TOrderIdentifier> orders = new ArrayList<>(0);
}
