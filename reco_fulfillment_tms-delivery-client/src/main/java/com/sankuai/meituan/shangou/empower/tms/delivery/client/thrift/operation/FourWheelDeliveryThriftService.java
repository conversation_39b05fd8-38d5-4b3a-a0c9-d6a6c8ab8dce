package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.dto.response.TmsResponse;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.request.FourWheelDeliveryDispatchRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.request.FourWheelDeliveryPreviewRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.response.FourWheelDeliveryDispatchResponse;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.response.FourWheelDeliveryPreviewResponse;

@InterfaceDoc(
        displayName = "四轮配送",
        type = "octo.thrift.annotation",
        scenarios = "配送操作服务，提供发起配送等能力",
        description = "配送操作服务，提供发起配送等能力"
)
@ThriftService
public interface FourWheelDeliveryThriftService {

    @MethodDoc(
            displayName = "四轮配送询价",
            description = "四轮配送询价",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "四轮配送询价请求",
                            type = FourWheelDeliveryPreviewRequest.class,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            returnValueDescription = "四轮配送询价结果",
            restExampleResponseData = "{\"status\":{\"code\":0, \"msg\":\"\"}}",
            example = "暂无"
    )
    @ThriftMethod
    TmsResponse<FourWheelDeliveryPreviewResponse> dispatchPreview(FourWheelDeliveryPreviewRequest request);

    @MethodDoc(
            displayName = "发四轮配送",
            description = "发四轮配送",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "发四轮配送请求",
                            type = FourWheelDeliveryDispatchRequest.class,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            returnValueDescription = "发四轮配送结果",
            restExampleResponseData = "{\"status\":{\"code\":0, \"msg\":\"\"}}",
            example = "暂无"
    )
    @ThriftMethod
    TmsResponse<FourWheelDeliveryDispatchResponse> orderDispatch(FourWheelDeliveryDispatchRequest request);

}
