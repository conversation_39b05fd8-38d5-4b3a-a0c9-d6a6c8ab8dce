package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.callback.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.Optional;

/**
 * 回调配送进度变更请求体
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/1/7
 */
@TypeDoc(
		description = "回调配送进度变更请求体",
		authors = {
				"hedong07"
		}
)
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
@SuppressWarnings("DuplicatedCode")
public class DeliveryChangeNotifyRequest {

	@FieldDoc(
			description = "闪购赋能订单号",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(1)
	public String orderId;

	@FieldDoc(
			description = "闪购中台门店id",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(2)
	public String storeId;

	@FieldDoc(
			description = "包裹id",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(3)
	public Long pkgId;

	@FieldDoc(
			description = "配送渠道id",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(4)
	public String deliveryChannelId;

	@FieldDoc(
			description = "配送服务包",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(5)
	public String servicePackage;

	@FieldDoc(
			description = "配送距离，单位：米",
			requiredness = Requiredness.OPTIONAL
	)
	@ThriftField(6)
	public Long distance;

	@FieldDoc(
			description = "配送费用，单位：元",
			requiredness = Requiredness.OPTIONAL
	)
	@ThriftField(7)
	public Double deliveryFee;

	@FieldDoc(
			description = "配送状态",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(8)
	public Integer deliveryStatus;

	@FieldDoc(
			description = "取消原因code",
			requiredness = Requiredness.OPTIONAL
	)
	@ThriftField(9)
	public Integer cancelReasonCode;

	@FieldDoc(
			description = "取消原因描述",
			requiredness = Requiredness.OPTIONAL
	)
	@ThriftField(10)
	public String cancelReasonDesc;

	@FieldDoc(
			description = "骑手姓名",
			requiredness = Requiredness.OPTIONAL
	)
	@ThriftField(11)
	public String riderName;

	@FieldDoc(
			description = "骑手电话",
			requiredness = Requiredness.OPTIONAL
	)
	@ThriftField(12)
	public String riderPhone;

	@FieldDoc(
			description = "更新时间，毫秒时间戳",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(13)
	public Long updateTime;

	public String validate() {
		if (!StringUtils.isNumeric(orderId)) {
			return "闪购赋能订单号非法";
		}

		if (!StringUtils.isNumeric(storeId)) {
			return "闪购中台门店id非法";
		}

		if (pkgId == null) {
			return "包裹id不能为空";
		}

		if (!StringUtils.isNumeric(deliveryChannelId)) {
			return "配送渠道id非法";
		}

		if (StringUtils.isEmpty(servicePackage)) {
			return "服务包不能为空";
		}

		if (deliveryStatus == null) {
			return "配送状态不能为空";
		}

		if (updateTime == null) {
			return "更新时间戳不能为空";
		}

		return null;
	}
}
