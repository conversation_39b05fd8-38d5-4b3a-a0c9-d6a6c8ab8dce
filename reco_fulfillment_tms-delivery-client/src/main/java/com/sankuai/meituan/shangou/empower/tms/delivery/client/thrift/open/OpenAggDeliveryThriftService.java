package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.open;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.open.request.OpenTurnToAggregationDeliveryRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.open.request.QueryAggStoreConfigUrlRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.open.response.QueryAggStoreConfigUrlResponse;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.request.CreateAggregationDeliveryPoiRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.request.TurnToAggregationDeliveryRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.response.CreateAggregationDeliveryPoiResponse;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.response.TurnToAggregationDeliveryResponse;

/**
 * <AUTHOR>
 * @date 2023-08-29
 * @email <EMAIL>
 */
@InterfaceDoc(
        displayName = "开放的三方配送服务",
        type = "octo.thrift.annotation",
        scenarios = "开放的三方配送服务",
        description = "开放的三方配送服务",
        authors = {
                "jianglilin02"
        }
)
@ThriftService
public interface OpenAggDeliveryThriftService {

    @MethodDoc(
            displayName = "查聚合门店配置URL",
            description = "查聚合门店配置URL",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查聚合门店配置URL请求",
                            type = QueryAggStoreConfigUrlRequest.class,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            returnValueDescription = "查聚合门店配置URL结果",
            restExampleResponseData = "{\"status\":{\"code\":0, \"msg\":\"\"}}",
            example = "暂无"
    )
    @ThriftMethod
    QueryAggStoreConfigUrlResponse queryAggStoreConfigUrlInfo(QueryAggStoreConfigUrlRequest request);

    @MethodDoc(
            displayName = "转三方配送",
            description = "转三方配送",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "转三方配送请求",
                            type = TurnToAggregationDeliveryRequest.class,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            returnValueDescription = "转三方配送操作结果",
            restExampleResponseData = "{\"status\":{\"code\":0, \"msg\":\"\"}}",
            example = "暂无"
    )
    @ThriftMethod
    TurnToAggregationDeliveryResponse turnToAggregationDelivery(OpenTurnToAggregationDeliveryRequest request);



    @MethodDoc(
            displayName = "同步三方配送门店",
            description = "同步三方配送门店",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "同步三方配送门店请求",
                            type = CreateAggregationDeliveryPoiRequest.class,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            returnValueDescription = "同步三方配送门店请求",
            restExampleResponseData = "{\"status\":{\"code\":0, \"msg\":\"\"}}",
            example = "暂无"
    )
    @ThriftMethod
    CreateAggregationDeliveryPoiResponse createAggregationDeliveryPoi(CreateAggregationDeliveryPoiRequest request);
}
