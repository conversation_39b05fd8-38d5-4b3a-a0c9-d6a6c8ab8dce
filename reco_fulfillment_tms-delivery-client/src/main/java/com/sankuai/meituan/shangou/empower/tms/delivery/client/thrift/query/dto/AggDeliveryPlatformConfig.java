package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
public class AggDeliveryPlatformConfig {

    @FieldDoc(
            description = "三方运力平台配置跳转链接",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(1)
    public String configUrl;

    @FieldDoc(
            description = "三方运力平台配送详情跳转链接",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(2)
    public String redirectUrl;

    @FieldDoc(
            description = "三方运力平台配送appKey",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(3)
    public String appKey;

    @FieldDoc(
            description = "三方运力平台配SecretKey",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(4)
    public String appSecretKey;
}
