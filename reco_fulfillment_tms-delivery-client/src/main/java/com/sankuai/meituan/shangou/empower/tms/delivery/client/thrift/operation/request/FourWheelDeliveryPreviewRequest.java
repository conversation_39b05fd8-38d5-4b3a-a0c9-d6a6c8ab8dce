package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
public class FourWheelDeliveryPreviewRequest {

    @ThriftField(1)
    public Long orderId;

    @ThriftField(2)
    public Integer previewType;

    @ThriftField(3)
    public Long tenantId;

    @ThriftField(4)
    public Long accountId;

    @ThriftField(5)
    public String accountName;

}
