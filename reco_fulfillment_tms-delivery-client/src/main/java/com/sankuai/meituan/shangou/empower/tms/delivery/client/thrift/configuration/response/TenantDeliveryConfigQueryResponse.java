package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.common.Status;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/11/22
 */
@TypeDoc(
		description = "租户配送配置查询响应体",
		authors = {"hedong07"}
)
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
public class TenantDeliveryConfigQueryResponse {

	@FieldDoc(
			description = "执行状态",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(1)
	public Status status;

	@FieldDoc(
			description = "渠道配送配置",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(2)
	public List<TChannelDeliveryConfig> channelDeliveryConfigs;

	public TenantDeliveryConfigQueryResponse(Status status) {
		this.status = status;
		this.channelDeliveryConfigs = new ArrayList<>();
	}

	public TenantDeliveryConfigQueryResponse(List<TChannelDeliveryConfig> channelDeliveryConfigs) {
		this.status = Status.SUCCESS;
		this.channelDeliveryConfigs = channelDeliveryConfigs;
	}
}
