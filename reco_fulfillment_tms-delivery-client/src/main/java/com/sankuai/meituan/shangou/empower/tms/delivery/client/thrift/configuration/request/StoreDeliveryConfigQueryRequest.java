package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


@TypeDoc(
		description = "查询门店/仓库配送配置请求体"
)
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
public class StoreDeliveryConfigQueryRequest {

	@FieldDoc(
			description = "租户id",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(1)
	private Long tenantId;

	@FieldDoc(
			description = "门店id",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(2)
	private Long storeId;

	@FieldDoc(
			description = "token",
			requiredness = Requiredness.OPTIONAL
	)
	@ThriftField(3)
	private String token;

	@FieldDoc(
			description = "端类型： 参考 SiteTypeEnum",
			requiredness = Requiredness.OPTIONAL
	)
	@ThriftField(4)
	private String deviceType;

	public String validate() {
		if (tenantId == null || tenantId <= 0L) {
			return "租户ID不合法";
		}
		if (storeId == null || storeId <= 0L) {
			return "门店ID不合法";
		}
		return null;
	}
}
