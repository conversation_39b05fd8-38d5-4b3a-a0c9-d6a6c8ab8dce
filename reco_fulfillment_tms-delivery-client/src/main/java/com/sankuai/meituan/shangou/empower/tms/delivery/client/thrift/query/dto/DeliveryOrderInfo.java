package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ThriftStruct
@Data
public class DeliveryOrderInfo {
    @FieldDoc(
            description = "赋能统一订单号",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public Long orderId;

    @FieldDoc(
            description = "租户Id",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(2)
    public Long tenantId;

    @FieldDoc(
            description = "门店Id",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(3)
    public Long storeId;

    @FieldDoc(
            description = "配送渠道",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(4)
    public Integer deliveryChannel;

    @FieldDoc(
            description = "配送平台",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(5)
    public Integer deliveryPlatformCode;

    @FieldDoc(
            description = "订单业务类型",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(6)
    public Integer orderBizType;

    @FieldDoc(
            description = "渠道订单号",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(7)
    public String channelOrderId;

    @FieldDoc(
            description = "配送状态",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(8)
    public Integer status;

    @FieldDoc(
            description = "配送订单号",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(9)
    public String deliveryOrderId;

}
