package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@TypeDoc(
        description = "门店信息"
)
@NoArgsConstructor
@AllArgsConstructor
@Data
@ThriftStruct
@Builder
public class ShopDetail {

    @ThriftField(
            value = 1,
            requiredness = ThriftField.Requiredness.REQUIRED
    )
    @FieldDoc(
            description = "赋能租户id"
    )
    public Long tenantId;

    @ThriftField(
            value = 2,
            requiredness = ThriftField.Requiredness.REQUIRED
    )
    @FieldDoc(
            description = "赋能门店id"
    )
    public Long shopId;
    @ThriftField(
            value = 3,
            requiredness = ThriftField.Requiredness.OPTIONAL
    )
    @FieldDoc(
            description = "赋能门店名称"
    )
    public String shopName;
    @ThriftField(
            value = 4,
            requiredness = ThriftField.Requiredness.OPTIONAL
    )
    @FieldDoc(
            description = "门店手机号"
    )
    public String shopPhone;
    @ThriftField(
            value = 5,
            requiredness = ThriftField.Requiredness.OPTIONAL
    )
    @FieldDoc(
            description = "门店地址"
    )
    public String shopAddress;
    @ThriftField(
            value = 6,
            requiredness = ThriftField.Requiredness.OPTIONAL
    )
    @FieldDoc(
            description = "门店经度"
    )
    public Double shopLongitude;
    @ThriftField(
            value = 7,
            requiredness = ThriftField.Requiredness.OPTIONAL
    )
    @FieldDoc(
            description = "门店维度"
    )
    public Double shopLatitude;
    @ThriftField(
            value = 8,
            requiredness = ThriftField.Requiredness.OPTIONAL
    )
    @FieldDoc(
            description = "物品类别"
    )
    public Integer category;
    @ThriftField(
            value = 9,
            requiredness = ThriftField.Requiredness.OPTIONAL
    )
    @FieldDoc(
            description = "坐标类型"
    )
    public Integer mapType;
    @ThriftField(
            value = 10,
            requiredness = ThriftField.Requiredness.OPTIONAL
    )
    @FieldDoc(
            description = "赋能租户名称"
    )
    public String tenantName;

    @FieldDoc(
            description = "品类code",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(
            value = 11,
            requiredness = ThriftField.Requiredness.OPTIONAL
    )
    public String categoryCode;
}
