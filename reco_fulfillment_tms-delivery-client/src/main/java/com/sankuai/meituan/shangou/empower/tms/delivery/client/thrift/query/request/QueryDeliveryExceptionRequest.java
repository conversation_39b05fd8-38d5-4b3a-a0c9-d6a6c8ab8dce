package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@TypeDoc(
        description = "查询异常运单"
)
@AllArgsConstructor
@NoArgsConstructor
@ThriftStruct
@Data
@Builder
public class QueryDeliveryExceptionRequest {


    @FieldDoc(
            description = "门店ID集合",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public List<Long> storeIdList;

    @FieldDoc(
            description = "第几页", requiredness = Requiredness.REQUIRED
    )
    @ThriftField(2)
    private Integer page = 1;

    @FieldDoc(
            description = "每页行数", requiredness = Requiredness.REQUIRED
    )
    @ThriftField(3)
    private Integer size = 20;

    @FieldDoc(
            description = "渠道订单ID",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(4)
    public String viewOrderId;

    @FieldDoc(
            description = "租户ID",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(5)
    public Long tenantId;

}
