package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.deliverychannel.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.Assert;

import java.util.Set;

/**
 * 根据承运商code批量查询配送渠道信息请求体
 *
 * <AUTHOR>
 * @date 2023/4/6
 */
@TypeDoc(
		description = "根据承运商code批量查询配送渠道信息请求体",
		authors = {
				"zhangjian155"
		}
)
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
public class DeliveryChannelBatchQueryByCarrierCodeRequest {

	@FieldDoc(
			description = "牵牛花维护的承运商code列表",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(1)
	public Set<Integer> carrierCodeSet;

	public void validate() {
		Assert.notNull(carrierCodeSet, "carrierCodeSet不能为null");
	}
}
