package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/5/17
 */
@TypeDoc(
	description = "运单信息dto",
	authors = {
		"zhangyusen"
	}
)
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
@ThriftStruct
public class TDeliveryOrder {

	@FieldDoc(
		description = "运单id",
		requiredness = Requiredness.REQUIRED
	)
	@ThriftField(2)
	public Long id;

	@FieldDoc(
		description = "租户id",
		requiredness = Requiredness.REQUIRED
	)
	@ThriftField(3)
	public Long tenantId;

	@FieldDoc(
		description = "赋能门店id",
		requiredness = Requiredness.REQUIRED
	)
	@ThriftField(4)
	public Long storeId;

	@FieldDoc(
		description = "赋能订单id",
		requiredness = Requiredness.REQUIRED
	)
	@ThriftField(5)
	public Long orderId;

	@FieldDoc(
		description = "渠道订单id",
		requiredness = Requiredness.REQUIRED
	)
	@ThriftField(6)
	public String channelOrderId;

	@FieldDoc(
		description = "预计送达时间",
		requiredness = Requiredness.OPTIONAL
	)
	@ThriftField(7)
	public Long estimatedDeliveryTime;

	@FieldDoc(
		description = "预计送达结束时间",
		requiredness = Requiredness.OPTIONAL
	)
	@ThriftField(8)
	public Long estimatedDeliveryEndTime;

	@FieldDoc(
		description = "配送渠道",
		requiredness = Requiredness.OPTIONAL
	)
	@ThriftField(9)
	public Integer deliveryChannel;

	@FieldDoc(
		description = "运单状态",
		requiredness = Requiredness.OPTIONAL
	)
	@ThriftField(10)
	public Integer status;

	@FieldDoc(
		description = "运单是否生效",
		requiredness = Requiredness.REQUIRED
	)
	@ThriftField(11)
	public Boolean activeStatus;

	@FieldDoc(
		description = "配送运费，单位：元",
		requiredness = Requiredness.OPTIONAL
	)
	@ThriftField(12)
	public Double deliveryFee;

	@FieldDoc(
		description = "配送距离，单位：米",
		requiredness = Requiredness.OPTIONAL
	)
	@ThriftField(13)
	public Long distance;

	@FieldDoc(
			description = "小费",
			requiredness = Requiredness.OPTIONAL
	)
	@ThriftField(14)
	public Double tipAmount;

	@FieldDoc(
			description = "第N次配送",
			requiredness = Requiredness.OPTIONAL
	)
	@ThriftField(15)
	public Integer deliveryCount;

	@FieldDoc(
			description = "骑手名字",
			requiredness = Requiredness.OPTIONAL
	)
	@ThriftField(16)
	public String riderName;

	@FieldDoc(
			description = "骑手电话",
			requiredness = Requiredness.OPTIONAL
	)
	@ThriftField(17)
	public String riderPhone;

	@FieldDoc(
			description = "异常类型",
			requiredness = Requiredness.OPTIONAL
	)
	@ThriftField(18)
	public Integer exceptionType;

	@FieldDoc(
			description = "异常code",
			requiredness = Requiredness.OPTIONAL
	)
	@ThriftField(19)
	public Integer exceptionCode;

	@FieldDoc(
			description = "异常最后操作时间",
			requiredness = Requiredness.OPTIONAL
	)
	@ThriftField(20)
	public String allowLatestAuditTime;

	@FieldDoc(
			description = "平台来源",
			requiredness = Requiredness.OPTIONAL
	)
	@ThriftField(21)
	public Integer platformSourceCode;

	@FieldDoc(
			description = "履约订单号",
			requiredness = Requiredness.OPTIONAL
	)
	@ThriftField(22)
	public Long fulfillmentOrderId;

	@FieldDoc(
			description = "配送订单号",
			requiredness = Requiredness.OPTIONAL
	)
	@ThriftField(23)
	public String deliveryOrderId;

	@FieldDoc(
			description = "创建时间",
			requiredness = Requiredness.OPTIONAL
	)
	@ThriftField(24)
	public Long createTime;

	@FieldDoc(
			description = "拣配分离标签",
			requiredness = Requiredness.OPTIONAL
	)
	@ThriftField(25)
	public Boolean pickDeliverySplitTag;

	@FieldDoc(
			description = "是否四轮配送 1是 0否",
			requiredness = Requiredness.OPTIONAL
	)
	@ThriftField(26)
	public Integer isFourWheelDelivery;

	@FieldDoc(
			description = "订单业务类型",
			requiredness = Requiredness.OPTIONAL
	)
	@ThriftField(27)
	public Integer orderBizType;

	@FieldDoc(
			description = "渠道运单id",
			requiredness = Requiredness.OPTIONAL
	)
	@ThriftField(28)
	public String channelDeliveryId;

	@FieldDoc(
			description = "原始运单号",
			requiredness = Requiredness.OPTIONAL
	)
	@ThriftField(29)
	public String originWaybillNo;

}
