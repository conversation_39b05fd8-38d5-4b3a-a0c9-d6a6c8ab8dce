package com.sankuai.meituan.shangou.empower.tms.delivery.client.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/10/22
 */
public enum ResponseCodeEnum {

	FAILED(-1),
	SUCCESS(0),
	SUCCESS_PARTITION(1),
	REQUEST_PARAMS_ERROR(100),
	THRIFT_SERVICE_ERROR(200),
	ORDER_SERVER_ERROR(201),
	CHANNEL_SERVER_FAIL(205),
	RHINO_ERROR(300),
	UNKNOWN_ERROR(500),
	NOT_OPENED_CHANNEL_STORES(601),
	ANALYZE_EXCEL_ERROR(602),
	SYSTEM_INTERNAL_ERROR(900),
	DUP_DATA(701),
	CHECK_BIZ_ERROR(702),
	QUERY_EXCEED_LIMIT(703),

	/**
	 * 配送方式不匹配
	 */
	DELIVERY_METHOD_NOT_MATCH(2001000),
	/**
	 * 骑手已经接单
	 */
	DELIVERY_RIDER_HAS_ACCEPT(2001001),

	/**
	 * 订单不满足汽车配送条件
	 */
	ORDER_NOT_MATCH_FOUR_WHEEL(2001002),

	/**
	 * 汽车询价失败
	 */
	FOUR_WHEEL_PREVIEW_FAIL(2001003),

	/**
	 * 呼叫汽车配送失败
	 */
	FOUR_WHEEL_DISPATCH_FAIL(2001004),

	/**
	 * 报价已失效，请重新选择车型
	 */
	FOUR_WHEEL_PRICE_INVALID(2001005),

	;

	private static final Map<Integer, ResponseCodeEnum> VALUE_ENUM_MAP = new HashMap<>();
	private final int value;

	static {
		for (ResponseCodeEnum each : values()) {
			VALUE_ENUM_MAP.put(each.getValue(), each);
		}
	}

	ResponseCodeEnum(int value) {
		this.value = value;
	}

	public static ResponseCodeEnum enumOf(int value){
		return VALUE_ENUM_MAP.get(value);
	}

	public int getValue() {
		return value;
	}
}
