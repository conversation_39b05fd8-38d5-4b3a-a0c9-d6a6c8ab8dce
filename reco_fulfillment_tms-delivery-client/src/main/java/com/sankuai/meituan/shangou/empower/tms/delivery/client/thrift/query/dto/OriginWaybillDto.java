package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024-08-08
 */
@TypeDoc(
        description = "原始运单"
)
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
public class OriginWaybillDto {
    @FieldDoc(
            description = "运单状态",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(1)
    public Integer deliveryStatus;

    @FieldDoc(
            description = "原始运单id",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(2)
    public String originWaybillNo;

}
