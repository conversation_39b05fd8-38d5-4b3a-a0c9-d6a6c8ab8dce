package com.sankuai.meituan.shangou.empower.tms.delivery.client.enums;

import java.util.Objects;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/10/22
 */
public enum DeliveryEntityEnum {
	/**
	 * 平台配送
	 */
	PLATFORM(0),
	/**
	 * 商家选择三方配送
	 */
	THIRD_PART(1),
	/**
	 * 商家亲自配送
	 */
	DELIVER_BY_SELF(2);

	private final int value;

	DeliveryEntityEnum(int value) {
		this.value = value;
	}

	public int getValue() {
		return value;
	}

	public static DeliveryEntityEnum findByValue(Integer value) {
		for (DeliveryEntityEnum each : values()) {
			if (Objects.equals(each.getValue(), value)) {
				return each;
			}
		}

		return null;
	}
}
