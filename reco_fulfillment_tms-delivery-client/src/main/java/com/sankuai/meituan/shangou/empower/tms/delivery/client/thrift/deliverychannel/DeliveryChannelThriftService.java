package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.deliverychannel;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.deliverychannel.request.DeliveryChannelBatchQueryByCarrierCodeRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.deliverychannel.request.DeliveryChannelQueryByCarrierCodeRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.deliverychannel.request.DeliveryChannelQueryByLogisticMarkRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.deliverychannel.response.DeliveryChannelBatchQueryResponse;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.deliverychannel.response.DeliveryChannelQueryResponse;

/**
 * 配送渠道服务
 *
 * <AUTHOR>
 * @date 2023/4/6
 */
@InterfaceDoc(
		displayName = "配送渠道服务",
		type = "octo.thrift.annotation",
		scenarios = "配送渠道服务，提供查询配送渠道信息（比如承运商名称）的能力",
		description = "配送渠道服务，提供查询配送渠道信息（比如承运商名称）的能力",
		authors = {
				"zhangjian155"
		}
)
@ThriftService
public interface DeliveryChannelThriftService {

	@MethodDoc(
			displayName = "根据牵牛花维护的承运商code查询配送渠道信息",
			description = "根据牵牛花维护的承运商code查询配送渠道信息",
			parameters = {
					@ParamDoc(
							name = "request",
							description = "根据牵牛花维护的承运商code查询配送渠道信息",
							type = DeliveryChannelQueryByCarrierCodeRequest.class,
							requiredness = Requiredness.REQUIRED
					)
			},
			returnValueDescription = "配送渠道信息",
			restExampleResponseData = "{\"status\":{\"code\":0, \"msg\":\"\"}}",
			example = "暂无"
	)
	@ThriftMethod
	DeliveryChannelQueryResponse queryDeliveryChannelByCarrierCode(DeliveryChannelQueryByCarrierCodeRequest request);

	@MethodDoc(
			displayName = "根据聚合配送平台的承运商信息查询配送渠道信息",
			description = "根据聚合配送平台的承运商信息查询配送渠道信息",
			parameters = {
					@ParamDoc(
							name = "request",
							description = "配送变更回调请求",
							type = DeliveryChannelQueryByLogisticMarkRequest.class,
							requiredness = Requiredness.REQUIRED
					)
			},
			returnValueDescription = "配送渠道信息",
			restExampleResponseData = "{\"status\":{\"code\":0, \"msg\":\"\"}}",
			example = "暂无"
	)
	@ThriftMethod
	DeliveryChannelQueryResponse queryDeliveryChannelByLogisticMark(DeliveryChannelQueryByLogisticMarkRequest request);

	@MethodDoc(
			displayName = "根据牵牛花维护的承运商code批量查询配送渠道信息",
			description = "根据牵牛花维护的承运商code批量查询配送渠道信息",
			parameters = {
					@ParamDoc(
							name = "request",
							description = "根据牵牛花维护的承运商code批量查询配送渠道信息",
							type = DeliveryChannelQueryByCarrierCodeRequest.class,
							requiredness = Requiredness.REQUIRED
					)
			},
			returnValueDescription = "配送渠道信息",
			restExampleResponseData = "{\"status\":{\"code\":0, \"msg\":\"\"}}",
			example = "暂无"
	)
	@ThriftMethod
	DeliveryChannelBatchQueryResponse batchQueryDeliveryChannelByCarrierCodeList(DeliveryChannelBatchQueryByCarrierCodeRequest request);
}
