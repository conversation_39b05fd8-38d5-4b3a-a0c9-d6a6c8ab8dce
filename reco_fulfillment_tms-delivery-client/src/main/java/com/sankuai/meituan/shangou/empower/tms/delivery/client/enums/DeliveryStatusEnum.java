package com.sankuai.meituan.shangou.empower.tms.delivery.client.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

import java.util.*;

import static com.google.common.collect.ImmutableSet.of;

/**
 * 配送运单状态枚举
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/3
 */
public enum DeliveryStatusEnum {

    /**
     * 初始化，等待发起配送
     */
    INIT(0, "等待发起配送"),

    /**
     * 已发起配送，等待配送平台接单
     */
    DELIVERY_LAUNCHED(1, "等待配送平台接单"),

    /**
     * 等待分配骑手，配送平台已接单
     */
    WAITING_TO_ASSIGN_RIDER(30, "等待分配骑手"),

    /**
     * 骑手已接单
     */
    RIDER_ASSIGNED(40, "骑手已接单"),

    /**
     * 骑手已到店
     */
    RIDER_ARRIVED_SHOP(45, "骑手已到店"),

    /**
     * 骑手已取货
     */
    RIDER_TAKEN_GOODS(50, "骑手已取货"),

    /**
     * 商家配送中
     */
    MERCHANT_DELIVERING(51, "自行配送"),

    /**
     * 配送完成，骑手已送达
     */
    DELIVERY_DONE(60, "骑手已送达"),

    /**
     * 配送拒单
     */
    DELIVERY_REJECTED(110, "配送拒单"),

    /**
     * 配送失败
     */
    DELIVERY_FAILED(120, "配送失败"),

    /**
     * 配送已取消
     */
    DELIVERY_CANCELLED(130, "配送已取消");

    private static final Set<DeliveryStatusEnum> OPEN_STATUS_SET = of(INIT, DELIVERY_LAUNCHED, WAITING_TO_ASSIGN_RIDER, RIDER_ASSIGNED,
            RIDER_ARRIVED_SHOP, RIDER_TAKEN_GOODS, MERCHANT_DELIVERING);

    private static final Set<DeliveryStatusEnum> FINAL_STATUS_SET = of(DELIVERY_DONE, DELIVERY_REJECTED, DELIVERY_FAILED, DELIVERY_CANCELLED);

    private static final Set<DeliveryStatusEnum> CAN_TURN_TO_SELF_DELIVERY_STATUS_SET = of(WAITING_TO_ASSIGN_RIDER, DELIVERY_REJECTED,
            DELIVERY_CANCELLED);

    private static final Set<DeliveryStatusEnum> NEED_QUERY_RIDER_LOCATION_STATUS_SET = of(RIDER_ASSIGNED, RIDER_ARRIVED_SHOP, RIDER_TAKEN_GOODS, DELIVERY_DONE);

    private static final Set<DeliveryStatusEnum> CAN_CLEAR_EXCEPTION_SET = of(DELIVERY_LAUNCHED, WAITING_TO_ASSIGN_RIDER, RIDER_ASSIGNED, RIDER_ARRIVED_SHOP, RIDER_TAKEN_GOODS, MERCHANT_DELIVERING, DELIVERY_DONE, DELIVERY_CANCELLED);

    private static final Set<DeliveryStatusEnum> FAIL_STATUS_SET = of(DELIVERY_REJECTED, DELIVERY_FAILED, DELIVERY_CANCELLED);

    private static final Set<DeliveryStatusEnum> RIDER_ACCEPT_SET = of(RIDER_ASSIGNED,RIDER_ARRIVED_SHOP,RIDER_TAKEN_GOODS,DELIVERY_DONE);

    private static final Map<Integer, DeliveryStatusEnum> CODE_TO_ENUM_MAP = new HashMap<>();
    private final int code;
    private final String desc;

    static {
        for (DeliveryStatusEnum each : values()) {
            CODE_TO_ENUM_MAP.put(each.getCode(), each);
        }
    }

    DeliveryStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @JsonValue
    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public boolean isFinalStatus() {
        return FINAL_STATUS_SET.contains(this);
    }

    public boolean canTurnToSelfDelivery() {
        return CAN_TURN_TO_SELF_DELIVERY_STATUS_SET.contains(this);
    }

    public boolean needQueryRiderLocation() {
        return NEED_QUERY_RIDER_LOCATION_STATUS_SET.contains(this);
    }

    public boolean riderHasAccept(){
        return RIDER_ACCEPT_SET.contains(this);
    }

    public boolean isExceptionStatus(){
        return FAIL_STATUS_SET.contains(this);
    }


    public static List<Integer> getOpenStatusCodes() {
        List<Integer> list = new ArrayList<>(OPEN_STATUS_SET.size());
        for (DeliveryStatusEnum each : OPEN_STATUS_SET) {
            list.add(each.getCode());
        }
        return list;
    }

    @JsonCreator
    public static DeliveryStatusEnum valueOf(int code) {
        return CODE_TO_ENUM_MAP.get(code);
    }
}
