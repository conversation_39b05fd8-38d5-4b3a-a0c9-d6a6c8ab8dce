package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang.StringUtils;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 */
@TypeDoc(
        description = "查询门店信息请求体",
        authors = {
                "yuanyu09"
        }
)
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
public class QueryShopDetailRequest {
        @FieldDoc(
                description = "赋能门店id",
                requiredness = Requiredness.REQUIRED
        )
        @ThriftField(1)
        public String storeId;

        public String validate() {
                if (storeId == null || storeId.length() == 0) {
                        return "赋能门店ID不合法";
                }

                return null;
        }
}
