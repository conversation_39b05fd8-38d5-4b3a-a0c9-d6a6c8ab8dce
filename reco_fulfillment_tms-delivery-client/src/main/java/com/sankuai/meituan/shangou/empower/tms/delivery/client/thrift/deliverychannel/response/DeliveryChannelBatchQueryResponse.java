package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.deliverychannel.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.common.Status;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.deliverychannel.dto.DeliveryChannelDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 配送渠道批量查询响应体.
 *
 * <AUTHOR>
 * @since 2023/4/6
 */
@TypeDoc(
        description = "配送渠道批量查询响应体",
        authors = {
                "zhangjian155"
        }
)
@ThriftStruct
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DeliveryChannelBatchQueryResponse {

    @FieldDoc(
            description = "执行状态",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public Status status;

    @FieldDoc(
            description = "配送渠道信息",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(2)
    public List<DeliveryChannelDto> deliveryChannelDtoList;
}
