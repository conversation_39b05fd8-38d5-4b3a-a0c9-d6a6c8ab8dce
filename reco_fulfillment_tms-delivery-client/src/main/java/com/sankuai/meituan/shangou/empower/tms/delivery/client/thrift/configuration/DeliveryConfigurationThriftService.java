package com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.callback.request.DapShopAuthNotifyRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.callback.request.SelfDeliveryCallbackReq;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.callback.response.DapShopAuthNotifyResp;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.request.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.response.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.request.DeliveryDimensionPoiQueryRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.response.DeliveryDimensionPoiQueryResponse;
import org.apache.thrift.TException;

/**
 * 配送操作服务，提供发起配送，转自配送等能力
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/11/20
 */
@InterfaceDoc(
		displayName = "配送配置服务",
		type = "octo.thrift.annotation",
		scenarios = "配送配置服务，提供配送对接配置修改，同步配送范围等能力",
		description = "配送配置服务，提供配送对接配置修改，同步配送范围等能力",
		authors = {
				"hedong07"
		}
)
@ThriftService
public interface DeliveryConfigurationThriftService {


	@MethodDoc(
			displayName = "查询门店配送配置",
			description = "查询门店配送配置",
			parameters = {
					@ParamDoc(
							name = "request",
							description = "查询门店配送配置",
							type = DeliveryRangeSyncRequest.class,
							requiredness = Requiredness.REQUIRED
					)
			},
			returnValueDescription = "查询门店配送配置",
			restExampleResponseData = "{\"status\":{\"code\":0},\"msg\":\"\"}",
			example = "暂无"
	)
	@ThriftMethod
	StoreConfigQueryResponse queryStoreConfiguration(StoreConfigQueryRequest request);

	@MethodDoc(
			displayName = "查询门店配送配置",
			description = "查询门店配送配置",
			parameters = {
					@ParamDoc(
							name = "request",
							description = "查询门店配送配置",
							type = DeliveryRangeSyncRequest.class,
							requiredness = Requiredness.REQUIRED
					)
			},
			returnValueDescription = "查询门店配送配置",
			restExampleResponseData = "{\"status\":{\"code\":0},\"msg\":\"\"}",
			example = "暂无"
	)
	@ThriftMethod
	BatchStoreConfigQueryResponse batchQueryStoreConfiguration(BatchStoreConfigQueryRequest request);

	@MethodDoc(
			displayName = "保存门店配送配置",
			description = "保存门店配送配置",
			parameters = {
					@ParamDoc(
							name = "request",
							description = "查询门店配送配置",
							type = DeliveryRangeSyncRequest.class,
							requiredness = Requiredness.REQUIRED
					)
			},
			returnValueDescription = "查询门店配送配置",
			restExampleResponseData = "{\"status\":{\"code\":0},\"msg\":\"\"}",
			example = "暂无"
	)
	@ThriftMethod
	ConfigCommonResponse saveStoreConfiguration(StoreConfigSaveRequest request);

	@MethodDoc(
			displayName = "保存门店配送配置",
			description = "保存门店配送配置",
			parameters = {
					@ParamDoc(
							name = "request",
							description = "保存门店配送配置",
							type = DeliveryRangeSyncRequest.class,
							requiredness = Requiredness.REQUIRED
					)
			},
			returnValueDescription = "查询门店配送配置",
			restExampleResponseData = "{\"status\":{\"code\":0},\"msg\":\"\"}",
			example = "暂无"
	)
	@ThriftMethod
	ConfigCommonResponse saveDeliveryStoreConfiguration(DeliveryStoreConfigSaveRequest request);


	@MethodDoc(
			displayName = "中台门店解绑（聚合运力平台）配送商门店",
			description = "中台门店解绑（聚合运力平台）配送商门店",
			parameters = {
					@ParamDoc(
							name = "request",
							description = "中台门店解绑（聚合运力平台）配送商门店请求",
							type = DeliveryRangeSyncRequest.class,
							requiredness = Requiredness.REQUIRED
					)
			},
			returnValueDescription = "解绑配送商门店的结果",
			restExampleResponseData = "{\"status\":{\"code\":0},\"msg\":\"\"}",
			example = "暂无"
	)
	@ThriftMethod
	ConfigCommonResponse unbindDeliveryCompanyPoiOnAggr(UnbindDeliveryPoiOnAggrPlatformRequest request);

	@MethodDoc(
			displayName = "同步配送范围",
			description = "同步配送范围",
			parameters = {
					@ParamDoc(
							name = "request",
							description = "同步配送范围请求",
							type = DeliveryRangeSyncRequest.class,
							requiredness = Requiredness.REQUIRED
					)
			},
			returnValueDescription = "同步配送范围操作结果",
			restExampleResponseData = "{\"status\":{\"code\":0},\"msg\":\"\"}",
			example = "暂无"
	)
	@ThriftMethod
	DeliveryRangeSyncResponse syncDeliveryRange(DeliveryRangeSyncRequest request);

	@MethodDoc(
			displayName = "查询收货人对应的配送范围的标签",
			description = "查询收货人对应的配送范围的标签",
			parameters = {
					@ParamDoc(
							name = "request",
							description = "查询收货人对应的配送范围的标签",
							type = DeliveryRangeSyncRequest.class,
							requiredness = Requiredness.REQUIRED
					)
			},
			returnValueDescription = "查询收货人对应的配送范围的标签",
			restExampleResponseData = "{\"status\":{\"code\":0},\"msg\":\"\"}",
			example = "暂无"
	)
	@ThriftMethod
	QueryDeliveryShippingTagResponse queryReceiverRelatedDeliveryRangeTagList(QueryDeliveryShippingTagRequest request) throws TException;

	@MethodDoc(
			displayName = "查询租户配送配置",
			description = "查询租户配送配置",
			parameters = {
					@ParamDoc(
							name = "request",
							description = "查询租户配送配置请求",
							type = TenantDeliveryConfigQueryRequest.class,
							requiredness = Requiredness.REQUIRED
					)
			},
			returnValueDescription = "租户配送配置信息",
			restExampleResponseData = "{\"status\":{\"code\":0},\"msg\":\"\"}",
			example = "暂无"
	)
	@ThriftMethod
	TenantDeliveryConfigQueryResponse queryTenantDeliveryConfig(TenantDeliveryConfigQueryRequest request);

	@MethodDoc(
			displayName = "修改租户配送配置",
			description = "修改租户配送配置",
			parameters = {
					@ParamDoc(
							name = "request",
							description = "修改租户配送配置请求",
							type = TenantDeliveryConfigUpdateRequest.class,
							requiredness = Requiredness.REQUIRED
					)
			},
			returnValueDescription = "修改租户配送配置结果",
			restExampleResponseData = "{\"status\":{\"code\":0},\"msg\":\"\"}",
			example = "暂无"
	)
	@ThriftMethod
	TenantDeliveryConfigUpdateResponse updateTenantDeliveryConfig(TenantDeliveryConfigUpdateRequest request);

	@MethodDoc(
			displayName = "查询门店/仓库聚合配送配置",
			description = "查询门店/仓库聚合配送配置",
			parameters = {
					@ParamDoc(
							name = "request",
							description = "查询门店/仓库聚合配送配置",
							type = StoreAggDeliveryConfigQueryRequest.class,
							requiredness = Requiredness.REQUIRED
					)
			},
			returnValueDescription = "查询门店/仓库聚合配送配置",
			restExampleResponseData = "{\"status\":{\"code\":0},\"msg\":\"\"}",
			example = "暂无"
	)
	/**
	 * @Description:该门店配置查询的方法已经废弃
	 */
	@ThriftMethod
	StoreAggDeliveryConfigQueryResponse queryStoreAggDeliveryConfig(StoreAggDeliveryConfigQueryRequest request);

	@MethodDoc(
			displayName = "查询门店/仓库配送配置",
			description = "查询门店/仓库配送配置",
			parameters = {
					@ParamDoc(
							name = "request",
							description = "查询门店/仓库聚合配送配置",
							type = StoreDeliveryConfigQueryRequest.class,
							requiredness = Requiredness.REQUIRED
					)
			},
			returnValueDescription = "查询门店/仓库聚合配送配置",
			restExampleResponseData = "{\"status\":{\"code\":0},\"msg\":\"\"}",
			example = "暂无"
	)
	@ThriftMethod
	StoreDeliveryConfigQueryResponse queryStoreDeliveryConfig(StoreDeliveryConfigQueryRequest request);

	@MethodDoc(
			displayName = "修改门店/仓库聚合配送配置",
			description = "查询门店/修改门店/仓库聚合配送配置",
			parameters = {
					@ParamDoc(
							name = "request",
							description = "修改门店/仓库聚合配送配置",
							type = StoreAggDeliveryConfigModifyRequest.class,
							requiredness = Requiredness.REQUIRED
					)
			},
			returnValueDescription = "修改门店/仓库聚合配送配置",
			restExampleResponseData = "{\"status\":{\"code\":0},\"msg\":\"\"}",
			example = "暂无"
	)
	@ThriftMethod
	ConfigCommonResponse modifyStoreAggDeliveryConfig(StoreAggDeliveryConfigModifyRequest request);

	@MethodDoc(
			displayName = "批量查询门店/仓库聚合配送配置",
			description = "批量查询门店/仓库聚合配送配置",
			parameters = {
					@ParamDoc(
							name = "request",
							description = "批量查询门店/仓库聚合配送配置",
							type = BatchStoreDeliveryConfigQueryRequest.class,
							requiredness = Requiredness.REQUIRED
					)
			},
			returnValueDescription = "批量查询门店/仓库聚合配送配置",
			restExampleResponseData = "{\"status\":{\"code\":0},\"msg\":\"\"}",
			example = "暂无"
	)
	@ThriftMethod
	StoreDeliveryConfigBatchQueryResponse batchQueryStoreDeliveryConfig(BatchStoreDeliveryConfigQueryRequest request);

	@MethodDoc(
			displayName = "批量导入门店配送配置时，调用的保存门店配置接口",
			description = "批量导入门店配送配置时，调用的保存门店配置接口",
			parameters = {
					@ParamDoc(
							name = "request",
							description = "批量导入门店配送配置时，调用的保存门店配置接口",
							type = DeliveryRangeSyncRequest.class,
							requiredness = Requiredness.REQUIRED
					)
			},
			returnValueDescription = "批量导入门店配送配置时，调用的保存门店配置接口",
			restExampleResponseData = "{\"status\":{\"code\":0},\"msg\":\"\"}",
			example = "暂无"
	)
	@ThriftMethod
	ConfigCommonResponse saveBatchImportDeliveryStoreConfiguration(BatchImportDeliveryStoreConfigSaveRequest request);

	@MethodDoc(
			displayName = "青云门店授权成功通知",
			description = "青云门店授权成功通知",
			parameters = {
					@ParamDoc(
							name = "request",
							description = "青云门店授权成功请求",
							type = SelfDeliveryCallbackReq.class,
							requiredness = Requiredness.REQUIRED
					)
			},
			returnValueDescription = "青云门店授权成功通知",
			restExampleResponseData = "{\"status\":{\"code\":0, \"msg\":\"\"}}",
			example = "暂无"
	)
	@ThriftMethod
	DapShopAuthNotifyResp dapShopAuthNotify(DapShopAuthNotifyRequest req);

	@MethodDoc(
			displayName = "简单查询门店配送配置，只从DB查询数据，不做查询租户渠道信息以及初始化动作",
			description = "简单查询门店配送配置，只从DB查询数据，不做查询租户渠道信息以及初始化动作",
			parameters = {
					@ParamDoc(
							name = "request",
							description = "简单查询租户下门店配送配置请求体",
							type = StoreDeliveryConfigSimpleQueryRequest.class,
							requiredness = Requiredness.REQUIRED
					)
			},
			returnValueDescription = "简单查询门店/仓配送配置返回体",
			restExampleResponseData = "{\"status\":{\"code\":0, \"msg\":\"\"}}",
			example = "暂无"
	)
	@ThriftMethod
	DeliveryConfigSimpleQueryResponse simpleQueryDeliveryConfig(StoreDeliveryConfigSimpleQueryRequest request);

	@MethodDoc(
		displayName = "查询配送门店维度配置",
		description = "根据租户ID和门店ID查询配送门店维度配置信息",
		parameters = {
			@ParamDoc(
				name = "request",
				description = "查询配送门店维度配置请求体",
				type = DeliveryDimensionPoiQueryRequest.class,
				requiredness = Requiredness.REQUIRED
			)
		},
		returnValueDescription = "查询配送门店维度配置响应体",
		restExampleResponseData = "{\"status\":{\"code\":0, \"msg\":\"\"}}",
		example = "暂无"
	)
	@ThriftMethod
	DeliveryDimensionPoiQueryResponse queryDeliveryDimensionPoi(DeliveryDimensionPoiQueryRequest request);
}
