package com.sankuai.meituan.shangou.empower.tms.delivery.client.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/7/13
 */
public enum DeliveryCallbackTypeEnum {

	/**
	 * 配送发起回调
	 */
	LAUNCH_CALLBACK(1),

	/**
	 * 配送取消回调
	 */
	CANCEL_CALLBACK(2),

	/**
	 * 配送变更回调
	 */
	CHANGE_CALLBACK(3),

	/**
	 * 配送异常回调
	 */
	EXCEPTION_CALLBACK(4);

	private static final Map<Integer, DeliveryCallbackTypeEnum> CODE_ENUM_MAP = new HashMap<>();
	private final int code;

	static {
		for (DeliveryCallbackTypeEnum each : values()) {
			CODE_ENUM_MAP.put(each.getCode(), each);
		}
	}

	DeliveryCallbackTypeEnum(int code) {
		this.code = code;
	}

	public int getCode() {
		return code;
	}

	public static DeliveryCallbackTypeEnum enumOf(Integer code) {
		return CODE_ENUM_MAP.get(code);
	}
}
