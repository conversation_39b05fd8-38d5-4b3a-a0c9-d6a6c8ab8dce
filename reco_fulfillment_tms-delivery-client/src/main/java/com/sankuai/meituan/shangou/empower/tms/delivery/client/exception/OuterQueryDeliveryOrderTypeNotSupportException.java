package com.sankuai.meituan.shangou.empower.tms.delivery.client.exception;

import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.FailureCodeEnum;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/02/11
 * desc: 运单类型不支持对外查询
 */
@Data
public class OuterQueryDeliveryOrderTypeNotSupportException extends RuntimeException {

    private String msg;

    private Integer code;

    public OuterQueryDeliveryOrderTypeNotSupportException(FailureCodeEnum failureCodeEnum) {
        super(failureCodeEnum.getMessage());

        this.code = failureCodeEnum.getCode();
        this.msg = failureCodeEnum.getMessage();
    }

}
