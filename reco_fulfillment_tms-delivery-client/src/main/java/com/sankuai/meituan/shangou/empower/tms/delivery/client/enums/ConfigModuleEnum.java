package com.sankuai.meituan.shangou.empower.tms.delivery.client.enums;

/**
 * <AUTHOR>
 * @email LIUYONG<PERSON><EMAIL>
 * @date 2021/1/7
 */
public enum ConfigModuleEnum {
	/**
	 * 渠道开通或者修改
	 */
	CHANNEL(1),
	/**
	 * 发配送方式和节点修改
	 */
	CONFIG(2),

	/**
	 * 聚合运力平台开通或者修改
	 */
	PLATFORM(3);

	private final int value;

	private ConfigModuleEnum(int value) {
		this.value = value;
	}

	public int getValue() {
		return value;
	}

	public static ConfigModuleEnum findByValue(int value) {
		for (ConfigModuleEnum each : values()) {
			if (each.getValue() == value) {
				return each;
			}
		}

		return null;
	}
}
