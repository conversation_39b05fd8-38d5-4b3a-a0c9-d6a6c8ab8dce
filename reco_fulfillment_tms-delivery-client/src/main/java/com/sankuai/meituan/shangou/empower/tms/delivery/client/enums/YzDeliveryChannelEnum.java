package com.sankuai.meituan.shangou.empower.tms.delivery.client.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * 有赞平台配送，三方运力枚举
 * @see "https://doc.youzanyun.com/detail/api/0/858"
 */
public enum YzDeliveryChannelEnum {

	/**
	 * 达达
	 */
	DADA_DELIVERY_CHANNEL(1, DeliveryChannelEnum.ORDER_CHANNEL_DELIVERY_DADA, "达达"),

	/**
	 * 蜂鸟
	 */
	FENGNIAO_DELIVERY_CHANNEL(2, DeliveryChannelEnum.ORDER_CHANNEL_DELIVERY_FENGNIAO, "蜂鸟"),

	/**
	 * 点我达
	 */
	DIANWODA_DELIVERY_CHANNEL(3,  DeliveryChannelEnum.ORDER_CHANNEL_DELIVERY_DIANWODA, "点我达"),

	/**
	 * 顺丰同城
	 */
	SHUNFENG_DELIVERY_CHANNEL(4, DeliveryChannelEnum.ORDER_CHANNEL_DELIVERY_SHUNFENG, "顺丰同城"),

	/**
	 * 美团配送
	 */
	MEITUAN_DELIVERY_CHANNEL(5, DeliveryChannelEnum.ORDER_CHANNEL_DELIVERY_MEITUAN, "美团配送"),

	/**
	 * 同城上云（uu跑腿，快服务等）包含了包括闪送在内的所有没有单独列出code的运力平台
	 */
	TCSY_DELIVERY_CHANNEL(9, DeliveryChannelEnum.ORDER_CHANNEL_DELIVERY_TCSY, "同城上云"),

	/**
	 * 未知三方运力
	 */
	UNKNOWN_DELIVERY_CHANNEL(-1, DeliveryChannelEnum.ORDER_CHANNEL_DELIVERY_UNKNOW, "平台配送-未知配送商")
	;

	private final int code;

	private final DeliveryChannelEnum deliveryChannel;

	private final String desc;

	private static final Map<DeliveryChannelEnum, YzDeliveryChannelEnum> ENUM_MAP = new HashMap<>();

	private static final Map<Integer, YzDeliveryChannelEnum> CODE_ENUM_MAP = new HashMap<>();

	static {
		for (YzDeliveryChannelEnum each : values()) {
			ENUM_MAP.put(each.deliveryChannel, each);
			CODE_ENUM_MAP.put(each.code, each);
		}
	}

	YzDeliveryChannelEnum(int code, DeliveryChannelEnum deliveryChannel, String desc) {
		this.code = code;
		this.deliveryChannel = deliveryChannel;
		this.desc = desc;
	}

	public static YzDeliveryChannelEnum getYzDeliveryChannel(DeliveryChannelEnum deliveryChannel) {
		return ENUM_MAP.get(deliveryChannel);
	}

	public static YzDeliveryChannelEnum getYzDeliveryChannel(int code) {
		return CODE_ENUM_MAP.get(code);
	}

	public int getCode() {
		return code;
	}

	public DeliveryChannelEnum getDeliveryChannel() {
		return deliveryChannel;
	}
}
