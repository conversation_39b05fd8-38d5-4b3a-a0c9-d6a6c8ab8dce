package com.sankuai.shangou.waima.demo;

import com.google.common.collect.Lists;

/**
 * <AUTHOR>
 * @date 2025-08-12
 * @email <EMAIL>
 */
public class Main {

    public static void main(String[] args) {
        String INSERT_CONFIG = "INSERT INTO `sdms_store_config` (`id`, `tenant_id`, `store_operation_mode`, `city_id`, `store_id`, `valid_time_start`, `valid_time_end`, `is_deleted`) VALUES ('%d', '0', '0', '0', '%d', '2025-03-12 11:28:53.206', '2025-03-12 11:28:53.206', '0');";
        String INSERT_TIME1 = "INSERT INTO `assess_delivery_time_config` (`id`, `belong_store_config_id`, `config_json`, `calc_unit`, `create_time`, `update_time`, `is_deleted`) VALUES ('%d', '%d', '{\\\"subs\\\": [{\\\"subs\\\": [{\\\"subs\\\": [{\\\"subs\\\": [{\\\"formula\\\": \\\"15\\\", \\\"condition\\\": {\\\"name\\\": \\\"配送距离\\\", \\\"formula\\\": \\\"${delivery_distance}\\\", \\\"defineId\\\": 9, \\\"interval\\\": {\\\"values\\\": [\\\"0\\\", \\\"2500\\\"], \\\"intervalType\\\": 2}, \\\"identifier\\\": \\\"@{delivery_distance}\\\"}}, {\\\"formula\\\": \\\"20\\\", \\\"condition\\\": {\\\"name\\\": \\\"配送距离\\\", \\\"formula\\\": \\\"${delivery_distance}\\\", \\\"defineId\\\": 9, \\\"interval\\\": {\\\"values\\\": [\\\"2500\\\", \\\"infinity\\\"], \\\"intervalType\\\": 2}, \\\"identifier\\\": \\\"@{delivery_distance}\\\"}}]}], \\\"condition\\\": {\\\"name\\\": \\\"餐馆场景\\\", \\\"formula\\\": \\\"${restaurant_scene}\\\", \\\"defineId\\\": 9, \\\"interval\\\": {\\\"values\\\": [\\\"1\\\"], \\\"intervalType\\\": 6}, \\\"identifier\\\": \\\"@{restaurant_scene}\\\"}}], \\\"condition\\\": {\\\"name\\\": \\\"订单预约类型\\\", \\\"formula\\\": \\\"${reserve_type}\\\", \\\"interval\\\": {\\\"values\\\": [\\\"0\\\"], \\\"intervalType\\\": 6}, \\\"identifier\\\": \\\"@{reserve_type}\\\"}}]}', '0', '2025-08-08 15:21:38.522', '2025-08-08 15:21:38.522', '0');";
        String INSERT_TIME2 = "INSERT INTO `assess_delivery_time_config` (`id`, `belong_store_config_id`, `config_json`, `calc_unit`, `create_time`, `update_time`, `is_deleted`) VALUES ('%d', '%d', '{\\\"subs\\\": [{\\\"subs\\\": [{\\\"subs\\\": [{\\\"subs\\\": [{\\\"formula\\\": \\\"${eta_duration}\\\", \\\"condition\\\": {\\\"name\\\": \\\"配送距离\\\", \\\"formula\\\": \\\"${delivery_distance}\\\", \\\"defineId\\\": 9, \\\"interval\\\": {\\\"values\\\": [\\\"0\\\", \\\"infinity\\\"], \\\"intervalType\\\": 2}, \\\"identifier\\\": \\\"@{delivery_distance}\\\"}}]}], \\\"condition\\\": {\\\"name\\\": \\\"餐馆场景\\\", \\\"formula\\\": \\\"${restaurant_scene}\\\", \\\"defineId\\\": 9, \\\"interval\\\": {\\\"values\\\": [\\\"0\\\"], \\\"intervalType\\\": 6}, \\\"identifier\\\": \\\"@{restaurant_scene}\\\"}}], \\\"condition\\\": {\\\"name\\\": \\\"订单预约类型\\\", \\\"formula\\\": \\\"${reserve_type}\\\", \\\"interval\\\": {\\\"values\\\": [\\\"0\\\"], \\\"intervalType\\\": 6}, \\\"identifier\\\": \\\"@{reserve_type}\\\"}}]}', '0', '2025-08-08 15:21:38.658', '2025-08-08 15:21:38.658', '0');";
        String INSERT_TIME3 = "INSERT INTO `assess_delivery_time_config` (`id`, `belong_store_config_id`, `config_json`, `calc_unit`, `create_time`, `update_time`, `is_deleted`) VALUES ('%d', '%d', '{\\\"subs\\\": [{\\\"subs\\\": [{\\\"subs\\\": [{\\\"subs\\\": [{\\\"formula\\\": \\\"15*60\\\", \\\"condition\\\": {\\\"name\\\": \\\"配送距离\\\", \\\"formula\\\": \\\"${delivery_distance}\\\", \\\"defineId\\\": 9, \\\"interval\\\": {\\\"values\\\": [\\\"0\\\", \\\"2500\\\"], \\\"intervalType\\\": 2}, \\\"identifier\\\": \\\"@{delivery_distance}\\\"}}, {\\\"formula\\\": \\\"20*60\\\", \\\"condition\\\": {\\\"name\\\": \\\"配送距离\\\", \\\"formula\\\": \\\"${delivery_distance}\\\", \\\"defineId\\\": 9, \\\"interval\\\": {\\\"values\\\": [\\\"2500\\\", \\\"infinity\\\"], \\\"intervalType\\\": 2}, \\\"identifier\\\": \\\"@{delivery_distance}\\\"}}]}], \\\"condition\\\": {\\\"name\\\": \\\"餐馆场景\\\", \\\"formula\\\": \\\"${restaurant_scene}\\\", \\\"defineId\\\": 9, \\\"interval\\\": {\\\"values\\\": [\\\"1\\\"], \\\"intervalType\\\": 6}, \\\"identifier\\\": \\\"@{restaurant_scene}\\\"}}], \\\"condition\\\": {\\\"name\\\": \\\"订单预约类型\\\", \\\"formula\\\": \\\"${reserve_type}\\\", \\\"interval\\\": {\\\"values\\\": [\\\"0\\\"], \\\"intervalType\\\": 6}, \\\"identifier\\\": \\\"@{reserve_type}\\\"}}]}', '1', '2025-08-08 15:21:38.522', '2025-08-08 15:21:38.522', '0');";
        String INSERT_TIME4 = "INSERT INTO `assess_delivery_time_config` (`id`, `belong_store_config_id`, `config_json`, `calc_unit`, `create_time`, `update_time`, `is_deleted`) VALUES ('%d', '%d', '{\\\"subs\\\": [{\\\"subs\\\": [{\\\"subs\\\": [{\\\"subs\\\": [{\\\"formula\\\": \\\"${eta_duration}\\\", \\\"condition\\\": {\\\"name\\\": \\\"配送距离\\\", \\\"formula\\\": \\\"${delivery_distance}\\\", \\\"defineId\\\": 9, \\\"interval\\\": {\\\"values\\\": [\\\"0\\\", \\\"infinity\\\"], \\\"intervalType\\\": 2}, \\\"identifier\\\": \\\"@{delivery_distance}\\\"}}]}], \\\"condition\\\": {\\\"name\\\": \\\"餐馆场景\\\", \\\"formula\\\": \\\"${restaurant_scene}\\\", \\\"defineId\\\": 9, \\\"interval\\\": {\\\"values\\\": [\\\"0\\\"], \\\"intervalType\\\": 6}, \\\"identifier\\\": \\\"@{restaurant_scene}\\\"}}], \\\"condition\\\": {\\\"name\\\": \\\"订单预约类型\\\", \\\"formula\\\": \\\"${reserve_type}\\\", \\\"interval\\\": {\\\"values\\\": [\\\"0\\\"], \\\"intervalType\\\": 6}, \\\"identifier\\\": \\\"@{reserve_type}\\\"}}]}', '1', '2025-08-08 15:21:38.658', '2025-08-08 16:51:34.853', '0');";

        long configId = 47;
        long timeId = 242;
        for (Integer storeId : Lists.newArrayList(1127514,1117485,1102091,1093784,1091018,1075798,1075347,1099110,1083979,1086596,1119380,1095693,1125693,1127938,1096768,1096204,1123215,1125212,1101840,1114970,1092041,1096070,1103537,1160972,1160499,1156740,1156248,1156743,1156741,1174933,1160616,1156247)) {
            configId++;
            String INSERT_CONFIG_FORMAT = String.format(INSERT_CONFIG, configId, storeId);
            System.out.println(INSERT_CONFIG_FORMAT);

            timeId++;
            String INSERT_TIME1_FORMAT = String.format(INSERT_TIME1, timeId, configId);
            System.out.println(INSERT_TIME1_FORMAT);

            timeId++;
            String INSERT_TIME2_FORMAT = String.format(INSERT_TIME2, timeId, configId);
            System.out.println(INSERT_TIME2_FORMAT);

            timeId++;
            String INSERT_TIME3_FORMAT = String.format(INSERT_TIME3, timeId, configId);
            System.out.println(INSERT_TIME3_FORMAT);


            timeId++;
            String INSERT_TIME4_FORMAT = String.format(INSERT_TIME4, timeId, configId);
            System.out.println(INSERT_TIME4_FORMAT);

            System.out.println();
        }

    }
}
