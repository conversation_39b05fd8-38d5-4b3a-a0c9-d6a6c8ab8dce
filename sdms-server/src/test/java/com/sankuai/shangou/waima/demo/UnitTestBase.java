package com.sankuai.shangou.logistics.sdms;

import org.junit.Before;
import org.junit.Ignore;
import org.junit.Rule;
import org.junit.rules.ExpectedException;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * 单元测试基类
 * @Author: gantianxing
 * @Date: 2019-09-24 16-44
 **/
@RunWith(MockitoJUnitRunner.class)
@Ignore
public class UnitTestBase {
    protected static final String loginEmployeeId = "1200";
    @Rule
    public ExpectedException thrown = ExpectedException.none();
    @Before
    public void startUp() {

    }

}
