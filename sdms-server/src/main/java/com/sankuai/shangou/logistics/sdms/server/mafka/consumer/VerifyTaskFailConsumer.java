package com.sankuai.shangou.logistics.sdms.server.mafka.consumer;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.meituan.xframe.boot.mafka.autoconfigure.annotation.MafkaConsumer;
import com.sankuai.shangou.bizmng.labor.api.employee.dto.LaborDTO;
import com.sankuai.shangou.commons.exception.common.exceptions.BizException;
import com.sankuai.shangou.infra.osw.api.org.dto.response.EmployeeDTO;
import com.sankuai.shangou.logistics.sdms.application.utils.LionConfigUtils;
import com.sankuai.shangou.logistics.sdms.dao.mapper.YodaIdentifyTaskOpLogPOMapper;
import com.sankuai.shangou.logistics.sdms.dao.mapper.YodaIdentifyTaskPOMapper;
import com.sankuai.shangou.logistics.sdms.dao.model.YodaIdentifyTaskPO;
import com.sankuai.shangou.logistics.sdms.domain.entity.poi.PoiBaseInfo;
import com.sankuai.shangou.logistics.sdms.domain.entity.yoda.VerifyNoControlInfo;
import com.sankuai.shangou.logistics.sdms.domain.entity.yoda.VerifyNoControlRule;
import com.sankuai.shangou.logistics.sdms.domain.entity.yoda.YodaIdentityTaskConfig;
import com.sankuai.shangou.logistics.sdms.domain.entity.yoda.YodaVerifyTaskExtInfo;
import com.sankuai.shangou.logistics.sdms.domain.external.LaborClient;
import com.sankuai.shangou.logistics.sdms.domain.mq.message.VerifyFailMessage;
import com.sankuai.shangou.logistics.sdms.domain.repository.YodaIdentityTaskConfigRepository;
import com.sankuai.shangou.logistics.sdms.domain.utils.TimeUtils;
import com.sankuai.shangou.logistics.sdms.infrastructure.external.DxMsgServiceClient;
import com.sankuai.shangou.logistics.sdms.infrastructure.external.OswClient;
import com.sankuai.shangou.logistics.sdms.infrastructure.utils.MccUtils;
import com.sankuai.shangou.logistics.sdms.sdk.verify.enums.CollectContentTypeEnum;
import com.sankuai.shangou.logistics.sdms.sdk.verify.enums.PunishTypeEnum;
import com.sankuai.shangou.logistics.sdms.sdk.verify.enums.VerifyTaskNoControlEnum;
import com.sankuai.shangou.waima.support.api.pl.request.punish.PunishTicketFormAssociationCreateReq;
import com.sankuai.shangou.waima.support.api.service.punish.TPunishService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2024-09-24
 * @email <EMAIL>
 */
@Service
@Slf4j
public class VerifyTaskFailConsumer {

    @Resource
    private YodaIdentityTaskConfigRepository configRepository;

    @Resource
    private YodaIdentifyTaskPOMapper yodaIdentifyTaskPOMapper;

    @Resource
    private TPunishService tPunishService;

    @Resource
    private String smileMsgRobotKey;

    @Resource
    private String smileMsgRobotToken;

    @Resource
    private Long smileMsgRobotPubId;

    @Resource
    private OswClient oswClient;

    @Resource
    private DxMsgServiceClient dxMsgServiceClient;

    @Resource
    private YodaIdentifyTaskOpLogPOMapper yodaIdentifyTaskOpLogPOMapper;

    @Resource
    private LaborClient laborClient;


    private static final String PUNISH_DATA_SOURCE = "deliverySmile";

    @MafkaConsumer(namespace = "com.sankuai.mafka.castle.daojiacommon", topic = "verify_task_fail_message", group = "verify_task_fail_message_consumer")
    public boolean consume(String messageBody) {
        log.info("开始消费微笑任务验证失败消息:{}", messageBody);
        VerifyFailMessage verifyFailMessage = parse(messageBody);
        if (Objects.isNull(verifyFailMessage)) {
            return true;
        }

        YodaIdentifyTaskPO yodaIdentifyTaskPO = yodaIdentifyTaskPOMapper.selectByPrimaryKey(verifyFailMessage.getYodaIdentifyTaskId());
        if (!MccUtils.isVerifyPunishGrayStore(yodaIdentifyTaskPO.getStoreId())) {
            log.info("非灰度门店，放弃消费");
            return true;
        }

        Optional<YodaIdentityTaskConfig> taskConfigOptional = configRepository.selectByPrimaryKey(yodaIdentifyTaskPO.getRelRuleId());
        if (!taskConfigOptional.isPresent()) {
            log.error("没有对应的config, configId = {}", yodaIdentifyTaskPO.getRelRuleId());
        }
        YodaIdentityTaskConfig yodaIdentityTaskConfig = taskConfigOptional.get();
        Map<CollectContentTypeEnum, PunishTypeEnum> punishConfigMap = yodaIdentityTaskConfig.getPunishConfigMap();
        if (MapUtils.isEmpty(punishConfigMap)) {
            log.info("no PunishConfigMap, will return");
            return true;
        }
        // 获取员工入职时间
        Long entryTime = getEntryTime(yodaIdentifyTaskPO.getTenantId(), yodaIdentifyTaskPO.getRiderAccountId());
        // 不管控的类型
        Set<CollectContentTypeEnum> noControlTypes = getNoControlTypes(entryTime, yodaIdentifyTaskPO, yodaIdentityTaskConfig);

        Map<String/*collectType*/, Integer/*punishType*/> punishInfoMap = Maps.newHashMap();
        //超时
        if (verifyFailMessage.getVerifyExpire() && punishConfigMap.containsKey(CollectContentTypeEnum.EXPIRE)
                && !noControlTypes.contains(CollectContentTypeEnum.EXPIRE)) {
            punish(yodaIdentifyTaskPO, punishConfigMap.get(CollectContentTypeEnum.EXPIRE), CollectContentTypeEnum.EXPIRE);
            punishInfoMap.put(String.valueOf(CollectContentTypeEnum.EXPIRE.getCode()), punishConfigMap.get(CollectContentTypeEnum.EXPIRE).getCode());
        }
        //人脸
        if (verifyFailMessage.getFaceIdentifyFail() && punishConfigMap.containsKey(CollectContentTypeEnum.FACE)
                && !noControlTypes.contains(CollectContentTypeEnum.FACE)) {
            punish(yodaIdentifyTaskPO, punishConfigMap.get(CollectContentTypeEnum.FACE), CollectContentTypeEnum.FACE);
            punishInfoMap.put(String.valueOf(CollectContentTypeEnum.FACE.getCode()), punishConfigMap.get(CollectContentTypeEnum.FACE).getCode());
        }
        //头盔
        if (verifyFailMessage.getHelmetIdentifyFail() && punishConfigMap.containsKey(CollectContentTypeEnum.HELMET)
                && !noControlTypes.contains(CollectContentTypeEnum.HELMET)) {
            punish(yodaIdentifyTaskPO, punishConfigMap.get(CollectContentTypeEnum.HELMET), CollectContentTypeEnum.HELMET);
            punishInfoMap.put(String.valueOf(CollectContentTypeEnum.HELMET.getCode()), punishConfigMap.get(CollectContentTypeEnum.HELMET).getCode());
        }
        //着装
        if (verifyFailMessage.getDressingIdentifyFail() && punishConfigMap.containsKey(CollectContentTypeEnum.DRESS)
                && !noControlTypes.contains(CollectContentTypeEnum.DRESS)) {
            punish(yodaIdentifyTaskPO, punishConfigMap.get(CollectContentTypeEnum.DRESS), CollectContentTypeEnum.DRESS);
            punishInfoMap.put(String.valueOf(CollectContentTypeEnum.DRESS.getCode()), punishConfigMap.get(CollectContentTypeEnum.DRESS).getCode());
        }

        //更新落罚信息,新建一个对象别更新到别的了
        YodaIdentifyTaskPO updateTaskPO = new YodaIdentifyTaskPO();
        updateTaskPO.setId(yodaIdentifyTaskPO.getId());
        updateTaskPO.setPunishInfo(JSON.toJSONString(punishInfoMap));
        updateTaskPO.setUpdateTime(LocalDateTime.now());
        setExtInfo(yodaIdentityTaskConfig, entryTime, yodaIdentifyTaskPO, updateTaskPO, verifyFailMessage);
        yodaIdentifyTaskPOMapper.updateByPrimaryKeySelective(updateTaskPO);
        return true;
    }

    private void setExtInfo(YodaIdentityTaskConfig config, Long entryTime, YodaIdentifyTaskPO taskPO, YodaIdentifyTaskPO updateTaskPO,
                            VerifyFailMessage verifyFailMessage) {
        try {
            List<VerifyNoControlInfo> noControl = config.getNoControl();
            if (CollectionUtils.isEmpty(noControl)) {
                return;
            }
            Optional<VerifyNoControlInfo> verifyNoControlInfoOpt = noControl.stream()
                    .filter(item -> isNewLabor(entryTime, taskPO.getPushTaskTime(), item))
                    .findFirst();
            if (!verifyNoControlInfoOpt.isPresent()) {
                return;
            }
            VerifyNoControlInfo verifyNoControlInfo = verifyNoControlInfoOpt.get();
            VerifyNoControlRule rule = verifyNoControlInfo.getRules().get(0);
            List<CollectContentTypeEnum> collectionTypes = rule.getCollectionTypes();
            boolean isNewLabor = true;
            if (verifyFailMessage.getVerifyExpire() && !collectionTypes.contains(CollectContentTypeEnum.EXPIRE)) {
                isNewLabor = false;
            }

            if (verifyFailMessage.getFaceIdentifyFail() && !collectionTypes.contains(CollectContentTypeEnum.FACE)) {
                isNewLabor = false;
            }

            if (verifyFailMessage.getHelmetIdentifyFail() && !collectionTypes.contains(CollectContentTypeEnum.HELMET)) {
                isNewLabor = false;
            }

            if (verifyFailMessage.getDressingIdentifyFail() && !collectionTypes.contains(CollectContentTypeEnum.DRESS)) {
                isNewLabor = false;
            }

            if (isNewLabor) {
                YodaVerifyTaskExtInfo extInfo = JSON.parseObject(taskPO.getExtInfo(), YodaVerifyTaskExtInfo.class);
                if (extInfo == null) {
                    extInfo = new YodaVerifyTaskExtInfo();
                }
                extInfo.setNewLabor(true);
                updateTaskPO.setExtInfo(JSON.toJSONString(extInfo));
            }
        } catch (Exception e) {
            log.error("setExtInfo error", e);
        }
    }
    
    private Long getEntryTime(Long tenantId, Long accountId) {
        try {
            Optional<EmployeeDTO> empOpt = oswClient.queryEmpByAccountId(tenantId, accountId);
            if (!empOpt.isPresent()) {
                return null;
            }
            Long empId = empOpt.get().getEmpId();
            return laborClient.queryOnboardTimeByEmpId(tenantId, empId);
        } catch (Exception e) {
            log.error("查询员工入职时间失败", e);
            return null;
        }
    }

    private Set<CollectContentTypeEnum> getNoControlTypes(Long entryTime, YodaIdentifyTaskPO po, YodaIdentityTaskConfig config) {
        Set<CollectContentTypeEnum> types = Sets.newHashSet();
        List<VerifyNoControlInfo> noControl = config.getNoControl();
        if (CollectionUtils.isEmpty(noControl)) {
            return Collections.emptySet();
        }

        for (VerifyNoControlInfo verifyNoControlInfo : noControl) {
            log.info("verifyNoControlInfo:{}", JSON.toJSONString(verifyNoControlInfo));
            // 判断是否为新员工保护期
            if (isNewLabor(entryTime, po.getPushTaskTime(), verifyNoControlInfo)) {
                types.addAll(verifyNoControlInfo.getRules().get(0).getCollectionTypes());
            }
        }
        log.info("noControlTypes:{}", JSON.toJSONString(types));
        return types;
    }

    private boolean isNewLabor(Long entryTime, LocalDateTime pushTaskTime, VerifyNoControlInfo verifyNoControlInfo) {
        try {
            VerifyTaskNoControlEnum type = verifyNoControlInfo.getType();
            if (type == null) {
                return false;
            }
            if (entryTime == null || !type.equals(VerifyTaskNoControlEnum.EQUIPMENT_PICKUP_TIME)) {
                return false;
            }
            VerifyNoControlRule rule = verifyNoControlInfo.getRules().get(0);
            Integer pickupTime = rule.getPickupTime();
            LocalDateTime protectionPeriod = TimeUtils.fromMilliSeconds(entryTime).plusDays(pickupTime);
            return TimeUtils.isBeforeOrEquals(pushTaskTime, protectionPeriod);
        } catch (Exception e) {
            log.error("isNewLabor error", e);
        }
        return false;
    }

    private void punish(YodaIdentifyTaskPO yodaIdentifyTaskPO, PunishTypeEnum punishTypeEnum, CollectContentTypeEnum collectContentTypeEnum) {
        switch (punishTypeEnum) {
            case BY_MANUAL: {
                //通知人工处理
                PoiBaseInfo poiBaseInfo = oswClient.queryWarehouseInfo(yodaIdentifyTaskPO.getTenantId(), yodaIdentifyTaskPO.getStoreId());
                Optional<EmployeeDTO> employeeDTOOpt = oswClient.queryEmpByAccountId(yodaIdentifyTaskPO.getTenantId(), yodaIdentifyTaskPO.getRiderAccountId());
                if (!employeeDTOOpt.isPresent()) {
                    throw new BizException("未查询到员工信息");
                }

                //按岗位和白名单推送
                Integer storeOperationMode = poiBaseInfo.getStoreOperationMode();
                List<String> verifyTaskPushWhiteList = LionConfigUtils.getVerifyTaskPushWhiteList(storeOperationMode);
                String msgContent = buildMsgContent(poiBaseInfo, employeeDTOOpt.get(), yodaIdentifyTaskPO.getPushTaskTime(), collectContentTypeEnum);
                dxMsgServiceClient.sendCrossEnterpriseDxMsgByPosition(yodaIdentifyTaskPO.getTenantId(), smileMsgRobotKey,
                        smileMsgRobotToken, smileMsgRobotPubId, msgContent, LionConfigUtils.getVerifyTaskPushPositionIds(),
                        yodaIdentifyTaskPO.getStoreId(), verifyTaskPushWhiteList);
                break;
            }
            case BY_PUNISH_SYSTEM: {
                PunishTicketFormAssociationCreateReq req = new PunishTicketFormAssociationCreateReq();
                    req.setTenantId(yodaIdentifyTaskPO.getTenantId());
                    req.setPoiId(yodaIdentifyTaskPO.getStoreId());
                    req.setDataSource(PUNISH_DATA_SOURCE + "_" + collectContentTypeEnum.name());
                    req.setAccountId(yodaIdentifyTaskPO.getRiderAccountId());
                    req.setAssociationKey(String.valueOf(yodaIdentifyTaskPO.getId()));
                    req.setScene(collectContentTypeEnum.name());
                tPunishService.createPunishTicketFormAssociation(req);
                break;
            }
            case NONE:
            default:
                break;
        }
    }

    private static VerifyFailMessage parse(String messageBody) {
        try {
            return JSON.parseObject(messageBody, VerifyFailMessage.class);
        } catch (Exception e) {
            log.error("parse json error", e);
            return null;
        }
    }

    private String buildMsgContent(PoiBaseInfo poiBaseInfo, EmployeeDTO employeeDTO, LocalDateTime taskPushTime, CollectContentTypeEnum collectContentTypeEnum) {
        return "微笑行动" + collectContentTypeEnum.getDesc() + "识别未通过通知\n" +
                "【门店名称】" + poiBaseInfo.getPoiName() + "\n" +
                "【人员信息】" + employeeDTO.getEmpName() + employeeDTO.getAccountName() + "\n" +
                "【采集详情】" + String.format("[配送-图像采集任务|%s]", LionConfigUtils.getVerifyManagementPageUrl(poiBaseInfo.getPoiId(), employeeDTO.getAccountName(), taskPushTime)) + "\n" +
                "请核对是否本人，如不是本人建议及时关闭账号，如是本人无需操作";
    }

}
