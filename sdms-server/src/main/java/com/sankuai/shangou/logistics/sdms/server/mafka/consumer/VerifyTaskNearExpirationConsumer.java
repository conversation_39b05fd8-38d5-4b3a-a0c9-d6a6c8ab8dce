package com.sankuai.shangou.logistics.sdms.server.mafka.consumer;

import com.alibaba.fastjson.JSON;
import com.meituan.linz.boot.util.Bssert;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.consumer.IMessageListener;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.meituan.xframe.boot.mafka.autoconfigure.annotation.MafkaConsumer;
import com.sankuai.shangou.commons.exception.common.exceptions.BizException;
import com.sankuai.shangou.logistics.sdms.dao.mapper.YodaIdentifyTaskPOMapper;
import com.sankuai.shangou.logistics.sdms.dao.model.YodaIdentifyTaskPO;
import com.sankuai.shangou.logistics.sdms.domain.mq.message.VerifyTaskNearExpirationMessage;
import com.sankuai.shangou.logistics.sdms.infrastructure.external.PushClient;
import com.sankuai.shangou.logistics.sdms.sdk.verify.enums.VerifyTaskStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description 微笑任务临期提醒消息消费者
 * @date 2025-03-21
 */
@Slf4j
@Component
@MafkaConsumer(namespace = "com.sankuai.mafka.castle.daojiacommon",
        topic = "verify_task_near_expiration_message",
        group = "verify_task_near_expiration_message_consumer",
        deadLetter = false)
public class VerifyTaskNearExpirationConsumer implements IMessageListener {
    @Resource
    private YodaIdentifyTaskPOMapper yodaIdentifyTaskPOMapper;
    @Resource
    private PushClient pushClient;


    @Override
    public ConsumeStatus recvMessage(MafkaMessage mafkaMessage, MessagetContext messagetContext) {
        try {
            log.info("开始消费微笑任务临期提醒消息: {}", mafkaMessage);
            String body = (String) mafkaMessage.getBody();

            VerifyTaskNearExpirationMessage message = transform(body);
            YodaIdentifyTaskPO taskPO = yodaIdentifyTaskPOMapper.selectByPrimaryKey(message.getTaskId());
            if (taskPO == null) {
                log.error("采集任务不存在");
                throw new BizException("采集任务不存在");
            }

            if (VerifyTaskStatusEnum.getEndStatus().contains(taskPO.getTaskStatus())) {
                log.info("采集任务已结束");
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            pushClient.pushSmileNearExpirationTask(taskPO.getTenantId(), taskPO.getRiderAccountId(), taskPO.getStoreId());
            return ConsumeStatus.CONSUME_SUCCESS;
        } catch (BizException e) {
            log.error("消费微笑任务临期提醒消息失败", e);
            return ConsumeStatus.RECONSUME_LATER;
        }
    }

    private VerifyTaskNearExpirationMessage transform(String body) {
        VerifyTaskNearExpirationMessage message = JSON.parseObject(body, VerifyTaskNearExpirationMessage.class);
        Bssert.throwIfNull(message, "消息为空");
        Bssert.throwIfNull(message.getTaskId(), "任务id为空");
        return message;
    }
}
