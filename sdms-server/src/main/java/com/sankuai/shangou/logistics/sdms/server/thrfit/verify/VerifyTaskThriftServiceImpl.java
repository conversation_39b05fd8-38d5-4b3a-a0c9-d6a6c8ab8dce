package com.sankuai.shangou.logistics.sdms.server.thrfit.verify;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.dianping.cat.Cat;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.meituan.mafka.client.producer.IProducerProcessor;
import com.meituan.mafka.client.producer.ProducerResult;
import com.meituan.xframe.boot.mafka.autoconfigure.annotation.MafkaProducer;
import com.sankuai.shangou.commons.exception.common.exceptions.BizException;
import com.sankuai.shangou.commons.thrift.publisher.annotation.ShangouThriftServer;
import com.sankuai.shangou.commons.thrift.publisher.response.TPageResult;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.commons.utils.log.MethodLog;
import com.sankuai.shangou.commons.utils.todo.PoiAccountInfo;
import com.sankuai.shangou.infra.osw.api.org.dto.response.EmpDepRelDTO;
import com.sankuai.shangou.infra.osw.api.org.dto.response.OrgDTO;
import com.sankuai.shangou.infra.osw.api.poi.warehouse.dto.response.WarehouseDTO;
import com.sankuai.shangou.logistics.sdms.dao.mapper.YodaIdentifyTaskOpLogPOMapper;
import com.sankuai.shangou.logistics.sdms.dao.mapper.YodaIdentifyTaskPOExMapper;
import com.sankuai.shangou.logistics.sdms.dao.mapper.YodaIdentifyTaskPOMapper;
import com.sankuai.shangou.logistics.sdms.dao.model.*;
import com.sankuai.shangou.logistics.sdms.domain.constants.CatEventEnum;
import com.sankuai.shangou.logistics.sdms.domain.constants.VerifyTypeEnum;
import com.sankuai.shangou.logistics.sdms.domain.entity.order.OrderTagInfo;
import com.sankuai.shangou.logistics.sdms.domain.entity.rider.RiderDeliveryOrderInfo;
import com.sankuai.shangou.logistics.sdms.domain.entity.yoda.VerifyNoControlLabel;
import com.sankuai.shangou.logistics.sdms.domain.entity.yoda.YodaIdentityTaskConfig;
import com.sankuai.shangou.logistics.sdms.domain.entity.yoda.YodaVerifyResult;
import com.sankuai.shangou.logistics.sdms.domain.entity.yoda.YodaVerifyTaskExtInfo;
import com.sankuai.shangou.logistics.sdms.domain.external.*;
import com.sankuai.shangou.logistics.sdms.domain.mq.message.VerifyFailMessage;
import com.sankuai.shangou.logistics.sdms.domain.mq.message.VerifyTaskExpireMessage;
import com.sankuai.shangou.logistics.sdms.domain.mq.message.VerifyTaskNearExpirationMessage;
import com.sankuai.shangou.logistics.sdms.domain.repository.YodaIdentityTaskConfigRepository;
import com.sankuai.shangou.logistics.sdms.domain.utils.TimeUtils;
import com.sankuai.shangou.logistics.sdms.infrastructure.external.OswClient;
import com.sankuai.shangou.logistics.sdms.infrastructure.external.PushClient;
import com.sankuai.shangou.logistics.sdms.infrastructure.external.WarehouseQueryWrapper;
import com.sankuai.shangou.logistics.sdms.infrastructure.utils.MccUtils;
import com.sankuai.shangou.logistics.sdms.sdk.config.dto.request.QueryByTaskIdRequest;
import com.sankuai.shangou.logistics.sdms.sdk.config.dto.request.QueryVerifyImageRequest;
import com.sankuai.shangou.logistics.sdms.sdk.verify.VerifyTaskThriftService;
import com.sankuai.shangou.logistics.sdms.sdk.verify.dto.*;
import com.sankuai.shangou.logistics.sdms.sdk.verify.enums.VerifyTaskNoControlEnum;
import com.sankuai.shangou.logistics.sdms.sdk.verify.enums.VerifyTaskStatusEnum;
import com.sankuai.shangou.logistics.sdms.sdk.verify.request.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/7/11 21:14
 **/
@Slf4j
@ShangouThriftServer
public class VerifyTaskThriftServiceImpl implements VerifyTaskThriftService {

    @Resource
    private RiderDeliveryQueryClient riderDeliveryQueryClient;

    @Resource
    private YodaIdentityTaskConfigRepository yodaIdentityTaskConfigRepository;

    @Resource
    private YodaIdentifyTaskPOMapper yodaIdentifyTaskPOMapper;

    @Resource
    private YodaIdentifyTaskPOExMapper yodaIdentifyTaskPOExMapper;

    @Resource
    private YodaClient yodaClient;

    @Resource
    private LaborClient laborClient;

    @Resource
    private YodaIdentifyTaskOpLogPOMapper yodaIdentifyTaskOpLogPOMapper;

    @Resource
    private PullNewThriftServiceClient pullNewThriftServiceClient;

    @Resource
    private OCMSOrderQueryClient ocmsOrderQueryClient;

    @Resource
    private OswClient oswClient;

    @Resource
    private WarehouseQueryWrapper warehouseQueryWrapper;

    private final Integer TASK_IS_COMPLETE_ERROR_CODE = 20000200;


    @MafkaProducer(namespace = "com.sankuai.mafka.castle.daojiacommon", topic = "verify_task_expire_delay_message", delay = true)
    @SuppressWarnings("rawtypes")
    private IProducerProcessor yodaIdentifyTaskExpireDelayMessageProducer;

    @MafkaProducer(namespace = "com.sankuai.mafka.castle.daojiacommon", topic = "verify_task_fail_message")
    private IProducerProcessor<Object, String> yodaVerifyFailMessageProducer;

    @MafkaProducer(namespace = "com.sankuai.mafka.castle.daojiacommon", topic = "verify_task_near_expiration_message", delay = true)
    @SuppressWarnings("rawtypes")
    private IProducerProcessor smileTaskNearExpirationMessage;

    @Resource
    private PushClient pushClient;

    private final LocalDateTime DEFAULT_DATE_TIME = LocalDateTime.of(1970, 1, 1, 0, 0, 0);
    private static final int VERIFY_PASS = 1;
    private static final int VERIFY_FAIL = 0;
    private static final String SMILE_ACT_WAIT_COLLECT = "SMILE_ACT_WAIT_COLLECT";

    @Override
    @MethodLog(logRequest = true, logResponse = true)
    public TResult<TVerifyTaskInfo> tryToAssignVerifyTask(AssignVerifyTaskRequest request) {
        String errMsg = request.validate();
        if (StringUtils.isNotBlank(errMsg)) {
            throw new IllegalArgumentException(errMsg);
        }

        //查询骑手此时是否还有其他运单
        List<RiderDeliveryOrderInfo> inProgressDeliveryOrderList = riderDeliveryQueryClient.queryRiderInProgressOrder(request.getTenantId(), request.getStoreId(), request.getRiderAccountId());

        if (CollectionUtils.isNotEmpty(inProgressDeliveryOrderList)) {
            log.info("骑手有进行中的运单,不下发采集任务");
            return TResult.buildSuccess(null);
        }
        //查询命中了哪个规则
        Optional<YodaIdentityTaskConfig> hitConfigOpt = yodaIdentityTaskConfigRepository.getHitConfig(request.getTenantId(), request.getStoreId());
        log.info("hitConfigOpt: {}", hitConfigOpt);
        if (!hitConfigOpt.isPresent()) {
            log.info("没有命中的下发规则,不下发采集任务");
            return TResult.buildSuccess(null);
        }
        YodaIdentityTaskConfig taskConfig = hitConfigOpt.get();

        //判断是否需要下发采集任务
        boolean needAssignTask = judgeShouldAssignTask(request.getTenantId(), request.getStoreId(), taskConfig,
                request.getRiderAccountId(), request.getViewOrderId(), request.getOrderBizType(), request.getRiderEmpId());
        if (!needAssignTask) {
            return TResult.buildSuccess(null);
        }

        //采集任务落库
        YodaIdentifyTaskPO taskPO = buildYodaTaskPO(taskConfig, request);
        yodaIdentifyTaskPOMapper.insertSelective(taskPO);

        //发送异步消息 自动过期任务
        sendExpireDelayMessage(taskPO);

        if (MccUtils.isSmilePushGrayStore(taskPO.getStoreId())) {
            // 发送push消息提醒骑手
            pushClient.pushSmileTask(taskPO.getTenantId(), taskPO.getRiderAccountId(), taskPO.getStoreId());
            // 发送异步消息 任务临期提醒
            sendNearExpirationMessage(taskPO, taskConfig);
        }

        return TResult.buildSuccess(transform(taskPO));
    }


    @Override
    @MethodLog(logRequest = true, logResponse = true)
    public TResult<TVerifyTaskInfo> queryTaskInfo(QueryVerifyTaskRequest request) {
        String errMsg = request.validate();
        if (StringUtils.isNotBlank(errMsg)) {
            throw new IllegalArgumentException(errMsg);
        }

        YodaIdentifyTaskPOExample example = new YodaIdentifyTaskPOExample();
        example.createCriteria()
                .andRiderAccountIdEqualTo(request.getRiderAccountId())
                .andTaskStatusIn(VerifyTaskStatusEnum.getOngoingStatus());
        example.setOrderByClause("create_time desc");
        List<YodaIdentifyTaskPO> taskPOS = yodaIdentifyTaskPOMapper.selectByExample(example);


        if (CollectionUtils.isEmpty(taskPOS)) {
            return TResult.buildSuccess(null);
        }

        YodaIdentifyTaskPO taskPO = taskPOS.get(0);
        return TResult.buildSuccess(transform(taskPO));
    }


    @Override
    @MethodLog(logRequest = true, logResponse = true)
    public TResult<AuthorizeCodeInfo> getAuthorizeCode(GetAuthorizeCodeRequest request) {
        String errMsg = request.validate();
        if (StringUtils.isNotBlank(errMsg)) {
            throw new IllegalArgumentException(errMsg);
        }

        YodaIdentifyTaskPO yodaIdentifyTaskPO = yodaIdentifyTaskPOMapper.selectByPrimaryKey(request.getTaskId());
        if (yodaIdentifyTaskPO == null) {
            throw new BizException("采集任务不存在");
        }

        if (!Objects.equals(yodaIdentifyTaskPO.getRiderAccountId(), request.getRiderAccountId())) {
            throw new BizException("采集任务不属于当前骑手");
        }

        if (Objects.equals(yodaIdentifyTaskPO.getTaskStatus(), VerifyTaskStatusEnum.EXPIRE.getCode()) ||
                Objects.equals(yodaIdentifyTaskPO.getTaskStatus(), VerifyTaskStatusEnum.CANCEL.getCode())||
                Objects.equals(yodaIdentifyTaskPO.getTaskStatus(), VerifyTaskStatusEnum.COMPLETED.getCode())) {
            throw new BizException(TASK_IS_COMPLETE_ERROR_CODE, "微笑行动已结束");
        }

        //查询骑手身份证号
        String idCardNum = laborClient.queryEmpIdCardByEmpId(request.getTenantId(), request.getEmployeeId());

        List<Integer> contentTypes = JSON.parseObject(yodaIdentifyTaskPO.getCollectContentType(), new TypeReference<List<Integer>>(){});
        List<VerifyTypeEnum> verifyTypeEnums = contentTypes.stream()
                .map(VerifyTypeEnum::enumOf)
                .collect(Collectors.toList());
        String authorizeCode = yodaClient.authorize(request.userAgent, request.getUuid(), request.getIp(), request.getVersion(),
                request.getRiderAccountId(), request.getUserPhone(), request.getUserName(), idCardNum,
                verifyTypeEnums, transPlatform(request.getPlatform()));

        //记录requestCode
        YodaIdentifyTaskPO newYodaIdentityTaskPO = new YodaIdentifyTaskPO();
        newYodaIdentityTaskPO.setId(yodaIdentifyTaskPO.getId());
        newYodaIdentityTaskPO.setUpdateTime(LocalDateTime.now());
        YodaVerifyTaskExtInfo yodaVerifyTaskExtInfo = JSON.parseObject(newYodaIdentityTaskPO.getExtInfo(), YodaVerifyTaskExtInfo.class);
        if (yodaVerifyTaskExtInfo == null) {
            yodaVerifyTaskExtInfo = new YodaVerifyTaskExtInfo();
        }
        yodaVerifyTaskExtInfo.setRequestCode(authorizeCode);
        newYodaIdentityTaskPO.setExtInfo(JSON.toJSONString(yodaVerifyTaskExtInfo));
        yodaIdentifyTaskPOMapper.updateByPrimaryKeySelective(newYodaIdentityTaskPO);

        //记录日志
        yodaIdentifyTaskOpLogPOMapper.insertSelective(buildYodaTaskOpLogPO(authorizeCode, request.getTaskId()));

        return TResult.buildSuccess(new AuthorizeCodeInfo(authorizeCode));
    }

    @Override
    @MethodLog(logRequest = true, logResponse = true)
    public TResult<Void> postVerifySuccessResult(PostVerifySuccessResultRequest request) {
        String errMsg = request.validate();
        if (StringUtils.isNotBlank(errMsg)) {
            throw new IllegalArgumentException(errMsg);
        }

        YodaIdentifyTaskPO yodaIdentifyTaskPO = yodaIdentifyTaskPOMapper.selectByPrimaryKey(request.getTaskId());
        if (yodaIdentifyTaskPO == null) {
            throw new BizException("采集任务不存在");
        }

        if (!Objects.equals(yodaIdentifyTaskPO.getRiderAccountId(), request.getRiderAccountId())) {
            throw new BizException("采集任务不属于当前骑手");
        }

        if (Objects.equals(yodaIdentifyTaskPO.getTaskStatus(), VerifyTaskStatusEnum.EXPIRE.getCode()) ||
                Objects.equals(yodaIdentifyTaskPO.getTaskStatus(), VerifyTaskStatusEnum.CANCEL.getCode()) ||
                Objects.equals(yodaIdentifyTaskPO.getTaskStatus(), VerifyTaskStatusEnum.COMPLETED.getCode())) {
            throw new BizException(TASK_IS_COMPLETE_ERROR_CODE, "微笑行动已结束");
        }

        //如果识别成功 验证结果是否可信 并且将任务结束
        Boolean checkResult = yodaClient.result(request.getRequestCode(), request.getResponseCode(), request.getRiderAccountId());
        if (checkResult != null && !checkResult) {
            Cat.logEvent(CatEventEnum.VERIFY_RESULT_CHECK_NOT_PASS.getType(), CatEventEnum.VERIFY_RESULT_CHECK_NOT_PASS.getName());
        }

        YodaVerifyResult yodaVerifyResult;
        if(MccUtils.getYodaForcedInterceptSwitch()) {
            yodaIdentifyTaskPO.setHelmetIdentifyResult(1);
            yodaIdentifyTaskPO.setDressingIdentifyResult(1);
            yodaIdentifyTaskPO.setFaceIdentifyResult(1);
        } else {
            yodaVerifyResult = yodaClient.queryFaceVerifyData(request.getRequestCode());
            yodaIdentifyTaskPO.setHelmetIdentifyResult(Optional.ofNullable(yodaVerifyResult.getHelmetVerifyResult()).map(result -> result ? 1 : 0).orElse(null));
            yodaIdentifyTaskPO.setDressingIdentifyResult(Optional.ofNullable(yodaVerifyResult.getOutfitVerifyResult()).map(result -> result ? 1 : 0).orElse(null));
            yodaIdentifyTaskPO.setFaceIdentifyResult(Optional.ofNullable(yodaVerifyResult.getCompareVerifyResult()).map(result -> result ? 1 : 0).orElse(null));
        }

        yodaIdentifyTaskPO.setUpdateTime(LocalDateTime.now());
        yodaIdentifyTaskPO.setCompleteTime(LocalDateTime.now());
        yodaIdentifyTaskPO.setTaskStatus(VerifyTaskStatusEnum.COMPLETED.getCode());
        yodaIdentifyTaskPO.setResultCheckPass(Optional.ofNullable(checkResult).map(val -> val ? 1 : 0).orElse(null));
        yodaIdentifyTaskPO.setCallbackCode(null);
        yodaIdentifyTaskPO.setCallbackMsg(null);
        //记录uuid
        YodaVerifyTaskExtInfo yodaVerifyTaskExtInfo = JSON.parseObject(yodaIdentifyTaskPO.getExtInfo(), YodaVerifyTaskExtInfo.class);
        if (yodaVerifyTaskExtInfo == null) {
            yodaVerifyTaskExtInfo = new YodaVerifyTaskExtInfo();
        }
        yodaVerifyTaskExtInfo.setUuid(request.getUuid());
        yodaIdentifyTaskPO.setExtInfo(JSON.toJSONString(yodaVerifyTaskExtInfo));
        yodaIdentifyTaskPOMapper.updateByPrimaryKey(yodaIdentifyTaskPO);

        YodaIdentifyTaskOpLogPOExample example = new YodaIdentifyTaskOpLogPOExample();
        example.createCriteria()
                .andTaskIdEqualTo(request.getTaskId())
                .andAuthorizeCodeEqualTo(request.getRequestCode());
        List<YodaIdentifyTaskOpLogPO> taskOpLogPOS = yodaIdentifyTaskOpLogPOMapper.selectByExample(example);

        if (CollectionUtils.isNotEmpty(taskOpLogPOS)) {
            YodaIdentifyTaskOpLogPO taskOpLogPO = taskOpLogPOS.get(0);
            taskOpLogPO.setUpdateTime(LocalDateTime.now());
            taskOpLogPO.setResponseCode(request.getResponseCode());
            yodaIdentifyTaskOpLogPOMapper.updateByPrimaryKey(taskOpLogPO);
        }

        //发MQ通知验证失败结果
        sendVerifyFailMessage(yodaIdentifyTaskPO, CollectionUtils.isNotEmpty(taskOpLogPOS) ? taskOpLogPOS.get(0).getAuthorizeCode() : "");

        return TResult.buildSuccess(null);
    }

    @Override
    @MethodLog(logRequest = true, logResponse = true)
    public TResult<PostVerifyFailResponse> postVerifyFailResult(PostVerifyFailResultRequest request) {
        String errMsg = request.validate();
        if (StringUtils.isNotBlank(errMsg)) {
            throw new IllegalArgumentException(errMsg);
        }

        YodaIdentifyTaskPO yodaIdentifyTaskPO = yodaIdentifyTaskPOMapper.selectByPrimaryKey(request.getTaskId());
        if (yodaIdentifyTaskPO == null) {
            throw new BizException("采集任务不存在");
        }

        if (!Objects.equals(yodaIdentifyTaskPO.getRiderAccountId(), request.getRiderAccountId())) {
            throw new BizException("采集任务不属于当前骑手");
        }

        if (Objects.equals(yodaIdentifyTaskPO.getTaskStatus(), VerifyTaskStatusEnum.EXPIRE.getCode()) ||
                Objects.equals(yodaIdentifyTaskPO.getTaskStatus(), VerifyTaskStatusEnum.CANCEL.getCode())||
                Objects.equals(yodaIdentifyTaskPO.getTaskStatus(), VerifyTaskStatusEnum.COMPLETED.getCode())) {
            throw new BizException(TASK_IS_COMPLETE_ERROR_CODE, "微笑行动已结束");
        }

        YodaVerifyResult yodaVerifyResult;
        Boolean verifyTaskCompleted = false;

        yodaVerifyResult = yodaClient.queryFaceVerifyData(request.getRequestCode());
        //头盔或者着装识别有结果 并且人脸比对无结果时 单独识别一次人脸
        Pair<Boolean, String> samePersonResult = null;
        if (Objects.nonNull(yodaVerifyResult.getOutfitVerifyResult()) || Objects.nonNull(yodaVerifyResult.getHelmetVerifyResult())) {
            if(Objects.isNull(yodaVerifyResult.getCompareVerifyResult())) {
                String idCardNum = laborClient.queryEmpIdCardByEmpId(request.getTenantId(), request.getEmployeeId());
                samePersonResult = yodaClient.verifySamePerson(request.userAgent, request.getUuid(), request.getIp(), request.getRiderAccountId(),
                        request.getUserName(), idCardNum, yodaVerifyResult.getFaceImageUrl());
                yodaVerifyResult.setCompareVerifyResult(samePersonResult.getLeft());
            }
        }


        //只要有验证结果 就把验证任务关闭
        if (yodaVerifyResult.getOutfitVerifyResult() != null
                || yodaVerifyResult.getHelmetVerifyResult() != null
                || yodaVerifyResult.getCompareVerifyResult() != null) {
            yodaIdentifyTaskPO.setTaskStatus(VerifyTaskStatusEnum.COMPLETED.getCode());
            yodaIdentifyTaskPO.setCompleteTime(LocalDateTime.now());
            verifyTaskCompleted = true;
        }

        yodaIdentifyTaskPO.setCallbackCode(request.getErrCode());
        yodaIdentifyTaskPO.setCallbackMsg(mapBusinessErrMsg(request.getErrCode(), request.getErrMsg()));
        yodaIdentifyTaskPO.setHelmetIdentifyResult(Optional.ofNullable(yodaVerifyResult.getHelmetVerifyResult()).map(result -> result ? 1 : 0).orElse(null));
        yodaIdentifyTaskPO.setDressingIdentifyResult(Optional.ofNullable(yodaVerifyResult.getOutfitVerifyResult()).map(result -> result ? 1 : 0).orElse(null));
        yodaIdentifyTaskPO.setFaceIdentifyResult(Optional.ofNullable(yodaVerifyResult.getCompareVerifyResult()).map(result -> result ? 1 : 0).orElse(null));
        yodaIdentifyTaskPO.setUpdateTime(LocalDateTime.now());
        //记录uuid
        YodaVerifyTaskExtInfo yodaVerifyTaskExtInfo = JSON.parseObject(yodaIdentifyTaskPO.getExtInfo(), YodaVerifyTaskExtInfo.class);
        if (yodaVerifyTaskExtInfo == null) {
            yodaVerifyTaskExtInfo = new YodaVerifyTaskExtInfo();
        }
        yodaVerifyTaskExtInfo.setUuid(request.getUuid());
        yodaIdentifyTaskPO.setExtInfo(JSON.toJSONString(yodaVerifyTaskExtInfo));
        yodaIdentifyTaskPOMapper.updateByPrimaryKey(yodaIdentifyTaskPO);

        YodaIdentifyTaskOpLogPOExample example = new YodaIdentifyTaskOpLogPOExample();
        example.createCriteria()
                .andTaskIdEqualTo(request.getTaskId())
                .andAuthorizeCodeEqualTo(request.getRequestCode());
        List<YodaIdentifyTaskOpLogPO> taskOpLogPOS = yodaIdentifyTaskOpLogPOMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(taskOpLogPOS)) {
            YodaIdentifyTaskOpLogPO taskOpLogPO = taskOpLogPOS.get(0);
            taskOpLogPO.setUpdateTime(LocalDateTime.now());
            taskOpLogPO.setErrCode(request.getErrCode());
            taskOpLogPO.setErrMsg(mapBusinessErrMsg(request.getErrCode(), request.getErrMsg()));
            yodaIdentifyTaskOpLogPOMapper.updateByPrimaryKey(taskOpLogPO);
        }

        //发MQ通知验证失败结果
        String requestCode = "";

        //如果单独验证了人脸 就取单独验证人脸的requestCode
        if (samePersonResult != null && Objects.equals(samePersonResult.getLeft(), false) && StringUtils.isNotBlank(samePersonResult.getRight())) {
            requestCode = samePersonResult.getRight();
        } else {
            requestCode = CollectionUtils.isNotEmpty(taskOpLogPOS) ? taskOpLogPOS.get(0).getAuthorizeCode() : "";
        }
        sendVerifyFailMessage(yodaIdentifyTaskPO, requestCode);

        return TResult.buildSuccess(new PostVerifyFailResponse(verifyTaskCompleted));
    }

    @Override
    public TPageResult<TVerifyTaskDTO> searchVerifyTask(SearchVerifyTaskRequest request) {
        String errMsg = request.validate();
        if (StringUtils.isNotBlank(errMsg)) {
            throw new IllegalArgumentException(errMsg);
        }

        YodaIdentifyTaskPOExample example = new YodaIdentifyTaskPOExample();
        YodaIdentifyTaskPOExample.Criteria criteria = example.createCriteria()
                .andPushTaskTimeBetween(request.getPushTimeBegin(), request.getPushTimeEnd())
                .andTenantIdEqualTo(request.getTenantId())
                .andStoreIdIn(request.getStoreIdList());

        if (CollectionUtils.isNotEmpty(request.getStatusList())) {
            criteria.andTaskStatusIn(request.getStatusList());
        }

        if (Objects.nonNull(request.getRiderAccountId())) {
            criteria.andRiderAccountIdEqualTo(request.getRiderAccountId());
        }

        if (Objects.nonNull(request.getTaskId())) {
            criteria.andIdEqualTo(request.getTaskId());
        }

        if (Objects.nonNull(request.getHelmetIdentifyResult())) {
            criteria.andHelmetIdentifyResultEqualTo(request.getHelmetIdentifyResult() ? VERIFY_PASS : VERIFY_FAIL);
        }

        if(Objects.nonNull(request.getDressingIdentifyResult())) {
            criteria.andDressingIdentifyResultEqualTo(request.getDressingIdentifyResult() ? VERIFY_PASS : VERIFY_FAIL);
        }

        if (Objects.nonNull(request.getFaceIdentifyResult())) {
            criteria.andFaceIdentifyResultEqualTo(request.getFaceIdentifyResult() ? VERIFY_PASS : VERIFY_FAIL);
        }

        example.setOrderByClause("push_task_time desc");

        PageHelper.startPage(request.getPage(), request.getPageSize());
        List<YodaIdentifyTaskPO> yodaIdentifyTaskPOS = yodaIdentifyTaskPOMapper.selectByExample(example);
        PageInfo<YodaIdentifyTaskPO> taskPOPageInfo = new PageInfo<>(yodaIdentifyTaskPOS);

        return TPageResult.buildSuccess(taskPOPageInfo.getPageNum(), taskPOPageInfo.getPageSize(), taskPOPageInfo.getTotal(),
                yodaIdentifyTaskPOS.stream().map(this::transform2DTO).collect(Collectors.toList()));
    }

    @Override
    public TResult<String> queryVerifyImageBase64(QueryVerifyImageRequest request) {
        YodaIdentifyTaskPO yodaIdentifyTaskPO = yodaIdentifyTaskPOMapper.selectByPrimaryKey(request.getTaskId());

        if (yodaIdentifyTaskPO == null) {
            throw new BizException("采集任务不存在");
        }

        YodaVerifyTaskExtInfo yodaVerifyTaskExtInfo = JSON.parseObject(yodaIdentifyTaskPO.getExtInfo(), YodaVerifyTaskExtInfo.class);

        if (Objects.isNull(yodaVerifyTaskExtInfo) || StringUtils.isBlank(yodaVerifyTaskExtInfo.getRequestCode())) {
            throw new BizException("未查询到requestCode");
        }

        String faceImageBase64 = yodaClient.queryFaceImageBase64(yodaVerifyTaskExtInfo.getRequestCode());
        return TResult.buildSuccess(faceImageBase64);
    }

    @Override
    public TResult<TVerifyTaskDTO> queryByTaskId(QueryByTaskIdRequest request) {
        YodaIdentifyTaskPO yodaIdentifyTaskPO = yodaIdentifyTaskPOMapper.selectByPrimaryKey(request.getTaskId());

        if (yodaIdentifyTaskPO == null) {
            throw new BizException("采集任务不存在");
        }

        return TResult.buildSuccess(transform2DTO(yodaIdentifyTaskPO));
    }

    @Override
    public TPageResult<TVerifyEndTaskDTO> queryRiderEndTask(QueryRiderEndTaskRequest request) {
        YodaIdentifyEndTaskQuery query = new YodaIdentifyEndTaskQuery();
        query.setRiderAccountId(request.getRiderAccountId());
        query.setBeginTime(request.getBeginTime());
        query.setEndTime(request.getEndTime());
        query.setTaskStatus(request.getTaskStatus());
        query.setVerifyResult(request.getVerifyResult());

        List<YodaIdentifyTaskPO> yodaIdentifyTaskPOS;
        PageInfo<YodaIdentifyTaskPO> taskPOPageInfo;
        try {
            PageHelper.startPage(request.getPage(), request.getPageSize());
            yodaIdentifyTaskPOS = yodaIdentifyTaskPOExMapper.queryRiderEndTask(query);
            taskPOPageInfo = new PageInfo<>(yodaIdentifyTaskPOS);
        } finally {
            PageHelper.clearPage();
        }
        // 查询规则配置
        Set<Long> ruleIds = yodaIdentifyTaskPOS.stream().map(YodaIdentifyTaskPO::getRelRuleId).collect(Collectors.toSet());
        List<YodaIdentityTaskConfig> taskConfigs = yodaIdentityTaskConfigRepository.selectByPrimaryKeys(ruleIds);

        List<TVerifyEndTaskDTO> dtoList = yodaIdentifyTaskPOS.stream().map(po -> {
            YodaIdentityTaskConfig taskConfig = taskConfigs.stream()
                    .filter(config -> config.getId().equals(po.getRelRuleId()))
                    .findFirst().orElse(null);
            return transform2EndTaskDTO(po, taskConfig);
        }).collect(Collectors.toList());

        return TPageResult.buildSuccess(taskPOPageInfo.getPageNum(), taskPOPageInfo.getPageSize(), taskPOPageInfo.getTotal(), dtoList);
    }

    @Override
    public TResult<Integer> queryRiderDoingTaskCount(QueryVerifyTaskRequest request) {
        String errMsg = request.validate();
        if (StringUtils.isNotBlank(errMsg)) {
            log.error("查询骑手待采集任务数量参数异常, request:{}", request);
            return TResult.buildSuccess(0);
        }

        YodaIdentifyTaskPOExample example = new YodaIdentifyTaskPOExample();
        example.createCriteria()
                .andRiderAccountIdEqualTo(request.getRiderAccountId())
                .andTaskStatusIn(VerifyTaskStatusEnum.getOngoingStatus());
        long count = yodaIdentifyTaskPOMapper.countByExample(example);
        return TResult.buildSuccess(Long.valueOf(count).intValue());
    }

    @Override
    public TResult<String> queryRiderPendingTaskInfo(PoiAccountInfo poiAccountInfo) {
        SmilePendingTaskInfo pendingTaskInfo = new SmilePendingTaskInfo();
        pendingTaskInfo.setTaskType(SMILE_ACT_WAIT_COLLECT);
        ArrayList<SmilePendingTaskInfo.Detail> details = Lists.newArrayList();
        pendingTaskInfo.setDetailList(details);
        SmilePendingTaskInfo.Detail detail = new SmilePendingTaskInfo.Detail();
        details.add(detail);
        Long poiId = CollectionUtils.isEmpty(poiAccountInfo.getPoiIds()) ?
                0L : poiAccountInfo.getPoiIds().get(0);
        // 初始化返回信息
        detail.setPoiId(poiId);
        detail.setTaskCount(0);
        detail.setDelayTaskCount(0);
        detail.setNearExpirationTaskCount(0);

        YodaIdentifyTaskPOExample example = new YodaIdentifyTaskPOExample();
        example.createCriteria()
                .andRiderAccountIdEqualTo(poiAccountInfo.getAccountId())
                .andTaskStatusIn(VerifyTaskStatusEnum.getOngoingStatus());
        example.setOrderByClause("push_task_time desc");
        List<YodaIdentifyTaskPO> tasks = yodaIdentifyTaskPOMapper.selectByExample(example);
        // 判断是否存有进行中的任务
        if (CollectionUtils.isEmpty(tasks)) {
            return TResult.buildSuccess(pendingTaskInfo.toJsonStr());
        }

        YodaIdentifyTaskPO po = tasks.get(0);
        // 判断任务是否过期
        if (TimeUtils.isAfterOrEquals(LocalDateTime.now(), po.getTaskExpireTime())) {
            return TResult.buildSuccess(pendingTaskInfo.toJsonStr());
        }
        detail.setTaskCount(tasks.size());
        // 查询规则配置
        Optional<YodaIdentityTaskConfig> configOpt = yodaIdentityTaskConfigRepository.selectByPrimaryKey(po.getRelRuleId());
       if (!configOpt.isPresent()) {
            return TResult.buildSuccess(pendingTaskInfo.toJsonStr());
        }

        YodaIdentityTaskConfig config = configOpt.get();
        Integer taskNearExpiration = config.getTaskNearExpiration();
        // 判断任务是否即将过期
        if (taskNearExpiration == null || taskNearExpiration <= 0) {
            return TResult.buildSuccess(pendingTaskInfo.toJsonStr());
        }
        LocalDateTime expireTime = po.getTaskExpireTime().minusMinutes(taskNearExpiration);
        if (TimeUtils.isAfterOrEquals(LocalDateTime.now(), expireTime)) {
            detail.setNearExpirationTaskCount(tasks.size());
        }
        return TResult.buildSuccess(pendingTaskInfo.toJsonStr());
    }

    @Override
    public TResult<TVerifyPendingTaskInfo> queryPendingTaskInfo(QueryVerifyTaskRequest request) {
        String errMsg = request.validate();
        if (StringUtils.isNotBlank(errMsg)) {
            throw new IllegalArgumentException(errMsg);
        }

        YodaIdentifyTaskPOExample example = new YodaIdentifyTaskPOExample();
        example.createCriteria()
                .andRiderAccountIdEqualTo(request.getRiderAccountId())
                .andTaskStatusIn(VerifyTaskStatusEnum.getOngoingStatus());
        example.setOrderByClause("create_time desc");
        List<YodaIdentifyTaskPO> taskPOS = yodaIdentifyTaskPOMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(taskPOS)) {
            return TResult.buildSuccess(null);
        }
        YodaIdentifyTaskPO taskPO = taskPOS.get(0);
        TVerifyPendingTaskInfo pendingTaskInfo = buildTVerifyPendingTaskInfo(taskPO);
        return TResult.buildSuccess(pendingTaskInfo);
    }

    private TVerifyPendingTaskInfo buildTVerifyPendingTaskInfo(YodaIdentifyTaskPO taskPO) {
        TVerifyPendingTaskInfo pendingTaskInfo = new TVerifyPendingTaskInfo();
        pendingTaskInfo.setId(taskPO.getId());

        long remindTime = calRemindSecond(taskPO);
        if (remindTime > 0) {
            pendingTaskInfo.setRemindTime(remindTime);
            pendingTaskInfo.setTaskStatus(taskPO.getTaskStatus());
        } else {
            pendingTaskInfo.setRemindTime(remindTime);
            pendingTaskInfo.setTaskStatus(VerifyTaskStatusEnum.EXPIRE.getCode());
        }
        pendingTaskInfo.setRiderAccountId(taskPO.getRiderAccountId());
        pendingTaskInfo.setPushTaskTime(TimeUtils.toMilliSeconds(taskPO.getPushTaskTime()));
        pendingTaskInfo.setCollectMode(taskPO.getCollectMode());

        Optional<YodaIdentityTaskConfig> configOpt = yodaIdentityTaskConfigRepository.selectByPrimaryKey(taskPO.getRelRuleId());
        if (configOpt.isPresent()) {
            YodaIdentityTaskConfig taskConfig = configOpt.get();
            if (taskConfig.getTaskNearExpiration() != null && taskConfig.getTaskNearExpiration() > 0) {
                pendingTaskInfo.setNearExpirationTime(taskConfig.getTaskNearExpiration() * 60);
            }else {
                pendingTaskInfo.setNearExpirationTime(0);
            }
        }
        return pendingTaskInfo;
    }

    private boolean judgeShouldAssignTask(Long tenantId, Long storeId,
                                          YodaIdentityTaskConfig taskConfig, Long riderAccountId,
                                          String viewOrderId, Integer orderBizType, Long riderEmpId) {
        LocalTime periodBeginTime = taskConfig.getPeriodBeginTime();
        LocalTime periodEndTime = taskConfig.getPeriodEndTime();

        LocalDateTime periodBeginLocalDateTime;
        LocalDateTime periodEndLocalDateTime;
        if (periodEndTime.isBefore(periodBeginTime)) {
            //跨天场景
            periodBeginLocalDateTime = LocalDateTime.of(LocalDate.now(), periodBeginTime);
            periodEndLocalDateTime = LocalDateTime.of(LocalDate.now().plusDays(1), periodEndTime);
        } else {
            //非跨天场景
            periodBeginLocalDateTime = LocalDateTime.of(LocalDate.now(), periodBeginTime);
            periodEndLocalDateTime = LocalDateTime.of(LocalDate.now(), periodEndTime);

        }

        //判断是否在黑名单内
        if (MccUtils.getAssignVerifyTaskAccountBlackList().contains(riderAccountId)) {
            log.info("命中黑名单,放弃下发");
            return false;
        }

        //判断现在是否在下发周期内
        LocalDateTime now = LocalDateTime.now();
        if (now.isBefore(periodBeginLocalDateTime) || now.isAfter(periodEndLocalDateTime)) {
            log.info("不在下发周期内, 放弃下发");
            return false;
        }

        //判断是否满足时间间隔
        List<YodaIdentifyTaskPO> riderLatestAssignTaskList = yodaIdentifyTaskPOExMapper.getRiderLatestAssignTask(riderAccountId);
        if (CollectionUtils.isNotEmpty(riderLatestAssignTaskList)) {
            YodaIdentifyTaskPO riderLatestAssignTask = riderLatestAssignTaskList.get(0);
            if(Duration.between(riderLatestAssignTask.getCreateTime(), now).getSeconds() < taskConfig.getCollectInterval() * 60) {
                log.info("不满足时间间隔,放弃下发");
                return false;
            }
        }

        //判断是否满足最大下发次数
        Integer collectPeriod = taskConfig.getCollectPeriod();
        YodaIdentifyTaskPOExample example = new YodaIdentifyTaskPOExample();
        example.createCriteria()
                .andRiderAccountIdEqualTo(riderAccountId)
                .andCreateTimeBetween(now.minusHours(collectPeriod), now);
        long collectTimes = yodaIdentifyTaskPOMapper.countByExample(example);
        if (collectTimes >= taskConfig.getMaxCollectTime()) {
            log.info("已到达最大采集次数, 放弃下发采集任务");
            return false;
        }

        //上一次采集任务是否报错，24小时之后再重试
        if (CollectionUtils.isNotEmpty(riderLatestAssignTaskList)) {
            YodaIdentifyTaskPO riderLatestAssignTask = riderLatestAssignTaskList.get(0);

            if (!isFinalStatus(riderLatestAssignTask)) {
                Cat.logEvent(CatEventEnum.VERIFY_ASSIGN_FAIL.getType(), CatEventEnum.VERIFY_ASSIGN_FAIL.getName());
                log.info("上一个采集任务还没结束, 放弃下发采集任务");
                return false;
            }

            boolean isSpecialCallbackCode = MccUtils.getInterceptAssignIdentifyCallbackCodes().contains(riderLatestAssignTask.getCallbackCode());
            if (isSpecialCallbackCode) {
                //上一次采集后的24小时内不能再下发任务
                if (LocalDateTime.now().isBefore(riderLatestAssignTask.getUpdateTime().plusHours(24))) {
                    log.error("24小时内不能使用yoda验证, 放弃下发采集任务");
                    return false;
                }
            }
        }

        //判断是否是一元单或推广自提单
        OrderTagInfo orderTagInfo = ocmsOrderQueryClient.checkOrderTag(tenantId, storeId, viewOrderId, orderBizType);
        if (orderTagInfo != null && Objects.equals(orderTagInfo.getIsOfflinePromoteOrder(), true)) {
            log.info("订单是一元单, 放弃下发采集任务");
            return false;
        }

        if (orderTagInfo != null && Objects.equals(orderTagInfo.getIsOneYuanOrder(), true)) {
            log.info("订单是推广自提单, 放弃下发采集任务");
            return false;
        }

        //判断单据类型是否属于首单、商品单、二单
        Boolean isSpecialType = pullNewThriftServiceClient.checkOrderIsSpecialType(viewOrderId, MccUtils.getFilterOrderPullNewEventTypes());
        if (Objects.equals(isSpecialType, true)) {
            log.info("订单属于首单、商品单、二单, 放弃下发采集任务");
            return false;
        }

        // 判断是否在下发岗位中
        if (!verifyPosition(taskConfig.getPositionList(), tenantId, storeId, riderEmpId)) {
            log.info("员工不在下发岗位中, 放弃下发采集任务");
            return false;
        }
        return true;
    }

    /**
     * 判断是否在下发岗位中
     */
    private boolean verifyPosition(List<String> positionList, Long tenantId, Long storeId, Long riderEmpId) {
        if (positionList == null) {
            return true;
        }
        if (CollectionUtils.isEmpty(positionList)) {
            return false;
        }
        WarehouseDTO warehouseDTO = warehouseQueryWrapper.queryWarehouse(tenantId, storeId);
        List<EmpDepRelDTO> empDepRelList = oswClient.batchQueryBelongDepByEmp(tenantId, Lists.newArrayList(riderEmpId));
        if (CollectionUtils.isEmpty(empDepRelList)) {
            Cat.logEvent("QUERY_EMP_LEAST_POSITION", "empty");
            log.warn("员工与部门关系为空");
            return false;
        }
        Long orgId = warehouseDTO.getOrgId();
        String positionCode = queryEmpLeastPosition(tenantId, orgId, orgId, empDepRelList);
        return positionList.contains(positionCode);
    }

    private String queryEmpLeastPosition(Long tenantId, Long depId, Long parentId, List<EmpDepRelDTO> empDepRelDTOS) {
        for (EmpDepRelDTO empDepRelDTO : empDepRelDTOS) {
            if (Objects.equals(depId, empDepRelDTO.getDepId()) && empDepRelDTO.getPositionId() != null) {
                return empDepRelDTO.getPositionCode();
            }
            if (Objects.equals(parentId, empDepRelDTO.getDepId()) && empDepRelDTO.getPositionId() != null) {
                return empDepRelDTO.getPositionCode();
            }
        }
        // 当前节点没有分配员工岗位找父节点
        Cat.logEvent("QUERY_EMP_LEAST_POSITION", "query_parent");
        log.info("当前节点没有分配员工岗位找父节点和祖节点");
        OrgDTO parentDept = oswClient.getParentDeptById(tenantId, parentId);
        if (parentDept == null) {
            return null;
        }
        return queryEmpLeastPosition(tenantId, parentDept.getId(), parentDept.getParentId(), empDepRelDTOS);
    }


    private YodaIdentifyTaskPO buildYodaTaskPO(YodaIdentityTaskConfig taskConfig, AssignVerifyTaskRequest request) {
        LocalDateTime now = LocalDateTime.now();
        YodaIdentifyTaskPO po = new YodaIdentifyTaskPO();
        po.setRiderAccountId(request.getRiderAccountId());
        po.setStoreId(request.getStoreId());
        po.setChannelOrderId(request.getViewOrderId());
        po.setOrderBizType(request.getOrderBizType());
        po.setCreateTime(now);
        po.setTaskStatus(VerifyTaskStatusEnum.INIT.getCode());
        po.setTenantId(request.getTenantId());
        po.setDeliveryOrderId(request.getDeliveryOrderId());
        po.setCollectMode(taskConfig.getCollectMode());
        po.setCollectContentType(JSON.toJSONString(taskConfig.getCollectContentTypeList()));
        po.setPushTaskTime(now);
        po.setRelRuleId(taskConfig.getId());
        po.setTaskExpireTime(now.plusMinutes(taskConfig.getTaskValidPeriod()));

        return po;
    }


    private YodaIdentifyTaskOpLogPO buildYodaTaskOpLogPO(String authorizeCode, Long taskId) {
        YodaIdentifyTaskOpLogPO po = new YodaIdentifyTaskOpLogPO();
        po.setTaskId(taskId);
        po.setAuthorizeCode(authorizeCode);
        po.setCreateTime(LocalDateTime.now());
        po.setUpdateTime(LocalDateTime.now());

        return po;
    }

    private TVerifyTaskInfo transform(YodaIdentifyTaskPO po) {
        TVerifyTaskInfo tVerifyTaskInfo = new TVerifyTaskInfo();
        tVerifyTaskInfo.setId(po.getId());


        long remindTime = calRemindSecond(po);

        if (remindTime > 0) {
            tVerifyTaskInfo.setRemindTime(remindTime);
            tVerifyTaskInfo.setTaskStatus(po.getTaskStatus());
        } else {
            tVerifyTaskInfo.setRemindTime(remindTime);
            tVerifyTaskInfo.setTaskStatus(VerifyTaskStatusEnum.EXPIRE.getCode());
        }

        tVerifyTaskInfo.setRiderAccountId(po.getRiderAccountId());

        return tVerifyTaskInfo;
    }

    @SuppressWarnings("unchecked")
    private void sendExpireDelayMessage(YodaIdentifyTaskPO taskPO) {
        try {
            long delayMilliSecs = TimeUtils.toMilliSeconds(taskPO.getTaskExpireTime()) - TimeUtils.toMilliSeconds(LocalDateTime.now());
            yodaIdentifyTaskExpireDelayMessageProducer.sendDelayMessage(JSON.toJSONString(new VerifyTaskExpireMessage(taskPO.getId())), delayMilliSecs);
        } catch (Exception e) {
            log.error("发送消息失败", e);
            Cat.logEvent(CatEventEnum.SEND_MESSAGE_ERROR.getType(), CatEventEnum.SEND_MESSAGE_ERROR.getName());
        }
    }

    /**
     * 发送临期提醒消息
     */
    private void sendNearExpirationMessage(YodaIdentifyTaskPO taskPO, YodaIdentityTaskConfig taskConfig) {
        try {
            if (taskConfig.getTaskNearExpiration() == null || taskConfig.getTaskNearExpiration() <= 0) {
                return;
            }
            // 临期时间配置的是分钟(过期前xx分钟)
            LocalDateTime nearExpirationTime = taskPO.getTaskExpireTime().minusMinutes(taskConfig.getTaskNearExpiration());
            long delayMilliSecs = TimeUtils.between(LocalDateTime.now(), nearExpirationTime, ChronoUnit.MILLIS);
            if (delayMilliSecs <= 0) {
                log.info("提醒时间小于当前时间, 无需发送临期提醒消息");
                return;
            }
            ProducerResult producerResult = smileTaskNearExpirationMessage.sendDelayMessage(JSON.toJSONString(new VerifyTaskNearExpirationMessage(taskPO.getId())), delayMilliSecs);
            log.info("发送临期提醒消息成功, taskId:{}, messageId:{}", taskPO.getId(), producerResult.getMessageID());
        } catch (Exception e) {
            log.error("发送临期提醒消息失败", e);
            Cat.logEvent(CatEventEnum.SEND_MESSAGE_ERROR.getType(), CatEventEnum.SEND_MESSAGE_ERROR.getName());
        }
    }


    /**
     * 4-安卓
     * 5-ios
     * @param platform
     * @return
     */
    private static int transPlatform(String platform) {
        String upperCasePlatform = platform.toUpperCase();

        if (StringUtils.equals(upperCasePlatform, "IOS")) {
            return 5;
        }

        return 4;
    }


    private static Long calRemindSecond(YodaIdentifyTaskPO po) {
        return (TimeUtils.toMilliSeconds(po.getTaskExpireTime()) - TimeUtils.toMilliSeconds(LocalDateTime.now())) / 1000;
    }

    private static String mapBusinessErrMsg(String errCode, String originalErrMsg) {
        if (StringUtils.isBlank(errCode)) {
            return originalErrMsg;
        }

        if (MccUtils.getYodaBisinsessErrCodeMap().containsKey(errCode)) {
            return MccUtils.getYodaBisinsessErrCodeMap().get(errCode);
        }

        //匹配不成功就埋点
        Cat.logEvent("UNDEFINED_ERR_CODE", errCode);
        return originalErrMsg;
    }

    private boolean isFinalStatus(YodaIdentifyTaskPO yodaIdentifyTaskPO) {
        return Objects.equals(yodaIdentifyTaskPO.getTaskStatus(), VerifyTaskStatusEnum.EXPIRE.getCode())
                || Objects.equals(yodaIdentifyTaskPO.getTaskStatus(), VerifyTaskStatusEnum.COMPLETED.getCode())
                || Objects.equals(yodaIdentifyTaskPO.getTaskStatus(), VerifyTaskStatusEnum.CANCEL.getCode());
    }

    private TVerifyTaskDTO transform2DTO(YodaIdentifyTaskPO po) {
        TVerifyTaskDTO tVerifyTaskDTO = new TVerifyTaskDTO();
        tVerifyTaskDTO.setTaskId(po.getId());
        tVerifyTaskDTO.setStoreId(po.getStoreId());
        tVerifyTaskDTO.setTaskStatus(po.getTaskStatus());
        tVerifyTaskDTO.setCollectContents(JSON.parseArray(po.getCollectContentType(), Integer.class));
        tVerifyTaskDTO.setCollectMode(po.getCollectMode());
        tVerifyTaskDTO.setRiderAccountId(po.getRiderAccountId());
        tVerifyTaskDTO.setChannelOrderId(po.getChannelOrderId());
        tVerifyTaskDTO.setTaskPushTime(po.getPushTaskTime());
        if (!Objects.equals(DEFAULT_DATE_TIME, po.getCompleteTime())) {
            tVerifyTaskDTO.setTaskCompleteTime(po.getCompleteTime());
        };
        tVerifyTaskDTO.setExpireTime(po.getTaskExpireTime());
        tVerifyTaskDTO.setHelmetIdentifyResult(Optional.ofNullable(po.getHelmetIdentifyResult()).map(val -> Objects.equals(val,1)).orElse(null));
        tVerifyTaskDTO.setFaceIdentifyResult(Optional.ofNullable(po.getFaceIdentifyResult()).map(val -> Objects.equals(val,1)).orElse(null));
        tVerifyTaskDTO.setDressingIdentifyResult(Optional.ofNullable(po.getDressingIdentifyResult()).map(val -> Objects.equals(val,1)).orElse(null));
        tVerifyTaskDTO.setCallbackCode(po.getCallbackCode());
        tVerifyTaskDTO.setCallbackMsg(po.getCallbackMsg());
        YodaVerifyTaskExtInfo yodaVerifyTaskExtInfo = JSON.parseObject(po.getExtInfo(), YodaVerifyTaskExtInfo.class);
        if (Objects.nonNull(yodaVerifyTaskExtInfo)) {
            tVerifyTaskDTO.setRequestCode(yodaVerifyTaskExtInfo.getRequestCode());
            tVerifyTaskDTO.setUuid(yodaVerifyTaskExtInfo.getUuid());
        }

        Map<String/*collectType*/, Integer/*punishType*/> punishInfoMap = JSON.parseObject(po.getPunishInfo(), new TypeReference<Map<String, Integer>>() {});
        if (MapUtils.isNotEmpty(punishInfoMap)) {
            tVerifyTaskDTO.setPunishInfoMap(punishInfoMap);
        }
        return tVerifyTaskDTO;
    }

    private TVerifyEndTaskDTO transform2EndTaskDTO(YodaIdentifyTaskPO po, YodaIdentityTaskConfig taskConfig) {
        TVerifyEndTaskDTO dto = new TVerifyEndTaskDTO();
        dto.setTaskId(po.getId());
        dto.setTaskStatus(po.getTaskStatus());
        dto.setPushTaskTime(TimeUtils.toMilliSeconds(po.getPushTaskTime()));
        dto.setExpireTime(TimeUtils.toMilliSeconds(po.getTaskExpireTime()));
        dto.setCompleteTime(TimeUtils.toMilliSeconds(po.getCompleteTime()));
        dto.setCollectMode(po.getCollectMode());
        dto.setFaceIdentifyResult(Objects.equals(po.getFaceIdentifyResult(), 1));
        dto.setHelmetIdentifyResult(Objects.equals(po.getHelmetIdentifyResult(), 1));
        dto.setDressingIdentifyResult(Objects.equals(po.getDressingIdentifyResult(), 1));
        dto.setVerifyStatus(po.getResultCheckPass());
        try {
            YodaVerifyTaskExtInfo extInfo = JSON.parseObject(po.getExtInfo(), YodaVerifyTaskExtInfo.class);
            if (Objects.nonNull(extInfo) && Objects.equals(true, extInfo.getNewLabor())) {
                taskConfig.getNoControl().stream().filter(item -> VerifyTaskNoControlEnum.EQUIPMENT_PICKUP_TIME.equals(item.getType()))
                        .findFirst().ifPresent(item -> {
                            Integer pickupTime = item.getRules().get(0).getPickupTime();
                            VerifyNoControlLabel newLaborLabel = item.getLabels().get(0);
                            TLabelDTO labelDTO = new TLabelDTO();
                            labelDTO.setLabelName(newLaborLabel.getName());
                            labelDTO.setLabelDesc(MessageFormat.format(newLaborLabel.getDesc(), pickupTime));
                            dto.setLabels(Lists.newArrayList(labelDTO));
                        });
            }else {
                dto.setLabels(Lists.newArrayList());
            }
        } catch (Exception e) {
            log.error("设置标签信息异常", e);
        }
        return dto;
    }


    private void sendVerifyFailMessage(YodaIdentifyTaskPO yodaIdentifyTaskPO, String requestCode) {
        try {
            VerifyFailMessage verifyFailMessage = new VerifyFailMessage();
            verifyFailMessage.setYodaIdentifyTaskId(yodaIdentifyTaskPO.getId());
            if (Objects.equals(yodaIdentifyTaskPO.getHelmetIdentifyResult(), VERIFY_FAIL)) {
                verifyFailMessage.setHelmetIdentifyFail(true);
            }
            if (Objects.equals(yodaIdentifyTaskPO.getFaceIdentifyResult(), VERIFY_FAIL)) {
                verifyFailMessage.setFaceIdentifyFail(true);
                verifyFailMessage.setRequestCode(requestCode);
            }
            if (Objects.equals(yodaIdentifyTaskPO.getDressingIdentifyResult(), VERIFY_FAIL)) {
                verifyFailMessage.setDressingIdentifyFail(true);
            }
            if (verifyFailMessage.getDressingIdentifyFail() || verifyFailMessage.getHelmetIdentifyFail() || verifyFailMessage.getFaceIdentifyFail()) {
                yodaVerifyFailMessageProducer.sendMessage(JSON.toJSONString(verifyFailMessage));
            }
        } catch (Exception e) {
            log.error("发送验证失败结果失败", e);
        }
    }


}
