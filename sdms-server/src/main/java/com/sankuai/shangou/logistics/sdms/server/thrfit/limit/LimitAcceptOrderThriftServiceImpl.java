package com.sankuai.shangou.logistics.sdms.server.thrfit.limit;

import com.sankuai.shangou.commons.thrift.publisher.annotation.ShangouThriftServer;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.logistics.sdms.application.utils.LionConfigUtils;
import com.sankuai.shangou.logistics.sdms.domain.entity.limit.LimitItemClientProxyHelper;
import com.sankuai.shangou.logistics.sdms.sdk.limit.LimitAcceptOrderThriftService;
import com.sankuai.shangou.logistics.sdms.sdk.limit.dto.LimitItemDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/10/31 16:54
 **/
@Slf4j
@ShangouThriftServer
public class LimitAcceptOrderThriftServiceImpl implements LimitAcceptOrderThriftService {

    @Resource
    private LimitItemClientProxyHelper limitItemClientProxyHelper;

    @Override
    public TResult<List<LimitItemDTO>> queryLimitItemListByAccountIdNew(Long tenantId, Long accountId, Long storeId) {
        if (tenantId == null) {
            throw new IllegalArgumentException("tenantId is null");
        }

        if ( accountId == null) {
            throw new IllegalArgumentException("accountId is null");
        }

        List<LimitItemDTO> limitItemDTOS = limitItemClientProxyHelper.queryLimitItemListByAccountId(tenantId, accountId, storeId);
        return TResult.buildSuccess(limitItemDTOS);
    }

    @Override
    public TResult<Map<Long, List<LimitItemDTO>>> queryLimitItemListByAccountIdListNew(Long tenantId, List<Long> accountIdList, Long storeId) {
        if (tenantId == null) {
            throw new IllegalArgumentException("tenantId is null");
        }

        if (CollectionUtils.isEmpty(accountIdList)) {
            return TResult.buildSuccess(Collections.emptyMap());
        }

        Map<Long, List<LimitItemDTO>> riderLimitItemListMap = limitItemClientProxyHelper.queryLimitItemListByAccountIdList(tenantId, accountIdList, storeId);

        return TResult.buildSuccess(riderLimitItemListMap);
    }

    @Override
    public TResult<List<LimitItemDTO>> queryLimitItemListByAccountId(Long tenantId, Long accountId) {
        return queryLimitItemListByAccountIdNew(tenantId, accountId, null);
    }

    @Override
    public TResult<Map<Long, List<LimitItemDTO>>> queryLimitItemListByAccountIdList(Long tenantId, List<Long> accountIdList) {
        return queryLimitItemListByAccountIdListNew(tenantId, accountIdList, null);
    }
}
