package com.sankuai.shangou.logistics.sdms.server.thrfit.config;

import com.sankuai.shangou.commons.thrift.publisher.annotation.ShangouThriftServer;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.logistics.sdms.application.config.SdmsStoreConfigApplicationService;
import com.sankuai.shangou.logistics.sdms.sdk.config.SdmsStoreConfigService;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2024-07-08
 * @email <EMAIL>
 */
@Slf4j
@ShangouThriftServer
public class SdmsStoreConfigServiceImpl implements SdmsStoreConfigService {

    @Resource
    private SdmsStoreConfigApplicationService sdmsStoreConfigApplicationService;

    @Override
    @Deprecated
    public TResult<Integer> calcAssessDeliveryDuration(Long merchantId, Long warehouseId, String tradeOrderNo, Integer orderBizType, Long deliveryDistance) {
        return TResult.buildSuccess(sdmsStoreConfigApplicationService.calcAssessDeliveryDuration(merchantId, warehouseId, tradeOrderNo, orderBizType, deliveryDistance, null, null));
    }

    @Override
    public TResult<Integer> calcAssessDeliveryDurationBySeconds(Long merchantId, Long warehouseId, String tradeOrderNo, Integer orderBizType, Long deliveryDistance) {
        return TResult.buildSuccess(sdmsStoreConfigApplicationService.calcAssessDeliveryDurationBySeconds(merchantId, warehouseId, tradeOrderNo, orderBizType, deliveryDistance, null, null));
    }

    @Override
    public TResult<Long> calcPushDownTimestamp(Long merchantId, Long warehouseId, String tradeOrderNo, Integer orderBizType, Long deliveryDistance) {
        return TResult.buildSuccess(sdmsStoreConfigApplicationService.calcPushDownTimestamp(merchantId, warehouseId, tradeOrderNo, orderBizType, deliveryDistance));
    }
}
