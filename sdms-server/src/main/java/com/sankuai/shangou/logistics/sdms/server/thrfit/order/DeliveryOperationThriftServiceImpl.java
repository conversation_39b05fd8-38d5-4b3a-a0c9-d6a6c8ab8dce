package com.sankuai.shangou.logistics.sdms.server.thrfit.order;

import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.meituan.mafka.client.producer.IProducerProcessor;
import com.meituan.xframe.boot.mafka.autoconfigure.annotation.MafkaProducer;
import com.sankuai.meituan.reco.pickselect.query.thrift.lackLocked.OutboundOrderLackLockedThriftService;
import com.sankuai.meituan.reco.pickselect.query.thrift.lackLocked.dto.GoodsLackLockedDetailDTO;
import com.sankuai.meituan.reco.pickselect.query.thrift.lackLocked.request.QueryOrderLackLockedDetailRequest;
import com.sankuai.shangou.commons.thrift.publisher.annotation.ShangouThriftServer;
import com.sankuai.shangou.commons.thrift.publisher.exception.ShangouBizException;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.logistics.sdms.dao.mapper.YodaIdentifyTaskPOExMapper;
import com.sankuai.shangou.logistics.sdms.dao.mapper.YodaIdentifyTaskPOMapper;
import com.sankuai.shangou.logistics.sdms.domain.entity.common.Operator;
import com.sankuai.shangou.logistics.sdms.domain.entity.config.SelfDeliveryPoiConfig;
import com.sankuai.shangou.logistics.sdms.domain.external.OpenAggDeliveryClient;
import com.sankuai.shangou.logistics.sdms.domain.external.RiderDeliveryQueryClient;
import com.sankuai.shangou.logistics.sdms.domain.external.SelfDeliveryPoiConfigClient;
import com.sankuai.shangou.logistics.sdms.domain.repository.YodaIdentityTaskConfigRepository;
import com.sankuai.shangou.logistics.sdms.domain.utils.IListUtils;
import com.sankuai.shangou.logistics.sdms.infrastructure.external.OswClient;
import com.sankuai.shangou.logistics.sdms.infrastructure.utils.MccUtils;
import com.sankuai.shangou.logistics.sdms.sdk.common.TContextInfo;
import com.sankuai.shangou.logistics.sdms.sdk.order.DeliveryOperationThriftService;
import com.sankuai.shangou.logistics.sdms.sdk.verify.request.TurnAggDeliveryRequest;
import com.sankuai.shangou.logistics.warehouse.AbnOrderService;
import com.sankuai.shangou.logistics.warehouse.dto.AbnOrderDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023-08-31
 * @email <EMAIL>
 */
@Slf4j
@ShangouThriftServer
public class DeliveryOperationThriftServiceImpl implements DeliveryOperationThriftService {

    @Resource
    private OpenAggDeliveryClient openAggDeliveryClient;
    @Resource
    private SelfDeliveryPoiConfigClient selfDeliveryPoiConfigClient;
    @Resource
    private AbnOrderService abnOrderService;
    @Resource
    private RedisStoreClient redisClient;

    @Resource
    private RiderDeliveryQueryClient riderDeliveryQueryClient;

    @Resource
    private YodaIdentityTaskConfigRepository yodaIdentityTaskConfigRepository;

    @Resource
    private YodaIdentifyTaskPOMapper yodaIdentifyTaskPOMapper;

    @Resource
    private YodaIdentifyTaskPOExMapper yodaIdentifyTaskPOExMapper;

    @Resource
    private OswClient oswClient;

    private static final String CATEGORY_NAME = "NEW_PICK_GRAY_STORE";

    @Resource
    private OutboundOrderLackLockedThriftService outboundOrderLackLockedThriftService;

    /**
     * todo:现在运单还没有迁移过来。运单迁移过来后，应该是把配送信息同步过去。取消自己的自配运单。并且两边交互应该用orderId
     */
    @Override
    public TResult<Void> turnToAggDelivery(TContextInfo tContextInfo, TurnAggDeliveryRequest req) {

        //校验该门店是否可以发三方
        SelfDeliveryPoiConfig selfDeliveryPoiConfig = selfDeliveryPoiConfigClient.querySelfDeliveryConfig(tContextInfo.getTenantId(), tContextInfo.getPoiId());
        if (Objects.isNull(selfDeliveryPoiConfig) || !selfDeliveryPoiConfig.getEnableTurnDelivery()) {
            throw new ShangouBizException("该门店不支持转三方配送");
        }

        //校验是否有异常单，有异常单也不能转三方
        //注意这里的逻辑有点绕的是，即使有异常单，可能现在库存已经满足了（此时没有items）。依然让转成功（这里就不在转三方里去关闭异常单了）
        if (isNewPickStore(tContextInfo.getPoiId())) {

            if(MccUtils.getPickSelectLackLockedSwitch()){
                QueryOrderLackLockedDetailRequest request = new QueryOrderLackLockedDetailRequest();
                request.setTenantId(tContextInfo.getTenantId());
                request.setOfflineStoreId(tContextInfo.getPoiId());
                request.setChannelOrderId(req.getChannelOrderId());
                request.setOrderBizType(req.getChannelId());
                TResult<List<GoodsLackLockedDetailDTO>>  result = outboundOrderLackLockedThriftService.queryOrderLackLockedDetail(request);
                if(result == null || !result.isSuccess() || CollectionUtils.isNotEmpty(result.getData())) {
                    throw new ShangouBizException("有异常单不支持转三方！");
                }
            }else {
                TResult<List<AbnOrderDTO>> unprocessedAbnOrderResult = abnOrderService.getUnprocessed(tContextInfo.getPoiId());
                boolean hasAbnItem = IListUtils.nullSafeStream(unprocessedAbnOrderResult.getData())
                        .filter(abnOrderDTO -> Objects.equals(abnOrderDTO.getSourceOrderNo(), req.getChannelOrderId()))
                        .anyMatch(abnOrderDTO -> CollectionUtils.isNotEmpty(abnOrderDTO.getItems()));
                if (!unprocessedAbnOrderResult.isSuccess() || hasAbnItem) {
                    throw new ShangouBizException("有异常单不支持转三方！");
                }
            }

        }

        //发三方,注意tms的operator_id是account_id
        openAggDeliveryClient.turnToAggDelivery(
                tContextInfo.getTenantId(), tContextInfo.getPoiId(), req.getChannelOrderId(), req.getChannelId(),
                new Operator(tContextInfo.getEmployeeName(), tContextInfo.getAccountId(), LocalDateTime.now())
        );

        return new TResult<Void>().success(null);
    }


    private boolean isNewPickStore(long warehouseId) {
        return true;
    }

}
