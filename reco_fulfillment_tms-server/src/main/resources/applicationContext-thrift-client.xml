<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:util="http://www.springframework.org/schema/util"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd
        http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util.xsd">

    <import resource="classpath:clients/applicationContext-thrift-client-emproduct.xml"/>
    <import resource="classpath:clients/applicationContext-thrift-client-ocms.xml"/>
    <import resource="classpath:clients/applicationContext-thrift-client-ocmschannel.xml"/>
    <import resource="classpath:clients/applicationContext-thrift-client-orderbiz.xml"/>
    <import resource="classpath:clients/applicationContext-thrift-client-orderplatform.xml"/>
    <import resource="classpath:clients/applicationContext-thrift-client-saasauth.xml"/>
    <import resource="classpath:clients/applicationContext-thrift-client-saasmessage.xml"/>
    <import resource="classpath:clients/applicationContext-thrift-client-tenant.xml"/>
    <import resource="classpath:clients/applicationContext-thrift-client-xm.xml"/>
    <import resource="classpath:clients/applicationContext-thrift-client-banma.xml"/>
    <import resource="classpath:clients/applicationContext-thrift-client-saas-sac-search.xml"/>
    <import resource="classpath:clients/applicationContext-thrift-client-order-mng.xml"/>
    <import resource="classpath:clients/applicationContext-thrift-client-labor.xml"/>

    <!-- thrift客户端线程池配置 -->
    <bean id="mtThriftPoolConfig" class="com.meituan.service.mobile.mtthrift.client.pool.MTThriftPoolConfig">
        <property name="maxActive" value="50"/>
        <property name="maxIdle" value="20"/>
        <property name="minIdle" value="1"/>
        <property name="maxWait" value="1000"/>
        <property name="testOnBorrow" value="true"/>
        <property name="testOnReturn" value="false"/>
        <property name="testWhileIdle" value="false"/>
        <property name="minEvictableIdleTimeMillis" value="86400000"/>
    </bean>

    <!--履约配置详情-->
    <bean id="rpcStoreFulfillConfigThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.reco.pickselect.ebase.thrift.StoreFulfillConfigThriftService"/>
        <property name="timeout" value="500"/>
        <property name="connTimeout" value="200"/>
        <!-- 本地 appkey, 改成自己服务的 appKey -->
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.sc.pickselectebase"/>
        <property name="remoteServerPort" value="8421"/>
        <property name="nettyIO" value="true"/> <!-- 开启 Netty IO  -->
    </bean>

    <bean id="mapOpenApiService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.map.open.platform.api.MapOpenApiService"/>
        <property name="timeout" value="500"/>
        <property name="connTimeout" value="200"/>
        <!-- 本地 appkey, 改成自己服务的 appKey -->
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.apigw.map.facadecenter"/>
        <property name="filterByServiceName" value="true"/>
        <property name="remoteUniProto" value="true"/>
        <property name="nettyIO" value="true"/> <!-- 开启 Netty IO  -->
    </bean>

    <bean id="mapOpenApiService4DrunkHorse" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.map.open.platform.api.MapOpenApiService"/>
        <property name="timeout" value="3000"/>
        <!-- 本地 appkey, 改成自己服务的 appKey -->
        <property name="appKey" value="${app.name}"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="remoteAppkey" value="com.sankuai.apigw.map.facadecenter"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/> <!-- 开启 Netty IO  -->
    </bean>

    <bean id="drunkHorsePushThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.sgxsupply.wxmall.bizmanagement.client.thrift.DrunkHorsePushThriftService"/>
        <property name="timeout" value="2000"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.sgxsupply.wxmall.bizmanagement"/>
        <property name="remoteServerPort" value="8001"/>
        <property name="nettyIO" value="true"/> <!-- 开启 Netty IO  -->
    </bean>

    <bean id="userThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface" value="com.meituan.shangou.saas.tenant.thrift.UserThriftService"/>
        <property name="timeout" value="1000"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.tenant"/>
        <property name="remoteServerPort" value="8799"/>
    </bean>

    <bean id="riderPickingThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.reco.pickselect.thrift.picking.rider.RiderPickingThriftService"/>
        <property name="timeout" value="1000"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.sc.pickselectservice"/>
        <property name="nettyIO" value="true"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="selfDeliveryPoiConfigThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.shangou.logistics.delivery.poi.SelfDeliveryPoiConfigThriftService"/>
        <property name="timeout" value="1000"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.logistics.dmp"/>
        <property name="nettyIO" value="true"/> <!-- 开启 Netty IO  -->
        <property name="filterByServiceName" value="true"/>
    </bean>


    <bean id="ofwClientThriftPoolConfig" class="com.meituan.service.mobile.mtthrift.client.pool.MTThriftPoolConfig">
        <property name="maxActive" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('qnh_ofc_ofw_thrift_client_pool_max-active', '20')}"/>
        <property name="maxIdle" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('qnh_ofc_ofw_thrift_client_pool_max-idle', '10')}"/>
        <property name="minIdle" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('qnh_ofc_ofw_thrift_client_pool_min-idle', '5')}"/>
        <property name="maxWait" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('qnh_ofc_ofw_thrift_client_pool_max-wait', '1000')}"/>
        <property name="testOnBorrow" value="true"/>
        <property name="testOnReturn" value="false"/>
        <property name="testWhileIdle" value="false"/>
    </bean>

    <bean id="fulfillmentOrderSearchThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="ofwClientThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.qnh.ofc.ofw.client.thrift.service.order.FulfillmentOrderSearchThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('ofc_ofw_order_search_thrift_timeout','10000')}"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.qnh.ofc.ofw"/>
        <property name="remoteServerPort" value="8411"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="tradeShippingGrayService" class="com.sankuai.shangou.commons.thrift.augment.RichTypeThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.shangou.logistics.warehouse.TradeShippingGrayService"/>
        <property name="timeout" value="3000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.logistics.oio"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="abnOrderService" class="com.sankuai.shangou.commons.thrift.augment.RichTypeThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.shangou.logistics.warehouse.AbnOrderService"/>
        <property name="timeout" value="3000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.logistics.oio"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="shortLinkOperateServiceThrift" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.dianping.mobileossapi.service.operate.OperateServiceThrift"/>
        <property name="timeout" value="3000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="mobile-oss-operation-service"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="tWarehouseService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.shangou.infra.osw.api.poi.warehouse.TWarehouseService"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.infra.osw"/>
        <property name="timeout" value="5000"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="sdmsStoreConfigThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.shangou.logistics.sdms.sdk.config.SdmsStoreConfigService"/>
        <property name="timeout" value="3000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.logistics.sdms"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="sealContainerLogService" class="com.sankuai.shangou.commons.thrift.augment.RichTypeThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.shangou.logistics.hu.api.service.SealContainerLogService"/>
        <property name="timeout" value="5000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.logistics.hucenter"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/> <!-- 开启 Netty IO  -->
    </bean>

    <bean id="fulfillmentStoreConfigThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.shangou.qnh.ofc.ebase.service.FulfillmentStoreConfigThriftService"/>
        <property name="timeout"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getInt('fulfill_store_config_thrift_timeout',1500)}"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.qnh.ofc.ebase"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="outboundOrderLackLockedThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.meituan.reco.pickselect.query.thrift.lackLocked.OutboundOrderLackLockedThriftService"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.qnh.fulfill.pickquery"/>
        <property name="timeout" value="5000"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="tEmployeeService" class="com.sankuai.shangou.commons.thrift.augment.RichTypeThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.shangou.infra.osw.api.org.TEmployeeService"/>
        <property name="timeout" value="5000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.infra.osw"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="limitAcceptOrderThriftService" class="com.sankuai.shangou.commons.thrift.augment.RichTypeThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.shangou.logistics.sdms.sdk.limit.LimitAcceptOrderThriftService"/>
        <property name="timeout" value="5000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.logistics.sdms"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/> <!-- 开启 Netty IO  -->
    </bean>

    <bean id="deliveryConfigManageThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.shangou.logistics.delivery.configure.DeliveryConfigManageThriftService"/>
        <property name="timeout" value="8000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.logistics.dmp"/>
        <property name="nettyIO" value="true"/> <!-- 开启 Netty IO  -->
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="authenticateService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.meituan.shangou.sac.thrift.authenticate.AuthenticateService"/>
        <property name="timeout" value="3000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.saasauth"/>
        <property name="nettyIO" value="true"/>
        <property name="filterByServiceName" value="true"/>
    </bean>
</beans>
