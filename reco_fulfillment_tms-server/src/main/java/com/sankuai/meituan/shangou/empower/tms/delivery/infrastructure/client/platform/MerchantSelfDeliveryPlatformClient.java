package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.client.platform;

import com.dianping.cat.Cat;
import com.sankuai.meituan.shangou.dms.base.model.value.DeliveryChannel;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrder;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.LaunchFailure;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderInfo;
import com.sankuai.meituan.shangou.dms.base.model.value.CoordinatePoint;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.push.PushClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.producer.MafkaMessageProducer;
import com.sankuai.meituan.shangou.empower.tms.delivery.model.AppVersionLevelDTO;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoi;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Failure;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Rider;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.RiderLocationDetail;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.RiderLocationRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.StaffRider;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.client.sync.DeliveryChangeSyncOutMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.client.sync.RiderDeliveryOrderSyncOutClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.utils.DeliveryRiderMccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.utils.TimeUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.utils.version.VersionComparator;
import com.sankuai.meituan.shangou.saas.common.cat.CatTransaction;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.log.changeinfo.DeliveryExceptionInfo.NO_EXCEPTION;
import static com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryEventEnum.DELIVERY_PLATFORM_ACCEPT_DELIVERY_ORDER;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/7/13
 */
@Slf4j
@Component
public class MerchantSelfDeliveryPlatformClient extends AbstractDeliveryPlatformClient {

	@Resource
	private PushClient pushClient;
	@Resource
	private RiderLocationRepository riderLocationRepository;

	@Resource
	private RiderDeliveryOrderSyncOutClient riderDeliveryOrderSyncOutClient;

	@Resource
	private MafkaMessageProducer<DeliveryChangeSyncOutMessage> deliveryChangeSyncOutMessageProducer;

	@Override
	public DeliveryPlatformEnum getDeliveryPlatform() {
		return DeliveryPlatformEnum.MERCHANT_SELF_DELIVERY;
	}

	@Override
	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public Optional<LaunchFailure> launch(DeliveryPoi deliveryPoi, OrderInfo orderInfo, DeliveryOrder deliveryOrder,Integer transferOrderMark) {
		//推进到等待分配骑手状态，并记录流水
		deliveryOrder.onChange(DELIVERY_PLATFORM_ACCEPT_DELIVERY_ORDER, NO_EXCEPTION, null, LocalDateTime.now());

		// 发送新任务通知 PUSH (压测时跳过发PUSH流程)
		if (!MccConfigUtils.getPressureTestTenantIds().contains(deliveryOrder.getTenantId())) {
			pushClient.pushMerchantSelfDeliveryNewDeliveryTask(deliveryOrder, orderInfo);
		}

		// 自营骑手新任务业务埋点
		Cat.logEvent("SELF_RIDER_NEW_TASK_CREATED", "SUCCESS");

		return Optional.empty();
	}

	@Override
	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public Optional<Failure> cancelDelivery(DeliveryOrder deliveryOrder) {
		DeliveryStatusEnum beforeStatus = deliveryOrder.getStatus();
		//推进状态
		deliveryOrder.onChange(DeliveryEventEnum.DELIVERY_CANCEL, NO_EXCEPTION, deliveryOrder.getRiderInfo(), LocalDateTime.now());

		//推送取消通知
		pushClient.pushMerchantSelfDeliveryOrderCancelled(deliveryOrder, beforeStatus);

		//发送MQ消息通知外部
		Long currentRiderAccountId = null;
		if (Objects.equals(deliveryOrder.getDeliveryChannel(), DeliveryChannelEnum.MERCHANT_DELIVERY.getCode())) {
			Rider riderInfo = deliveryOrder.getRiderInfo();
			if (Objects.nonNull(riderInfo) && (riderInfo instanceof StaffRider)) {
				currentRiderAccountId = ((StaffRider)riderInfo).getRiderAccountId();
			}
		}

		deliveryChangeSyncOutMessageProducer.sendMessage(new DeliveryChangeSyncOutMessage(DeliveryAsyncOutTypeEnum.DELIVERY_CANCEL.getValue(),
				new DeliveryChangeSyncOutMessage.Head(deliveryOrder.getTenantId(), deliveryOrder.getStoreId(),deliveryOrder.getId(), deliveryOrder.getOrderBizType(),deliveryOrder.getOrderId(), deliveryOrder.getChannelOrderId(), deliveryOrder.getStatus().getCode()),
				new DeliveryChangeSyncOutMessage.DeliveryCancelBody(currentRiderAccountId)));

		return Optional.empty();
	}

	@Override
	@CatTransaction
	// @MethodLog(logRequest = false, logResponse = true)
	public Optional<CoordinatePoint> queryRiderLocation(DeliveryOrder deliveryOrder, DeliveryPoi deliveryPoi) {
		if(deliveryOrder==null || deliveryOrder.getRiderInfo()==null){
			return Optional.empty();
		}
		try {
			StaffRider rider= (StaffRider) deliveryOrder.getRiderInfo();
			if(rider.getRiderAccountId()==null){
				return Optional.empty();
			}
			RiderLocationDetail riderLocationDetail = riderLocationRepository.getStaffRiderLocation(rider.getRiderAccountId());
			if(riderLocationDetail==null){
				return Optional.empty();
			}
			log.info("MerchantSelfDeliveryPlatformClient.queryRiderLocation riderLocationDetail:{}", riderLocationDetail);
			CoordinatePoint coordinatePoint = new CoordinatePoint(riderLocationDetail.getLongitude(), riderLocationDetail.getLatitude(), TimeUtil.toMilliSeconds(riderLocationDetail.getTime()));
			//app版本判断
			doAppVersionLevel(riderLocationDetail, coordinatePoint, deliveryOrder.getTenantId(), deliveryPoi.getStoreId());
			return Optional.of(coordinatePoint);
		}catch (Exception e){
			log.error("MerchantSelfDeliveryPlatformClient.queryRiderLocation error .deliveryOrder:{},deliveryPoi:{}",deliveryOrder,deliveryPoi);
		}
		return Optional.empty();
	}

	/**
	 * app版本判断
	 */
	private void doAppVersionLevel(RiderLocationDetail riderLocationDetail, CoordinatePoint coordinatePoint, Long tenantId, Long storeId) {
		try {
			if (StringUtils.isBlank(riderLocationDetail.getOs())
					|| StringUtils.isBlank(riderLocationDetail.getAppVersion())) {
				return;
			}

			// 判断是否新版本
			if (!riderDeliveryOrderSyncOutClient.checkOpenBatchRiderPointSync(storeId, tenantId)) {
				return;
			}


			List<AppVersionLevelDTO> list = MccConfigUtils.getLionListDataConf(LionConfigNameEnum.MERCHANT_SELF_DELIVERY_APP_VERSION_CONF.getName(), AppVersionLevelDTO.class, Collections.emptyList());
			if (CollectionUtils.isEmpty(list)) {
				return;
			}

			//商家自配送判断版本 Android是4.3.3 iOS是3.9.1
			String appVersion = riderLocationDetail.getAppVersion();
			String os = riderLocationDetail.getOs();

			Integer appVersionLevel = list.stream()
					// 判断系统
					.filter(item -> os.equalsIgnoreCase(item.getOs()))
					// 筛选版本
					.filter(item -> {
						if (StringUtils.isBlank(item.getVersionStart()) && StringUtils.isBlank(item.getVersionEnd())) {
							return false;
						} else if (StringUtils.isBlank(item.getVersionStart())) {
							return VersionComparator.INSTANCE.compare(item.getVersionEnd(), appVersion) > 0;
						} else if (StringUtils.isBlank(item.getVersionEnd())) {
							return VersionComparator.INSTANCE.compare(item.getVersionStart(), appVersion) <= 0;
						} else {
							return VersionComparator.INSTANCE.compare(item.getVersionStart(), appVersion) <= 0
									&& VersionComparator.INSTANCE.compare(item.getVersionEnd(), appVersion) > 0;
						}
					})
					.map(AppVersionLevelDTO::getLevel)
					.findFirst()
					.orElse(null);

			coordinatePoint.setAppVersionLevel(appVersionLevel);
		} catch (Exception e) {
			log.error("MerchantSelfDeliveryPlatformClient.doAppVersionLevel error", e);
		}
	}

	@Override
	@CatTransaction
	// @MethodLog(logRequest = false, logResponse = true)
	public Optional<CoordinatePoint> queryRiderLocation(DeliveryOrder deliveryOrder, DeliveryChannel deliveryChannelDto, DeliveryPoi deliveryPoi) {
		return queryRiderLocation(deliveryOrder, deliveryPoi);
	}

	@Override
	@CatTransaction
	// @MethodLog(logRequest = false, logResponse = true)
	public Optional<CoordinatePoint> queryRiderLocation(DeliveryOrder deliveryOrder) {
		if (deliveryOrder == null || deliveryOrder.getRiderInfo() == null) {
			return Optional.empty();
		}
		try {
			StaffRider rider = (StaffRider) deliveryOrder.getRiderInfo();
			if (rider.getRiderAccountId() == null) {
				return Optional.empty();
			}
			RiderLocationDetail riderLocationDetail =
					riderLocationRepository.getStaffRiderLocation(rider.getRiderAccountId());
			if (riderLocationDetail == null) {
				return Optional.empty();
			}
			log.info("MerchantSelfDeliveryPlatformClient.queryRiderLocation riderLocationDetail:{}", riderLocationDetail);
			return Optional.of(new CoordinatePoint(riderLocationDetail.getLongitude(),
					riderLocationDetail.getLatitude(), TimeUtil.toMilliSeconds(riderLocationDetail.getTime())));
		} catch (Exception e) {
			log.error("MerchantSelfDeliveryPlatformClient.queryRiderLocation error .deliveryOrder:{}", deliveryOrder);
		}
		return Optional.empty();
	}

	@Override
	public Optional<Failure> cancelDeliveryForTransOrder(DeliveryOrder deliveryOrder) {
		return cancelDelivery(deliveryOrder);
	}

	@Override
	public Optional<Failure> cancelDeliveryForOFC(DeliveryOrder deliveryOrder) {
		DeliveryStatusEnum beforeStatus = deliveryOrder.getStatus();
		//推进状态
		deliveryOrder.onChange(DeliveryEventEnum.DELIVERY_CANCEL, NO_EXCEPTION, deliveryOrder.getRiderInfo(), LocalDateTime.now());

		//推送取消通知
		pushClient.pushMerchantSelfDeliveryOrderCancelled(deliveryOrder, beforeStatus);
		return Optional.empty();
	}
}
