package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.client.rider.sync;

import com.sankuai.meituan.shangou.dms.base.model.value.CoordinatePoint;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryChangeNotifyService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.LionConfigNameEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.channel.OcmsChannelClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryChannelEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.DeliveryChangeNotifyMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.message.RiderPointBatchSyncMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.producer.MafkaDelayMessageProducer;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.producer.MafkaMessageProducer;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.service.OcmsChannelService;
import com.sankuai.meituan.shangou.empower.tms.delivery.model.RiderInfoChangeBo;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Rider;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.RiderDeliveryOrder;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.RiderLocationDetail;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.client.sync.DeliveryChangeSyncOutMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.client.sync.RiderDeliveryOrderSyncOutClient;
import com.sankuai.meituan.shangou.saas.common.cat.CatTransaction;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/7/12
 */
@Slf4j
@Service
public class DefaultRiderDeliveryOrderSyncOutClient implements RiderDeliveryOrderSyncOutClient {

	@Resource
	private MafkaMessageProducer<DeliveryChangeNotifyMessage> deliveryChangeNotifyMessageProducer;

	@Resource
	private MafkaMessageProducer<DeliveryChangeSyncOutMessage> deliveryChangeSyncOutMessageProducer;

	@Resource
	private MafkaMessageProducer<DeliveryChangeSyncOutMessage> laborAttendanceTrackingProducer;

	@Resource
	private MafkaDelayMessageProducer<RiderPointBatchSyncMessage> batchSyncRiderPointToChannelProducer;

	@Resource
	private MafkaDelayMessageProducer<RiderPointBatchSyncMessage> afterUnifySyncRiderPointToChannelProducer;

	@Resource
	private DeliveryChangeNotifyService deliveryChangeNotifyService;

	@Resource
	private OcmsChannelClient ocmsChannelClient;

	@Resource
	private OcmsChannelService ocmsChannelService;

	private static final int SUCCESS = 0;

	//syncToOrderSystem这个名字表明该方法只能同步给订单服务，且目前该消息只能同步运单状态变更消息，所以后续建议使用asyncOut方法
	@Deprecated
	@Override
	@CatTransaction
	@MethodLog(logRequest = true)
	public void syncToOrderSystem(RiderDeliveryOrder deliveryOrder, LocalDateTime changeTime, RiderLocationDetail riderLocationDetail) {
		deliveryChangeNotifyMessageProducer.sendMessage(new DeliveryChangeNotifyMessage(deliveryOrder, changeTime,riderLocationDetail));
	}

	/**
	 * 发异步消息，本服务也可以监听，外部服务需要的也可以监听，通用的同步接口
	 *
	 */
	@Override
	public void asyncOut(DeliveryChangeSyncOutMessage message) {
		long time = System.currentTimeMillis();
		log.info("before invoke asyncOut timestamp:{}, message:{}", time, message);
		deliveryChangeSyncOutMessageProducer.sendMessage(message);
		log.info("after invoke asyncOut, costs:{}", System.currentTimeMillis() - time);
	}


	@Override
	@CatTransaction
	@MethodLog(logRequest = true)
	public void notifySyncRiderPosition(Long deliveryId, DeliveryPlatformEnum platformEnum,Long tenantId,Long storeId) {
		deliveryChangeNotifyService.notifySyncRiderPosition(deliveryId,platformEnum,tenantId,storeId);
	}

	@Override
	public void updateDeliveryProofPhoto(RiderDeliveryOrder deliveryOrder, CoordinatePoint coordinatePoint) throws TException {
		ocmsChannelClient.updateDeliveryProofPhoto(deliveryOrder, coordinatePoint);
	}

	@Override
	public void syncRiderInfoChange(RiderDeliveryOrder deliveryOrder, Rider rider, String channelOrderId) {
		RiderInfoChangeBo riderInfoChangeBo = new RiderInfoChangeBo();
		riderInfoChangeBo.setTenantId(deliveryOrder.getTenantId());
		riderInfoChangeBo.setStoreId(deliveryOrder.getStoreId());
		riderInfoChangeBo.setOrderBizType(deliveryOrder.getCustomerOrderKey().getOrderBizType());
		riderInfoChangeBo.setChannelOrderId(channelOrderId);
		riderInfoChangeBo.setDeliveryChannelId(DeliveryChannelEnum.MERCHANT_DELIVERY.getCode());
		riderInfoChangeBo.setDeliveryOrderId(deliveryOrder.getId());
		if (Objects.nonNull(rider)) {
			riderInfoChangeBo.setRiderName(rider.getRiderName());
			riderInfoChangeBo.setRiderPhone(rider.getRiderPhone());
		}

		ocmsChannelService.syncRiderInfoChange(riderInfoChangeBo);
	}

	@Override
	public boolean checkOpenBatchRiderPointSync(Long storeId, Long tenantId) {
		boolean storeOpen = MccConfigUtils.checkStoreOpenConfigForList(LionConfigNameEnum.OPEN_CHANNEL_RIDER_POINT_BATCH_SYNC.getName(), storeId);
		boolean tenantOpen = MccConfigUtils.checkTenantOpenConfigForList(LionConfigNameEnum.OPEN_CHANNEL_RIDER_POINT_BATCH_SYNC_FOR_TENANT.getName(), tenantId);
		if (!storeOpen && !tenantOpen) {
			log.info("未开启批量骑手轨迹上传");
		}
		return storeOpen || tenantOpen;
	}

	@Override
	public void sendRiderPointBatchSyncMsg(Long tenantId, Long storeId, Long deliveryOrderId, Long orderId, Long lastSyncEndTime) {
		try {
			RiderPointBatchSyncMessage message = new RiderPointBatchSyncMessage(deliveryOrderId, tenantId, storeId, orderId, lastSyncEndTime);
			Integer delayMinutes = MccConfigUtils.getLionIntConf(LionConfigNameEnum.RIDER_POINT_BATCH_SYNC_DELAY_TIME.getName(), 60 * 1000);

			//发送延时消息
			batchSyncRiderPointToChannelProducer.sendDelayMessageInMillis(message, delayMinutes);
		} catch (Exception e) {
			log.error("发送批量轨迹上传消息失败", e);
		}
	}

	@Override
	public void sendRiderAllPointSyncMsg(Long tenantId, Long storeId, Long deliveryOrderId, Long orderId) {
		try {
			RiderPointBatchSyncMessage message = new RiderPointBatchSyncMessage(deliveryOrderId, tenantId, storeId, orderId, null);
			Integer delayMinutes = MccConfigUtils.getLionIntConf(LionConfigNameEnum.RIDER_ALL_POINT_SYNC_DELAY_TIME.getName(), 60 * 1000);

			//发送延时消息
			afterUnifySyncRiderPointToChannelProducer.sendDelayMessageInMillis(message, delayMinutes);
		} catch (Exception e) {
			log.error("发送骑手所有点位同步消息失败", e);
		}
	}
}
