package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.thrift;

import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.google.common.base.Functions;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.meituan.shangou.saas.order.platform.enums.OrderBizTypeEnum;
import com.sankuai.meituan.reco.pickselect.query.thrift.lackLocked.dto.GoodsLackLockedDetailDTO;
import com.sankuai.meituan.shangou.dms.base.model.value.DeliveryChannel;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.AggDeliveryLinkAuthSquirrelOperationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.DeliveryChannelApplicationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.DeliveryOperationApplicationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.DeliveryProcessApplicationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.order.OrderSystemClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.platform.DeliveryChannelPreLaunchInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.platform.DeliveryPlatformUrlInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.common.Status;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.DeliveryOperationThriftService;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.request.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.request.orderplatform.DeliveryManualLaunchRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.response.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.facade.PickSelectRemoteService;
import com.sankuai.meituan.shangou.empower.tms.delivery.facade.TenantRemoteService;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.cache.TransferOrderSquirrelOperateService;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.client.platform.DapDeliveryPlatformClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.OrderLockEndNotifyMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.producer.MafkaMessageProducer;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.wrapper.LimitAcceptServiceWrapper;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.wrapper.OSWServiceWrapper;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.StaffRider;
import com.sankuai.meituan.shangou.empower.tms.delivery.utils.LimitTakeOrderUtils;
import com.sankuai.meituan.shangou.saas.common.cat.CatTransaction;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import com.sankuai.shangou.commons.exception.common.exceptions.BizException;
import com.sankuai.shangou.infra.osw.api.org.dto.response.EmployeeDTO;
import com.sankuai.shangou.infra.osw.api.poi.dto.response.BusinessPoiDTO;
import com.sankuai.shangou.logistics.sdms.sdk.limit.dto.LimitItemDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.FailureCodeEnum.*;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/10/22
 */
@Slf4j
@Service
public class DeliveryOperationThriftServiceImpl implements DeliveryOperationThriftService {

    @Resource
    private DeliveryProcessApplicationService deliveryProcessApplicationService;
    @Resource
    private DeliveryPoiRepository deliveryPoiRepository;
    @Resource
    private DeliveryOperationApplicationService deliveryOperationApplicationService;
    @Resource
    private OrderSystemClient orderSystemClient;

    @Resource
    private DapDeliveryPlatformClient dapDeliveryPlatformClient;

    @Resource
    private DeliveryOrderRepository deliveryOrderRepository;
    
    @Resource
    private TransferOrderSquirrelOperateService transferOrderSquirrelOperateService;

    @Resource
    private TenantRemoteService tenantRemoteService;

    @Resource
    private MafkaMessageProducer<OrderLockEndNotifyMessage> orderLockEndNotifyMessageProducer;

    @Resource
    private AggDeliveryLinkAuthSquirrelOperationService aggDeliveryLinkAuthSquirrelOperationService;

    @Resource
    private DeliveryChannelApplicationService deliveryChannelApplicationService;

    @Resource
    private LimitAcceptServiceWrapper limitAcceptServiceWrapper;

    @Resource
    private OSWServiceWrapper oswServiceWrapper;

    @Resource
    private PickSelectRemoteService pickSelectRemoteService;

    @Override
    public DeliveryLaunchResponse launchWithOutChannelDelivery(DLaunchWithOutChannelReq request) {
        try {
            String requestErrorMsg = request.validate();
            if (requestErrorMsg != null) {
                return new DeliveryLaunchResponse(new Status(FailureCodeEnum.INVALID_PARAM.getCode(), requestErrorMsg));
            }

            Result<OrderInfo> orderQueryResult = orderSystemClient.getOrderInfo(new OrderKey(request.getTenantId(), request.getStoreId(), request.getOrderId()), false);
            if (orderQueryResult.isFail()) {
                return new DeliveryLaunchResponse(new Status(orderQueryResult.getFailure().getFailureCode(), orderQueryResult.getFailure().getFailureMessage()));
            }

            Optional<DeliveryPoi> opDeliveryPoi = Optional.empty();
            DynamicOrderBizType orderBizType = ObjectUtils.defaultIfNull(DynamicOrderBizType.findOf(
                    orderQueryResult.getInfo().getOrderBizType()), DynamicOrderBizType.MEITUAN_WAIMAI);
            opDeliveryPoi = deliveryPoiRepository.queryDeliveryPoiWithDefault(request.getTenantId(), request.getStoreId(), orderBizType.getChannelId());
            if (!opDeliveryPoi.isPresent()) {
		        return new DeliveryLaunchResponse(new Status(
				        FailureCodeEnum.TENANT_SHOP_NOT_CONFIG_DELIVERY_CHANNEL_SHOP.getCode(),
				        FailureCodeEnum.TENANT_SHOP_NOT_CONFIG_DELIVERY_CHANNEL_SHOP.getMessage()
		        ));
	        }

	        if(opDeliveryPoi.get().getDeliveryPlatform() != DeliveryPlatformEnum.MALT_FARM_DELIVERY_PLATFORM && opDeliveryPoi.get().getDeliveryPlatform() != DeliveryPlatformEnum.DAP_DELIVERY_PLATFORM){
	            return new DeliveryLaunchResponse(new Status(FailureCodeEnum.LAUNCH_DELIVERY_FAILED.getCode(), "暂不支持当前渠道发起配送"));
            }

	        //后续如果扩展，考虑store判断渠道,目前默认麦芽田
	        return deliveryOperationApplicationService
			        .launchDelivery(
					        opDeliveryPoi.get(), new OrderKey(request.getTenantId(), request.getStoreId(), request.getOrderId()), false,PlatformSourceEnum.OMS,null
			        )
			        .map(value -> new DeliveryLaunchResponse(new Status(value.getFailureCode(), value.getFailureMessage())))
			        .orElseGet(() -> new DeliveryLaunchResponse(Status.SUCCESS));
        } catch (Exception e) {
	        log.error("DeliveryOperationThriftService.launchDelivery error", e);
            return new DeliveryLaunchResponse(new Status(SYSTEM_ERROR.getCode(), SYSTEM_ERROR.getMessage()));
        }
    }

    @Override
    public DeliveryOrderUrlResponse queryOrderDeliveryUrl(DeliveryOrderUrlRequest request) {
        DeliveryOrderUrlResponse response=new DeliveryOrderUrlResponse();
        if(request==null || CollectionUtils.isEmpty(request.getOrderIdList()) && CollectionUtils.isEmpty(request.getFulfillMarkIdList())){
            response.setStatus(new Status(FailureCodeEnum.INVALID_PARAM.getCode(),FailureCodeEnum.INVALID_PARAM.getMessage()));
            return response;
        }

        List<DeliveryPoi>  deliveryPoiList = deliveryPoiRepository.queryAllDeliveryPoi(request.getTenantId(), request.getStoreId());

        if (CollectionUtils.isEmpty(deliveryPoiList)) {
            response.setStatus(new Status(FailureCodeEnum.INVALID_PARAM.getCode(),FailureCodeEnum.INVALID_PARAM.getMessage()));
            return response;
        }

        Optional<DeliveryPoi> opDeliveryPoi=deliveryPoiList.stream().filter(w ->w.getChannelType() == DynamicChannelType.MEITUAN.getChannelId()).findFirst();
        DeliveryPoi deliveryPoi=null;
        if(opDeliveryPoi.isPresent()){
            deliveryPoi = opDeliveryPoi.get();
        }
        if(deliveryPoi == null){
            Optional<DeliveryPoi> opAnyDeliveryPoi = deliveryPoiList.stream().findFirst();
            if(opAnyDeliveryPoi.isPresent()){
                deliveryPoi = opAnyDeliveryPoi.get();
            }else {
                response.setStatus(new Status(FailureCodeEnum.INVALID_PARAM.getCode(),FailureCodeEnum.INVALID_PARAM.getMessage()));
                return response;
            }
        }
        List<String> markIdList = new ArrayList<>();

        if(CollectionUtils.isNotEmpty(request.getOrderIdList())){
            markIdList.addAll(Lists.transform(request.getOrderIdList(), Functions.toStringFunction()));
        }

       if(CollectionUtils.isNotEmpty(request.getFulfillMarkIdList())){
           markIdList.addAll(request.getFulfillMarkIdList());
       }

        Optional<Map<String, DeliveryPlatformUrlInfo>> mapOptional = dapDeliveryPlatformClient.queryLinkInfo(deliveryPoi,markIdList, LinkTypeEnum.ORDER_LINK_TYPE);

        if(!mapOptional.isPresent()){
            response.setStatus(new Status(SYSTEM_ERROR.getCode(),FailureCodeEnum.SYSTEM_ERROR.getMessage()));
            return response;
        }
        Map<String,DeliveryPlatformUrlInfo> infoMap=mapOptional.get();
        if(MapUtils.isEmpty(infoMap)){
            response.setStatus(new Status());
            return response;
        }
        Map<String,String> urlMap=new HashMap<>();
        for (Map.Entry<String,DeliveryPlatformUrlInfo> entry : infoMap.entrySet()){
            urlMap.put(entry.getKey(),entry.getValue().getLinkUrl());
        }
        response.setStatus(new Status());
        response.setOrderUrlMap(urlMap);
        return response;
    }

    @Override
    public TurnToAggregationDeliveryResponse turnToAggregationDelivery(TurnToAggregationDeliveryRequest request) {

        String requestErrorMsg = request.validate();
        if (requestErrorMsg != null) {
            return new TurnToAggregationDeliveryResponse(new Status(FailureCodeEnum.INVALID_PARAM.getCode(), requestErrorMsg));
        }

        DeliveryPlatformEnum deliveryPlatform = DeliveryPlatformEnum.enumOf(request.getDeliveryPlatformId());
        if (deliveryPlatform == null) {
            return new TurnToAggregationDeliveryResponse(new Status(FailureCodeEnum.INVALID_PARAM.getCode(), "不支持的配送渠道"));
        }

        OrderKey orderKey = new OrderKey(request.getTenantId(), request.getStoreId(), request.getOrderId());

        //查询订单信息
        Result<OrderInfo> orderQueryResult = orderSystemClient.getOrderInfo(orderKey, false);
        if (orderQueryResult.isFail()) {
            return new TurnToAggregationDeliveryResponse(new Status(SYSTEM_ERROR.getCode(), SYSTEM_ERROR.getMessage()));
        }

        Optional<TurnToAggregationDeliveryResponse> optionalLimit = checkLimitAcceptItem(request.getTenantId(),request.getNewRiderAccountId(),request.getStoreId());

        if(optionalLimit.isPresent()){
            return optionalLimit.get();
        }

        if(isAbnOrder(request.getTenantId(), request.getStoreId(),orderQueryResult.getInfo().getChannelOrderId(),orderQueryResult.getInfo().getOrderBizType(),deliveryPlatform)){
            return new TurnToAggregationDeliveryResponse(new Status(FailureCodeEnum.ABN_ORDER_NOT_TURN_AGG.getCode(), FailureCodeEnum.ABN_ORDER_NOT_TURN_AGG.getMessage()));
        }

        EmployeeDTO employeeDTO = null;
        if(request.getNewRiderAccountId() != null && request.getNewRiderAccountId()>0L){
            try {
                List<EmployeeDTO> employeeDTOList = oswServiceWrapper.queryEmpByAccountIds(request.getTenantId(), Arrays.asList(request.getNewRiderAccountId()));
                if(CollectionUtils.isNotEmpty(employeeDTOList)){
                    employeeDTO = employeeDTOList.get(0);
                }

            }catch (Exception e){
                log.error("queryOperatePoiByPoiId error",e);
            }
        }

        //校验订单是否可以转三方配送
        OrderInfo orderInfo = orderQueryResult.getInfo();
        if (!orderInfo.canTurnToSelfDelivery()) {
            log.warn("订单[{}]不满足发起配送条件，转配送失败", orderInfo);
            return new TurnToAggregationDeliveryResponse(new Status(FailureCodeEnum.ORDER_STATUS_ERROR.getCode(), FailureCodeEnum.ORDER_STATUS_ERROR.getMessage()));
        }

        //将配送平台、转单操作人保存在缓存中
        String key = transferOrderSquirrelOperateService.getKey(orderInfo.getOrderKey().getTenantId(), orderInfo.getOrderKey().getStoreId(), orderInfo.getOrderKey().getOrderId());
        transferOrderSquirrelOperateService.set(key,new TransferOperateInfo(deliveryPlatform, request.getOperatorId(), request.getOperatorName()));

        try {
            return deliveryOperationApplicationService
                    .turnToAggregationDelivery(
                            orderInfo, deliveryPlatform,new Operator(request.getOperatorId(),request.getOperatorName()),request.getTenantId(),employeeDTO
                    )
                    .map(value -> new TurnToAggregationDeliveryResponse(new Status(value.getFailureCode(), value.getFailureMessage())))
                    .orElseGet(() -> new TurnToAggregationDeliveryResponse(Status.SUCCESS));
        }catch (Exception e){
            log.error("DeliveryOperationThriftService.turnToAggregationDelivery error", e);
            return new TurnToAggregationDeliveryResponse(new Status(SYSTEM_ERROR.getCode(), SYSTEM_ERROR.getMessage()));
        }

    }
    private Optional<TurnToAggregationDeliveryResponse> checkLimitAcceptItem(Long tenantId, Long accountId, Long storeId) {
        if(accountId==null || accountId<=0L){
            return Optional.empty();
        }

        List<LimitItemDTO> limitItemList = limitAcceptServiceWrapper.queryLimitItemByAccountId(tenantId,accountId,storeId);
        if(CollectionUtils.isEmpty(limitItemList)){
            return Optional.empty();
        }
        List<String> msg = limitItemList.stream()
                .filter(Objects::nonNull)
                // 限制接单类型，1-直接限制直接过，2-先提示后限制，需要先判断是否已经超过限制开始时间
                .filter(LimitTakeOrderUtils::checkCurrentIsLimit)
                .map(LimitItemDTO::getReason)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(msg)) {
            return Optional.empty();
        }
        String errMsg = FailureCodeEnum.LIMIT_ACCEPT_ORDER.getMessage(Joiner.on("、").join(msg));
        return Optional.of(new TurnToAggregationDeliveryResponse(new Status(FailureCodeEnum.LIMIT_ACCEPT_ORDER.getCode(), errMsg)));
    }

    @MethodLog(logRequest = true, logResponse = true)
    private boolean isAbnOrder(Long tenantId,Long warehouseId,String channelOrderId,Integer orderBizType,DeliveryPlatformEnum deliveryPlatform){

        if(!MccConfigUtils.checkPickLackTenant(tenantId)){
            return false;
        }

        if(deliveryPlatform != DeliveryPlatformEnum.DAP_DELIVERY_PLATFORM && deliveryPlatform != DeliveryPlatformEnum.MALT_FARM_DELIVERY_PLATFORM){
            return false;
        }

        List<GoodsLackLockedDetailDTO> lackLockedDetailDTOList = pickSelectRemoteService.queryOrderLackLockedDetail(tenantId,warehouseId,channelOrderId,orderBizType);
        if(CollectionUtils.isEmpty(lackLockedDetailDTOList)){
            return false;
        }
        return true;

    }

    @Override
    public DeliveryCancelResponse cancelDelivery(DeliveryCancelRequest request) {
        try {
            String requestErrorMsg = request.validate();
            if (requestErrorMsg != null) {
                return new DeliveryCancelResponse(new Status(FailureCodeEnum.INVALID_PARAM.getCode(), requestErrorMsg));
            }
            OrderKey orderKey = new OrderKey(request.getTenantId(), request.getStoreId(), request.getOrderId());
            Optional<Failure> failure = deliveryOperationApplicationService.cancelDelivery(orderKey);
            if (failure.isPresent()) {
                log.error("发起取消配送失败 deliveryId={}", request);
                return new DeliveryCancelResponse(
                        new Status(failure.get().getFailureCode(), failure.get().getFailureMessage()));
            }
            return new DeliveryCancelResponse(Status.SUCCESS);
        } catch (Exception e) {
            log.error("发起取消配送失败 deliveryId={}", e);
        }
        return new DeliveryCancelResponse(new Status(SYSTEM_ERROR.getCode(), SYSTEM_ERROR.getMessage()));
    }

    @Override
    @CatTransaction
    @MethodLog(logRequest = false, logResponse = true)
    public DeliveryPreLaunchResponse preLaunchDelivery(DeliveryPreLaunchRequest request) {
        try {
            String requestErrorMsg = request.validate();
            if (requestErrorMsg != null) {
                return new DeliveryPreLaunchResponse(new Status(FailureCodeEnum.INVALID_PARAM.getCode(), requestErrorMsg));
            }

            Result<List<DeliveryChannelPreLaunchInfo>> result = deliveryOperationApplicationService.preLaunchDelivery(
                    new OrderKey(request.getTenantId(), request.getStoreId(), request.getOrderId())
            );
            if (result.isSuccess()) {
                return new DeliveryPreLaunchResponse(
                        Optional.ofNullable(result.getInfo())
                                .orElse(new ArrayList<>())
                                .stream()
                                .map(it -> new DeliveryChannelPreLaunchResponse(
                                        it.getDeliveryChannel().getCode(),
                                        it.getDeliveryChannel().getName(),
                                        it.getServicePackage(),
                                        it.isAvailable(),
                                        it.getFailReason(),
                                        Optional.ofNullable(it.getEstimatedDeliveryFee()).map(BigDecimal::doubleValue).orElse(null),
                                        Optional.ofNullable(it.getDiscountAmount()).map(BigDecimal::doubleValue).orElse(null))
                                )
                                .collect(Collectors.toList())
                );
            } else {
                Failure failure = result.getFailure();
                return new DeliveryPreLaunchResponse(new Status(failure.getFailureCode(), failure.getFailureMessage()));
            }
        } catch (Exception e) {
            log.error("DeliveryOperationThriftService.preLaunchDelivery error", e);
            return new DeliveryPreLaunchResponse(new Status(SYSTEM_ERROR.getCode(), SYSTEM_ERROR.getMessage()));
        }
    }

    @Override
    @CatTransaction
    @MethodLog(logRequest = false, logResponse = true)
    public DeliveryLaunchResponse launchDelivery(DeliveryLaunchRequest request) {

        try {
            String requestErrorMsg = request.validate();
            if (requestErrorMsg != null) {
                return new DeliveryLaunchResponse(new Status(FailureCodeEnum.INVALID_PARAM.getCode(), requestErrorMsg));
            }

            DeliveryChannelEnum deliveryChannel = DeliveryChannelEnum.valueOf(request.getDeliveryChannelId());
            if (deliveryChannel == null) {
                return new DeliveryLaunchResponse(new Status(FailureCodeEnum.INVALID_PARAM.getCode(), "不支持的配送渠道"));
            }
            if (deliveryChannel.equals(DeliveryChannelEnum.HAI_KUI_DELIVERY) && Lion.getConfigRepository().getBooleanValue("delivery.manual.launch.haikui",true)) {
                return deliveryProcessApplicationService
                        .manualLaunchDelivery(new OrderKey(request.getTenantId(),request.getStoreId(),request.getOrderId()))
                        .map(value -> new DeliveryLaunchResponse(new Status(value.getFailureCode(), value.getFailureMessage())))
                        .orElseGet(() -> new DeliveryLaunchResponse(Status.SUCCESS));
            }

            return deliveryOperationApplicationService
                    .launchDelivery(
                            new OrderKey(request.getTenantId(), request.getStoreId(), request.getOrderId()),
                            deliveryChannel,
                            request.getServicePackage(),
                            BigDecimal.valueOf(request.getEstimatedDeliveryFee()),
                            false
                    )
                    .map(value -> new DeliveryLaunchResponse(new Status(value.getFailureCode(), value.getFailureMessage())))
                    .orElseGet(() -> new DeliveryLaunchResponse(Status.SUCCESS));

        } catch (Exception e) {
            log.error("DeliveryOperationThriftService.launchDelivery error", e);
            return new DeliveryLaunchResponse(new Status(SYSTEM_ERROR.getCode(), SYSTEM_ERROR.getMessage()));
        }
    }

    @Override
    @CatTransaction
    @MethodLog(logRequest = false, logResponse = true)
    public TurnToThirdDeliveryResponse turnToThirdDelivery(TurnToThirdDeliveryRequest request) {
        try {
            String requestErrorMsg = request.validate();
            if (requestErrorMsg != null) {
                return new TurnToThirdDeliveryResponse(new Status(FailureCodeEnum.INVALID_PARAM.getCode(), requestErrorMsg));
            }

            DeliveryChannelEnum deliveryChannel = DeliveryChannelEnum.valueOf(request.getDeliveryChannelId());
            if (deliveryChannel == null) {
                return new TurnToThirdDeliveryResponse(new Status(FailureCodeEnum.INVALID_PARAM.getCode(), "不支持的配送渠道"));
            }

            return deliveryOperationApplicationService
                    .turnToThirdDelivery(
                            new OrderKey(request.getTenantId(), request.getStoreId(), request.getOrderId()),
                            deliveryChannel,
                            request.getServicePackage(),
                            BigDecimal.valueOf(request.getEstimatedDeliveryFee())
                    )
                    .map(value -> new TurnToThirdDeliveryResponse(new Status(value.getFailureCode(), value.getFailureMessage())))
                    .orElseGet(() -> new TurnToThirdDeliveryResponse(Status.SUCCESS));

        } catch (Exception e) {
            log.error("DeliveryOperationThriftService.turnToThirdDelivery error", e);
            return new TurnToThirdDeliveryResponse(new Status(SYSTEM_ERROR.getCode(), SYSTEM_ERROR.getMessage()));
        }
    }

    @Override
    @CatTransaction
    @MethodLog(logRequest = false, logResponse = true)
    public TurnToMerchantSelfDeliveryResponse turnToMerchantSelfDelivery(TurnToMerchantSelfDeliveryRequest request) {
        try {
            String requestErrorMsg = request.validate();
            if (requestErrorMsg != null) {
                return new TurnToMerchantSelfDeliveryResponse(new Status(FailureCodeEnum.INVALID_PARAM.getCode(), requestErrorMsg));
            }

            return deliveryOperationApplicationService
                    .turnToMerchantSelfDelivery(new OrderKey(request.getTenantId(), request.getStoreId(), request.getOrderId()))
                    .map(value -> new TurnToMerchantSelfDeliveryResponse(new Status(value.getFailureCode(), value.getFailureMessage())))
                    .orElseGet(() -> new TurnToMerchantSelfDeliveryResponse(Status.SUCCESS));

        } catch (Exception e) {
            log.error("DeliveryOperationThriftService.turnToMerchantSelfDelivery error", e);
            return new TurnToMerchantSelfDeliveryResponse(new Status(SYSTEM_ERROR.getCode(), SYSTEM_ERROR.getMessage()));
        }
    }


    @Deprecated
    @Override
    @CatTransaction
    @MethodLog(logRequest = true, logResponse = true)
    public TurnToMerchantSelfDeliveryResponse turnToMerchantSelfDeliveryForDrunkHorse(DrunkHorseTurnToMerchantSelfDeliveryRequest request) {
        try {
            String requestErrorMsg = request.validate();
            if (requestErrorMsg != null) {
                return new TurnToMerchantSelfDeliveryResponse(new Status(FailureCodeEnum.INVALID_PARAM.getCode(), requestErrorMsg));
            }

            if (!MccConfigUtils.checkIsDHTenant(request.getTenantId())) {
                return new TurnToMerchantSelfDeliveryResponse(new Status(FailureCodeEnum.TENANT_NOT_SUPPORT_OPERATION.getCode(), FailureCodeEnum.TENANT_NOT_SUPPORT_OPERATION.getMessage()));
            }

            OrderKey orderKey = new OrderKey(request.getTenantId(), request.getStoreId(), request.getOrderId());
            StaffRider rider = new StaffRider(request.getNewAccountName(), request.getNewAccountPhone(), null, request.getNewAccountId());
            return deliveryOperationApplicationService
                    .turnToMerchantSelfDeliveryForDrunkHorse(orderKey, rider)
                    .map(value -> new TurnToMerchantSelfDeliveryResponse(new Status(value.getFailureCode(), value.getFailureMessage())))
                    .orElseGet(() -> new TurnToMerchantSelfDeliveryResponse(Status.SUCCESS));

        } catch (Exception e) {
            log.error("DeliveryOperationThriftService.turnToMerchantSelfDelivery error", e);
            return new TurnToMerchantSelfDeliveryResponse(new Status(SYSTEM_ERROR.getCode(), SYSTEM_ERROR.getMessage()));
        }
    }

    @Override
    public DeliveryLaunchResponse manualLaunchDelivery(DeliveryManualLaunchRequest request) {
        String requestErrorMsg = request.validate();
        if (requestErrorMsg != null) {
            return new DeliveryLaunchResponse(new Status(FailureCodeEnum.INVALID_PARAM.getCode(), requestErrorMsg));
        }

        try {
            Optional<LaunchFailure> failure = deliveryOperationApplicationService.manualLaunchDelivery(request.getTenantId(), request.getStoreId(), request.getOrderId(), request.getOperatorId(), request.getAppId());
            return failure.map(value -> new DeliveryLaunchResponse(new Status(value.getFailureCode(),
                    value.getFailureMessage()))).orElseGet(() -> new DeliveryLaunchResponse(Status.SUCCESS));
        } catch (Exception e) {
            log.error("DeliveryOperationThriftService.manualLaunchDelivery4OrderChannelDeliveryPlatform error", e);
            return new DeliveryLaunchResponse(new Status(SYSTEM_ERROR.getCode(), SYSTEM_ERROR.getMessage()));
        }
    }


    @Override
    public PaoTuiLockStatusNotifyResponse paoTuiLockStatusNotify(PaoTuiLockStatusNotifyRequest req) {

        if (!MccConfigUtils.isSwitchLockEndLaunchDelivery()) {
            return new PaoTuiLockStatusNotifyResponse(Status.SUCCESS);
        }
        try {
            if(req==null || StringUtils.isEmpty(req.getChannelOrderId())){
                return new PaoTuiLockStatusNotifyResponse(new Status(FailureCodeEnum.INVALID_PARAM.getCode(), FailureCodeEnum.INVALID_PARAM.getMessage()));
            }
            Result<OrderInfo> orderInfoResult = orderSystemClient.queryByViewOrderId(OrderBizTypeEnum.MEITUAN_WAIMAI.getValue(),req.getChannelOrderId(),false);
            OrderInfo orderInfo=orderInfoResult.getInfo();
            if(orderInfo==null){
                return new PaoTuiLockStatusNotifyResponse(new Status(FailureCodeEnum.SYSTEM_ERROR.getCode(), FailureCodeEnum.SYSTEM_ERROR.getMessage()));
            }
            OrderLockEndNotifyMessage message = new OrderLockEndNotifyMessage();
            message.setOrderId(orderInfo.getOrderKey().getOrderId());
            message.setOrderBizType(orderInfo.getOrderBizType());
            message.setChannelOrderId(req.getChannelOrderId());
            message.setLockOrderStatus(0);
            message.setIsLockOrderV2(false);

            orderLockEndNotifyMessageProducer.sendMessage(message,orderInfo.getOrderKey().getOrderId());
        } catch (Exception e) {
            log.error("DeliveryOperationThriftServiceImpl#paoTuiLockStatusNotify error, request:{}", req, e);
            return new PaoTuiLockStatusNotifyResponse(new Status(SYSTEM_ERROR.getCode(), SYSTEM_ERROR.getMessage()));
        }
        return new PaoTuiLockStatusNotifyResponse(Status.SUCCESS);

    }

    @Override
    public PaoTuiLockStatusNotifyResponse paoTuiLockStatusNotifyV2(PaoTuiLockStatusNotifyV2Request req) {
        if (MccConfigUtils.getPaotuiLockOrderV2Switch()) {
            log.error("DeliveryOperationThriftServiceImpl#paoTuiLockStatusNotifyV2 降级");
            return new PaoTuiLockStatusNotifyResponse(Status.SUCCESS);
        }
        try {
            if(req==null || StringUtils.isEmpty(req.getChannelOrderId())){
                return new PaoTuiLockStatusNotifyResponse(new Status(FailureCodeEnum.INVALID_PARAM.getCode(), FailureCodeEnum.INVALID_PARAM.getMessage()));
            }
            Result<OrderInfo> orderInfoResult = orderSystemClient.queryByViewOrderId(OrderBizTypeEnum.MEITUAN_WAIMAI.getValue(), req.getChannelOrderId(),false);
            OrderInfo orderInfo=orderInfoResult.getInfo();
            if(orderInfo==null){
                return new PaoTuiLockStatusNotifyResponse(new Status(FailureCodeEnum.SYSTEM_ERROR.getCode(), FailureCodeEnum.SYSTEM_ERROR.getMessage()));
            }
            if (isDhTenantOrMedicineUW(orderInfo.getOrderKey())) {
                return new PaoTuiLockStatusNotifyResponse(Status.SUCCESS);
            }
            OrderLockEndNotifyMessage message = new OrderLockEndNotifyMessage();
            message.setOrderId(orderInfo.getOrderKey().getOrderId());
            message.setOrderBizType(orderInfo.getOrderBizType());
            message.setChannelOrderId(req.getChannelOrderId());
            message.setLockOrderStatus(req.deliveryAvailable);
            message.setIsLockOrderV2(true);
            orderLockEndNotifyMessageProducer.sendMessage(message,orderInfo.getOrderKey().getOrderId());
        } catch (Exception e) {
            log.error("DeliveryOperationThriftServiceImpl#paoTuiLockStatusNotifyV2 error, request:{}", req, e);
            return new PaoTuiLockStatusNotifyResponse(new Status(SYSTEM_ERROR.getCode(), SYSTEM_ERROR.getMessage()));
        }
        return new PaoTuiLockStatusNotifyResponse(Status.SUCCESS);

    }

    private boolean isDhTenantOrMedicineUW(OrderKey orderKey) {
        boolean isMedicineUW = tenantRemoteService.isMedicineUnmannedWarehouse(orderKey.getTenantId());
        return isMedicineUW;
    }


    @Override
    @CatTransaction
    @MethodLog(logRequest = false, logResponse = true)
    public QueryAggLinkResponse queryAggOrderDetailLink(QueryAggOrderDetailLinkRequest request) {
        QueryAggLinkResponse response = new QueryAggLinkResponse();
        Optional<DeliveryOrder> deliveryOrderOptional = deliveryOrderRepository
                .getLastDeliveryOrderSlave(request.getOrderId(), request.getTenantId(), request.getPoiId());
        if (!deliveryOrderOptional.isPresent()) {
            response.setStatus(Status.from(QUERY_ORDER_FAIL));
            return response;
        }
        DeliveryOrder deliveryOrder = deliveryOrderOptional.get();
        DeliveryChannel deliveryChannel = deliveryChannelApplicationService.queryDeliveryChannelByCarrierCode(deliveryOrder.getDeliveryChannel());
        AggDeliveryPlatformEnum deliveryPlatform = AggDeliveryPlatformEnum.codeValueOf(deliveryChannel.getDeliveryPlatFormCode());
        if (deliveryPlatform == null) {
            response.setStatus(Status.from(ORDER_NOT_SUPPORT_ERROR));
            Cat.logEvent("QUERY_AGG_LINK", "noSupportPlatform");
            return response;
        }
        String url = null;
        int exceptionTypeCode = Optional.ofNullable(deliveryOrder.getExceptionType()).orElse(DeliveryExceptionTypeEnum.NO_EXCEPTION).getCode();
        boolean isException = !Objects.equals(exceptionTypeCode, DeliveryExceptionTypeEnum.NO_EXCEPTION.getCode());
        switch (deliveryPlatform) {
            case DAP_DELIVERY:
                url = deliveryOperationApplicationService.queryDapOrderDetailLink(request, deliveryOrder);
                if (StringUtils.isBlank(url)) {
                    Cat.logEvent("QUERY_AGG_LINK", "dapLinkEmpty");
                }
                break;
            case MALT_FARM:
                url = deliveryOperationApplicationService.queryMaltFarmOrderDetailLink(request, deliveryOrder);
                if (StringUtils.isBlank(url)) {
                    Cat.logEvent("QUERY_AGG_LINK", "maltLinkEmpty");
                }
                break;
            default:
                Cat.logEvent("QUERY_AGG_LINK", "noSupportPlatform");
                break;
        }
        DeliveryRedirectModule deliveryRedirectModule = deliveryPlatform.fillDeliveryRedirectModule(url, isException);
        response.setUrlText(deliveryRedirectModule.getUrlText());
        response.setTitle(deliveryRedirectModule.getTitle());
        response.setUrl(deliveryRedirectModule.getUrl());
        return response;
    }

    @Override
    @CatTransaction
    @MethodLog(logRequest = true, logResponse = true)
    public QueryAggLinkResponse queryAggStoreSettingsLink(QueryAggStoreSettingsLinkRequest request) {
        QueryAggLinkResponse response = new QueryAggLinkResponse();
        try {
            AggDeliveryPlatformEnum deliveryPlatform = AggDeliveryPlatformEnum.codeValueOf(request.getPlatformCode());
            if  (deliveryPlatform == null) {
                Cat.logEvent("QUERY_AGG_LINK", "noAggPlatform");
                return response;
            }
            String url = null;
            switch (deliveryPlatform) {
                case DAP_DELIVERY:
                    url = deliveryOperationApplicationService.queryDapStoreSettingsLink(request);
                    if (StringUtils.isBlank(url)) {
                        Cat.logEvent("QUERY_AGG_LINK", "dapLinkEmpty");
                    }
                    break;
                case MALT_FARM:
                    url = deliveryOperationApplicationService.queryMaltFarmStoreSettingsLink(request);
                    if (StringUtils.isBlank(url)) {
                        Cat.logEvent("QUERY_AGG_LINK", "maltLinkEmpty");
                    }
                    break;
                default:
                    Cat.logEvent("QUERY_AGG_LINK", "noSupportPlatform");
                    break;
            }
            response.setUrl(url);
        } catch (Exception e) {
            response.setStatus(Status.from(SYSTEM_ERROR));
            log.error("queryAggStoreSettingsLink error", e);
        }
        return response;
    }

    @Override
    @CatTransaction
    @MethodLog(logRequest = true, logResponse = true)
    public QueryAggLinkAuthResponse queryAggLinkAuthInfo(QueryAggLinkAuthRequest request) {
        QueryAggLinkAuthResponse response = new QueryAggLinkAuthResponse();
        if (StringUtils.isBlank(request.getCode())) {
            response.setStatus(Status.from(INVALID_PARAM));
            return response;
        }
        try {
            Optional<AggLinkAuthInfo> aggLinkAuthInfo = aggDeliveryLinkAuthSquirrelOperationService.get(request.getCode());
            if (!aggLinkAuthInfo.isPresent()) {
                response.setStatus(Status.from(AGG_LINK_AUTH_FAIL));
                Cat.logEvent("QUERY_AGG_LINK", "authFail");
                return response;
            }
            response.setData(aggLinkAuthInfo.get());
            aggDeliveryLinkAuthSquirrelOperationService.remove(request.getCode());
        } catch (Exception e) {
            response.setStatus(Status.from(SYSTEM_ERROR));
            Cat.logEvent("QUERY_AGG_LINK", "authError");
            log.error("queryAggLinkAuthInfo error", e);
        }
        return response;
    }
}
