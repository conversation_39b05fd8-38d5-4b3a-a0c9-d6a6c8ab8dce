package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.cache;

import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.Objects;


@SuppressWarnings("all")
@Slf4j
@Service
public class BillCurrentRiderPointRecordOperateService extends SquirrelOperateService {

    @Resource(name = "redisSgNewSupplyOfc")
    protected RedisStoreClient redisNewSupplyClient;

    @Override
    protected RedisStoreClient getRedisClient() {
        return redisNewSupplyClient;
    }

    // 6小时过期时间
    public static final int expireSeconds = 6 * 60 * 60;

    private static final String CATEGORY_NAME = "bill_rider_point_record";

    @Override
    public String getCategoryName() {
        return CATEGORY_NAME;
    }

    /**
     * 构建key
     */
    private static String buildKey(Long tenantId, Long storeId, Long deliveryId) {
        if (Objects.isNull(tenantId)
                || Objects.isNull(storeId)
                || Objects.isNull(deliveryId)) {
            return null;
        }
        return String.format("%s:%s:%s", tenantId, storeId, deliveryId);
    }

    /**
     * 批量设置
     */
    public <T> boolean addList(Long tenantId, Long storeId, Long deliveryId, Collection<T> data, int expireInSeconds) {
        return super.addList(buildKey(tenantId, storeId, deliveryId), data, expireInSeconds);
    }

    /**
     * 获取列表并删除
     */
    public <T> List<T> getAllListAndRemove(Long tenantId, Long storeId, Long deliveryId, Class<T> clazz) {
        return super.getAllListAndRemove(buildKey(tenantId, storeId, deliveryId), clazz);
    }

}
