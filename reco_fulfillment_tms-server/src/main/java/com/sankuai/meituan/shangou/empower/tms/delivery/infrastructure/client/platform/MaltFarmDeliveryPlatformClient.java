package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.client.platform;

import com.dianping.cat.Cat;
import com.dianping.rhino.annotation.Degrade;
import com.sankuai.meituan.shangou.dms.base.model.value.Address;
import com.sankuai.meituan.shangou.dms.base.model.value.CoordinatePoint;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.DeliveryResultCodeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.delivery.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.MaltChannelAggDeliveryThriftService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrder;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.warn.event.AggDeliveryCancelHandleFailedEvent;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.log.changeinfo.DeliveryExceptionInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.CoordinateUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.exception.DeliveryPlatformException;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.tenant.TenantStoreInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.maltfarm.MaltFarmDeliveryPoi;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.RiderLocationDetail;
import com.sankuai.meituan.shangou.empower.tms.delivery.utils.RetryTemplateUtil;
import com.sankuai.meituan.shangou.saas.common.cat.CatTransaction;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.retry.RetryCallback;
import org.springframework.retry.RetryContext;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigInteger;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryExceptionCodeEnum.SYNC_CANCEL_FAIL;
import static com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryExceptionTypeEnum.LAUNCH_DELIVERY_FAILED_BY_SYSTEM_EXCEPTION;
import static com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum.MALT_FARM_DELIVERY_PLATFORM;
import static com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.FailureCodeEnum.PLATFORM_AUTO_SEND_EXCEPTION;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/7/12
 */
@Slf4j
@Component
public class MaltFarmDeliveryPlatformClient extends AbstractAggDeliveryPlatformClient {

	// 取消原因code
	public static final int CANCEL_REASON_CODE = 2;

	@Resource
	protected MaltChannelAggDeliveryThriftService maltChannelAggDeliveryThriftService;

	protected IChannelAggDeliveryPlatform iChannelAggDeliveryPlatform = new IChannelAggDeliveryPlatform() {
		@Override
		public CreateAggDeliveryResponse createDelivery(CreateAggDeliveryRequest request) throws TException {
			return maltChannelAggDeliveryThriftService.createDelivery(request);
		}

		@Override
		public QueryAggDeliveryRiderInfoResponse queryRiderLocation(QueryAggDeliveryRiderInfoRequest request) throws TException {
			return maltChannelAggDeliveryThriftService.queryRiderLocation(request);
		}

		@Override
		public CreateAggDeliveryShopResponse createAggDeliveryShop(CreateAggDeliveryShopRequest request) throws TException {
			return maltChannelAggDeliveryThriftService.createAggDeliveryShop(request);
		}

		@Override
		public SyncOrderInfoChangeResponse syncOrderInfoChange(SyncOrderInfoChangeRequest request) throws TException {
			return maltChannelAggDeliveryThriftService.syncOrderInfoChange(request);
		}
	};

	@Override
	public DeliveryPlatformEnum getDeliveryPlatform() {
		return MALT_FARM_DELIVERY_PLATFORM;
	}

	@Override
	protected int getCancelReasonCode() {
		return CANCEL_REASON_CODE;
	}

	@Override
	protected IChannelAggDeliveryPlatform getChannelAggDeliveryThriftService() {
		return iChannelAggDeliveryPlatform;
	}

	@Override
	@CatTransaction
	// @MethodLog(logRequest = false, logResponse = true)
	public Optional<Failure> cancelDelivery(DeliveryOrder deliveryOrder) {
		log.info("MaltFarmDeliveryPlatformClient.cancelDelivery deliveryOrderPrimaryId:{}", deliveryOrder.getId());
		if (deliveryOrder.getDeliveryChannel() == DeliveryChannelEnum.FARM_DELIVERY_MERCHANT.getCode()) {
			Optional<Failure> failure = cancelMaltSelfDelivery(deliveryOrder);
			if (failure.isPresent()) {
				deliveryOrder.onExceptionWithChangeTime(
						new DeliveryExceptionInfo(LAUNCH_DELIVERY_FAILED_BY_SYSTEM_EXCEPTION, failure.get().getFailureMessage(), SYNC_CANCEL_FAIL.getCode()),
						LocalDateTime.now()
				);
				deliveryOrderRepository.save(deliveryOrder);
				pushClient.pushDeliveryException(deliveryOrder);
			}
			return failure;
		}
		return super.cancelDelivery(deliveryOrder);
	}

	/**
	 * 麦芽田取消自己送.
	 *
	 * @param deliveryOrder
	 */
	private Optional<Failure> cancelMaltSelfDelivery(DeliveryOrder deliveryOrder) {
		deliveryOrder.onChange(DeliveryEventEnum.DELIVERY_CANCEL, DeliveryExceptionInfo.NO_EXCEPTION, deliveryOrder.getRiderInfo(),
				LocalDateTime.now());
		return Optional.empty();
	}


	@Override
	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	@Degrade(rhinoKey = "MaltFarmPlatformClient-autoSend", fallBackMethod = "autoSendFallback", timeoutInMilliseconds = 2000)
	public Optional<Failure> autoSend(DeliveryOrder deliveryOrder){
		AutoSendAggDeliveryRequest request=new AutoSendAggDeliveryRequest();
		request.setAggDeliveryPlatformId(getDeliveryPlatform().getCode());
		request.setAppInfo(getAppInfo());
		request.setOrderId(deliveryOrder.getOrderId());
		try {
			AutoSendAggDeliveryResponse response = maltChannelAggDeliveryThriftService.autoSendAggDelivery(request);
			if (response == null || response.getCode() == null ) {
				log.error("MaltFarmPlatformClient autoSend error request:{},orderId:{},response:{}", request, deliveryOrder.getOrderId(), response);
				Cat.logEvent("MALT_FARM_PLATFORM", "AUTO_SEND_FAIL");
				return Optional.of(new Failure(false,PLATFORM_AUTO_SEND_EXCEPTION.getCode(),PLATFORM_AUTO_SEND_EXCEPTION.getMessage()));
			}
			if(response.getCode() != BigInteger.ZERO.intValue()){
				return Optional.of(new Failure(false,PLATFORM_AUTO_SEND_EXCEPTION.getCode(),response.getMsg()));
			}
			return Optional.empty();
		} catch (Exception e) {
			log.error("MaltFarmPlatformClient autoSend error request:{},orderId:{}", request, deliveryOrder.getOrderId(),e);
			return Optional.of(new Failure(false,PLATFORM_AUTO_SEND_EXCEPTION.getCode(),PLATFORM_AUTO_SEND_EXCEPTION.getMessage()));
		}
	}

	public Optional<Failure> autoSendFallback(DeliveryOrder deliveryOrder) {
		log.error("autoSendFallback : orderId:{}", deliveryOrder.getOrderId());
		return Optional.of(new Failure(false,PLATFORM_AUTO_SEND_EXCEPTION.getCode(),PLATFORM_AUTO_SEND_EXCEPTION.getMessage()));
	}

	@Override
	@CatTransaction
	// @MethodLog(logRequest = false, logResponse = true)
	protected Optional<Failure> doCancelDelivery(DeliveryOrder deliveryOrder) {
		log.info("MaltFarmDeliveryPlatformClient.doCancelDelivery deliveryOrderPrimaryId:{}", deliveryOrder.getId());
		try {
			CancelAggDeliveryRequest cancelAggDeliveryRequest = new CancelAggDeliveryRequest(
					deliveryOrder.getOrderId(), getDeliveryPlatform().getCode(), "订单取消", getCancelReasonCode(), getAppInfo(),
					deliveryOrder.getDeliveryOrderId()
			);

			log.info("ChannelAggDeliveryThriftService.cancelAggDelivery begin, request={}", cancelAggDeliveryRequest);
			CancelAggDeliveryResponse response = maltChannelAggDeliveryThriftService.cancelDelivery(cancelAggDeliveryRequest);
			log.info("ChannelAggDeliveryThriftService.cancelAggDelivery finish, result={}", response);
			if (response == null || response.getCode() != BigInteger.ZERO.intValue()) {
				warnEventPublisher.postEvent(new AggDeliveryCancelHandleFailedEvent(getDeliveryPlatform(),
						deliveryOrder.getOrderKey(), Optional.ofNullable(response).map(CancelAggDeliveryResponse::getMsg).orElse("取消运单失败")));
				if(response != null && response.getCode() == DeliveryResultCodeEnum.MALT_FALLBACK_ERROR.getCode()){
					return Optional.of(new Failure(false, FailureCodeEnum.CANCEL_DELIVERY_FAILED.getCode(),
							Optional.ofNullable(response).map(CancelAggDeliveryResponse::getMsg).orElse("调用聚合运力平台取消配送异常")));
				}
				return Optional.of(new Failure(true, FailureCodeEnum.CANCEL_DELIVERY_FAILED.getCode(),
						Optional.ofNullable(response).map(CancelAggDeliveryResponse::getMsg).orElse("调用聚合运力平台取消配送异常")));
			}
		} catch (Exception e) {
			warnEventPublisher.postEvent(new AggDeliveryCancelHandleFailedEvent(getDeliveryPlatform(),
					deliveryOrder.getOrderKey(), "取消运单失败"));
			log.info("ChannelDeliveryThriftService.cancelAggDelivery exception", e);
			return Optional.of(new Failure(true, FailureCodeEnum.CANCEL_DELIVERY_FAILED.getCode(),
					"调用聚合运力平台取消配送异常"));
		}
		return Optional.empty();
	}

	/**
	 * 转自配送后取消原有的配送单
	 * @param deliveryOrder
	 * @return
	 */
	@Override
	@CatTransaction
	// @MethodLog(logRequest = false, logResponse = true)
	protected Optional<Failure> doCancelDeliveryForTransOrder(DeliveryOrder deliveryOrder) {
		return doCancelDelivery(deliveryOrder);
	}

	@Override
	public Optional<Failure> cancelDeliveryForOFC(DeliveryOrder deliveryOrder) {
		try {
			CancelAggDeliveryRequest cancelAggDeliveryRequest = new CancelAggDeliveryRequest(
					deliveryOrder.getOrderId(), getDeliveryPlatform().getCode(), "订单取消", getCancelReasonCode(), getAppInfo(),
					deliveryOrder.getDeliveryOrderId()
			);

			log.info("ChannelAggDeliveryThriftService.cancelDeliveryForOFC begin, request={}", cancelAggDeliveryRequest);
			CancelAggDeliveryResponse response = maltChannelAggDeliveryThriftService.cancelDeliveryChannel(cancelAggDeliveryRequest);
			log.info("ChannelAggDeliveryThriftService.cancelDeliveryForOFC finish, result={}", response);
			if (response == null || response.getCode() != BigInteger.ZERO.intValue()) {
				warnEventPublisher.postEvent(new AggDeliveryCancelHandleFailedEvent(getDeliveryPlatform(),
						deliveryOrder.getOrderKey(), Optional.ofNullable(response).map(CancelAggDeliveryResponse::getMsg).orElse("取消运单失败")));
				if(response != null && response.getCode() == DeliveryResultCodeEnum.MALT_FALLBACK_ERROR.getCode()){
					return Optional.of(new Failure(false, FailureCodeEnum.CANCEL_DELIVERY_FAILED.getCode(),
							Optional.ofNullable(response).map(CancelAggDeliveryResponse::getMsg).orElse("调用聚合运力平台取消配送异常")));
				}
				return Optional.of(new Failure(true, FailureCodeEnum.CANCEL_DELIVERY_FAILED.getCode(),
						Optional.ofNullable(response).map(CancelAggDeliveryResponse::getMsg).orElse("调用聚合运力平台取消配送异常")));
			}
		} catch (Exception e) {
			warnEventPublisher.postEvent(new AggDeliveryCancelHandleFailedEvent(getDeliveryPlatform(),
					deliveryOrder.getOrderKey(), "取消运单失败"));
			log.info("ChannelDeliveryThriftService.cancelAggDelivery exception", e);
			return Optional.of(new Failure(true, FailureCodeEnum.CANCEL_DELIVERY_FAILED.getCode(),
					"调用聚合运力平台取消配送异常"));
		}
		return Optional.empty();
	}

	@Override
	@CatTransaction
	protected Result<Address> getStoreAddress(DeliveryPoi deliveryPoi, TenantStoreInfo tenantStoreInfo) {
		MaltFarmDeliveryPoi maltFarmDeliveryPoi = (MaltFarmDeliveryPoi) deliveryPoi;
		Address storeAddress = maltFarmDeliveryPoi.getStoreAddress();
		if (storeAddress == null) {
			Result<CoordinatePoint> coordinateQueryResult = mapClient.queryCoordinatesByDetailAddress(tenantStoreInfo.getAddress());
			if (coordinateQueryResult.isFail()) {
				log.warn("查询门店坐标失败, tenantId:{}, storeId:{}, address:{}, failure:{}",
						deliveryPoi.getTenantId(), deliveryPoi.getStoreId(), tenantStoreInfo.getAddress(), coordinateQueryResult.getFailure()
				);
				Cat.logEvent("CREATE_DELIVERY_SHOP_FAIL", "GLORY_STORE_COORDINATES_QUERY_FAILED");
				return new Result<>(new Failure(true, FailureCodeEnum.OTHER_SYSTEM_CALL_FAILED, "查询门店经纬度失败"));
			}

			storeAddress = new Address(tenantStoreInfo.getAddress(), CoordinateTypeEnum.MARS, coordinateQueryResult.getInfo());
			maltFarmDeliveryPoi.setStoreAddress(storeAddress);
			deliveryPoiRepository.saveDeliveryPoi(deliveryPoi);
		}

		return new Result<>(storeAddress);
	}

	@Override
	@MethodLog(logRequest = true, logResponse = true)
	@CatTransaction
	public Optional<Failure> syncPaoTuiDeliveryStatusAfterLockOrder(DeliveryOrder deliveryOrder, String channelServicePackageId) {
		try {
			//0：等待分配骑手
			SyncPaoTuiDeliveryStatusAfterLockOrderRequest request = new SyncPaoTuiDeliveryStatusAfterLockOrderRequest(
					deliveryOrder.getOrderId(), channelServicePackageId, getDeliveryPlatform().getCode(), 0);
			RetryTemplate retryTemplate= RetryTemplateUtil.simpleWithFixedRetry(2,100);
			SyncPaoTuiDeliveryStatusAfterLockOrderResponse response = retryTemplate.execute(new RetryCallback<SyncPaoTuiDeliveryStatusAfterLockOrderResponse, Throwable>() {
				@Override
				public SyncPaoTuiDeliveryStatusAfterLockOrderResponse doWithRetry(RetryContext retryContext) throws Throwable {
					SyncPaoTuiDeliveryStatusAfterLockOrderResponse response = maltChannelAggDeliveryThriftService.syncPaoTuiDeliveryStatusAfterLockOrder(request);
					if(response ==null || response.getCode()!=FailureCodeEnum.SUCCESS.getCode()){
						if(response != null && response.getCode() == DeliveryResultCodeEnum.MALT_FALLBACK_ERROR.getCode()){
							return response;
						}
						throw new DeliveryPlatformException(FailureCodeEnum.SYNC_PAO_TUI_DELIVERY_STATUS_FAILED);
					}
					return response;
				}
			});
			if (response == null || response.getCode() == null || response.getCode() != DeliveryResultCodeEnum.SUCCESS.getCode()) {
				return Optional.of(new Failure(false, FailureCodeEnum.SYNC_PAO_TUI_DELIVERY_STATUS_FAILED));
			}
			return Optional.empty();
		} catch (Throwable e) {
			log.error("syncWaitingToAssignRiderAfterLockOrder error", e);
			return Optional.of(new Failure(false, FailureCodeEnum.SYNC_PAO_TUI_DELIVERY_STATUS_FAILED));
		}
	}

	@Override
	public Optional<Failure> notifyDeliveryPlatformLockStatusChange(DeliveryPoi deliveryPoi, OrderInfo orderInfo, DeliveryOrder deliveryOrder) {
		try {
			SyncLockStatusChangeRequest request = new SyncLockStatusChangeRequest();
			request.setAppInfo(getAppInfo());
			request.setOrderId(deliveryOrder.getOrderId());
			request.setAggDeliveryPlatformId(getDeliveryPlatform().getCode());
			boolean orderLockLabel = false;
			if(orderInfo.getOrderLockLabel()!=null){
				orderLockLabel = orderInfo.getOrderLockLabel();
			}
			boolean isLocked = false;
			if(orderInfo.getIsLocked()!=null){
				isLocked = orderInfo.getIsLocked();
			}
			request.setDeliveryAvailable(orderLockLabel && isLocked ? DeliveryAvailableEnum.NO.getValue() : DeliveryAvailableEnum.YES.getValue());
			RetryTemplate retryTemplate= RetryTemplateUtil.simpleWithFixedRetry(2,100);
			SyncLockStatusChangeResponse response = retryTemplate.execute(new RetryCallback<SyncLockStatusChangeResponse, Throwable>() {
				@Override
				public SyncLockStatusChangeResponse doWithRetry(RetryContext retryContext) throws Throwable {
					SyncLockStatusChangeResponse response = maltChannelAggDeliveryThriftService.syncLockStatusChange(request);
					if(response ==null || response.getCode()!=FailureCodeEnum.SUCCESS.getCode()){
						if(response != null && response.getCode() == DeliveryResultCodeEnum.MALT_FALLBACK_ERROR.getCode()){
							return response;
						}
						throw new DeliveryPlatformException(FailureCodeEnum.SYNC_PAO_TUI_LOCK_STATUS_FAILED);
					}
					return response;
				}
			});
			if (response == null || response.getCode() == null || response.getCode() != DeliveryResultCodeEnum.SUCCESS.getCode()) {
				return Optional.of(new Failure(false, FailureCodeEnum.SYNC_PAO_TUI_LOCK_STATUS_FAILED));
			}
			return Optional.empty();
		} catch (Throwable e) {
			log.error("notifyDeliveryPlatformLockStatusChange error", e);
			return Optional.of(new Failure(false, FailureCodeEnum.SYNC_PAO_TUI_LOCK_STATUS_FAILED));
		}
	}

	@Override
	public Optional<Failure> syncTenantPoi(DeliveryTenantPoiSyncRequest request) {
		throw new UnsupportedOperationException("租户门店暂不支持同步麦芽田");
	}


	/**
	 * 麦芽田查询骑手轨迹
	 */
	public List<RiderLocationDetail> queryRiderPointTrace(Long deliveryOrderId, int type, Long startTime, Long endTime) {
		try {
			FarmRiderPointReq farmRiderPointReq = new FarmRiderPointReq();
			farmRiderPointReq.setDeliveryOrderId(deliveryOrderId.toString());
			farmRiderPointReq.setType(type);
			farmRiderPointReq.setStartTime(startTime);
			farmRiderPointReq.setEndTime(endTime);
			farmRiderPointReq.setAppInfo(getAppInfo());
			FarmRiderPointResp riderPoint = maltChannelAggDeliveryThriftService.getRiderPoint(farmRiderPointReq);
			if (Objects.isNull(riderPoint) || CollectionUtils.isEmpty(riderPoint.getList())) {
				log.error("maltChannelAggDeliveryThriftService.getRiderPoint is null");
				return Collections.emptyList();
			}
			return riderPoint.getList()
					.stream()
					.map(point -> {
						RiderLocationDetail riderLocationDetail = new RiderLocationDetail();
						riderLocationDetail.setRiderName(riderPoint.getRiderName());
						riderLocationDetail.setRiderPhone(riderPoint.getRiderPhone());
						if (StringUtils.isNotBlank(point.getGdLng()) && StringUtils.isNotBlank(point.getGdLat())) {
							riderLocationDetail.setLatitude(point.getGdLat());
							riderLocationDetail.setLongitude(point.getGdLng());
						} else {
							try {
								CoordinatePoint coordinatePoint = CoordinateUtil.translateFromBaiduToMars(new CoordinatePoint(point.getBdLng(), point.getBdLat()));
								riderLocationDetail.setLatitude(coordinatePoint.getLatitude());
								riderLocationDetail.setLongitude(coordinatePoint.getLongitude());
							} catch (Exception e) {
								log.error("CoordinateUtil.translateFromBaiduToMars error, point = {}", point, e);
								return null;
							}
						}

						riderLocationDetail.setTime(point.getTime());
						return riderLocationDetail;
					})
					.filter(Objects::nonNull)
					.collect(Collectors.toList());
		} catch (Exception e) {
			log.error("queryRiderPointTrace error, deliveryOrderId = {}, type = {}, startTime = {}, endTime = {}", deliveryOrderId, type, startTime, endTime, e);
		}
		return Collections.emptyList();
	}
}
