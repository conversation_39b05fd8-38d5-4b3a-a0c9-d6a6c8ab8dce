package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.client.push;

import lombok.Getter;
import lombok.ToString;

import java.text.MessageFormat;

/**
 * 配送异常推送消息体
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/11
 */
@Getter
@ToString
public class DeliveryExceptionPushMessage {

	private final Long orderId;
	private final Long tenantId;
	private final Long poiId;
	private final Integer appId;
	private final String authCode;
	private final String title;
	private final String content;
	private final String jumpUrl;
	private final String soundFileName;
	private final String sharkPushContent;

	public DeliveryExceptionPushMessage(Long tenantId, Long storeId, Long orderId, AppEnum app) {
		this.tenantId = tenantId;
		this.poiId = storeId;
		this.orderId = orderId;
		this.appId = app.getAppId();
		this.authCode = app.getAuthCode();
		this.jumpUrl = app.getJumpUrl(tenantId, storeId);
		this.title = "配送异常";
		this.content = "您有新的配送异常，请尽快处理！";
		this.soundFileName = "deliver_alert";
		this.sharkPushContent = "{\"operation\":\"audio\",\"serverTS\":" + System.currentTimeMillis() + ",\"biz\":\"pickselect\",\"event\":\"pickselect\",\"fileName\":\"deliver_alert\",\"priority\":1,\"frequency\":1,\"interval\":2000}";
	}

	public enum AppEnum {
		/**
		 * 履约助手
		 */
		LV_YUE_ZHU_SHOU(
				1,
				"ocms_delivery_exception",
				"outside/push?target=pickselect%3a%2f%2fmrn&mrn_biz=supermarket&mrn_entry=ocmsDelivery&mrn_component=ocmsDelivery&tenantId={0}&storeId={1}"
		),

		/**
		 * 牵牛花
		 */
		SHU_GUO_PAI(
				5,
				"ocms_delivery_exception",
				"outside/push?target=shuguopai%3a%2f%2fmrn&mrn_biz=supermarket&mrn_entry=ocmsDelivery&mrn_component=ocmsDelivery&tenantId={0}&storeId={1}"
		),

		/**
		 * 新版本牵牛花
		 */
		NEW_SHU_GUO_PAI(
				5,
				"DELIVERY_ABNORMAL",
				"outside/push?target=shuguopai%3a%2f%2fmrn&mrn_biz=supermarket&mrn_entry=ocmsDelivery&mrn_component=ocmsDelivery&tenantId={0}&storeId={1}"
		),

		/**
		 * 牵牛花-麦芽田异常配送通知
		 */
		SHU_GUO_PAI_MALT_FARM(
				5,
				"ORDER_DELIVERY_ERROR",
				"outside/push?target=shuguopai%3a%2f%2fmrn&mrn_biz=supermarket&mrn_entry=ocmsDelivery&mrn_component=ocmsDelivery&tenantId={0}&storeId={1}"
		),;

		private final int appId;
		private final String authCode;
		private final String jumpUrlTemplate;

		AppEnum(int appId, String authCode, String jumpUrlTemplate) {
			this.appId = appId;
			this.authCode = authCode;
			this.jumpUrlTemplate = jumpUrlTemplate;
		}

		public int getAppId() {
			return appId;
		}

		public String getAuthCode() {
			return authCode;
		}

		public String getJumpUrl(Long tenantId, Long storeId) {
			return MessageFormat.format(jumpUrlTemplate, tenantId, storeId);
		}
	}
}
