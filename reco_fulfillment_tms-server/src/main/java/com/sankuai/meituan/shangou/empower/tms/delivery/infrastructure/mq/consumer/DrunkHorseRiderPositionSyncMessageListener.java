package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.consumer;

import com.dianping.cat.Cat;
import com.google.common.base.Preconditions;
import com.meituan.inf.xmdlog.XMDLogFormat;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.sankuai.meituan.shangou.dms.base.model.value.DeliveryChannel;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;

import com.meituan.shangou.saas.tenant.thrift.common.enums.ChannelOnlineTypeEnum;
import com.sankuai.meituan.shangou.dms.base.model.value.CoordinatePoint;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.DeliveryChannelApplicationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.channel.ChannelDeliveryService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.channel.OcmsChannelClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.platform.DeliveryPlatformClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryChannelEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.RiderLocationDataDO;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.cache.MqMsgIdempotentOperateService;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.message.SyncRiderPositionMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.repository.SquirrelOrderLastRiderLocationRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.repository.SquirrelRiderLocationRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoi;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoiRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.utils.LionConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.enums.RiderLocatingExceptionEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.utils.CoordinateUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.utils.DeliveryRiderMccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.utils.TimeUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.utils.CatLogEventUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.utils.MccConfigUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.Optional;

import static com.meituan.mafka.client.consumer.ConsumeStatus.CONSUME_FAILURE;
import static com.meituan.mafka.client.consumer.ConsumeStatus.CONSUME_SUCCESS;
import static com.sankuai.meituan.shangou.empower.tms.delivery.rider.utils.CatLogEventReportUtils.reportSelfDeliveryRiderLocationSyncLogEvent;

/**
 * 骑手位置同步
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
@Slf4j
@Component
@SuppressWarnings("rawtypes")
public class DrunkHorseRiderPositionSyncMessageListener extends AbstractDeadLetterConsumer {
    private static final int EXPIRE_TIME = 3;
    public static final String DELIVERY = "delivery";
    public static final String RIDER_POSITION = "rider.position";

    @Resource
    private DeliveryOrderRepository deliveryOrderRepository;
    @Resource
    private DeliveryPlatformClient deliveryPlatformClient;
    @Resource
    private DeliveryChangeNotifyService deliveryChangeNotifyService;
    @Resource
    private OcmsChannelClient ocmsChannelClient;
    @Resource
    private ChannelDeliveryService channelDeliveryService;
    @Resource
    private DeliveryPoiRepository deliveryPoiRepository;
    @Resource
    private RiderLocatingExceptionRepository riderLocatingExceptionRepository;

    @Resource
    private DeliveryChannelApplicationService deliveryChannelApplicationService;
    @Resource
    private SquirrelRiderLocationRepository squirrelRiderLocationRepository;

    @Resource
    private MqMsgIdempotentOperateService mqMsgIdempotentOperateService;

    @Resource(name = "squirrelRiderLocationAggDeliveryRepository")
    private SquirrelNewSupplyRiderLocationRepository squirrelRiderLocationAggDeliveryRepository;

    @Resource(name = "squirrelRiderLocationSelfMerchantDeliveryRepository")
    private SquirrelNewSupplyRiderLocationRepository squirrelRiderLocationSelfMerchantDeliveryRepository;

    @Resource
    private SquirrelOrderLastRiderLocationRepository squirrelOrderLastRiderLocationRepository;

    private static final Logger riderLocationSyncLog = LoggerFactory.getLogger("logger_drunk.horse.rider.location.sync.log");

    @Override
    protected MQConsumerEnum consumerConfig() {
        return MQConsumerEnum.DRUNK_HORSE_RIDER_LOCATION_SYNC_CONSUMER;
    }

    /**
     * 同步骑手位置，失败则等待下一次自动同步
     *
     * @param mafkaMessage
     * @return
     */
    @Override
    protected ConsumeStatus consume(MafkaMessage mafkaMessage) {
        log.info("开始消费同步歪马骑手位置消息: {}", mafkaMessage);
        SyncRiderPositionMessage message = translateMessage(mafkaMessage);
        if (message == null) {
            return CONSUME_SUCCESS;
        }
        // TODO 2025-07-31:幂等处理
        try {
            boolean lockStatus = mqMsgIdempotentOperateService.tryLock(mafkaMessage.getMessageID(), MqMsgIdempotentOperateService.MsgIdempotentBusinessEnum.DRUNK_HORSE_RIDER_POSITION_SYNC);
            if (!lockStatus) {
                log.info("DrunkHorseRiderPositionSyncMessageListener 消息幂等加锁失败 msgId:{}", mafkaMessage.getMessageID());
                return CONSUME_SUCCESS;
            }
            DeliveryOrder deliveryOrder = null;
            if(com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils.getDeliveryQueryTenantSwitch(message.getTenantId())){
                deliveryOrder = deliveryOrderRepository.getDeliveryOrderWithTenant(message.getDeliveryId(),message.getTenantId(),message.getStoreId());
            }else {
                deliveryOrder = deliveryOrderRepository.getDeliveryOrder(message.getDeliveryId());
            }
            DynamicOrderBizType dynamicOrderBizType = DynamicOrderBizType.findOf(deliveryOrder.getOrderBizType());
            //pos渠道判断过滤
            if(Objects.nonNull(dynamicOrderBizType)
                    && ChannelOnlineTypeEnum.isOfflinePos(dynamicOrderBizType.getChannelStandard())){
                return CONSUME_SUCCESS;
            }

            //过滤非灰度门店和歪马门店
            if (!MccConfigUtils.isSwitchRiderPositionSyncGrayStore(deliveryOrder.getStoreId())
                    || !MccConfigUtils.checkIsDHTenant(deliveryOrder.getTenantId())) {
                return CONSUME_SUCCESS;
            }

            //上报同步灰度门店 && 自配单
            if (LionConfigUtils.isLocationUpstreamGrayStore(deliveryOrder.getStoreId()) && Objects.equals(deliveryOrder.getDeliveryChannel(), DeliveryChannelEnum.MERCHANT_DELIVERY.getCode())){
                //非直接同步直接返回成功
                if (!message.isFeDirectSyncRiderPosition()) {
                    return CONSUME_SUCCESS;
                }
            }


            DeliveryChannel deliveryChannelDto = deliveryChannelApplicationService.queryDeliveryChannelByCarrierCode(deliveryOrder.getDeliveryChannel());
            if (ignoreSyncDelivery(deliveryOrder, deliveryChannelDto)) {
                log.info("deliveryOrder[{}] ignoreSyncDelivery ", message.getDeliveryId());
                return CONSUME_SUCCESS;
            }

            Optional<DeliveryPoi> opDeliveryPoi = Optional.empty();

			opDeliveryPoi = deliveryPoiRepository.queryDeliveryPoi(deliveryOrder.getTenantId(), deliveryOrder.getStoreId());
			//歪马的门店在内存里置为和订单一样的配送平台
			opDeliveryPoi.ifPresent(deliveryPoi -> deliveryPoi.setDeliveryPlatform(DeliveryPlatformEnum.enumOf(deliveryChannelDto.getDeliveryPlatFormCode())));


            if (!opDeliveryPoi.isPresent()) {
                log.error("query stores empty {}", message.getDeliveryId());
                return CONSUME_SUCCESS;
            }

            if (opDeliveryPoi.get().getDeliveryPlatform() == DeliveryPlatformEnum.MERCHANT_SELF_DELIVERY
                    && (!MccConfigUtils.checkRiderLocationSyncGreySwitch(opDeliveryPoi.get().getStoreId()))) {
                log.info("MERCHANT_SELF_DELIVERY store not in grey {}", message.getDeliveryId());
                return CONSUME_SUCCESS;
            }

            Optional<CoordinatePoint> point;
            switch (opDeliveryPoi.get().getDeliveryPlatform()) {
                case DAP_DELIVERY_PLATFORM:
                    point = deliveryPlatformClient.queryRiderLocation(deliveryOrder, deliveryChannelDto, opDeliveryPoi.get());
                    CatLogEventUtils.reportAggDeliveryRiderLocationSyncLogEvent(opDeliveryPoi.get().getDeliveryPlatform(), point.isPresent());
                    break;
                case MERCHANT_SELF_DELIVERY:
                    point = deliveryPlatformClient.queryRiderLocation(deliveryOrder, opDeliveryPoi.get());
                    reportMerchantSelfDeliveryRiderLocationSyncLog(deliveryOrder, point);
                    reportSelfDeliveryRiderLocationSyncLogEvent(deliveryOrder.getTenantId(), deliveryOrder.getDeliveryChannel(), point);
                    break;
                default:
                    log.warn("当前平台[{}]暂不支持同步骑手轨迹，将放弃消费", opDeliveryPoi.get().getDeliveryPlatform());
                    return CONSUME_SUCCESS;
            }

            if (point.isPresent()) {
                    if (Objects.nonNull(deliveryOrder.getRiderInfo()) && StringUtils.isNotBlank(deliveryOrder.getRiderInfo().getRiderName()) && StringUtils.isNotBlank(deliveryOrder.getRiderInfo().getRiderPhone())) {
                        //歪马的三方配送需要写redis
                        squirrelRiderLocationRepository.saveThirdLocation(
                                new ThirdRiderPoint(new ThirdRiderKey(deliveryOrder.getRiderInfo().getRiderName(), deliveryOrder.getRiderInfo().getRiderPhone()), point.get(),
                                        TimeUtil.toMilliSeconds(LocalDateTime.now())
                                )
                        );
                    }
                ocmsChannelClient.syncDrunkHorseRiderLocation(deliveryOrder, point.get());
            } else {
                log.warn("no position，忽略 {}", message.getDeliveryId());
            }
            //触发下一次同步位置
            triggerDrunkHorseNextNotify(deliveryOrder.getId(), opDeliveryPoi.get().getDeliveryPlatform(), opDeliveryPoi.get().getTenantId(), opDeliveryPoi.get().getStoreId(), point);
            Cat.logEvent(DELIVERY, RIDER_POSITION);
        } catch (Exception e) {
            log.error("开始消费同步骑手位置消息，将会进行重试消费", e);
            Cat.logEvent(DELIVERY, RIDER_POSITION, "1", "");
            //解锁
            mqMsgIdempotentOperateService.unLock(mafkaMessage.getMessageID(), MqMsgIdempotentOperateService.MsgIdempotentBusinessEnum.DRUNK_HORSE_RIDER_POSITION_SYNC);
            return CONSUME_FAILURE;
        }

        return CONSUME_SUCCESS;
    }


    private SyncRiderPositionMessage translateMessage(MafkaMessage mafkaMessage) {
        try {
            SyncRiderPositionMessage message = translateMessage(mafkaMessage, SyncRiderPositionMessage.class);
            Preconditions.checkNotNull(message, "empty message");
            Preconditions.checkNotNull(message.getDeliveryId(), "orderId is null");
            return message;

        } catch (Exception e) {
            log.error("RIDER_LOCATION_SYNC_CONSUMER_FAILED:{}", mafkaMessage, e);
            Cat.logEvent("RIDER_LOCATION_SYNC_CONSUMER_FAILED", "MESSAGE_WRONG");
            return null;
        }
    }

    private boolean ignoreSyncDelivery(DeliveryOrder deliveryOrder, DeliveryChannel deliveryChannel) {
        Optional<DeliveryStatusEnum> syncRiderPositionPoint = deliveryOrder.getSyncRiderPositionPoint(deliveryChannel);
        if (!syncRiderPositionPoint.isPresent()) {
            return true;
        }

        DeliveryStatusEnum status = deliveryOrder.getStatus();
        boolean betweenSyncPeriod = status.getCode() >= syncRiderPositionPoint.get().getCode() &&
                status.getCode() <= DeliveryStatusEnum.RIDER_TAKEN_GOODS.getCode();
        return !betweenSyncPeriod || LocalDateTime.now().isAfter(deliveryOrder.getLastEventTime().plusHours(EXPIRE_TIME));

    }

    private void triggerDrunkHorseNextNotify(Long deliveryId, DeliveryPlatformEnum platformEnum, Long tenantId, Long storeId, Optional<CoordinatePoint> point) {
        log.info("triggerNextNotify platformEnum:{},point :{}", platformEnum, point);
        //新版本同步位置延迟消息
        //聚合配送统一使用1分钟，商家自配送新版本app也是用1分钟，其他情况走老逻辑
        if (!Objects.equals(platformEnum, DeliveryPlatformEnum.MERCHANT_SELF_DELIVERY)
                || (
                Objects.equals(platformEnum, DeliveryPlatformEnum.MERCHANT_SELF_DELIVERY)
                        && point.isPresent()
                        && Objects.equals(point.get().getAppVersionLevel(), 1))
        ) {
            deliveryChangeNotifyService.notifySyncRiderPositionV2(platformEnum, deliveryId, tenantId, storeId);
            return;
        }

        deliveryChangeNotifyService.notifySyncDrunkHorseRiderPosition(deliveryId, platformEnum,tenantId,storeId, false);
    }

    /**
     * 采集自营骑手位置同步到C端的日志，上报到kafka
     *
     * @param deliveryOrder
     * @param point
     */
    private void reportMerchantSelfDeliveryRiderLocationSyncLog(DeliveryOrder deliveryOrder, Optional<CoordinatePoint> point) {

        try {
            //过滤非歪马
            if (!MccConfigUtils.checkIsDHTenant(deliveryOrder.getTenantId())) {
                return;
            }

            //过滤非自配运单
            if (!Objects.equals(deliveryOrder.getDeliveryChannel(), DeliveryChannelEnum.MERCHANT_DELIVERY.getCode())) {
                return;
            }

            boolean getLocationSuccess = point.isPresent();
            if (Objects.isNull(deliveryOrder.getRiderInfo()) || !(deliveryOrder.getRiderInfo() instanceof StaffRider)) {
                return;
            }
            StaffRider rider = (StaffRider) deliveryOrder.getRiderInfo();
            Long riderAccountId = null;
            riderAccountId = rider.getRiderAccountId();

            Long orderId = deliveryOrder.getOrderId();
            String phoneManufacturer = "";
            Integer locatingExceptionType = null;
            String os = "";
            String uuid = "";
            String exceptionHappenTime = "";
            String appVersion = "";

            if (!getLocationSuccess) {
                Optional<RiderLocatingExceptionDetail> locatingExceptionOpt = riderLocatingExceptionRepository.getRiderLocatingException(riderAccountId);
                if (locatingExceptionOpt.isPresent()) {
                    RiderLocatingExceptionDetail locatingExceptionDetail = locatingExceptionOpt.get();
                    locatingExceptionType = locatingExceptionDetail.getRiderLocatingExceptionEnum().getValue();
                    phoneManufacturer = locatingExceptionDetail.getManufacturer();
                    os = locatingExceptionDetail.getPhoneOS();
                    uuid = locatingExceptionDetail.getUuid();
                    exceptionHappenTime = com.sankuai.meituan.common.time.TimeUtil.format((int) (locatingExceptionDetail.getUtime() / 1000));
                } else {
                    locatingExceptionType = RiderLocatingExceptionEnum.UNKNOWN_EXCEPTION.getValue();
                }
            }

            //获取os信息，取最后上报的点的os
            RiderLocationDataDO latestStaffRiderLocation = squirrelRiderLocationRepository.getLatestStaffRiderLocation(riderAccountId);
            if (latestStaffRiderLocation != null) {
                os = latestStaffRiderLocation.getOs();
                appVersion = latestStaffRiderLocation.getAppVersion();
                uuid = latestStaffRiderLocation.getUuid();
            }


            boolean isRealTimePosition = false;
            // 获取到位置 并且位置是30s以内的 认为取到了实时位置
            if (point.isPresent() && Objects.nonNull(point.get().getTimestamp()) && TimeUtil.toMilliSeconds(LocalDateTime.now()) - point.get().getTimestamp() < DeliveryRiderMccConfigUtils.getRealTimePositionDuration() * 1000) {
                isRealTimePosition = true;
            }

            //判断这次同步的位置是否跟上次一样
            boolean isRepeatPosition = false;
            Optional<OrderLastRiderLocationDetail> orderLastRiderLocationDetailOpt = squirrelOrderLastRiderLocationRepository.get(deliveryOrder.getOrderId());
            if (orderLastRiderLocationDetailOpt.isPresent() && point.isPresent()) {
                OrderLastRiderLocationDetail orderLastRiderLocationDetail = orderLastRiderLocationDetailOpt.get();
                if (StringUtils.equals(orderLastRiderLocationDetail.getLongitude(), point.get().getLongitude())
                        && StringUtils.equals(orderLastRiderLocationDetail.getLatitude(), point.get().getLatitude())) {
                    isRepeatPosition = true;
                }
            }

            //计算跟上一次同步的位置之间的距离
            Long distanceToLastPosition = 0L;
            if (orderLastRiderLocationDetailOpt.isPresent() && point.isPresent()) {
                OrderLastRiderLocationDetail orderLastRiderLocationDetail = orderLastRiderLocationDetailOpt.get();
                distanceToLastPosition = CoordinateUtil.calLineDistance(new CoordinatePoint(point.get().getLongitude(), point.get().getLatitude()),
                        new CoordinatePoint(orderLastRiderLocationDetail.getLongitude(), orderLastRiderLocationDetail.getLatitude()));
            }


            //将本次同步的位置计入到缓存中
            point.ifPresent(coordinatePoint -> squirrelOrderLastRiderLocationRepository.save(new OrderLastRiderLocationDetail(deliveryOrder.getOrderId(),
                    coordinatePoint.getLongitude(), coordinatePoint.getLatitude(), coordinatePoint.getTimestamp() == null ? TimeUtil.toMilliSeconds(LocalDateTime.now()) : coordinatePoint.getTimestamp())));


            riderLocationSyncLog.info(XMDLogFormat.build()
                    .putTag("rider_account_id", riderAccountId + "")
                    .putTag("longitude", point.isPresent() ? point.get().getLongitude() : "")
                    .putTag("latitude", point.isPresent() ? point.get().getLatitude() : "")
                    .putTag("is_real_time_position", isRealTimePosition + "")
                    .putTag("is_repeat_position", isRepeatPosition + "")
                    .putTag("distance_to_last_position", distanceToLastPosition.toString())
                    .putTag("last_sync_position_time", orderLastRiderLocationDetailOpt.map(orderLastRiderLocationDetail -> orderLastRiderLocationDetail.getTimestamp() + "").orElse(""))
                    .putTag("order_id", orderId.toString())
                    .putTag("view_order_id", deliveryOrder.getChannelOrderId())
                    .putTag("order_biz_type", deliveryOrder.getOrderBizType() + "")
                    .putTag("delivery_status", deliveryOrder.getStatus() + "")
                    .putTag("get_location_success", String.valueOf(getLocationSuccess))
                    .putTag("exception_type", locatingExceptionType + "")
                    .putTag("manufacturer", phoneManufacturer)
                    .putTag("phoneOS", os)
                    .putTag("uuid", uuid)
                    .putTag("exception_happen_time", exceptionHappenTime)
                    .putTag("last_post_location_time", latestStaffRiderLocation == null ? "" : latestStaffRiderLocation.getUtime() + "")
                    .putTag("store_id", deliveryOrder.getStoreId() + "")
                    .putTag("tenant_id",deliveryOrder.getTenantId()+"")
                    .putTag("app_version", appVersion)
                    .toString());
        } catch (Exception e) {
            log.error("采集自营骑手位置同步到C端的日志失败", e);
        }

    }

    private void saveNewSupplyRiderLocation(CoordinatePoint coordinatePoint, DeliveryChannel deliveryChannelDto, Long orderId) {
        try {
            if (Objects.isNull(deliveryChannelDto)) {
                log.error("saveNewSupplyRiderLocation, deliveryChannelDto is null");
                return;
            }
            Integer deliveryPlatFormCode = deliveryChannelDto.getDeliveryPlatFormCode();
            if (Objects.isNull(deliveryPlatFormCode)) {
                log.error("saveNewSupplyRiderLocation, deliveryPlatFormCode is null");
                return;
            }
            DeliveryPlatformEnum deliveryPlatformEnum = DeliveryPlatformEnum.enumOf(deliveryPlatFormCode);
            if (Objects.isNull(deliveryPlatformEnum)) {
                log.error("saveNewSupplyRiderLocation, deliveryPlatformEnum is null");
                return;
            }

            switch (deliveryPlatformEnum) {
                case MALT_FARM_DELIVERY_PLATFORM:
                case DAP_DELIVERY_PLATFORM:
                    squirrelRiderLocationAggDeliveryRepository.saveRiderLocation(orderId, coordinatePoint);
                    break;
                case MERCHANT_SELF_DELIVERY:
                    squirrelRiderLocationSelfMerchantDeliveryRepository.saveRiderLocation(orderId, coordinatePoint);
                    break;
                default:
                    log.warn("当前平台[{}]暂不支持保存骑手坐标", deliveryPlatformEnum);
            }
        } catch (Exception e) {
            log.error("saveNewSupplyRiderLocation error", e);
        }
    }

}
