package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.thrift;

import com.dianping.cat.Cat;
import com.google.common.base.Functions;
import com.google.common.collect.ArrayListMultimap;
import com.meituan.linz.boot.util.Bssert;
import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.meituan.shangou.saas.order.platform.enums.OrderStatusEnum;
import com.sankuai.meituan.shangou.dms.base.model.value.DeliveryChannel;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.AggDeliveryConfigApplicationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.DeliveryChannelApplicationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.QueryDeliveryInfoApplicationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.order.OrderSystemClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.platform.DeliveryPlatformUrlInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.AggDeliveryPlatformAppConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.TimeUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.common.Status;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.common.TPageInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.QueryDeliveryInfoThriftService;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.request.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.response.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.es.DeliveryOrderEsAliasDao;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.es.po.DeliveryOrderEsPo;
import com.sankuai.meituan.shangou.empower.tms.delivery.exception.DeliveryBaseException;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.cache.ShopAuthSquirrelOperationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.client.platform.DapDeliveryPlatformClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.service.DeliveryOperateItemService;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoi;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoiRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.PageRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.PageResult;
import com.sankuai.meituan.shangou.saas.common.cat.CatTransaction;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryExceptionSubTypeEnum.*;
import static com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.FailureCodeEnum.SYSTEM_ERROR;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/10/22
 */
@Slf4j
@Service
public class QueryDeliveryInfoThriftServiceImpl implements QueryDeliveryInfoThriftService {

    @Resource
    private QueryDeliveryInfoApplicationService queryDeliveryInfoApplicationService;

    @Resource
    private DeliveryOrderRepository deliveryOrderRepository;

    @Resource
    private OrderSystemClient orderSystemClient;

    @Resource
    private DeliveryOperateItemService deliveryOperateItemService;

    @Resource
    private DeliveryChannelApplicationService deliveryChannelApplicationService;

    @Resource
    private ShopAuthSquirrelOperationService shopAuthSquirrelOperationService;

    @Resource
    private DeliveryPoiRepository deliveryPoiRepository;

    @Resource
    private DapDeliveryPlatformClient dapDeliveryPlatformClient;

    @Resource
    private AggDeliveryConfigApplicationService aggDeliveryConfigApplicationService;

    @Resource
    private DeliveryOrderEsAliasDao deliveryOrderEsAliasDao;

    private static final int MAX_SIZE = 500;

    @Override
    @MethodLog(logRequest = false, logResponse = true)
    @CatTransaction
    public QueryDeliveryInfoResponse queryDeliveryInfoByOrderKeys(QueryDeliveryInfoRequest req) {

        QueryDeliveryInfoResponse response = QueryDeliveryInfoResponse.emptyResponse();
        if (req == null || CollectionUtils.isEmpty(req.orderKeys)) {
            return response;
        }

        List<TDeliveryDetail> tDeliveryDetails = Lists.newArrayList();
        try {

            //构造orderKey与order中发起配送相关字段的map
            Map<OrderKey, DeliveryConditionInOrder> orderKeyDeliveryConditionInOrderMap = req.orderKeys.stream().collect(Collectors.toMap(o -> new OrderKey(o.tenantId, o.storeId, o.empowerOrderId), o -> DeliveryConditionInOrder.builder()
                    .isSelfDelivery(o.selfDeliveryMark)
                    .deliveryMethod(o.deliveryMethod)
                    .orderSource(o.orderSource)
                    .orderStatus(o.orderStatus).build(), (o, n) -> n));
            List<OrderDeliveryDetail> deliveryDetails = queryDeliveryInfoApplicationService.queryOrderDeliveryDetail(buildOrderKeys(req.orderKeys), orderKeyDeliveryConditionInOrderMap);
            Set<Integer> carrierCodeSet = deliveryDetails.stream().map(OrderDeliveryDetail::getDeliveryChannel).filter(Objects::nonNull).collect(Collectors.toSet());
            List<DeliveryChannel> deliveryChannelList = deliveryChannelApplicationService.batchQueryDeliveryChannelByCarrierCodeSet(carrierCodeSet);
            Map<Integer, String> deliveryChannelNameMap = deliveryChannelList.stream().collect(Collectors.toMap(DeliveryChannel::getCarrierCode, DeliveryChannel::getCarrierName));
            Map<Integer, Integer> carrierCodePlatformCodeMap = deliveryChannelList.stream().collect(Collectors.toMap(DeliveryChannel::getCarrierCode, DeliveryChannel::getDeliveryPlatFormCode, (v1, v2) -> v2));
            tDeliveryDetails = buildTDeliveryDetail(deliveryDetails, deliveryChannelNameMap, carrierCodePlatformCodeMap);
        } catch (Exception e) {
            log.error("查询订单配送详情异常, req={}", req, e);
        }
        response.setTDeliveryDetails(tDeliveryDetails);
        return response;
    }

    @Override
    @CatTransaction
    public QueryActiveDeliveryInfoResponse queryActiveDeliveryInfoByOrderKeys(QueryActiveDeliveryInfoRequest req) {
        try {
            if (CollectionUtils.isEmpty(req.getOrderKeys())) {
                return QueryActiveDeliveryInfoResponse.emptyResponse();
            }

            Integer limitSize = MccConfigUtils.getQueryActiveDeliveryInfoLimitSize();
            if (req.getOrderKeys().size() > limitSize) {
                return new QueryActiveDeliveryInfoResponse(new Status(ResponseCodeEnum.QUERY_EXCEED_LIMIT.getValue(), "查询数量超过限制"), null);
            }

            List<TActiveDeliveryDetail> tDeliveryDetails = queryDeliveryInfoApplicationService.queryActiveDeliveryInfoByOrderKeys(req.getOrderKeys(), req.isNeedAggUrl());
            return new QueryActiveDeliveryInfoResponse(Status.SUCCESS, tDeliveryDetails);
        } catch (Exception e) {
            log.error("QueryDeliveryInfoThriftServiceImpl queryActiveDeliveryInfoByOrderKeys failed", e);
            return new QueryActiveDeliveryInfoResponse(new Status(ResponseCodeEnum.FAILED.getValue(), "系统异常"), null);
        }
    }

    @Override
    @MethodLog(logRequest = true, logResponse = true)
    @CatTransaction
    public PageQueryDeliveryInfoResponse pageQueryThirdDeliveryOrderList(@Valid QueryDeliveryByStatusRequest request) {
        if (CollectionUtils.isEmpty(request.getStatusList())) {
            return new PageQueryDeliveryInfoResponse(Status.SUCCESS, new TPageInfo(request.getPage(), request.getPageSize(), 0 ), Collections.emptyList());
        }

        //查运单
        PageResult<OrderDeliveryDetail> deliveryOrderPageResult =
                queryDeliveryInfoApplicationService.pageQueryThirdDeliveryOrder(request.getTenantId(), request.getStoreId(),
                        request.getStatusList(), request.getFilterException(), new PageRequest(request.getPage(), request.getPageSize()));

        if (CollectionUtils.isEmpty(deliveryOrderPageResult.getInfo())) {
            return new PageQueryDeliveryInfoResponse(Status.SUCCESS, new TPageInfo(request.getPage(), request.getPageSize(), 0), Collections.emptyList());
        }

        //查承运商
        Set<Integer> carrierCodeSet = deliveryOrderPageResult.getInfo().stream().map(OrderDeliveryDetail::getDeliveryChannel).filter(Objects::nonNull).collect(Collectors.toSet());
        List<DeliveryChannel> deliveryChannelList = deliveryChannelApplicationService.batchQueryDeliveryChannelByCarrierCodeSet(carrierCodeSet);
        Map<Integer/*CarrierCode*/, String/*CarrierName*/> deliveryChannelNameMap = deliveryChannelList.stream().collect(Collectors.toMap(DeliveryChannel::getCarrierCode, DeliveryChannel::getCarrierName));
        Map<Integer/*CarrierCode*/, Integer/*PlatformCode*/> carrierCodePlatformCodeMap = deliveryChannelList.stream().collect(Collectors.toMap(DeliveryChannel::getCarrierCode, DeliveryChannel::getDeliveryPlatFormCode));
        List<TDeliveryDetail> tDeliveryDetails = buildTDeliveryDetail(deliveryOrderPageResult.getInfo(), deliveryChannelNameMap, carrierCodePlatformCodeMap);

        return new PageQueryDeliveryInfoResponse(Status.SUCCESS, new TPageInfo(request.getPage(), request.getPageSize(), (int) deliveryOrderPageResult.getTotal()), tDeliveryDetails);

    }

    @Override
    @MethodLog(logRequest = true, logResponse = true)
    @CatTransaction
    public QueryThirdDeliveryOrderCountResponse queryThirdDeliveryOrderCount(Long tenantId, Long storeId) {
        Bssert.throwIfNull(tenantId, "租户id不能为空");
        Bssert.throwIfNull(tenantId, "门店id不能为空");

        Map<DeliveryStatusEnum, Integer> status2CountMap = queryDeliveryInfoApplicationService.queryThirdDeliveryOrderCount(tenantId, storeId);

        return buildQueryThirdDeliveryOrderCountResponse(status2CountMap);
    }

    @Override
    public QueryDeliveryInfoResponse queryDeliveryExceptionOrCanceledOrders(QueryDeliveryExceptionOrderRequest request) {
        QueryDeliveryInfoResponse response = QueryDeliveryInfoResponse.emptyResponse();
        String check = request.validate();
        if (check != null) {
            return response;
        }

        try {
            List<OrderDeliveryDetail> orderDeliveryDetails = queryDeliveryInfoApplicationService.queryExceptionOrCancelDeliveryOrders(request.getEmpowerStoreId(),request.getTenantId());

            Set<Integer> carrierCodeSet = orderDeliveryDetails.stream().map(OrderDeliveryDetail::getDeliveryChannel).filter(Objects::nonNull).collect(Collectors.toSet());
            List<DeliveryChannel> deliveryChannelList = deliveryChannelApplicationService.batchQueryDeliveryChannelByCarrierCodeSet(carrierCodeSet);
            Map<Integer/*CarrierCode*/, String/*CarrierName*/> deliveryChannelNameMap = deliveryChannelList.stream().collect(Collectors.toMap(DeliveryChannel::getCarrierCode, DeliveryChannel::getCarrierName));
            Map<Integer/*CarrierCode*/, Integer/*PlatformCode*/> carrierCodePlatformCodeMap = deliveryChannelList.stream().collect(Collectors.toMap(DeliveryChannel::getCarrierCode, DeliveryChannel::getDeliveryPlatFormCode));
            List<TDeliveryDetail> tDeliveryDetails = buildTDeliveryDetail(orderDeliveryDetails, deliveryChannelNameMap, carrierCodePlatformCodeMap);

            response.setTDeliveryDetails(tDeliveryDetails);
        } catch (Exception e) {
            log.error("QueryDeliveryInfoThriftService.queryExceptionOrCancelDeliveryOrders error", e);
        }
        return response;
    }

    @Override
    public DeliveryExceptionOrderNumResponse countDeliveryExceptionOrCanceledOrder(QueryDeliveryExceptionOrderRequest request) {
        DeliveryExceptionOrderNumResponse response = new DeliveryExceptionOrderNumResponse();
        String check = request.validate();
        if (check != null) {
            response.setStatus(new Status(FailureCodeEnum.INVALID_PARAM.getCode(), check));
            return response;
        }

        try {
            List<OrderDeliveryDetail> orderDeliveryDetails = queryDeliveryInfoApplicationService.queryExceptionOrCancelDeliveryOrders(request.getEmpowerStoreId(),request.getTenantId());
            int orderNum = orderDeliveryDetails.size();

            if(CollectionUtils.isNotEmpty(orderDeliveryDetails)){
                Optional<OrderDeliveryDetail> deliveryOrderOpt=orderDeliveryDetails.stream().findAny();
                if(deliveryOrderOpt.isPresent()){
                    Map<String,Integer> orderBizMap = orderDeliveryDetails.stream().collect(Collectors.toMap(OrderDeliveryDetail::getChannelOrderId, OrderDeliveryDetail::getOrderBizType));
                    Map<String,Integer> statusMap = orderSystemClient.queryOrderStatusMap(orderBizMap);
                    try {
                        orderNum = 0;
                        for (OrderDeliveryDetail deliveryOrder : orderDeliveryDetails){
                            if(deliveryOrder==null){
                                continue;
                            }
                            boolean contains = statusMap.containsKey(deliveryOrder.getChannelOrderId()) && statusMap.get(deliveryOrder.getChannelOrderId())!=null;
                            if (!contains) {
                                continue;
                            }
                            //所有单是取消都不在异常列表里
                            if(statusMap.get(deliveryOrder.getChannelOrderId()) == OrderStatusEnum.CANCELED.getValue()){
                                continue;
                            }
                            //运单取消但订单已完成也不记录
                            if (Objects.equals(deliveryOrder.getStatus(), DeliveryStatusEnum.DELIVERY_CANCELLED)
                                    && statusMap.get(deliveryOrder.getChannelOrderId()).equals(OrderStatusEnum.COMPLETED.getValue())
                            ) {
                                continue;
                            }
                            orderNum++;
                        }
                    }catch (Exception e){
                        log.error("QueryDeliveryInfoThriftService.countDeliveryExceptionOrCanceledOrder error", e);
                        orderNum = orderDeliveryDetails.size();
                    }
                }
            }
            response.setOrderNum(orderNum);
        } catch (Exception e) {
            log.error("QueryDeliveryInfoThriftService.countDeliveryExceptionOrder error", e);
            response.setStatus(new Status(SYSTEM_ERROR.getCode(), SYSTEM_ERROR.getMessage()));
        }
        return response;
    }

    private QueryThirdDeliveryOrderCountResponse buildQueryThirdDeliveryOrderCountResponse(Map<DeliveryStatusEnum, Integer> status2CountMap) {
        QueryThirdDeliveryOrderCountResponse response = new QueryThirdDeliveryOrderCountResponse();
        response.setWaitPlatformAcceptCount(status2CountMap.getOrDefault(DeliveryStatusEnum.DELIVERY_LAUNCHED, 0) + status2CountMap.getOrDefault(DeliveryStatusEnum.INIT, 0));
        response.setWaitRiderAcceptCount(status2CountMap.getOrDefault(DeliveryStatusEnum.WAITING_TO_ASSIGN_RIDER, 0));
        response.setRiderAcceptedCount(status2CountMap.getOrDefault(DeliveryStatusEnum.RIDER_ASSIGNED, 0));
        response.setWaitRiderArriveShopCount(status2CountMap.getOrDefault(DeliveryStatusEnum.RIDER_ARRIVED_SHOP, 0));
        response.setRiderDeliveringCount(status2CountMap.getOrDefault(DeliveryStatusEnum.RIDER_TAKEN_GOODS, 0));
        return response;
    }

    private List<OrderKey> buildOrderKeys(List<QueryOrderDeliveryInfoKey> orderKeys) {

        return orderKeys.stream().map(o -> new OrderKey(o.tenantId, o.storeId, o.empowerOrderId)).collect(Collectors.toList());
    }

    private List<TDeliveryDetail> buildTDeliveryDetail(List<OrderDeliveryDetail> deliveryDetails, Map<Integer, String> deliveryChannelNameMap,
                                                       Map<Integer/*CarrierCode*/, Integer/*PlatformCode*/> carrierCodePlatformCodeMap) {

        if (CollectionUtils.isEmpty(deliveryDetails)) {
            return Lists.newArrayList();
        }
        return deliveryDetails.stream().map(deliveryDetail -> convert(deliveryDetail, deliveryChannelNameMap, carrierCodePlatformCodeMap)).collect(Collectors.toList());
    }

    private static TDeliveryDetail convert(OrderDeliveryDetail domain, Map<Integer, String> deliveryChannelNameMap,
                                           Map<Integer/*CarrierCode*/, Integer/*PlatformCode*/> carrierCodePlatformCodeMap) {
        List<OriginWaybillDto> waybillList = Optional.ofNullable(domain.getWaybillList())
                .orElseGet(ArrayList::new).stream()
                .map(OriginWaybillInfo::convertToOriginWaybillDto)
                .collect(Collectors.toList());

        return TDeliveryDetail.builder().deliveryFee(domain.getDeliveryFee() == null? null : domain.getDeliveryFee().doubleValue())
                .deliveryDistance(domain.getDistance())
                .bizOrderId(domain.getOrderKey().getOrderId())
                .deliveryEntity(getDeliveryEntityCode(domain.getDeliveryChannel(), carrierCodePlatformCodeMap))
                .deliveryException(domain.getExceptionDescription())
                .deliveryExceptionCode(domain.getExceptionCode())
                .deliveryExceptionType(Objects.nonNull(domain.getExceptionType()) ? domain.getExceptionType().getCode() :
                        DeliveryExceptionTypeEnum.NO_EXCEPTION.getCode())
                .deliveryChannelName(getDeliveryChannelName(domain, deliveryChannelNameMap))
                .startWaitAssignRiderTime(domain.getStartWaitingAssignRiderTime() == null ? null : TimeUtil.toMilliSeconds(domain.getStartWaitingAssignRiderTime()))
                .riderName(domain.getRiderInfo() == null ? null : domain.getRiderInfo().getRiderName())
                .status(domain.getStatus() == null ? null : domain.getStatus().getCode())
                .tLaunchDeliveryType(buildLaunchItem(domain))
                .riderPhone(domain.getRiderInfo() == null ? null : domain.getRiderInfo().getRiderPhone())
                .displayCancelStatus(domain.getDisplayCancelStatusEnum().getCode())
                .deliveryChannelCode(Optional.ofNullable(domain.getDeliveryChannel()).orElse(DeliveryChannelEnum.AGGREGATION_DELIVERY.getCode()))
                .deliveryStatusChangeTime(domain.getDeliveryStatusChangeTime() == null ? null : TimeUtil.toMilliSeconds(domain.getDeliveryStatusChangeTime()))
                .deliveryCount(domain.getDeliveryCount()==null ? 1:domain.getDeliveryCount())
                .createTime(domain.getCreateTime())
                .transType(domain.getTransType())
                .tipFee(domain.getTipFee() == null ? null:domain.getTipFee().doubleValue())
                .allowLatestAuditTime(domain.getDealDeadline())
                .fulfillOrderId(domain.getFulfillOrderId())
                .platformSource(domain.getPlatformSource())
                .orderBizType(domain.getOrderBizType())
                .channelOrderId(domain.getChannelOrderId())
                .platformCode(getPlatformCode(domain, carrierCodePlatformCodeMap))
                .platformDesc(getPlatformDesc(domain, carrierCodePlatformCodeMap))
                .signPosition(domain.getSignPosition())
                .assessDeliveryTime(domain.getAssessDeliveryTime())
                .isFourWheelDelivery(domain.getIsFourWheelDelivery())
                .waybillList(waybillList)
                .build();
    }

    private static String getDeliveryChannelName(OrderDeliveryDetail domain, Map<Integer, String> deliveryChannelNameMap) {
        if (Objects.isNull(domain) || Objects.isNull(domain.getDeliveryChannel()) || MapUtils.isEmpty(deliveryChannelNameMap)) {
            return null;
        }

        return deliveryChannelNameMap.get(domain.getDeliveryChannel());
    }

    private static Integer getPlatformCode(OrderDeliveryDetail domain, Map<Integer/*CarrierCode*/, Integer/*PlatformCode*/> carrierCodePlatformCodeMap) {
        try {
            if (Objects.isNull(domain) || Objects.isNull(domain.getDeliveryChannel()) || MapUtils.isEmpty(carrierCodePlatformCodeMap)) {
                return null;
            }

            return carrierCodePlatformCodeMap.get(domain.getDeliveryChannel());
        } catch (Exception e) {
            log.error("getPlatformCode error", e);
            Cat.logEvent("DH_ADAPT_DAP", "GET_PLATFORM_CODE_ERROR");
            return null;
        }

    }

    private static String getPlatformDesc(OrderDeliveryDetail domain, Map<Integer/*CarrierCode*/, Integer/*PlatformCode*/> carrierCodePlatformCodeMap) {
        try {
            if (Objects.isNull(domain) || Objects.isNull(domain.getDeliveryChannel()) || MapUtils.isEmpty(carrierCodePlatformCodeMap)) {
                return null;
            }

            Integer platformCode = carrierCodePlatformCodeMap.get(domain.getDeliveryChannel());
            return Optional.ofNullable(DeliveryPlatformEnum.enumOf(platformCode)).map(DeliveryPlatformEnum::getDesc).orElse("");
        } catch (Exception e) {
            log.error("getPlatformDesc error", e);
            Cat.logEvent("DH_ADAPT_DAP", "GET_PLATFORM_DESC_ERROR");
            return "";
        }
    }

    private static String getDeliveryChannelName(DeliveryOrder deliveryOrder, Map<Integer, String> deliveryChannelNameMap) {
        if (Objects.isNull(deliveryOrder) || Objects.isNull(deliveryOrder.getDeliveryChannel()) || MapUtils.isEmpty(deliveryChannelNameMap)) {
            return null;
        }

        return deliveryChannelNameMap.get(deliveryOrder.getDeliveryChannel());
    }

    private static TLaunchDeliveryType buildLaunchItem(OrderDeliveryDetail domain) {

        TLaunchDeliveryType tLaunchDeliveryType = new TLaunchDeliveryType();
        tLaunchDeliveryType.setCanSelfDelivery(domain.isCanSelfDelivery());
        tLaunchDeliveryType.setCanRetryLaunch(domain.isCanRetryLaunch());
        tLaunchDeliveryType.setCanLaunchThirdPartWhenException(domain.isCanLaunchThirdPartWhenException());
        tLaunchDeliveryType.setCanManualLaunchThirdPart(domain.isCanManualLaunchThirdPart());
        tLaunchDeliveryType.setCanCancel(domain.isCanCancel());
        tLaunchDeliveryType.setCanRetryLaunchByMaltfarm(domain.isCanRetryLaunchByMaltfarm());
        tLaunchDeliveryType.setCanRetryLaunchByHaiKui(domain.isCanRetryLaunchByHaiKui());
        tLaunchDeliveryType.setCanRetryLaunchByDap(domain.isCanRetryLaunchByDap());
        return tLaunchDeliveryType;
    }

    private static Integer getDeliveryEntityCode(Integer deliveryChannel, Map<Integer, Integer> carrierCodePlatformCodeMap) {

        if (deliveryChannel == null) {
            return null;
        }
        if (deliveryChannel.equals(DeliveryChannelEnum.AGGREGATION_DELIVERY.getCode())) {
            return null;
        }
        Integer deliveryPlatFormCode = carrierCodePlatformCodeMap.get(deliveryChannel);
        if (deliveryChannel.equals(DeliveryChannelEnum.ORDER_PLATFORM_DELIVERY.getCode()) ||
                Objects.equals(DeliveryPlatformEnum.ORDER_CHANNEL_DELIVERY_PLATFORM.getCode(), deliveryPlatFormCode)) {
            return DeliveryEntityEnum.PLATFORM.getValue();
        }
        if (deliveryChannel.equals(DeliveryChannelEnum.MERCHANT_DELIVERY.getCode())) {
            return DeliveryEntityEnum.DELIVER_BY_SELF.getValue();
        }
        return DeliveryEntityEnum.THIRD_PART.getValue();
    }

    @Override
    @MethodLog(logRequest = false, logResponse = true)
    @CatTransaction
    public DeliveryExceptionOrdersResponse queryDeliveryExceptionOrders(QueryDeliveryExceptionOrderRequest request) {
        DeliveryExceptionOrdersResponse response = new DeliveryExceptionOrdersResponse();
        String check = request.validate();
        if (check != null) {
            response.setStatus(new Status(FailureCodeEnum.INVALID_PARAM.getCode(), check));
            return response;
        }

        try {
            List<DeliveryOrder> exceptionDeliveryOrders = queryDeliveryInfoApplicationService.queryExceptionDeliveryOrders(request.getEmpowerStoreId(),request.getTenantId());
            List<TOrderIdentifier> deliveryExceptionOrders = exceptionDeliveryOrders.stream()
                    .map(deliveryOrder -> new TOrderIdentifier(deliveryOrder.getOrderBizType(),
                    deliveryOrder.getChannelOrderId(), deliveryOrder.getStatus(), deliveryOrder.getExceptionType().getCode(), deliveryOrder.getDeliveryExceptionCode(),deliveryOrder.getDealDeadline())).collect(Collectors.toList());
            response.setOrders(deliveryExceptionOrders);
        } catch (Exception e) {
            log.error("QueryDeliveryInfoThriftService.queryDeliveryExceptionOrders error", e);
            response.setStatus(new Status(SYSTEM_ERROR.getCode(), SYSTEM_ERROR.getMessage()));
        }
        return response;
    }

    @Override
    @MethodLog(logRequest = false, logResponse = true)
    @CatTransaction
    public DeliveryExceptionOrderNumResponse countDeliveryExceptionOrder(QueryDeliveryExceptionOrderRequest request) {
        DeliveryExceptionOrderNumResponse response = new DeliveryExceptionOrderNumResponse();
        String check = request.validate();
        if (check != null) {
            response.setStatus(new Status(FailureCodeEnum.INVALID_PARAM.getCode(), check));
            return response;
        }

        try {
            List<DeliveryOrder> exceptionDeliveryOrders = queryDeliveryInfoApplicationService.queryExceptionDeliveryOrders(request.getEmpowerStoreId(),request.getTenantId());
            int orderNum = exceptionDeliveryOrders.size();

            if(CollectionUtils.isNotEmpty(exceptionDeliveryOrders)){
                Optional<DeliveryOrder> deliveryOrderOpt=exceptionDeliveryOrders.stream().findAny();
                if(deliveryOrderOpt.isPresent()){
                    Map<String,Integer> orderBizMap = exceptionDeliveryOrders.stream().collect(Collectors.toMap(DeliveryOrder::getChannelOrderId,DeliveryOrder::getOrderBizType));
                    Map<String,Integer> statusMap = orderSystemClient.queryOrderStatusMap(orderBizMap);
                    try {
                        orderNum = 0;
                        for (DeliveryOrder deliveryOrder : exceptionDeliveryOrders){
                            if(deliveryOrder==null){
                                continue;
                            }
                            if(statusMap.containsKey(deliveryOrder.getChannelOrderId()) && statusMap.get(deliveryOrder.getChannelOrderId())!=null && statusMap.get(deliveryOrder.getChannelOrderId()) == OrderStatusEnum.CANCELED.getValue()){
                                continue;
                            }
                            orderNum++;
                        }
                    }catch (Exception e){
                        log.error("QueryDeliveryInfoThriftService.countDeliveryExceptionOrder error", e);
                        orderNum = exceptionDeliveryOrders.size();
                    }
                }
            }
            response.setOrderNum(orderNum);
        } catch (Exception e) {
            log.error("QueryDeliveryInfoThriftService.countDeliveryExceptionOrder error", e);
            response.setStatus(new Status(SYSTEM_ERROR.getCode(), SYSTEM_ERROR.getMessage()));
        }
        return response;

    }

    @Override
    @MethodLog(logRequest = false, logResponse = true)
    @CatTransaction
    public QueryDeliveryOrderResponse queryDeliveryOrderByOrderId(QueryDeliveryOrderRequest request) {

        QueryDeliveryOrderResponse response = new QueryDeliveryOrderResponse();
        if (request == null) {
            return QueryDeliveryOrderResponse.buildFailResponse(FailureCodeEnum.INVALID_PARAM.getCode(), "请求不能为空");
        }
        String requestErrorMsg = request.validate();
        if (requestErrorMsg != null) {
            return QueryDeliveryOrderResponse.buildFailResponse(FailureCodeEnum.INVALID_PARAM.getCode(), requestErrorMsg);
        }
        List<DeliveryOrder> deliveryOrders = new ArrayList<>();
        if(request.isMasterOnly()){
            if(MccConfigUtils.getDeliveryQueryTenantSwitch()){
                deliveryOrders = deliveryOrderRepository.getDeliveryOrdersAllWithOrderId(request.getOrderId());
            }else {
                deliveryOrders = deliveryOrderRepository.getDeliveryOrdersForceMaster(request.getOrderId());
            }

        }else {
            if(MccConfigUtils.getDeliveryQueryTenantSwitch()){
                deliveryOrders = deliveryOrderRepository.getDeliveryOrdersAllWithOrderIdSlave(request.getOrderId());
            }else {
                deliveryOrders = deliveryOrderRepository.getDeliveryOrderSlave(request.getOrderId());
            }
        }
        
        if (CollectionUtils.isEmpty(deliveryOrders)) {
            return QueryDeliveryOrderResponse.buildFailResponse(FailureCodeEnum.INVALID_PARAM.getCode(), "赋能订单id错误");
        }
        List<TDeliveryOrder> tDeliveryOrders = buildTDeliveryOrder(deliveryOrders);
        response.setStatus(Status.SUCCESS);
        response.setTDeliveryOrders(tDeliveryOrders);
        return response;
    }

    @Override
    @MethodLog(logRequest = false, logResponse = true)
    @CatTransaction
    public QueryDeliveryOrderResponse queryDeliveryOrderByOrderIdWithEmpty(QueryDeliveryOrderRequest request) {

        QueryDeliveryOrderResponse response = new QueryDeliveryOrderResponse();
        if (request == null) {
            return QueryDeliveryOrderResponse.buildFailResponse(FailureCodeEnum.INVALID_PARAM.getCode(), "请求不能为空");
        }
        String requestErrorMsg = request.validate();
        if (requestErrorMsg != null) {
            return QueryDeliveryOrderResponse.buildFailResponse(FailureCodeEnum.INVALID_PARAM.getCode(), requestErrorMsg);
        }
        List<DeliveryOrder> deliveryOrders = new ArrayList<>();
        if(MccConfigUtils.getDeliveryQueryTenantSwitch()){
            deliveryOrders = deliveryOrderRepository.getDeliveryOrdersAllWithOrderIdSlave(request.getOrderId());
        }else {
            deliveryOrders = deliveryOrderRepository.getDeliveryOrderSlave(request.getOrderId());
        }
        if (CollectionUtils.isEmpty(deliveryOrders)) {
            response.setStatus(Status.SUCCESS);
            response.setTDeliveryOrders(Collections.emptyList());
            return response;
        }
        List<TDeliveryOrder> tDeliveryOrders = buildTDeliveryOrder(deliveryOrders);
        response.setStatus(Status.SUCCESS);
        response.setTDeliveryOrders(tDeliveryOrders);
        return response;
    }

    private List<TDeliveryOrder> buildTDeliveryOrder(List<DeliveryOrder> deliveryOrders) {

        return deliveryOrders.stream().map(QueryDeliveryInfoThriftServiceImpl::convert).collect(Collectors.toList());
    }

    private static TDeliveryOrder convert(DeliveryOrder deliveryOrder) {
        List<Integer> employDeliveryStatus = MccConfigUtils.getEmployDeliveryStatus();
        return TDeliveryOrder.builder().id(deliveryOrder.getId())
            .orderId(deliveryOrder.getOrderId())
            .tenantId(deliveryOrder.getTenantId())
            .storeId(deliveryOrder.getStoreId())
            .channelOrderId(deliveryOrder.getChannelOrderId())
            .activeStatus(deliveryOrder.isActive())
            .status(deliveryOrder.getStatus() == null ? null : deliveryOrder.getStatus().getCode())
            .deliveryFee(deliveryOrder.getDeliveryFee() == null ? null : deliveryOrder.getDeliveryFee().doubleValue())
            .deliveryChannel(deliveryOrder.getDeliveryChannel() == null ? null : deliveryOrder.getDeliveryChannel())
            .distance(deliveryOrder.getDistance())
            .estimatedDeliveryTime(deliveryOrder.getEstimatedDeliveryTime() == null ? null : TimeUtil.toMilliSeconds(deliveryOrder.getEstimatedDeliveryTime()))
            .estimatedDeliveryEndTime(deliveryOrder.getEstimatedDeliveryEndTime() == null ? null : TimeUtil.toMilliSeconds(deliveryOrder.getEstimatedDeliveryEndTime()))
            .tipAmount(deliveryOrder.getTipAmount() == null ? null : deliveryOrder.getTipAmount().doubleValue())
            .deliveryCount(deliveryOrder.getDeliveryCount())
            .riderName(deliveryOrder.getRiderInfo()==null ? "":deliveryOrder.getRiderInfo().getRiderName())
            .riderPhone(deliveryOrder.getRiderInfo()==null ? "":deliveryOrder.getRiderInfo().getRiderPhone())
            .exceptionType(deliveryOrder.getExceptionType() == null ? 0:deliveryOrder.getExceptionType().getCode())
            .exceptionCode(deliveryOrder.getDeliveryExceptionCode())
            .allowLatestAuditTime(deliveryOrder.getDealDeadline())
            .platformSourceCode(deliveryOrder.getPlatformSourceEnum() == null ? PlatformSourceEnum.OMS.getCode() : deliveryOrder.getPlatformSourceEnum().getCode())
            .fulfillmentOrderId(deliveryOrder.getFulfillmentOrderId())
            .deliveryOrderId(deliveryOrder.getDeliveryOrderId())
                .createTime(TimeUtil.toMilliSeconds(deliveryOrder.getCreateTime()))
                .pickDeliverySplitTag(deliveryOrder.getPickDeliverySplitTag())
                .isFourWheelDelivery(deliveryOrder.getIsFourWheelDelivery())
                .originWaybillNo(employDeliveryStatus.contains(deliveryOrder.getStatus().getCode()) ? deliveryOrder.getOriginWaybillNo() : null)
                .orderBizType(deliveryOrder.getOrderBizType())
                .channelDeliveryId(deliveryOrder.getChannelDeliveryId())
                .build();
    }

    @Override
    @MethodLog(logRequest = false, logResponse = true)
    @CatTransaction
    public DeliveryExceptionOrdersBySubTypeResponse queryDeliveryExceptionOrdersBySubType(QueryDeliveryExceptionOrderBySubTypeRequest request) {
        DeliveryExceptionOrdersBySubTypeResponse response = new DeliveryExceptionOrdersBySubTypeResponse();
        String requestErrorMsg = validateQueryDeliveryExceptionOrderBySubTypeRequest(request);
        if (requestErrorMsg != null) {
            response.setStatus(new Status(FailureCodeEnum.INVALID_PARAM.getCode(), requestErrorMsg));
            return response;
        }
        try {
            List<DeliveryOrder> allExceptionDeliveryOrders = queryDeliveryInfoApplicationService.queryExceptionDeliveryOrders(request.getEmpowerStoreId(),request.getTenantId());
            List<TOrderIdentifier> deliveryExceptionOrders = filterAndBuildExceptionDeliveryOrdersBySubType(allExceptionDeliveryOrders, request.getSubType());
            response.setOrders(deliveryExceptionOrders);
        } catch (Exception e) {
            log.error("QueryDeliveryInfoThriftService.queryDeliveryExceptionOrdersBySubType error", e);
            response.setStatus(new Status(SYSTEM_ERROR.getCode(), SYSTEM_ERROR.getMessage()));
        }
        return response;
    }

    @Override
    public DeliveryExceptionOrdersResponse queryDeliveryExceptionOrdersByStoreIdList(QueryDeliveryExceptionRequest request) {
        DeliveryExceptionOrdersResponse response = new DeliveryExceptionOrdersResponse();
        if (CollectionUtils.isEmpty(request.getStoreIdList())) {
            return response;
        }
        if(MccConfigUtils.getDeliveryQueryTenantSwitch()){
            try {
                List<String> viewOrderList = new ArrayList<>();
                if(StringUtils.isNotEmpty(request.getViewOrderId())){
                    viewOrderList.add(request.getViewOrderId());
                }
                Integer from = 0;
                if(request.getPage()!=null && request.getSize()!=null){
                    from = Math.max(request.getPage()-1,0)*request.getSize();
                }
                Pair<List<DeliveryOrderEsPo>, Integer> deliveryOrderEsList = deliveryOrderEsAliasDao.searchExceptionDeliveryOrdersBySubTypeAndStoreIds(null,null,null,request.getStoreIdList(),from,request.getSize(),viewOrderList);
                response.setOrders(deliveryOrderEsList.getLeft().stream().map(DeliveryOrderEsPo::transfer2TOrderIdentifier).collect(Collectors.toList()));
                response.setTotalCount(deliveryOrderEsList.getRight());
            }catch (Exception e){
                log.error("QueryDeliveryInfoThriftService.queryDeliveryExceptionOrders error", e);
                response.setStatus(new Status(SYSTEM_ERROR.getCode(), SYSTEM_ERROR.getMessage()));
            }
        }else {
            try {
                Pair<Integer, List<TOrderIdentifier>> tOrderIdentifiers = queryDeliveryInfoApplicationService.queryExceptionDeliveryOrdersByStoreList(request);
                response.setTotalCount(tOrderIdentifiers.getLeft());
                response.setOrders(tOrderIdentifiers.getRight());
            } catch (Exception e) {
                log.error("QueryDeliveryInfoThriftService.queryDeliveryExceptionOrders error", e);
                response.setStatus(new Status(SYSTEM_ERROR.getCode(), SYSTEM_ERROR.getMessage()));
            }
        }
        return response;
    }

    @Override
    public DeliveryExceptionOrdersCountResponse queryDeliveryExceptionCounts(QueryDeliveryExceptionCountRequest request) {
        DeliveryExceptionOrdersCountResponse response = new DeliveryExceptionOrdersCountResponse();
        if (CollectionUtils.isEmpty(request.getStoreIdList())) {
            return response;
        }
        if(MccConfigUtils.getDeliveryQueryTenantSwitch()){
            try {
                AtomicReference<Long> totalCount = new AtomicReference<>(0L);
                List<List<Long>> partList = com.google.common.collect.Lists.partition(request.getStoreIdList(),com.sankuai.meituan.shangou.empower.tms.delivery.utils.MccConfigUtils.esQueryRoutingStoreListMaxCount());
                partList.forEach(list->{
                    Long count = deliveryOrderEsAliasDao.countExceptionDeliveryOrdersByStoreIds(list);
                    if(count != null){
                        totalCount.updateAndGet(v -> v + count);
                    }
                });

                if(totalCount.get() != null){
                    response.setTotalCount(totalCount.get().intValue());
                }
            } catch (Exception e) {
                log.error("QueryDeliveryInfoThriftService.queryDeliveryExceptionOrders error", e);
                response.setStatus(new Status(SYSTEM_ERROR.getCode(), SYSTEM_ERROR.getMessage()));
            }
        }else {
            try {
                final Integer totalCount = queryDeliveryInfoApplicationService.queryExceptionDeliveryOrdersCount(request);
                response.setTotalCount(totalCount);
            } catch (Exception e) {
                log.error("QueryDeliveryInfoThriftService.queryDeliveryExceptionOrders error", e);
                response.setStatus(new Status(SYSTEM_ERROR.getCode(), SYSTEM_ERROR.getMessage()));
            }
        }

        return response;
    }

    private String validateQueryDeliveryExceptionOrderBySubTypeRequest(QueryDeliveryExceptionOrderBySubTypeRequest request) {
        if (request.getEmpowerStoreId() == null || request.getEmpowerStoreId() <= 0L) {
            return "赋能门店ID不合法";
        }
        if (request.getSubType() == null || DeliveryExceptionSubTypeEnum.valueOf(request.getSubType()) == null) {
            return "子类型不合法";
        }
        return null;
    }

    private List<TOrderIdentifier> filterAndBuildExceptionDeliveryOrdersBySubType(List<DeliveryOrder> allExceptionDeliveryOrders, Integer subType) {
        if (CollectionUtils.isEmpty(allExceptionDeliveryOrders)) {
            return Lists.emptyList();
        }
        DeliveryExceptionSubTypeEnum subTypeEnum = DeliveryExceptionSubTypeEnum.valueOf(subType);
        return allExceptionDeliveryOrders
                .stream()
                .filter(o ->
                        subTypeEnum.check(Optional.ofNullable(o.getExceptionType())
                                        .map(DeliveryExceptionTypeEnum::getCode)
                                        .orElse(null),
                        o.getDeliveryExceptionCode(),
                        o.getStatus().getCode())
                )
                .map(deliveryOrder -> new TOrderIdentifier(deliveryOrder.getOrderBizType(), deliveryOrder.getChannelOrderId(), deliveryOrder.getStatus(),
                        deliveryOrder.getExceptionType().getCode(), deliveryOrder.getDeliveryExceptionCode(),deliveryOrder.getDealDeadline()))
                .collect(Collectors.toList());
    }

    @Override
    @MethodLog(logRequest = false, logResponse = true)
    @CatTransaction
    public DeliveryExceptionOrderSubTypeCountResponse queryDeliveryExceptionOrderSubTypeCount(QueryDeliveryExceptionOrderRequest request) {
        DeliveryExceptionOrderSubTypeCountResponse response = new DeliveryExceptionOrderSubTypeCountResponse();
        String requestErrorMsg = request.validate();
        if (requestErrorMsg != null) {
            response.setStatus(new Status(FailureCodeEnum.INVALID_PARAM.getCode(), requestErrorMsg));
            return response;
        }
        try {
            List<DeliveryOrder> allExceptionDeliveryOrders = queryDeliveryInfoApplicationService.queryExceptionDeliveryOrders(request.getEmpowerStoreId(),request.getTenantId());

            List<DeliveryOrder> filterExceptionDeliveryOrders = allExceptionDeliveryOrders;
            if(CollectionUtils.isNotEmpty(allExceptionDeliveryOrders)){
                Map<String,Integer> orderBizMap = allExceptionDeliveryOrders.stream().collect(Collectors.toMap(DeliveryOrder::getChannelOrderId,DeliveryOrder::getOrderBizType));
                Map<String,Integer> statusMap = orderSystemClient.queryOrderStatusMap(orderBizMap);
                filterExceptionDeliveryOrders=new ArrayList<>();
                for (DeliveryOrder deliveryOrder : allExceptionDeliveryOrders){
                    if(statusMap.containsKey(deliveryOrder.getChannelOrderId()) && statusMap.get(deliveryOrder.getChannelOrderId())!=null && statusMap.get(deliveryOrder.getChannelOrderId()) == OrderStatusEnum.CANCELED.getValue()){
                        continue;
                    }
                    filterExceptionDeliveryOrders.add(deliveryOrder);
                }
            }

            countExceptionSubTypeOrderNum(response, filterExceptionDeliveryOrders);
        } catch (Exception e) {
            log.error("QueryDeliveryInfoThriftService.queryDeliveryExceptionOrderSubTypeCount error", e);
            response.setStatus(new Status(SYSTEM_ERROR.getCode(), SYSTEM_ERROR.getMessage()));
        }
        return response;
    }

    @Override
    public QueryDeliveryOrderResponse queryActiveDeliveryOrderByOrderIdList(List<Long> orderList) {
        QueryDeliveryOrderResponse response=new QueryDeliveryOrderResponse();
        if(CollectionUtils.isEmpty(orderList)){
            response.setStatus(new Status(FailureCodeEnum.INVALID_PARAM.getCode(),FailureCodeEnum.INVALID_PARAM.getMessage()));
            return response;
        }
        try {
            List<DeliveryOrder> allDeliveryOrders = deliveryOrderRepository.getDeliveryOrdersByOrderIdListSlave(orderList);
            if(CollectionUtils.isEmpty(allDeliveryOrders)){
                response.setStatus(Status.SUCCESS);
                return response;
            }
            ArrayListMultimap<Long,DeliveryOrder> multimap=ArrayListMultimap.create();
            allDeliveryOrders.forEach(w->{
                multimap.put(w.getOrderId(),w);
            });
            List<DeliveryOrder> deliveryOrderList=new ArrayList<>();
            for (Long orderId: multimap.keySet()){
                List<DeliveryOrder> list=multimap.get(orderId);
                if(CollectionUtils.isEmpty(list)){
                    continue;
                }
                Optional<DeliveryOrder> deliveryOrderOpt=list.stream().filter(DeliveryOrder::isActive).findAny();
                DeliveryOrder deliveryOrder=null;
                if(deliveryOrderOpt.isPresent()){
                    deliveryOrder=deliveryOrderOpt.get();
                }
                if(deliveryOrder==null){
                    deliveryOrderOpt=list.stream().filter(new Predicate<DeliveryOrder>() {
                        @Override
                        public boolean test(DeliveryOrder deliveryOrder) {
                            return deliveryOrder.getDeliveryCount().longValue()==deliveryOrder.getActiveStatus();
                        }
                    }).sorted(new Comparator<DeliveryOrder>() {
                        @Override
                        public int compare(DeliveryOrder o1, DeliveryOrder o2) {
                            return o2.getDeliveryCount()-o1.getDeliveryCount();
                        }
                    }).findFirst();
                    if(deliveryOrderOpt.isPresent()){
                        deliveryOrder=deliveryOrderOpt.get();
                    }
                }
                if(deliveryOrder!=null){
                    deliveryOrderList.add(deliveryOrder);
                }
            }
            response.setStatus(Status.SUCCESS);
            response.setTDeliveryOrders(buildTDeliveryOrder(deliveryOrderList));
        }catch (Exception e){
            response.setStatus(new Status(SYSTEM_ERROR.getCode(), SYSTEM_ERROR.getMessage()));
            log.error("QueryDeliveryInfoThriftService.queryActiveDeliveryOrderByOrderIdList error orderList:{}",orderList,e);
        }
        return response;
    }

    @Override
    public QueryDeliveryOperateItemResponse queryDeliveryOperateItem(QueryDeliveryOperateItemRequest request) {
        QueryDeliveryOperateItemResponse response = new QueryDeliveryOperateItemResponse();
        response.setStatus(Status.SUCCESS);
        try {
            Map<Long, DeliveryOperateItem> deliveryOperateItemMap = deliveryOperateItemService.queryOperateItem(request.getTenantId(), request.getStoreId(), request.getOrderIdList());
            response.setOperateItemMap(deliveryOperateItemMap);
        }catch (Exception e){
            response.setStatus(new Status(SYSTEM_ERROR.getCode(), SYSTEM_ERROR.getMessage()));
            log.error("queryDeliveryOperateItem error,request:{}",request);
        }

        return response;
    }

    @Override
    public DeliveryExceptionOrderNumResponse countDeliveryExceptionOrderByStoreIds(QueryDeliveryExceptionOrderByStoreIdsRequest request) {
        DeliveryExceptionOrderNumResponse response = new DeliveryExceptionOrderNumResponse();
        String check = request.validate();
        if (check != null) {
            response.setStatus(new Status(FailureCodeEnum.INVALID_PARAM.getCode(), check));
            return response;
        }

        try {
            Long count = queryDeliveryInfoApplicationService.queryExceptionDeliveryOrdersCountByStoreIds(request.getEmpowerStoreIds());
            response.setOrderNum(count.intValue());
            response.setStatus(Status.SUCCESS);
        } catch (Exception e) {
            log.error("countDeliveryExceptionOrderByStoreIds error, request is {}", request);
            response.setStatus(new Status(SYSTEM_ERROR.getCode(), SYSTEM_ERROR.getMessage()));
        }

        return response;
    }

    @Override
    public DeliveryExceptionOrderSubTypeCountResponse queryDeliveryExceptionOrderSubTypeCountByStoreIds(QueryDeliveryExceptionOrderByStoreIdsRequest request) {
        DeliveryExceptionOrderSubTypeCountResponse response = new DeliveryExceptionOrderSubTypeCountResponse();
        String requestErrorMsg = request.validate();
        if (requestErrorMsg != null) {
            response.setStatus(new Status(FailureCodeEnum.INVALID_PARAM.getCode(), requestErrorMsg));
            return response;
        }

        final List<DeliveryExceptionSubTypeEnum> deliveryExceptionSubTypeEnumList = Arrays.asList(NO_RIDER_ACCEPT,
                NO_ARRIVAL_STORE, NO_RIDER_TAKE_GOODS, DELIVERY_TIMEOUT, SYSTEM_EXCEPTION, REPORT_EXCEPTION, TAKE_EXCEPTION);
        try {
            Map<DeliveryExceptionSubTypeEnum, Integer> countMap = queryDeliveryInfoApplicationService.queryExceptionDeliveryOrdersCountBySubTypeAndStoreIds(deliveryExceptionSubTypeEnumList, request.getEmpowerStoreIds());
            if (MapUtils.isEmpty(countMap)) {
                log.warn("QueryDeliveryInfoThriftService.queryDeliveryExceptionOrderSubTypeCountByStoreIds, countMap is empty");
                response.setStatus(Status.SUCCESS);
            } else {
                response.setStatus(Status.SUCCESS);
                countExceptionSubTypeOrderNum(response, countMap);
            }
        } catch (Exception e) {
            log.error("QueryDeliveryInfoThriftService.queryDeliveryExceptionOrderSubTypeCountByStoreIds error", e);
            response.setStatus(new Status(SYSTEM_ERROR.getCode(), SYSTEM_ERROR.getMessage()));
        }

        return response;
    }

    @Override
    public DeliveryExceptionOrdersBySubTypeAndStoreIdsResponse queryDeliveryExceptionOrdersBySubTypeAndStoreIds(QueryDeliveryExceptionOrderBySubTypeAndStoreIdsRequest request) {
        DeliveryExceptionOrdersBySubTypeAndStoreIdsResponse response = new DeliveryExceptionOrdersBySubTypeAndStoreIdsResponse();
        String requestErrorMsg = request.validate();
        if (requestErrorMsg != null) {
            response.setStatus(new Status(FailureCodeEnum.INVALID_PARAM.getCode(), requestErrorMsg));
            return response;
        }

        try {
            DeliveryExceptionSubTypeEnum subTypeEnum = DeliveryExceptionSubTypeEnum.valueOf(request.getSubType());
            Pair<List<TOrderIdentifier>, Integer> searchResult = queryDeliveryInfoApplicationService.pageQueryExceptionDeliveryOrdersBySubTypeAndStoreIds(
                    subTypeEnum, request.getEmpowerStoreIds(), request.getPageNum(), request.getPageSize());
            List<TOrderIdentifier> tOrderIdentifierList = searchResult.getLeft();
            Integer total = searchResult.getRight();
            response.setOrders(tOrderIdentifierList);
            response.setStatus(Status.SUCCESS);
            response.setTPageInfo(TPageInfo.builder().pageNum(request.getPageNum()).pageSize(request.getPageSize()).total(total).build());
        } catch (Exception e) {
            log.error("queryDeliveryExceptionOrdersBySubTypeAndStoreIds error, request is {}", request);
            response.setOrders(Collections.emptyList());
            response.setStatus(new Status(SYSTEM_ERROR.getCode(), SYSTEM_ERROR.getMessage()));
            response.setTPageInfo(TPageInfo.builder().pageNum(NumberUtils.INTEGER_ZERO).pageSize(NumberUtils.INTEGER_ZERO).total(NumberUtils.INTEGER_ZERO).build());
        }

        return response;
    }

    @MethodLog(logRequest = true, logResponse = true)
    @Override
    public QueryShopDetailResponse queryShopDetail(QueryShopDetailRequest request) {
        QueryShopDetailResponse response = new QueryShopDetailResponse();
        String requestErrorMsg = request.validate();
        if (requestErrorMsg != null) {
            response.setStatus(new Status(FailureCodeEnum.INVALID_PARAM.getCode(), requestErrorMsg));
            return response;
        }

        try {
            Optional<ShopDetail> shopDetail = queryDeliveryInfoApplicationService.queryShopDetailByTenantIdAndStoreId(Long.valueOf(request.getStoreId()));
            if (!shopDetail.isPresent()) {
                response.setStatus(new Status(FailureCodeEnum.QUERY_POI_FAILED.getCode(), FailureCodeEnum.QUERY_POI_FAILED.getMessage()));
                return response;
            }
            response.setStatus(Status.SUCCESS);
            response.setShopDetails(shopDetail.get());
        } catch (Exception e) {
            log.error("QueryDeliveryInfoThriftService.queryShopDetail error", e);
            response.setStatus(new Status(SYSTEM_ERROR.getCode(), SYSTEM_ERROR.getMessage()));
        }
        return response;
    }

    @Override
    public QueryDeliveryOrderInfoResponse queryDeliveryInfo(QueryDeliveryOrderInfoRequest req) {

        QueryDeliveryOrderInfoResponse response = new QueryDeliveryOrderInfoResponse();
        String requestErrorMsg = req.validate();
        if (requestErrorMsg != null) {
            response.setStatus(new Status(FailureCodeEnum.INVALID_PARAM.getCode(), requestErrorMsg));
            return response;
        }
        try {
            List<DeliveryOrderInfo> deliveryOrderInfoList = queryDeliveryInfoApplicationService.queryDeliveryOrderByOrderIdList(req.getOrderIdList());
            if (CollectionUtils.isEmpty(deliveryOrderInfoList)) {
                return response;
            }
            Set<Integer> deliveryPlatformSet = deliveryOrderInfoList.stream().map(DeliveryOrderInfo::getDeliveryPlatformCode).collect(Collectors.toSet());
            //查询麦芽田配送详情url
            AggDeliveryPlatformConfig aggDeliveryPlatformConfig = new AggDeliveryPlatformConfig();
            if (deliveryPlatformSet.contains(DeliveryPlatformEnum.MALT_FARM_DELIVERY_PLATFORM.getCode())) {
                buildMaltAggDeliveryConfig(aggDeliveryPlatformConfig);
            }
            //查询青云配送url
            Map<String,String> urlMap=new HashMap<>();
            if (deliveryPlatformSet.contains(DeliveryPlatformEnum.DAP_DELIVERY_PLATFORM.getCode())) {
                buildDapAggDeliveryConfig(deliveryOrderInfoList, deliveryOrderInfoList.get(0).getTenantId(),urlMap);
            }
            response.setStatus(Status.SUCCESS);
            response.setDeliveryOrderInfoList(deliveryOrderInfoList);
            response.setMaltAggDeliveryPlatformConfig(aggDeliveryPlatformConfig);
            response.setDapAggDeliveryPlatformConfig(urlMap);
        } catch (Exception e) {
            log.error("查询配送详情异常",e);
            response.setStatus(new Status(SYSTEM_ERROR.getCode(), SYSTEM_ERROR.getMessage()));
        }
        return response;
    }

    @Override
    @CatTransaction
    public QueryAggDeliveryRedirectModuleResponse queryAggDeliveryRedirectModule(QueryAggDeliveryRedirectModuleRequest req) {
        try {
            Integer limitSize = MccConfigUtils.getAggRedirectConfigLimitSize();
            if (CollectionUtils.isEmpty(req.getOrderKeys())) {
                return new QueryAggDeliveryRedirectModuleResponse();
            }
            if (req.getOrderKeys().size() > limitSize) {
                return new QueryAggDeliveryRedirectModuleResponse(new Status(ResponseCodeEnum.QUERY_EXCEED_LIMIT.getValue(), "查询数量超过限制"), null);
            }

            List<TDeliveryRedirectDetail> tDeliveryRedirectDetails = aggDeliveryConfigApplicationService.queryAggDeliveryConfig(req.getOrderKeys());
            if (CollectionUtils.isEmpty(tDeliveryRedirectDetails)) {
                return QueryAggDeliveryRedirectModuleResponse.emptyResponse();
            }

            QueryAggDeliveryRedirectModuleResponse response = new QueryAggDeliveryRedirectModuleResponse();
            response.setStatus(Status.SUCCESS);
            response.setTDeliveryRedirectDetails(tDeliveryRedirectDetails);
            return response;
        } catch (Exception e) {
            log.error("QueryDeliveryInfoThriftServiceImpl queryAggDeliveryRedirectModule failed", e);
            return new QueryAggDeliveryRedirectModuleResponse(new Status(ResponseCodeEnum.FAILED.getValue(), "系统异常"), null);
        }
    }

    @Override
    public BatchQueryOriginWaybillNoResponse batchQueryOriginWaybillNo(BatchQueryOriginWaybillNoRequest req) {
        try {
            if (CollectionUtils.isEmpty(req.getOrderList())) {
                return new BatchQueryOriginWaybillNoResponse(new HashMap<>());
            }
            if (req.getOrderList().size() > MAX_SIZE) {
                throw new DeliveryBaseException("查询订单数量超过阈值500");
            }
            List<OriginWaybillInfo> originWaybillInfoList;
            if (MccConfigUtils.queryOriginWaybillNoSource()) {
                originWaybillInfoList = batchQueryOriginWaybillNoByES(req);
            } else {
                originWaybillInfoList = batchQueryOriginWaybillNoByDB(req);
            }
            Map<Long, List<OriginWaybillDto>> resultMap = new HashMap<>(req.getOrderList().size());
            for (OriginWaybillInfo originWaybillInfo : originWaybillInfoList) {
                if (!resultMap.containsKey(originWaybillInfo.getOrderId())) {
                    resultMap.put(originWaybillInfo.getOrderId(), new ArrayList<>());
                }
                List<OriginWaybillDto> dtoList = resultMap.get(originWaybillInfo.getOrderId());
                OriginWaybillDto dto = new OriginWaybillDto();
                dto.setOriginWaybillNo(originWaybillInfo.getOriginWaybillNo());
                dto.setDeliveryStatus(originWaybillInfo.getDeliveryStatus());
                dtoList.add(dto);
            }
            return new BatchQueryOriginWaybillNoResponse(resultMap);
        } catch (DeliveryBaseException e) {
            log.info("batchQueryOriginWaybillNo req: {}", req);
            log.error("batchQueryOriginWaybillNo error: ", e);
            return new BatchQueryOriginWaybillNoResponse(new HashMap<>());
        }
    }

    private List<OriginWaybillInfo> batchQueryOriginWaybillNoByES(BatchQueryOriginWaybillNoRequest req) {
        Long tenantId = req.getTenantId();
        List<Long> orderIds = new ArrayList<>(req.getOrderList().size());
        Set<Long> storeIds = new HashSet<>();
        req.getOrderList().forEach(item -> filterStoreId(item, orderIds, storeIds));
        return queryDeliveryInfoApplicationService.batchQueryOriginWaybillNoByES(orderIds, tenantId, storeIds);
    }

    private List<OriginWaybillInfo> batchQueryOriginWaybillNoByDB(BatchQueryOriginWaybillNoRequest req) {
        int size = req.getOrderList().size();
        List<OriginWaybillInfo> originWaybillInfoList = new ArrayList<>((int) (size * 1.1));
        Long tenantId = req.getTenantId();
        int limitNum = MccConfigUtils.queryOriginWaybillNoSourceDbLimit();
        List<List<QueryOrderKeyDto>> partition = com.google.common.collect.Lists.partition(req.getOrderList(), limitNum);
        partition.forEach(queryOrderKeyList->{
            List<Long> orderIds = new ArrayList<>(limitNum);
            Set<Long> storeIds = new HashSet<>();
            queryOrderKeyList.forEach(item -> filterStoreId(item, orderIds, storeIds));
            List<OriginWaybillInfo> list = queryDeliveryInfoApplicationService.batchQueryOriginWaybillNoByDB(orderIds, tenantId, storeIds);
            if (CollectionUtils.isNotEmpty(list)) {
                originWaybillInfoList.addAll(list);
            }
        });
        return originWaybillInfoList;
    }

    private static void filterStoreId(QueryOrderKeyDto item, List<Long> orderIds, Set<Long> storeIds) {
        orderIds.add(item.getOrderId());
        if (Objects.nonNull(item.getFulfillWareHouseId())) {
            storeIds.add(item.getFulfillWareHouseId());
        } else if (Objects.nonNull(item.getWarehouseId())) {
            storeIds.add(item.getWarehouseId());
        } else {
            storeIds.add(item.getShopId());
        }
    }

    private void buildMaltAggDeliveryConfig(AggDeliveryPlatformConfig aggDeliveryPlatformConfig) {
        AggDeliveryPlatformAppConfig appConfig =
                Optional.ofNullable(AggDeliveryPlatformAppConfigUtils.getAggDeliveryPlatformAppConfig(DeliveryPlatformEnum.MALT_FARM_DELIVERY_PLATFORM.getCode()))
                        .orElse(new AggDeliveryPlatformAppConfig());
        aggDeliveryPlatformConfig.setConfigUrl(appConfig.getConfigUrl());
        aggDeliveryPlatformConfig.setAppKey(appConfig.getAppKey());
        aggDeliveryPlatformConfig.setRedirectUrl(appConfig.getRedirectUrl());
        aggDeliveryPlatformConfig.setAppSecretKey(appConfig.getAppSecret());
    }

    private void buildDapAggDeliveryConfig(List<DeliveryOrderInfo> deliveryOrderInfoList, Long tenantId, Map<String,String> urlMap) {

        ArrayListMultimap<Long,String> dapOrderIdMultiMap=ArrayListMultimap.create();
        for (DeliveryOrderInfo vo : deliveryOrderInfoList){

            if (! vo.deliveryPlatformCode.equals(DeliveryPlatformEnum.DAP_DELIVERY_PLATFORM.getCode())) {
                continue;
            }
            dapOrderIdMultiMap.put(vo.storeId, vo.deliveryOrderId);
        }
        if (dapOrderIdMultiMap.isEmpty()) {
            return;
        }
        //目前都是单门店查询
        for (Long storeId : dapOrderIdMultiMap.keySet()) {
            List<String> orderIds=dapOrderIdMultiMap.get(storeId);
            Map<String, String> url = new HashMap<>();
            List<DeliveryPoi>  deliveryPoiList = deliveryPoiRepository.queryAllDeliveryPoi(tenantId, storeId);
            if (CollectionUtils.isEmpty(deliveryPoiList)) {
                continue;
            }
            Optional<DeliveryPoi> opDeliveryPoi=deliveryPoiList.stream().filter(w ->w.getChannelType() == DynamicChannelType.MEITUAN.getChannelId()).findFirst();
            DeliveryPoi deliveryPoi=null;
            if(opDeliveryPoi.isPresent()){
                deliveryPoi = opDeliveryPoi.get();
            }
            if(deliveryPoi == null){
                Optional<DeliveryPoi> opAnyDeliveryPoi = deliveryPoiList.stream().findFirst();
                if(opAnyDeliveryPoi.isPresent()){
                    deliveryPoi = opAnyDeliveryPoi.get();
                }else {
                    continue;
                }
            }
            Optional<Map<String, DeliveryPlatformUrlInfo>> mapOptional = dapDeliveryPlatformClient.queryLinkInfo(deliveryPoi, com.google.common.collect.Lists.transform(orderIds, Functions.toStringFunction()), LinkTypeEnum.ORDER_LINK_TYPE);

            if(!mapOptional.isPresent()){
                continue;
            }
            Map<String,DeliveryPlatformUrlInfo> infoMap=mapOptional.get();
            if(MapUtils.isEmpty(infoMap)){
                continue;
            }
            for (Map.Entry<String,DeliveryPlatformUrlInfo> entry : infoMap.entrySet()){
                url.put(entry.getKey(),entry.getValue().getLinkUrl());
            }
            urlMap.putAll(url);
        }
    }

    private void countExceptionSubTypeOrderNum(DeliveryExceptionOrderSubTypeCountResponse response, List<DeliveryOrder> allExceptionDeliveryOrders) {
        for (DeliveryOrder order : allExceptionDeliveryOrders) {
            DeliveryExceptionSubTypeEnum deliveryExceptionSubTypeEnum = DeliveryExceptionSubTypeEnum.deliveryStatusCodeValueOfWithOutAll(
                    Optional.ofNullable(order.getExceptionType()).map(DeliveryExceptionTypeEnum::getCode).orElse(null),
                    order.getDeliveryExceptionCode(),
                    order.getStatus().getCode());
            if (deliveryExceptionSubTypeEnum != null) {
                switch(deliveryExceptionSubTypeEnum){
                    case NO_RIDER_ACCEPT:
                        response.setNoRiderAcceptCount(response.getNoRiderAcceptCount() + 1);
                        break;
                    case NO_ARRIVAL_STORE:
                        response.setNoArrivalStoreCount(response.getNoArrivalStoreCount() + 1);
                        break;
                    case NO_RIDER_TAKE_GOODS:
                        response.setNoRiderTakeGoodsCount(response.getNoRiderTakeGoodsCount() + 1);
                        break;
                    case DELIVERY_TIMEOUT:
                        response.setDeliveryTimeoutCount(response.getDeliveryTimeoutCount() + 1);
                        break;
                    case SYSTEM_EXCEPTION:
                        response.setSystemExceptionCount(response.getSystemExceptionCount() + 1);
                        break;
                    case TAKE_EXCEPTION:
                        response.setTakeGoodsExceptionCount(response.getTakeGoodsExceptionCount() + 1);
                        break;
                    case REPORT_EXCEPTION:
                        response.setReportExceptionCount(response.getReportExceptionCount() + 1);
                        break;
                    default :
                        log.warn("订单的运单状态不在统计范围内，订单号:{}，运单状态:{}.", order.getOrderId(), order.getStatus().getCode());
                }
            }
        }
        response.setAllSubTypeCount(response.getNoRiderAcceptCount() + response.getNoArrivalStoreCount()
                + response.getNoRiderTakeGoodsCount() + response.getDeliveryTimeoutCount() + response.getSystemExceptionCount() + response.getReportExceptionCount() + response.getTakeGoodsExceptionCount());
    }

    private void countExceptionSubTypeOrderNum(DeliveryExceptionOrderSubTypeCountResponse response, Map<DeliveryExceptionSubTypeEnum, Integer> countMap) {
        countMap.forEach((deliveryExceptionSubTypeEnum, count) -> {
            switch(deliveryExceptionSubTypeEnum){
                case NO_RIDER_ACCEPT:
                    response.setNoRiderAcceptCount(count);
                    break;
                case NO_ARRIVAL_STORE:
                    response.setNoArrivalStoreCount(count);
                    break;
                case NO_RIDER_TAKE_GOODS:
                    response.setNoRiderTakeGoodsCount(count);
                    break;
                case DELIVERY_TIMEOUT:
                    response.setDeliveryTimeoutCount(count);
                    break;
                case SYSTEM_EXCEPTION:
                    response.setSystemExceptionCount(count);
                    break;
                case TAKE_EXCEPTION:
                    response.setTakeGoodsExceptionCount(count);
                    break;
                case REPORT_EXCEPTION:
                    response.setReportExceptionCount(count);
                    break;
                default :
                    log.warn("订单的运单状态不在统计范围内");
            }
        });

        response.setAllSubTypeCount(response.getNoRiderAcceptCount() + response.getNoArrivalStoreCount()
                + response.getNoRiderTakeGoodsCount() + response.getDeliveryTimeoutCount() + response.getSystemExceptionCount() + response.getReportExceptionCount() + response.getTakeGoodsExceptionCount());
    }
}
