package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.consumer;

import com.alibaba.fastjson.JSON;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrder;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrderRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.LionConfigNameEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.channel.OcmsChannelClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryChannelEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.cache.BillAllRiderPointRecordOperateService;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.cache.BillCurrentRiderPointRecordOperateService;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.cache.MqMsgIdempotentOperateService;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.client.platform.MaltFarmDeliveryPlatformClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.message.RiderPointBatchSyncMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.RiderLocationDetail;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.client.sync.RiderDeliveryOrderSyncOutClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static com.meituan.mafka.client.consumer.ConsumeStatus.CONSUME_SUCCESS;
import static com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum.MALT_FARM_DELIVERY_PLATFORM;
import static com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum.MERCHANT_SELF_DELIVERY;

/**
 * 骑手批量位置同步
 */
@Slf4j
@Component
@SuppressWarnings("all")
public class RiderPointBatchSyncMessageListener extends AbstractDeadLetterConsumer {

    @Resource
    private RiderDeliveryOrderSyncOutClient riderDeliveryOrderSyncOutClient;

    @Resource
    private BillCurrentRiderPointRecordOperateService billCurrentRiderPointRecordOperateService;

    @Resource
    private BillAllRiderPointRecordOperateService billAllRiderPointRecordOperateService;

    @Resource
    private MqMsgIdempotentOperateService mqMsgIdempotentOperateService;

    @Resource
    private DeliveryOrderRepository deliveryOrderRepository;

    @Resource
    private OcmsChannelClient ocmsChannelClient;

    @Resource
    private MaltFarmDeliveryPlatformClient maltFarmDeliveryPlatformClient;

    @Override
    protected MQConsumerEnum consumerConfig() {
        return MQConsumerEnum.BATCH_SYNC_RIDER_POINT_TO_CHANNEL_CONSUMER;
    }

    /**
     * 同步骑手位置，失败则等待下一次自动同步
     */
    @Override
    protected ConsumeStatus consume(MafkaMessage mafkaMessage) {
        try {
            log.info("开始消费批量同步骑手位置消息: {}", mafkaMessage);
            RiderPointBatchSyncMessage riderPointBatchSyncMessage = analysisAndValidParams(mafkaMessage);
            //校验参数
            if (Objects.isNull(riderPointBatchSyncMessage)) {
                log.error("消费结束，解析参数失败");
                return CONSUME_SUCCESS;
            }
            // 幂等处理
            boolean lockStatus = mqMsgIdempotentOperateService.tryLock(mafkaMessage.getMessageID(), MqMsgIdempotentOperateService.MsgIdempotentBusinessEnum.RIDER_POINT_BATCH_SYNC);
            if (!lockStatus) {
                log.info("RiderPointBatchSyncMessageListener 消息幂等加锁失败，已经被消费 msgId:{}", mafkaMessage.getMessageID());
                return CONSUME_SUCCESS;
            }

            //查看是否开启批量上传功能
            if (!riderDeliveryOrderSyncOutClient.checkOpenBatchRiderPointSync(riderPointBatchSyncMessage.getStoreId(), riderPointBatchSyncMessage.getTenantId())) {
                return CONSUME_SUCCESS;
            }

            //获取配送订单
            DeliveryOrder deliveryOrder = deliveryOrderRepository.getDeliveryOrderWithTenant(riderPointBatchSyncMessage.getDeliveryId(), riderPointBatchSyncMessage.getTenantId(), riderPointBatchSyncMessage.getStoreId());
            if (Objects.isNull(deliveryOrder)) {
                log.error("配送订单不存在 deliveryOrderId={}", riderPointBatchSyncMessage.getDeliveryId());
                return CONSUME_SUCCESS;
            }

            DeliveryChannelEnum deliveryChannelEnum = DeliveryChannelEnum.valueOf(deliveryOrder.getDeliveryChannel());
            if (Objects.isNull(deliveryChannelEnum)) {
                log.error("RiderPointBatchSyncMessageListener DeliveryChangeSyncPointMessageListener, deliveryChannelEnum is null");
                return CONSUME_SUCCESS;
            }


            Integer deliveryChannel = deliveryOrder.getDeliveryChannel();
            if (!Objects.equals(deliveryChannelEnum.getDeliveryPlatform(), MALT_FARM_DELIVERY_PLATFORM)
                    && !Objects.equals(deliveryChannelEnum.getDeliveryPlatform(), MERCHANT_SELF_DELIVERY)
            ) {
                log.error("配送订单需要商家自配送/麦芽田配送 deliveryOrderId={}，deliveryChannel={}", riderPointBatchSyncMessage.getDeliveryId(), deliveryChannel);
                return CONSUME_SUCCESS;
            }

            //查询点位结束时间，当前给麦芽田开放接口使用
            long pointSelectEndTime = System.currentTimeMillis() / 1000;
            List<RiderLocationDetail> list;
            if (Objects.equals(deliveryChannelEnum.getDeliveryPlatform(), MERCHANT_SELF_DELIVERY)) {
                //商家自配送 获取缓存中骑手位置数据
                list = billCurrentRiderPointRecordOperateService.getAllListAndRemove(riderPointBatchSyncMessage.getTenantId(),
                        riderPointBatchSyncMessage.getStoreId(),
                        riderPointBatchSyncMessage.getDeliveryId(),
                        RiderLocationDetail.class);
            } else if (Objects.equals(deliveryChannelEnum.getDeliveryPlatform(), MALT_FARM_DELIVERY_PLATFORM)) {
                list = maltFarmDeliveryPlatformClient.queryRiderPointTrace(deliveryOrder.getOrderId(), 1, riderPointBatchSyncMessage.getLastSyncEndTime(), pointSelectEndTime);
            } else {
                list = Collections.emptyList();
            }

            if (CollectionUtils.isEmpty(list)) {
                log.info("骑手位置数据查询失败！");
                //下一次同步消息
                sendNextSyncMsg(deliveryOrder, riderPointBatchSyncMessage, pointSelectEndTime);
                return CONSUME_SUCCESS;
            }

            //ocms渠道批量同步点位数据
            boolean reportSuccess = ocmsChannelClient.syncRiderBatchLocationToChanel(deliveryOrder, list);

            if (Objects.equals(deliveryChannel, DeliveryChannelEnum.MERCHANT_DELIVERY.getCode()) && !reportSuccess) {
                Integer expireInSeconds = MccConfigUtils.getLionIntConf(LionConfigNameEnum.RIDER_ALL_POINT_REDIS_EXPIRE_TIME.getName(), BillAllRiderPointRecordOperateService.expireSeconds);
                //失败情况下上传点位到所有点位缓存，等待最终同步
                billAllRiderPointRecordOperateService.addList(riderPointBatchSyncMessage.getTenantId(),
                        riderPointBatchSyncMessage.getStoreId(),
                        riderPointBatchSyncMessage.getDeliveryId(),
                        list,
                        expireInSeconds);
            }

            //下一次同步消息
            sendNextSyncMsg(deliveryOrder, riderPointBatchSyncMessage, pointSelectEndTime);
        } catch (Exception e) {
            log.error("消费批量同步骑手位置消息出现异常", e);
            mqMsgIdempotentOperateService.unLock(mafkaMessage.getMessageID(), MqMsgIdempotentOperateService.MsgIdempotentBusinessEnum.RIDER_POINT_BATCH_SYNC);
        }
        return CONSUME_SUCCESS;
    }

    /**
     * 发送下一次消息
     */
    private void sendNextSyncMsg(DeliveryOrder deliveryOrder, RiderPointBatchSyncMessage riderPointBatchSyncMessage, long pointSelectEndTime) {
        //运单状态为已接单和配送中需要再次发起下一次的消息
        DeliveryStatusEnum status = deliveryOrder.getStatus();
        if (!Objects.equals(DeliveryStatusEnum.RIDER_ASSIGNED, status)
                && !Objects.equals(DeliveryStatusEnum.RIDER_ARRIVED_SHOP, status)
                && !Objects.equals(DeliveryStatusEnum.RIDER_TAKEN_GOODS, status)
                && !Objects.equals(DeliveryStatusEnum.MERCHANT_DELIVERING, status)) {
            return;
        }

        //配送单无效不传
        Long activeStatus = deliveryOrder.getActiveStatus();
        if (!Objects.equals(activeStatus, DeliveryOrder.DELIVERY_ORDER_ACTIVE)) {
            log.error("配送订单无效 deliveryOrderId={}", riderPointBatchSyncMessage.getDeliveryId());
            return;
        }
        //发送同步消息
        riderDeliveryOrderSyncOutClient.sendRiderPointBatchSyncMsg(riderPointBatchSyncMessage.getTenantId(),
                riderPointBatchSyncMessage.getStoreId(),
                riderPointBatchSyncMessage.getDeliveryId(),
                riderPointBatchSyncMessage.getOrderId(),
                pointSelectEndTime);

    }

    /**
     * 校验参数及解析
     */
    private RiderPointBatchSyncMessage analysisAndValidParams(MafkaMessage mafkaMessage) {

        //解析参数
        RiderPointBatchSyncMessage riderPointBatchSyncMessage = Optional.ofNullable(mafkaMessage)
                .map(MafkaMessage::getBody)
                .map(Object::toString)
                .map(it -> JSON.parseObject(it, RiderPointBatchSyncMessage.class))
                .orElse(null);
        //订单ID不能为空
        if (Objects.nonNull(riderPointBatchSyncMessage)) {
            if (Objects.isNull(riderPointBatchSyncMessage.getOrderId())) {
                log.error("消费订单id为空");
                return null;
            }
            if (Objects.isNull(riderPointBatchSyncMessage.getStoreId())) {
                log.error("门店id为空");
                return null;
            }
        }
        return riderPointBatchSyncMessage;

    }


}
