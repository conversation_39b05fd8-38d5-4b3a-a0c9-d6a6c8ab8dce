package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.consumer;

import com.dianping.cat.Cat;
import com.google.common.collect.ImmutableSet;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.reco.pickselect.common.domain.orderTrack.TrackOpType;
import com.meituan.reco.pickselect.common.domain.orderTrack.TrackSource;
import com.meituan.reco.pickselect.common.mq.Dto.OrderTrackEvent;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.meituan.shangou.saas.order.platform.enums.DistributeMethodEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.AssessTimeApplicationService;
import com.meituan.shangou.saas.order.platform.enums.DistributeTypeEnum;
import com.meituan.shangou.saas.order.platform.enums.OrderGroupIdEnum;
import com.meituan.shangou.saas.tenant.thrift.common.enums.ChannelOnlineTypeEnum;
import com.sankuai.drunkhorsemgmt.labor.constants.CatEventEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.DeliveryOperationApplicationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.DeliveryProcessApplicationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.order.OrderSystemClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.monitor.DeliveryMonitorDomainService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.TimeUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryLaunchTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.PlatformSourceEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.launchpoint.BookingOrderDeliveryLaunchPointEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.launchpoint.ImmediateOrderDeliveryLaunchPointEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.configuration.TMSConstant;
import com.sankuai.meituan.shangou.empower.tms.delivery.facade.OFCRemoteService;
import com.sankuai.meituan.shangou.empower.tms.delivery.facade.PickSelectRemoteService;
import com.sankuai.meituan.shangou.empower.tms.delivery.facade.TenantRemoteService;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.DeliveryLaunchCmdMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.DeliveryLaunchCommandMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.DeliveryTimeOutCheckMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.message.PlatformDeliveryCheckMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.producer.MafkaDelayMessageProducer;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.producer.MafkaMessageProducer;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.service.DiscreetDeliveryService;
import com.sankuai.meituan.shangou.empower.tms.delivery.model.config.PickSelectStoreConfig;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.SelfBuiltDeliveryPoi;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.remind.DeliveryRemindConfigRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.facade.RouteFacade;
import com.sankuai.meituan.shangou.empower.tms.delivery.utils.OrderCheckUtil;
import com.sankuai.qnh.ofc.ofw.client.thrift.dto.order.FulfillmentOrderDTO;
import com.sankuai.qnh.ofc.ofw.common.consts.FulfillmentOrderStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import com.dianping.cat.util.MetricHelper;

import static com.meituan.mafka.client.consumer.ConsumeStatus.CONSUME_FAILURE;
import static com.meituan.mafka.client.consumer.ConsumeStatus.CONSUME_SUCCESS;
import static com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum.AGGREGATION_DELIVERY_PLATFORM;
import static com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum.MERCHANT_SELF_DELIVERY;

/**
 * 发起配送基类消费者
 * 包含配送发起时间点的检测和延时发起配送等逻辑
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/4
 */
@Slf4j
public abstract class AbstractLaunchDeliveryListener extends AbstractDeadLetterConsumer {

	public static List<Integer> FULFILL_ORDER_STATUS_INIT = Arrays.asList(FulfillmentOrderStatusEnum.INIT.getCode(),FulfillmentOrderStatusEnum.WAIT_SCHEDULING.getCode());

	public static List<Integer> FULFILL_ORDER_STATUS_CANCEL = Arrays.asList(FulfillmentOrderStatusEnum.CANCEL.getCode(),FulfillmentOrderStatusEnum.EXPIRE.getCode());

	private static final Long SYSTEM_OPERATOR_ID = 0L;
	public static final String LOCK = "lock";
	public static final String LOCK_ORDER = "lock.order";


	@Resource
	private OFCRemoteService ofcRemoteService;
	private static final Set<DeliveryPlatformEnum> SUPPORT_BOOKING_LAUNCH_PLATFORMS = ImmutableSet.of(
			AGGREGATION_DELIVERY_PLATFORM, MERCHANT_SELF_DELIVERY
	);

	@Resource
	private MafkaDelayMessageProducer<DeliveryLaunchCmdMessage> newSupplyDeliveryDelayLaunchMessageProducer;

	@Resource
	private DeliveryNotifyService deliveryNotifyService;

	@Resource
	private DeliveryOperationApplicationService deliveryOperationApplicationService;

	@Resource
	private RouteFacade routeFacade;
	@Resource
	private DiscreetDeliveryService discreetDeliveryService;
	@Resource
	private AssessTimeApplicationService assessTimeApplicationService;


	@Resource
	private MafkaMessageProducer<PlatformDeliveryCheckMessage> platformDeliveryCheckProducer;
	@Resource
	private DeliveryDimensionPoiRepository deliveryDimensionPoiRepository;


	protected abstract OrderSystemClient getOrderSystemClient();

	protected abstract DeliveryOperationApplicationService getDeliveryOperationApplicationService();

	protected abstract DeliveryProcessApplicationService getDeliveryProcessApplicationService();

	protected abstract MafkaDelayMessageProducer<DeliveryLaunchCommandMessage> getDeliveryDelayLaunchMessageProducer();

	protected abstract MafkaDelayMessageProducer<DeliveryTimeOutCheckMessage> getDeliveryTimeOutCheckProducer();

	protected abstract DeliveryMonitorDomainService getDeliveryMonitorDomainService();

	protected abstract DeliveryPoiRepository getDeliveryPoiRepository();

	protected abstract DeliveryOrderRepository getDeliveryOrderRepository();

	protected abstract DeliveryRemindConfigRepository getDeliveryRemindConfigRepository();

	protected abstract PickSelectRemoteService getPickSelectRemoteService();

	protected abstract TenantRemoteService getTenantRemoteService();

	@SuppressWarnings("rawtypes")
	protected ConsumeStatus tryLaunchDelivery(MafkaMessage mafkaMessage,
											  OrderKey orderKey,
											  ImmediateOrderDeliveryLaunchPointEnum immediateOrderTriggerPoint,
											  BookingOrderDeliveryLaunchPointEnum bookingOrderTriggerPoint) {

		// 查询订单信息，并获知订单是否为预约单
		Result<OrderInfo> orderInfoQueryResult = getOrderSystemClient().getOrderInfo(orderKey, false);
		if (orderInfoQueryResult.isFail()) {
			log.warn("查询订单[{}]失败，等待重试. failure:{}", orderKey, orderInfoQueryResult.getFailure());
			return CONSUME_FAILURE;
		}
		OrderInfo orderInfo = orderInfoQueryResult.getInfo();

		DynamicOrderBizType dynamicOrderBizType = DynamicOrderBizType.findOf(orderInfo.getOrderBizType());

		//pos渠道判断过滤
		if(Objects.nonNull(dynamicOrderBizType)
				&& ChannelOnlineTypeEnum.isOfflinePos(dynamicOrderBizType.getChannelStandard())){
			//线下pos订单与团购订单不发配送
			if(OrderGroupIdEnum.getByGroupId(orderInfo.getGroupId()) == OrderGroupIdEnum.OFFLINE_ORDER
					|| OrderGroupIdEnum.getByGroupId(orderInfo.getGroupId()) == OrderGroupIdEnum.GROUP_PURCHASE_ORDER){

				return CONSUME_SUCCESS;

			}

		}


		triggerPlatformDeliveryCheck(orderInfo,immediateOrderTriggerPoint);
		boolean isMedicineUW = getTenantRemoteService().isMedicineUnmannedWarehouse(orderKey.getTenantId());

		Long shopId = orderKey.getStoreId();
		if(orderInfo.getOrderTransInfo()!=null && orderInfo.getOrderTransInfo().getDispatchShopId()!=null){
			shopId = orderInfo.getOrderTransInfo().getDispatchShopId();
		}

		Optional<DeliveryPoi> opDeliveryPoi = Optional.empty();
		DynamicOrderBizType orderBizType = ObjectUtils.defaultIfNull(DynamicOrderBizType.findOf(
				orderInfo.getOrderBizType()), DynamicOrderBizType.MEITUAN_WAIMAI);
		opDeliveryPoi = getDeliveryPoiRepository().queryDeliveryPoiWithDefault(orderKey.getTenantId(), shopId, orderBizType.getChannelId());

		if (!opDeliveryPoi.isPresent()) {
			log.warn("门店[{}]未开通任何配送平台, 将会放弃发起配送.", orderKey.getStoreId());
			return CONSUME_SUCCESS;
		}
		DeliveryPoi deliveryPoi = opDeliveryPoi.get();

		if (!OrderCheckUtil.isLockOrderV2(orderInfo, deliveryPoi)){
			// 不是锁单2.0 走1.0流程
			if (OrderCheckUtil.isLockedOrder(orderInfo, isMedicineUW)) {
				if (orderInfo.getIsLocked() != null && orderInfo.getIsLocked()) {
					log.info("当前订单处于锁单状态，暂不发起配送。租户：{}，门店：{}",orderInfo.getOrderKey().getTenantId(),orderInfo.getOrderKey().getStoreId());
					Cat.logEvent(LOCK, LOCK_ORDER);
					return CONSUME_SUCCESS;
				}
				if (orderInfo.getIsLocked() != null && !orderInfo.getIsLocked()) {
					Optional<Failure> failure = deliveryOperationApplicationService.paoTuiLockStatusNotify(orderInfo);
					if (failure.isPresent()) {
						return CONSUME_FAILURE;
					}
					return CONSUME_SUCCESS;
				}
			}
		}

		PlatformSourceEnum platformSourceEnum = PlatformSourceEnum.OMS;
		Long fulfillOrderId = null;
		if(checkTrans(deliveryPoi,immediateOrderTriggerPoint,bookingOrderTriggerPoint,isMedicineUW)){
			List<FulfillmentOrderDTO> fulfillmentOrderDTOList = new ArrayList<>();
			if(com.sankuai.meituan.shangou.empower.tms.delivery.utils.MccConfigUtils.getOfcRemoteSwitch()){
				fulfillmentOrderDTOList = ofcRemoteService.getOfcOrder(deliveryPoi.getTenantId(),deliveryPoi.getStoreId(),orderInfo.getOrderKey().getOrderId());
			}
			if(CollectionUtils.isNotEmpty(fulfillmentOrderDTOList)){
				FulfillmentOrderDTO dto = fulfillmentOrderDTOList.get(0);
				if(FULFILL_ORDER_STATUS_INIT.contains(dto.getStatus())){
					return CONSUME_SUCCESS;
				}
				if(!FULFILL_ORDER_STATUS_CANCEL.contains(dto.getStatus())){
					platformSourceEnum = PlatformSourceEnum.OFC;
					fulfillOrderId = dto.getFulfillmentOrderId();
				}
			}
		}

		DeliveryDimensionPoi deliveryDimensionPoi = null;
		if (com.sankuai.meituan.shangou.dms.base.utils.MccConfigUtils.getFusionGaryV2(orderKey.getTenantId(), orderKey.getStoreId())) {
			deliveryDimensionPoi = deliveryDimensionPoiRepository.queryDeliveryDimensionPoi(orderKey.getTenantId(), orderKey.getStoreId());
		}


		//渠道降级暂不发配送
		if (OrderCheckUtil.isDownOrder(orderInfo, isMedicineUW, deliveryPoi)) {
			log.info("订单{}降级，暂不发起配送",orderInfo.getOrderKey().getOrderId());
			return CONSUME_SUCCESS;
		}

		// 判断是否非自动发起
		if (deliveryPoi.getDeliveryLaunchType() != DeliveryLaunchTypeEnum.AUTO_LAUNCH_DELIVERY) {
			log.info("门店[{}]配置为非自动发起，因此放弃自动发起配送", orderKey.getStoreId());
			return CONSUME_SUCCESS;
		}

		//判断是否已经在配送中
		if(orderInfo.isInDelivery() && StringUtils.isNotBlank(orderInfo.getRiderName())){
			log.info("订单[{}]已经在配送中，因此放弃自动发起配送", orderKey.getOrderId());
			return CONSUME_SUCCESS;
		}

		//业务线为医药无人仓 商品名称替换货号,门店白名单开关若开启,则进行再一次过滤。

		if(isMedicineUW){
			discreetDeliveryService.replaceMedicineGoodsCode2SkuName(orderKey, orderInfo, getOrderSystemClient(), orderInfo.getWarehouseId());
		}else {
			discreetDeliveryService.replaceNotMedicineUnmannedGoodsCode2SkuName(orderKey, orderInfo, getOrderSystemClient(), orderInfo.getWarehouseId());
		}
		// 如果是预约单，并且是支持预约配送的平台，进行预约发配送
		if (orderInfo.isBookingOrder() && SUPPORT_BOOKING_LAUNCH_PLATFORMS.contains(deliveryPoi.getDeliveryPlatform())) {
			if (com.sankuai.meituan.shangou.dms.base.utils.MccConfigUtils.getFusionGaryV2(orderKey.getTenantId(), orderKey.getStoreId())) {
				return tryLaunchDelivery4BookingOrderFusion(
						deliveryPoi,deliveryDimensionPoi,orderInfo, bookingOrderTriggerPoint, !isLastRetry(mafkaMessage),
						platformSourceEnum,fulfillOrderId
				);
			} else {
				return tryLaunchDelivery4BookingOrder(
						deliveryPoi, orderInfo, bookingOrderTriggerPoint, !isLastRetry(mafkaMessage),
						platformSourceEnum,fulfillOrderId
				);
			}

		} else if (checkIfLaunchDelivery4BookingOrder(orderInfo, deliveryPoi)) {
			return tryLaunchDelivery4NewSupplyBookingOrder(
					deliveryPoi, orderInfo, bookingOrderTriggerPoint, !isLastRetry(mafkaMessage),
					platformSourceEnum,fulfillOrderId
			);
		} else {
			//实时单发配送
			return tryLaunchDelivery4ImmediateOrder(
					deliveryPoi, deliveryDimensionPoi, orderInfo, immediateOrderTriggerPoint, !isLastRetry(mafkaMessage),
					platformSourceEnum,fulfillOrderId
			);
		}
	}

	/**
	 * 新供给租户 && 订单是预订单 && 订单渠道是抖音渠道 && 配送方式是平台配送，返回true
	*/
	private boolean checkIfLaunchDelivery4BookingOrder(OrderInfo orderInfo, DeliveryPoi deliveryPoi) {

		DynamicOrderBizType orderBizTypeEnum = DynamicOrderBizType.findOf(orderInfo.getOrderBizType());
		if (Objects.isNull(orderBizTypeEnum)) {
			return false;
		}

		DeliveryPlatformEnum deliveryPlatformEnum = deliveryPoi.getDeliveryPlatform();
		if (Objects.isNull(deliveryPlatformEnum)) {
			return false;
		}

		return orderInfo.isBookingOrder() && DynamicOrderBizType.DOU_YIN.equals(orderBizTypeEnum)
				&& DeliveryPlatformEnum.ORDER_CHANNEL_DELIVERY_PLATFORM.equals(deliveryPlatformEnum);
	}

	private boolean checkTrans(DeliveryPoi deliveryPoi,ImmediateOrderDeliveryLaunchPointEnum immediateOrderTriggerPoint,
							   BookingOrderDeliveryLaunchPointEnum bookingOrderTriggerPoint,boolean isMedicineUW){
		if(isMedicineUW){
			return false;
		}
		if(MccConfigUtils.checkIsDHTenant(deliveryPoi.getTenantId())){
			return false;
		}

		if(immediateOrderTriggerPoint == ImmediateOrderDeliveryLaunchPointEnum.PICK_DONE && deliveryPoi.getDeliveryLaunchPoint().getImmediateOrderDeliveryLaunchPointConfig().getLaunchPoint()==ImmediateOrderDeliveryLaunchPointEnum.PICK_DONE
				|| bookingOrderTriggerPoint == BookingOrderDeliveryLaunchPointEnum.PICK_DONE && deliveryPoi.getDeliveryLaunchPoint().getBookingOrderDeliveryLaunchPointConfig().getLaunchPoint() == BookingOrderDeliveryLaunchPointEnum.PICK_DONE){
			return true;
		}
		return false;
	}

	/**
	 * 校验触发节点是否应该跳出
	 * 场景：
	 * 1.订单状态和门店配置一致则直接发配送（开关关闭会直接进入这个分支）
	 * 2.订单状态为接单时，门店配置为用户支付完成发配送，但是没有运单，则需要补偿发配送
	 * 3.订单状态为接单时，门店配置为用户支付完成发配送，并且有运单，则需要跳出不需要继续
	 * 4.订单状态为拣货完成时，门店配置为接单发配送，并且没有运单，则需要补偿发配送
	 * @param deliveryPoi
	 * @param orderInfo
	 * @param immediateOrderTriggerPoint
	 * @return
	 */
	private boolean checkTriggerPointShouldBreak(DeliveryPoi deliveryPoi,
													OrderInfo orderInfo,
													ImmediateOrderDeliveryLaunchPointEnum immediateOrderTriggerPoint){
		//场景4
		if(immediateOrderTriggerPoint==ImmediateOrderDeliveryLaunchPointEnum.PICK_DONE
				&& deliveryPoi.getDeliveryLaunchPoint().getImmediateOrderDeliveryLaunchPointConfig().getLaunchPoint() != ImmediateOrderDeliveryLaunchPointEnum.PICK_DONE){
			DeliveryOrderRepository orderRepository=getDeliveryOrderRepository();
			if(orderRepository!=null) {
				List<DeliveryOrder> orderList = orderRepository.getDeliveryOrdersAllWithOrderId(orderInfo.getOrderKey().getOrderId());
				if (CollectionUtils.isEmpty(orderList)) {
					log.info("当前实时单触发点[{}]，门店配置的实时单触发点[{}]，无生成运单需要做补偿",
							immediateOrderTriggerPoint, deliveryPoi.getDeliveryLaunchPoint().getImmediateOrderDeliveryLaunchPointConfig().getLaunchPoint());
					return false;
				}
			}
		}


		if(immediateOrderTriggerPoint==ImmediateOrderDeliveryLaunchPointEnum.MERCHANT_ACCEPT
				&& ImmediateOrderDeliveryLaunchPointEnum.ORDER_PAID==deliveryPoi.getDeliveryLaunchPoint().getImmediateOrderDeliveryLaunchPointConfig().getLaunchPoint()
				&& com.sankuai.meituan.shangou.empower.tms.delivery.utils.MccConfigUtils.getOrderPaidLaunchDeliverySwitch()==1){
			//补偿作用，只针对没有运单的情况进行补偿
			//场景2和3
			DeliveryOrderRepository orderRepository=getDeliveryOrderRepository();
			if(orderRepository!=null){
				List<DeliveryOrder> orderList= orderRepository.getDeliveryOrdersAllWithOrderId(orderInfo.getOrderKey().getOrderId());
				if(CollectionUtils.isNotEmpty(orderList)){
					log.info("当前实时单触发点[{}]，门店配置的实时单触发点[{}]，已生成运单不做补偿",
							immediateOrderTriggerPoint, deliveryPoi.getDeliveryLaunchPoint().getImmediateOrderDeliveryLaunchPointConfig().getLaunchPoint());
					//场景3 有运单不进行补偿
					return true;
				}
			}
		}else if (immediateOrderTriggerPoint != deliveryPoi.getDeliveryLaunchPoint().getImmediateOrderDeliveryLaunchPointConfig().getLaunchPoint()) {
			// 配送触发时间点校验
			log.info("当前实时单触发点[{}]，不满足门店配置的实时单触发点[{}]，将会放弃本次触发",
					immediateOrderTriggerPoint, deliveryPoi.getDeliveryLaunchPoint().getImmediateOrderDeliveryLaunchPointConfig().getLaunchPoint());
			return true;
		}
		return false;
	}

	private boolean checkTriggerPointShouldBreakByBooking(DeliveryPoi deliveryPoi,
														  OrderInfo orderInfo,
														  BookingOrderDeliveryLaunchPointEnum bookingOrderDeliveryLaunchPointEnum){
		//场景4
		if(bookingOrderDeliveryLaunchPointEnum==BookingOrderDeliveryLaunchPointEnum.PICK_DONE
				&& deliveryPoi.getDeliveryLaunchPoint().getImmediateOrderDeliveryLaunchPointConfig().getLaunchPoint() != ImmediateOrderDeliveryLaunchPointEnum.PICK_DONE){
			DeliveryOrderRepository orderRepository=getDeliveryOrderRepository();
			if(orderRepository!=null) {
				List<DeliveryOrder> orderList = orderRepository.getDeliveryOrders(orderInfo.getOrderKey());
				if (CollectionUtils.isEmpty(orderList)) {
					log.info("当前预约单触发点[{}]，门店配置的触发点[{}]，无生成运单需要做补偿",
							bookingOrderDeliveryLaunchPointEnum, deliveryPoi.getDeliveryLaunchPoint().getImmediateOrderDeliveryLaunchPointConfig().getLaunchPoint());
					return false;
				}
			}
		}


		if (bookingOrderDeliveryLaunchPointEnum.bookingToImmediate() != deliveryPoi.getDeliveryLaunchPoint().getImmediateOrderDeliveryLaunchPointConfig().getLaunchPoint()) {
			// 配送触发时间点校验
			log.info("当前预约单触发点[{}]，不满足门店配置的触发点[{}]，将会放弃本次触发",
					bookingOrderDeliveryLaunchPointEnum, deliveryPoi.getDeliveryLaunchPoint().getImmediateOrderDeliveryLaunchPointConfig().getLaunchPoint());
			return true;
		}
		return false;
	}

	private ConsumeStatus tryLaunchDelivery4ImmediateOrder(DeliveryPoi deliveryPoi,
														   DeliveryDimensionPoi deliveryDimensionPoi,
	                                                       OrderInfo orderInfo,
	                                                       ImmediateOrderDeliveryLaunchPointEnum immediateOrderTriggerPoint,
														   boolean acceptRetry,PlatformSourceEnum platformSourceEnum,Long fulfillOrderId) {
		//发单节点校验
		if(checkTriggerPointShouldBreak(deliveryPoi,orderInfo,immediateOrderTriggerPoint)){
			return CONSUME_SUCCESS;
		}

		// 实时配送
		if (deliveryPoi.getDeliveryLaunchPoint().getImmediateOrderDeliveryLaunchPointConfig().getDelayMinutes() == 0) {
			Optional<Failure> failure;
			switch (deliveryPoi.getDeliveryPlatform()) {
				case SELF_BUILT_DELIVERY_PLATFORM:
					SelfBuiltDeliveryPoi selfBuiltDeliveryPoi = (SelfBuiltDeliveryPoi) deliveryPoi;
					if (!selfBuiltDeliveryPoi.canLaunchDelivery()) {
						log.warn("门店[{}]在自建平台没有可用的配送渠道，将放弃发起配送", selfBuiltDeliveryPoi.getStoreId());
						return CONSUME_SUCCESS;
					}

					failure = getDeliveryProcessApplicationService().launchDelivery(
							orderInfo.getOrderKey(), (SelfBuiltDeliveryPoi) deliveryPoi, acceptRetry, false, false
					);
					break;
				case AGGREGATION_DELIVERY_PLATFORM:
				case MALT_FARM_DELIVERY_PLATFORM:
				case MERCHANT_SELF_DELIVERY:
				case DAP_DELIVERY_PLATFORM:
				case ORDER_CHANNEL_DELIVERY_PLATFORM:
					failure = getDeliveryOperationApplicationService().launchDelivery(deliveryPoi, orderInfo, acceptRetry,platformSourceEnum,fulfillOrderId);
					break;

				default:
					log.warn("不支持的配送平台[{}], 将放弃发起配送", deliveryPoi.getDeliveryPlatform());
					return CONSUME_SUCCESS;
			}

			if (isDyPlatformDelivery(deliveryPoi, orderInfo)) {
				sendOrderTrack(deliveryPoi.getTenantId(), orderInfo.getChannelOrderId(), orderInfo.getOrderBizType(), SYSTEM_OPERATOR_ID, !failure.isPresent());
			}

			if (failure.isPresent() && failure.get().isNeedRetry()) {
				return CONSUME_FAILURE;
			}

		} else {
			// 延时配送
			if (isDyPlatformDelivery(deliveryPoi, orderInfo)) {
				// 新供给抖音渠道平台配送的订单走新的延迟mq发送
				DeliveryLaunchCmdMessage launchCmdMessage = new DeliveryLaunchCmdMessage(orderInfo);
				if (platformSourceEnum != null) {
					launchCmdMessage.setPlatformSource(platformSourceEnum.getCode());
				}
				launchCmdMessage.setFulfillOrderId(fulfillOrderId);
				newSupplyDeliveryDelayLaunchMessageProducer.sendDelayMessage(launchCmdMessage, deliveryPoi.getDeliveryLaunchPoint().getImmediateOrderDeliveryLaunchPointConfig().getDelayMinutes());
			} else {
				DeliveryLaunchCommandMessage launchCommandMessage = new DeliveryLaunchCommandMessage(orderInfo);
				if(platformSourceEnum!=null){
					launchCommandMessage.setPlatformSource(platformSourceEnum.getCode());
				}
				launchCommandMessage.setFulfillOrderId(fulfillOrderId);
				getDeliveryDelayLaunchMessageProducer().sendDelayMessage(launchCommandMessage, deliveryPoi.getDeliveryLaunchPoint().getImmediateOrderDeliveryLaunchPointConfig().getDelayMinutes());
			}
		}

		//超时监控
		if (com.sankuai.meituan.shangou.dms.base.utils.MccConfigUtils.getFusionGaryV2(orderInfo.getOrderKey().getTenantId(), orderInfo.getOrderKey().getStoreId())) {
			selfTriggerDeliveryTimeOutCheck(orderInfo, deliveryPoi, deliveryDimensionPoi,
					TimeUnit.MINUTES.toMillis(deliveryPoi.getDeliveryLaunchPoint().getImmediateOrderDeliveryLaunchPointConfig().getDelayMinutes()));
		} else {
			originalTriggerDeliveryTimeOutCheck(orderInfo, deliveryPoi,
					TimeUnit.MINUTES.toMillis(deliveryPoi.getDeliveryLaunchPoint().getImmediateOrderDeliveryLaunchPointConfig().getDelayMinutes()));
		}

		reportLaunchDeliveryMonitor(orderInfo);

		return CONSUME_SUCCESS;
	}

	/**
	 * 对预约单进行自动发单.
	 *
	 * @param bookingOrderTriggerPoint 预约单触发时间点
	 * @param deliveryPoi              配送规则（针对聚合运力平台配送）
	 * @param orderInfo                预约单信息
	 * @return 消息消费结果
	 */
	@Deprecated
	private ConsumeStatus tryLaunchDelivery4BookingOrder(DeliveryPoi deliveryPoi,
	                                                     OrderInfo orderInfo,
	                                                     BookingOrderDeliveryLaunchPointEnum bookingOrderTriggerPoint,
	                                                     boolean acceptRetry,PlatformSourceEnum platformSourceEnum,Long fulfillOrderId) {
		// 配送触发时间点校验
		LocalDateTime estimatedDeliveryTime = orderInfo.getEstimatedDeliveryTime();
		if(!MccConfigUtils.getDHTenantIdList().contains(deliveryPoi.getTenantId()+"")){
			//新供给节点为实时单节点
			if(orderInfo.getEstimatedDeliveryEndTime()!=null){
				estimatedDeliveryTime = orderInfo.getEstimatedDeliveryEndTime();
			}
			if (checkTriggerPointShouldBreakByBooking(deliveryPoi,orderInfo,bookingOrderTriggerPoint)) {
				log.info("当前预约单触发点[{}]，不满足门店配置的触发点[{}]，将会放弃本次触发",
						bookingOrderTriggerPoint, deliveryPoi.getDeliveryLaunchPoint().getImmediateOrderDeliveryLaunchPointConfig().getLaunchPoint());
				return CONSUME_SUCCESS;
			}

		}else {
			if (bookingOrderTriggerPoint != deliveryPoi.getDeliveryLaunchPoint().getBookingOrderDeliveryLaunchPointConfig().getLaunchPoint()) {
				log.info("当前预约单触发点[{}]，不满足门店配置的预约单触发点[{}]，将会放弃本次触发",
						bookingOrderTriggerPoint, deliveryPoi.getDeliveryLaunchPoint().getBookingOrderDeliveryLaunchPointConfig().getLaunchPoint());
				return CONSUME_SUCCESS;
			}
		}

		if (Objects.isNull(estimatedDeliveryTime)) {
			log.warn("预约单[{}]预计送达时间无效，将放弃发起配送", orderInfo);
			return CONSUME_SUCCESS;
		}

		long delayMillis = 0L;
		if(MccConfigUtils.isPreOrderGrayStore(orderInfo.getPoiId())) {
			delayMillis = calculateBookingOrderDelayMillisWithAssessTime(
					deliveryPoi,
					orderInfo,
					bookingOrderTriggerPoint,
					deliveryPoi.getDeliveryLaunchPoint().getBookingOrderDeliveryLaunchPointConfig().getConfigMinutes(),
					estimatedDeliveryTime
			);
		} else {
			delayMillis = calculateBookingOrderDelayMillis(
					bookingOrderTriggerPoint,
					deliveryPoi.getDeliveryLaunchPoint().getBookingOrderDeliveryLaunchPointConfig().getConfigMinutes(),
					estimatedDeliveryTime
			);
		}


		if (delayMillis >= TMSConstant.MAFKA_DELAY_MESSAGE_MILLIS_MIN) {
			// 发起毫秒级别延时消息
			DeliveryLaunchCommandMessage launchCommandMessage = new DeliveryLaunchCommandMessage(orderInfo);
			if(platformSourceEnum!=null){
				launchCommandMessage.setPlatformSource(platformSourceEnum.getCode());
			}
			launchCommandMessage.setFulfillOrderId(fulfillOrderId);
			getDeliveryDelayLaunchMessageProducer().sendDelayMessageInMillis(launchCommandMessage, delayMillis);
		} else {
			// 延时消息最小支持 5000 ms，小于 5000 时，不需要延时发配送
			Optional<Failure> failure = getDeliveryOperationApplicationService().launchDelivery(deliveryPoi, orderInfo, acceptRetry,platformSourceEnum,fulfillOrderId);
			if (failure.isPresent() && failure.get().isNeedRetry()) {
				return CONSUME_FAILURE;
			}
		}

		//超时监控
		originalTriggerDeliveryTimeOutCheck(orderInfo, deliveryPoi, delayMillis);

		return CONSUME_SUCCESS;
	}

	/**
	 * 对预约单进行自动发单.(歪赋逻辑融合)
	 *
	 * @param bookingOrderTriggerPoint 预约单触发时间点
	 * @param deliveryPoi              配送规则（针对聚合运力平台配送）
	 * @param orderInfo                预约单信息
	 * @return 消息消费结果
	 */
	private ConsumeStatus tryLaunchDelivery4BookingOrderFusion(DeliveryPoi deliveryPoi,
														 DeliveryDimensionPoi deliveryDimensionPoi,
														 OrderInfo orderInfo,
														 BookingOrderDeliveryLaunchPointEnum bookingOrderTriggerPoint,
														 boolean acceptRetry,PlatformSourceEnum platformSourceEnum,Long fulfillOrderId) {
		// 配送触发时间点校验
		LocalDateTime estimatedDeliveryTime = orderInfo.getEstimatedDeliveryEndTime();
		// 发配节点为实时单节点
		if (checkTriggerPointShouldBreakByBooking(deliveryPoi,orderInfo,bookingOrderTriggerPoint)) {
			log.info("当前预约单触发点[{}]，不满足门店配置的触发点[{}]，将会放弃本次触发",
					bookingOrderTriggerPoint, deliveryPoi.getDeliveryLaunchPoint().getImmediateOrderDeliveryLaunchPointConfig().getLaunchPoint());
			return CONSUME_SUCCESS;
		}

		if (Objects.isNull(estimatedDeliveryTime)) {
			log.warn("预约单[{}]预计送达时间无效，将放弃发起配送", orderInfo);
			return CONSUME_SUCCESS;
		}

		long delayMillis = 0L;
		if(StringUtils.isBlank(deliveryPoi.getSelfDeliveryBookingConfig())){
			PickSelectStoreConfig pickSelectStoreConfig = getPickSelectRemoteService().queryFulfillStoreConfig(
					deliveryPoi.getTenantId(), deliveryPoi.getStoreId(), orderInfo.getOrderBizType());
			Integer pushTime=pickSelectStoreConfig ==null || pickSelectStoreConfig.getBookingPushDownTime()==null ? 40:pickSelectStoreConfig.getBookingPushDownTime();
			delayMillis = calculateBookingOrderDelayMillis4NewSupply(pushTime,estimatedDeliveryTime);
		}else {
			delayMillis = calculateBookingOrderDelayMillisWithAssessTime(
					deliveryPoi,
					orderInfo,
					bookingOrderTriggerPoint,
					deliveryPoi.getDeliveryLaunchPoint().getBookingOrderDeliveryLaunchPointConfig().getConfigMinutes(),
					estimatedDeliveryTime
			);
		}

		if (delayMillis >= TMSConstant.MAFKA_DELAY_MESSAGE_MILLIS_MIN) {
			// 发起毫秒级别延时消息
			DeliveryLaunchCommandMessage launchCommandMessage = new DeliveryLaunchCommandMessage(orderInfo);
			if(platformSourceEnum!=null){
				launchCommandMessage.setPlatformSource(platformSourceEnum.getCode());
			}
			launchCommandMessage.setFulfillOrderId(fulfillOrderId);
			getDeliveryDelayLaunchMessageProducer().sendDelayMessageInMillis(launchCommandMessage, delayMillis);
		} else {
			// 延时消息最小支持 5000 ms，小于 5000 时，不需要延时发配送
			Optional<Failure> failure = getDeliveryOperationApplicationService().launchDelivery(deliveryPoi, orderInfo, acceptRetry,platformSourceEnum,fulfillOrderId);
			if (failure.isPresent() && failure.get().isNeedRetry()) {
				return CONSUME_FAILURE;
			}
		}

		//超时监控
		selfTriggerDeliveryTimeOutCheck(orderInfo, deliveryPoi, deliveryDimensionPoi, delayMillis);

		return CONSUME_SUCCESS;
	}

	/**
	 * 发新供给预订单
	 *
	 * @param bookingOrderTriggerPoint 预约单触发时间点
	 * @param deliveryPoi              配送规则（针对聚合运力平台配送）
	 * @param orderInfo                预约单信息
	 * @return 消息消费结果
	 */
	private ConsumeStatus tryLaunchDelivery4NewSupplyBookingOrder(DeliveryPoi deliveryPoi,
														 OrderInfo orderInfo,
														 BookingOrderDeliveryLaunchPointEnum bookingOrderTriggerPoint,
														 boolean acceptRetry,PlatformSourceEnum platformSourceEnum,Long fulfillOrderId) {
		// 配送触发时间点校验
		LocalDateTime estimatedDeliveryTime = orderInfo.getEstimatedDeliveryTime();
		if (orderInfo.getEstimatedDeliveryEndTime() != null) {
			estimatedDeliveryTime = orderInfo.getEstimatedDeliveryEndTime();
		}
		if (checkTriggerPointShouldBreakByBooking(deliveryPoi,orderInfo,bookingOrderTriggerPoint)) {
			log.info("当前预约单触发点[{}]，不满足门店配置的触发点[{}]，将会放弃本次触发",
					bookingOrderTriggerPoint, deliveryPoi.getDeliveryLaunchPoint().getImmediateOrderDeliveryLaunchPointConfig().getLaunchPoint());
			return CONSUME_SUCCESS;
		}

		if (Objects.isNull(estimatedDeliveryTime)) {
			log.warn("预约单[{}]预计送达时间无效，将放弃发起配送", orderInfo);
			return CONSUME_SUCCESS;
		}

		long delayMillis = calculateBookingOrderDelayMillis4NewSupply(deliveryPoi.getDeliveryLaunchPoint().getBookingOrderDeliveryLaunchPointConfig().getConfigMinutes(), estimatedDeliveryTime);
		if (delayMillis >= TMSConstant.MAFKA_DELAY_MESSAGE_MILLIS_MIN) {
			// 发起毫秒级别延时消息
			DeliveryLaunchCmdMessage launchCmdMessage = new DeliveryLaunchCmdMessage(orderInfo);
			if (platformSourceEnum != null) {
				launchCmdMessage.setPlatformSource(platformSourceEnum.getCode());
			}
			launchCmdMessage.setFulfillOrderId(fulfillOrderId);
			newSupplyDeliveryDelayLaunchMessageProducer.sendDelayMessageInMillis(launchCmdMessage, delayMillis);
		} else {
			// 延时消息最小支持 5000 ms，小于 5000 时，不需要延时发配送
			Optional<Failure> failure = getDeliveryOperationApplicationService().launchDelivery(deliveryPoi, orderInfo, acceptRetry,platformSourceEnum,fulfillOrderId);
			if (failure.isPresent() && failure.get().isNeedRetry()) {
				return CONSUME_FAILURE;
			}
		}

		//超时监控
		originalTriggerDeliveryTimeOutCheck(orderInfo, deliveryPoi, delayMillis);

		return CONSUME_SUCCESS;
	}

	private long calculateBookingOrderDelayMillis(BookingOrderDeliveryLaunchPointEnum bookingOrderTriggerPoint, Integer minutes,
	                                              LocalDateTime estimatedDeliveryTime) {
		long delayMillis = 0L;
		if (bookingOrderTriggerPoint == BookingOrderDeliveryLaunchPointEnum.BEFORE_DELIVERY) {
			// 预计送达前 minutes 分钟发起配送
			LocalDateTime launchDeliveryTime = estimatedDeliveryTime.minusMinutes(minutes.longValue());
			Duration duration = Duration.between(LocalDateTime.now(), launchDeliveryTime);
			delayMillis = duration.toMillis();
		} else {
			// 拣货完成后 minutes 分钟发起配送
			delayMillis = TimeUnit.MINUTES.toMillis(minutes.longValue());
		}
		return delayMillis;
	}

	private long calculateBookingOrderDelayMillisWithAssessTime(DeliveryPoi deliveryPoi, OrderInfo orderInfo, BookingOrderDeliveryLaunchPointEnum bookingOrderTriggerPoint, Integer minutes,
																LocalDateTime estimatedDeliveryTime) {
		try {
			Long timestamp = assessTimeApplicationService.calcPushDownTimestamp(orderInfo.getOrderKey().getTenantId(), orderInfo.getOrderKey().getStoreId(), orderInfo, deliveryPoi.getSelfDeliveryBookingConfig(), null);
			if (Objects.nonNull(timestamp) && timestamp > 0) {
				LocalDateTime pushTime = TimeUtil.fromMilliSeconds(timestamp);
				Duration duration = Duration.between(LocalDateTime.now(), pushTime);
				return duration.toMillis();
			}
			return calculateBookingOrderDelayMillis(bookingOrderTriggerPoint, minutes, estimatedDeliveryTime);
		} catch (Exception e) {
			log.error("calculateBookingOrderDelayMillisWithAssessTime error", e);
			Cat.logEvent("ASSESS_TIME", "PRE_ORDER_ERROR");
			return calculateBookingOrderDelayMillis(bookingOrderTriggerPoint, minutes, estimatedDeliveryTime);
		}
	}

	private long calculateBookingOrderDelayMillis4NewSupply(Integer minutes,
												  LocalDateTime estimatedDeliveryTime) {
		if(minutes == null || minutes<=0){
			return 0L;
		}
		// 预计送达前 minutes 分钟发起配送
		LocalDateTime launchDeliveryTime = estimatedDeliveryTime.minusMinutes(minutes.longValue());
		Duration duration = Duration.between(LocalDateTime.now(), launchDeliveryTime);
		long delayMillis = duration.toMillis();
		return delayMillis;
	}

	private void selfTriggerDeliveryTimeOutCheck(OrderInfo orderInfo, DeliveryPoi deliveryPoi, DeliveryDimensionPoi deliveryDimensionPoi, long launchDeliveryDelayMillis) {
		//如果整体没初始化就走老的
		if (Objects.isNull(deliveryDimensionPoi)) {
			originalTriggerDeliveryTimeOutCheck(orderInfo, deliveryPoi, launchDeliveryDelayMillis);
			return;
		}
		if (launchDeliveryDelayMillis <= 0) {
			launchDeliveryDelayMillis = 0;
		}
		if (Objects.equals(orderInfo.getDeliveryMethod(), DistributeMethodEnum.STORE_DELIVERY.getValue())) {
			log.info("自提订单无需触发超时监控");
			return;
		}
		//歪马的自提单超时预警不需要报
		if (orderInfo.isPullNewSelfPickGoodsOrder()) {
			log.info("歪马推广自提订单无需触发超时监控");
			return;
		}

		LocalDateTime now = LocalDateTime.now();
		// 自营配送（含歪马）新增任务领取超时、取货超时提醒
		if (deliveryPoi.getDeliveryPlatform() == MERCHANT_SELF_DELIVERY && MccConfigUtils.publishDeliveryTimeOutCheckRiderAssignedMsg()) {
			if (Objects.nonNull(deliveryDimensionPoi.getDeliveryRemindConfig())) {
				DeliveryRemindConfig deliveryRemindConfig = deliveryDimensionPoi.getDeliveryRemindConfig();
				if (Objects.nonNull(deliveryRemindConfig.getReceiveTimeOutMins()) && deliveryRemindConfig.getReceiveTimeOutMins() >=0) {
					long checkRiderAssignedDelayMillis = launchDeliveryDelayMillis + TimeUnit.MINUTES.toMillis(deliveryRemindConfig.getReceiveTimeOutMins());
					delayOrTriggerDeliveryTimeOutMonitoring(checkRiderAssignedDelayMillis, orderInfo, DeliveryTimeOutCheckTypeEnum.MERCHANT_SELF_DELIVERY_CHECK_RIDER_ASSIGNED);
				}

				if (Objects.nonNull(deliveryRemindConfig.getTakenTimeOutMins()) &&  deliveryRemindConfig.getTakenTimeOutMins() >= 0) {
					long checkRiderTakenGoodsMillis = launchDeliveryDelayMillis + TimeUnit.MINUTES.toMillis(deliveryRemindConfig.getTakenTimeOutMins());
					delayOrTriggerDeliveryTimeOutMonitoring(checkRiderTakenGoodsMillis, orderInfo, DeliveryTimeOutCheckTypeEnum.MERCHANT_SELF_DELIVERY_CHECK_RIDER_TAKEN_GOODS);
				}

				if (Objects.nonNull(deliveryRemindConfig.getSoonDeliveryTimeoutMinsBeforeEta()) && deliveryRemindConfig.getSoonDeliveryTimeoutMinsBeforeEta() > 0) {
					long pushDeliveryTimeoutWarningDelayMills;
					pushDeliveryTimeoutWarningDelayMills = Duration.between(
							now,
							orderInfo.getEstimatedDeliveryTime().plusMinutes(-deliveryRemindConfig.getSoonDeliveryTimeoutMinsBeforeEta())
					).toMillis();
					delayOrTriggerDeliveryTimeOutMonitoring(pushDeliveryTimeoutWarningDelayMills, orderInfo, DeliveryTimeOutCheckTypeEnum.MERCHANT_SELF_DELIVERY_CHECK_DELIVERY_WILL_TIMEOUT);
				}
			}
		}

		//歪马超时订单进行超时发券和推送大象提醒，暂不合并
		if (MccConfigUtils.getDHTenantIdList().contains(deliveryPoi.getTenantId().toString())) {
			long delayMillis4Rider;
			long delayMillis4Customer;
			if (orderInfo.isBookingOrder()) {
				delayMillis4Rider = Duration.between(
						now,
						orderInfo.getEstimatedDeliveryEndTime().plusMinutes(MccConfigUtils.getBookingOrderDeliveryTimeOutDuration2B())
				).toMillis();

				delayMillis4Customer = Duration.between(
						now,
						orderInfo.getEstimatedDeliveryEndTime().plusMinutes(MccConfigUtils.getBookingOrderDeliveryTimeOutDuration2C())
				).toMillis();
			} else {
				delayMillis4Rider = Duration.between(
						now,
						orderInfo.getEstimatedDeliveryEndTime().plusMinutes(MccConfigUtils.getInTimeOrderDeliveryTimeOutDuration2B())
				).toMillis();

				delayMillis4Customer = Duration.between(
						now,
						orderInfo.getEstimatedDeliveryEndTime().plusMinutes(MccConfigUtils.getInTimeOrderDeliveryTimeOutDuration2C())
				).toMillis();
			}
			//推大象，暂不合并
			delayOrTriggerDeliveryTimeOutMonitoring(delayMillis4Rider, orderInfo, DeliveryTimeOutCheckTypeEnum.CHECK_DELIVERY_DONE_4_RIDER);
			//超时发券，暂不合并
			delayOrTriggerDeliveryTimeOutMonitoring(delayMillis4Customer, orderInfo, DeliveryTimeOutCheckTypeEnum.CHECK_DELIVERY_DONE_4_CUS);
		}

	}


	private void originalTriggerDeliveryTimeOutCheck(OrderInfo orderInfo, DeliveryPoi deliveryPoi, long launchDeliveryDelayMillis) {
		if (launchDeliveryDelayMillis <= 0) {
			launchDeliveryDelayMillis = 0;
		}
		if (Objects.equals(orderInfo.getDeliveryMethod(), DistributeMethodEnum.STORE_DELIVERY.getValue())) {
			log.info("自提订单无需触发超时监控");
			return;
		}
		if (orderInfo.isPullNewSelfPickGoodsOrder()) {
			log.info("歪马推广自提订单无需触发超时监控");
			return;
		}

		LocalDateTime now = LocalDateTime.now();
		// 自营配送（含歪马）新增任务领取超时、取货超时提醒
		if (deliveryPoi.getDeliveryPlatform() == MERCHANT_SELF_DELIVERY && MccConfigUtils.publishDeliveryTimeOutCheckRiderAssignedMsg()) {
			long checkRiderAssignedDelayMillis = launchDeliveryDelayMillis + TimeUnit.MINUTES.toMillis(MccConfigUtils.getDHRiderAssignTimeoutMinutes());
			if(!MccConfigUtils.getDHTenantIdList().contains(deliveryPoi.getTenantId().toString())){
				checkRiderAssignedDelayMillis = launchDeliveryDelayMillis + TimeUnit.MINUTES.toMillis(MccConfigUtils.getRiderAssignTimeoutMinutes());
			}
			delayOrTriggerDeliveryTimeOutMonitoring(checkRiderAssignedDelayMillis, orderInfo, DeliveryTimeOutCheckTypeEnum.MERCHANT_SELF_DELIVERY_CHECK_RIDER_ASSIGNED);

			long checkRiderTakenGoodsMillis = launchDeliveryDelayMillis + TimeUnit.MINUTES.toMillis(MccConfigUtils.getDHRiderTakenGoodsTimeoutMinutes());
			if(!MccConfigUtils.getDHTenantIdList().contains(deliveryPoi.getTenantId().toString())){
				checkRiderTakenGoodsMillis = launchDeliveryDelayMillis + TimeUnit.MINUTES.toMillis(MccConfigUtils.getRiderTakenGoodsTimeoutMinutes());
			}
			delayOrTriggerDeliveryTimeOutMonitoring(checkRiderTakenGoodsMillis, orderInfo, DeliveryTimeOutCheckTypeEnum.MERCHANT_SELF_DELIVERY_CHECK_RIDER_TAKEN_GOODS);
		}

		//歪马超时订单进行超时发券和推送大象提醒
		if (MccConfigUtils.getDHTenantIdList().contains(deliveryPoi.getTenantId().toString())) {
			long pushDeliveryTimeoutWarningDelayMills;
			pushDeliveryTimeoutWarningDelayMills = Duration.between(
					now,
					orderInfo.getEstimatedDeliveryTime().plusMinutes(-MccConfigUtils.getPushDeliveryTimeoutWarningTime())
			).toMillis();

			long delayMillis4Rider;
			long delayMillis4Customer;
			if (orderInfo.isBookingOrder()) {
				delayMillis4Rider = Duration.between(
						now,
						orderInfo.getEstimatedDeliveryEndTime().plusMinutes(MccConfigUtils.getBookingOrderDeliveryTimeOutDuration2B())
				).toMillis();

				delayMillis4Customer = Duration.between(
						now,
						orderInfo.getEstimatedDeliveryEndTime().plusMinutes(MccConfigUtils.getBookingOrderDeliveryTimeOutDuration2C())
				).toMillis();
			} else {
				delayMillis4Rider = Duration.between(
						now,
						orderInfo.getEstimatedDeliveryEndTime().plusMinutes(MccConfigUtils.getInTimeOrderDeliveryTimeOutDuration2B())
				).toMillis();

				delayMillis4Customer = Duration.between(
						now,
						orderInfo.getEstimatedDeliveryEndTime().plusMinutes(MccConfigUtils.getInTimeOrderDeliveryTimeOutDuration2C())
				).toMillis();
			}

			delayOrTriggerDeliveryTimeOutMonitoring(delayMillis4Rider, orderInfo, DeliveryTimeOutCheckTypeEnum.CHECK_DELIVERY_DONE_4_RIDER);
			delayOrTriggerDeliveryTimeOutMonitoring(delayMillis4Customer, orderInfo, DeliveryTimeOutCheckTypeEnum.CHECK_DELIVERY_DONE_4_CUS);
			delayOrTriggerDeliveryTimeOutMonitoring(pushDeliveryTimeoutWarningDelayMills, orderInfo, DeliveryTimeOutCheckTypeEnum.MERCHANT_SELF_DELIVERY_CHECK_DELIVERY_WILL_TIMEOUT);
		}

	}

	private void delayOrTriggerDeliveryTimeOutMonitoring(long checkDelayMillis, OrderInfo orderInfo, DeliveryTimeOutCheckTypeEnum deliveryTimeOutCheckTypeEnum) {
		if (checkDelayMillis > TMSConstant.MAFKA_DELAY_MESSAGE_MILLIS_MIN) {
			getDeliveryTimeOutCheckProducer().sendDelayMessageInMillis(
					new DeliveryTimeOutCheckMessage(orderInfo.getOrderKey(), deliveryTimeOutCheckTypeEnum), checkDelayMillis);
		} else {
			getDeliveryMonitorDomainService().triggerDeliveryTimeOutMonitoring(orderInfo, deliveryTimeOutCheckTypeEnum);
		}
	}

	/**
	 * 判断是否是新供给的抖音平台配送
	*/
	private boolean isDyPlatformDelivery(DeliveryPoi deliveryPoi, OrderInfo orderInfo) {
		return DynamicOrderBizType.DOU_YIN.equals(DynamicOrderBizType.findOf(orderInfo.getOrderBizType()))
				&& DeliveryPlatformEnum.ORDER_CHANNEL_DELIVERY_PLATFORM.equals(deliveryPoi.getDeliveryPlatform());
	}

	private void sendOrderTrack(Long tenantId, String channelOrderId, Integer orderBizType, Long operatorId, boolean isSuccess) {
		OrderTrackEvent event = new OrderTrackEvent();
		event.setTrackSource(TrackSource.DELIVERY.getType());
		event.setTrackOpType(isSuccess ? TrackOpType.AUTO_LAUNCH_DELIVERY_SUCCESS.getOpType() : TrackOpType.AUTO_LAUNCH_DELIVERY_FAIL.getOpType());
		event.setAccountIdList(Collections.singletonList(operatorId));
		event.setOperateTime(System.currentTimeMillis());
		event.setTenantId(tenantId);
		event.setUnifyOrderId(channelOrderId);
		event.setOrderBizType(orderBizType);

		deliveryNotifyService.notifyDeliveryTrace(event);
	}



	private void reportLaunchDeliveryMonitor (OrderInfo orderInfo) {
		try {
			if (!orderInfo.isDeliveryToHome()) {
				return;
			}
			if (orderInfo.getOrderBizType().equals(DynamicOrderBizType.MEITUAN_WAIMAI.getValue()) && orderInfo.getBrandId() != null && orderInfo.getBrandId() > 0) {
				MetricHelper.build().name("qnh_launch_delivery")
						.tag("physicalBrand", String.valueOf(orderInfo.getBrandId()))
						.count();
				MetricHelper.build().name("qnh_launch_delivery_time")
						.tag("physicalBrand", String.valueOf(orderInfo.getBrandId()))
						.duration(Duration.between(orderInfo.getMerchantConfirmOrderTime(), LocalDateTime.now()).getSeconds());
			}
			if (MccConfigUtils.needReportWithTenantId(orderInfo.getOrderKey().getTenantId())) {
				MetricHelper.build().name("qnh_fn_launch_delivery")
						.tag("tenantId", String.valueOf(orderInfo.getOrderKey().getTenantId()))
						.count();
				MetricHelper.build().name("qnh_fn_launch_delivery_time")
						.tag("tenantId", String.valueOf(orderInfo.getOrderKey().getTenantId()))
						.duration(Duration.between(orderInfo.getMerchantConfirmOrderTime(), LocalDateTime.now()).getSeconds());
			}
		} catch (Exception e) {
			log.error("上报物理品牌监控失败", e);
		}

	}

	private void triggerPlatformDeliveryCheck(OrderInfo orderInfo,ImmediateOrderDeliveryLaunchPointEnum immediateOrderTriggerPoint){
		try{
			if(immediateOrderTriggerPoint != ImmediateOrderDeliveryLaunchPointEnum.MERCHANT_ACCEPT){
				return;
			}
			if(orderInfo == null || orderInfo.isSelfDelivery()){
				return;
			}
			if(orderInfo.getOriginalDistributeType() == null || (!com.sankuai.meituan.shangou.empower.tms.delivery.utils.MccConfigUtils.getFourWheelDistributeCodeList().contains(""+orderInfo.getOriginalDistributeType()))){
				return;
			}
			PlatformDeliveryCheckMessage checkMessage = new PlatformDeliveryCheckMessage();
			checkMessage.setTenantId(orderInfo.getOrderKey().getTenantId());
			checkMessage.setStoreId(orderInfo.getPoiId());
			checkMessage.setOrderId(orderInfo.getOrderKey().getOrderId());
			checkMessage.setViewOrderId(orderInfo.getChannelOrderId());
			checkMessage.setOrderBizType(orderInfo.getOrderBizType());
			platformDeliveryCheckProducer.sendMessage(checkMessage,checkMessage.getOrderId());
		}catch (Exception e){
			log.error("triggerPlatformDeliveryCheck error",e);
		}

	}
}
