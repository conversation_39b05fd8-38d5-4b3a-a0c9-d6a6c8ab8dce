package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.client.channel;

import com.dianping.cat.Cat;
import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.google.common.collect.Sets;
import com.meituan.reco.pickselect.common.constants.ResultCode;
import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.meituan.shangou.saas.order.platform.enums.OrderSourceEnum;
import com.sankuai.meituan.shangou.dms.base.model.value.CoordinatePoint;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.delivery.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.ChannelDeliveryDockingThriftService;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.FarmPaoTuiInnerThriftService;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.delivery.ChangeDeliveryPlatformRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.UpdateDeliveryInfoRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.UpdateDeliveryProofPhotoRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poishipping.PoiShippingInfoDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poishipping.QueryPoiShippingRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poishipping.QueryPoiShippingResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelDeliverySyncThriftService;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelOrderDockingThriftService;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelPoiShippingThriftService;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.QnhOrderDockingThriftService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrder;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderKey;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.channel.OcmsChannelClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.order.OrderSystemClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryAsyncOutTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.PlatformSourceEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.RiderLocationSyncToChannelMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.producer.MafkaMessageProducer;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.service.OcmsChannelService;
import com.sankuai.meituan.shangou.empower.tms.delivery.model.RiderInfoChangeBo;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Result;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Rider;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.RiderDeliveryExtInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.RiderDeliveryOrder;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.RiderLocationDetail;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.client.sync.DeliveryChangeSyncOutMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.enums.AuditStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.task.exception.CommonRuntimeException;
import com.sankuai.meituan.shangou.empower.tms.delivery.utils.RetryTemplateUtil;
import com.sankuai.meituan.shangou.saas.common.cat.CatTransaction;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import com.sankuai.shangou.commons.exception.common.exceptions.SystemException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.context.annotation.Lazy;
import org.springframework.retry.RetryCallback;
import org.springframework.retry.RetryContext;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 订单系统客户端实现
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/1/21
 */
@Slf4j
@Service
@Rhino
public class OcmsChannelClientImpl implements OcmsChannelClient {

	//需要处理的消息类型
	private Set<Integer> msgType = Sets.newHashSet(DeliveryAsyncOutTypeEnum.LOCK_DELIVERY_STATUS.getValue(),
			DeliveryAsyncOutTypeEnum.UNLOCK_DELIVERY_STATUS.getValue());

	private static final int SUCCESS = 0;
	@Resource
	private ChannelOrderDockingThriftService.Iface channelOrderDockingThriftServiceClient;
	@Resource
	private QnhOrderDockingThriftService.Iface qnhOrderDockingThriftServiceClient;
	@Resource
	@Lazy
	private OrderSystemClient orderSystemClient;

	@Resource
	private FarmPaoTuiInnerThriftService farmPaoTuiInnerThriftService;

	@Resource
	private MafkaMessageProducer<RiderLocationSyncToChannelMessage> riderLocationSyncToChannelProducer;

	@Resource
	private ChannelPoiShippingThriftService.Iface channelPoiShippingService;
	@Resource
	private ChannelDeliverySyncThriftService.Iface channelDeliverySyncThriftService;

	@Resource
	private OcmsChannelService ocmsChannelService;

	@Resource
	private ChannelDeliveryDockingThriftService channelDeliveryDockingThriftService;

	@Override
	// @MethodLog
	@CatTransaction
	@Degrade(rhinoKey = "syncRiderLocation", fallBackMethod = "syncRiderLocationFallback", timeoutInMilliseconds = 10000)
	public void syncRiderLocation(DeliveryOrder deliveryOrder, CoordinatePoint riderLocationPoint) {
		log.info("run syncRiderLocation");
		try {
			if (Objects.equals(deliveryOrder.getOrderSource(), OrderSourceEnum.GLORY.getValue())) {
				syncRiderLocationForQnhOrder(deliveryOrder, riderLocationPoint);
			} else {
				syncRiderLocationForEmpowerOrder(deliveryOrder, riderLocationPoint);
			}
		} catch (Exception e) {
			log.error("syncRiderLocation error", e);
		}

	}

	@Override
	// @MethodLog
	@Degrade(rhinoKey = "syncDrunkHorseRiderLocation", fallBackMethod = "syncDrunkHorseRiderLocationFallback", timeoutInMilliseconds = 1000)
	public void syncDrunkHorseRiderLocation(DeliveryOrder deliveryOrder, CoordinatePoint riderLocationPoint) {
		log.info("run syncDrunkHorseRiderLocation");
		try {
			syncRiderLocationForEmpowerOrder(deliveryOrder, riderLocationPoint);
		} catch (Exception e) {
			log.error("syncRiderLocation error", e);
		}
	}

	/**
	 * 通用接口，用于同步运单变更信息给ocms-channel，不建议上层业务直接调用，而是通过
	 * com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.consumer
	 * .MQConsumerEnum#DELIVERY_CHANGE_ASYNC_OUT发送消息，并通过DeliveryChangeSyncOutMessageListener异步调用
	 * @param syncOutMessage
	 */
	@Override
	public void syncDeliveryChange(DeliveryChangeSyncOutMessage syncOutMessage) {
		//目前只需要同步msgType包含的消息类型
		if (!msgType.contains(syncOutMessage.getChangeType())) {
			return;
		}
		try {
			UpdateDeliveryInfoRequest request = this.buildUpdateDeliverReq(syncOutMessage);
			channelOrderDockingThriftServiceClient.updateDeliveryInfo(request);
		} catch (IllegalArgumentException e) {
			//参数异常不重试
			log.error("channelOrderDockingThriftServiceClient.updateDeliveryInfo argument illegal, msg:{}", e.getMessage(), e);
			Cat.logError(e);
		} catch (TException e) {
			throw new RuntimeException("channelOrderDockingThriftServiceClient.updateDeliveryInfo rpc error", e);
		}
	}

	@Override
	@MethodLog(logRequest = false, logResponse = true)
	@Degrade(rhinoKey = "queryPoiShippingInfo", fallBackMethod = "queryPoiShippingInfoFallback", timeoutInMilliseconds = 1500)
	public List<PoiShippingInfoDTO> queryPoiShippingInfo(Long tenantId, Long storeId, Integer channelId) throws TException {
		QueryPoiShippingRequest request = new QueryPoiShippingRequest(tenantId, storeId, channelId);
		QueryPoiShippingResponse response = channelPoiShippingService.queryPoiShipping(request);
		if (response == null || response.getCode() != ResultCode.SUCCESS.getCode()) {
			log.error("invoke channelPoiShippingService.queryPoiShipping fail, request:{}, response:{}", request, response);
			throw new BizException("查询渠道门店配送范围失败");
		}

		if (CollectionUtils.isEmpty(response.getPoiShippingInfoDTOList())) {
			log.warn("invoke channelPoiShippingService.queryPoiShipping, no valid poiShipping, request:{}, response:{}", request, response);
			Cat.logEvent("QUERY_PIO_SHIPPING_INFO", "NO_DELIVERY_REGION");
			return Collections.emptyList();
		}

		return response.getPoiShippingInfoDTOList();
	}

	@Override
	@Degrade(rhinoKey = "updateDeliveryProofPhoto", fallBackMethod = "updateDeliveryProofPhotoFallback", timeoutInMilliseconds = 2000)
	public void updateDeliveryProofPhoto(RiderDeliveryOrder deliveryOrder, CoordinatePoint coordinatePoint) throws TException {
		DynamicChannelType channelType = DynamicOrderBizType.orderBizTypeValue2ChannelType(deliveryOrder.getCustomerOrderKey().getOrderBizType());
		if (Objects.isNull(channelType)) {
			throw new RuntimeException("不识别的渠道");
		}
		UpdateDeliveryProofPhotoRequest request = new UpdateDeliveryProofPhotoRequest();
		request.setTenantId(deliveryOrder.getTenantId());
		request.setStoreId(deliveryOrder.getStoreId());
		request.setChannelId(channelType.getChannelId());
		request.setOrderId(deliveryOrder.getCustomerOrderKey().getChannelOrderId());
		request.setDeliveryOrderId(deliveryOrder.getId());
		request.setRiderPhone(deliveryOrder.getRiderInfo().getRiderPhone());
		request.setRiderName(deliveryOrder.getRiderInfo().getRiderName());
		request.setDeliveryChannelId(deliveryOrder.getDeliveryChannel());

		List<String> photoUrls = Optional.ofNullable(deliveryOrder.getRiderDeliveryExtInfo())
				.map(RiderDeliveryExtInfo::getDeliveryProofPhotoInfoList)
				.orElse(Collections.emptyList())
				.stream().filter(photo -> Objects.equals(photo.getAuditStatusEnum(), AuditStatusEnum.PASS_AUDIT))
				.map(RiderDeliveryExtInfo.DeliveryProofPhotoInfo::getPhotoUrl)
				.collect(Collectors.toList());

		request.setPhotoUrlList(photoUrls);
		if (Objects.nonNull(coordinatePoint)) {
			request.setLongitude(Double.parseDouble(coordinatePoint.getLongitude()));
			request.setLatitude(Double.parseDouble(coordinatePoint.getLatitude()));
		}

		ResultStatus response = channelOrderDockingThriftServiceClient.updateDeliveryProofPhoto(request);
		log.info("invoke channelOrderDockingThriftServiceClient.updateDeliveryProofPhoto end, request:{}, response:{}", request, response);
		if (response == null) {
			log.error("向渠道更新送达照片失败,失败原因: response为null");
			throw new CommonRuntimeException("向渠道更新送达照片失败");
		}
		if (response.getCode() != ResultCode.SUCCESS.getCode()) {
			log.error("向渠道更新送达照片失败,失败原因:{}", response.getMsg());
			throw new BizException("向渠道更新送达照片失败");
		}
	}

	public void updateDeliveryProofPhotoFallback(RiderDeliveryOrder deliveryOrder, CoordinatePoint coordinatePoint) {
		log.warn("channelOrderDockingThriftServiceClient.updateDeliveryProofPhoto 接口降级");
	}

	@Override
	@Degrade(rhinoKey = "syncDeliveryPlatformChange", fallBackMethod = "syncDeliveryPlatformChangeFallback", timeoutInMilliseconds = 3000)
	public void syncDeliveryPlatformChange(Long tenantId, Long storeId, String channelOrderId, int orderBizType, DeliveryPlatformEnum deliveryPlatformEnum) throws TException {
		DynamicChannelType channelType = DynamicOrderBizType.orderBizTypeValue2ChannelType(orderBizType);
		if (Objects.isNull(channelType)) {
			throw new RuntimeException("不识别的渠道");
		}
		ChangeDeliveryPlatformRequest request = new ChangeDeliveryPlatformRequest();
		request.setTenantId(tenantId);
		request.setShopId(storeId);
		request.setChannelId(channelType.getChannelId());
		request.setChannelOrderId(channelOrderId);
		request.setDeliveryPlatform(deliveryPlatformEnum.getCode());
		ResultStatus response = channelDeliverySyncThriftService.syncDeliveryPlatformChange(request);
		log.info("invoke channelDeliverySyncThriftService.syncDeliveryPlatformChange end, request:{}, response:{}", request, response);
		if (response == null) {
			log.error("向渠道同步配送平台变更失败,失败原因: response为null");
			throw new BizException("向渠道同步配送平台变更失败");
		}
		if (response.getCode() != ResultCode.SUCCESS.getCode()) {
			log.error("向渠道同步配送平台变更失败,失败原因:{}", response.getMsg());
			throw new BizException("向渠道同步配送平台变更失败");
		}
	}

	private void syncDeliveryPlatformChangeFallback(Long tenantId, Long storeId, String channelOrderId, int orderBizType, DeliveryPlatformEnum deliveryPlatformEnum) {
		log.warn("channelOrderDockingThriftServiceClient.syncDeliveryPlatformChange 接口降级");
	}

	private void syncRiderLocationForEmpowerOrder(DeliveryOrder deliveryOrder, CoordinatePoint riderLocationPoint) throws TException {
		//指定租户走异步同步流程
		if (MccConfigUtils.isSyncByMQTenant(deliveryOrder.getTenantId())) {
			RiderLocationSyncToChannelMessage message = buildSyncToChannelMessage(deliveryOrder, riderLocationPoint);
			riderLocationSyncToChannelProducer.sendMessage(message);
			return;
		}

		UpdateDeliveryInfoRequest req = buildUpdateDeliverReq(deliveryOrder, riderLocationPoint);
		log.info("syncRiderLocation for empower order, {}", req);
		ResultStatus response = updateChannelRiderInfo(req);
		if (Objects.isNull(response) || response.getCode() != SUCCESS) {
			log.error("updateRiderInfo for empower order error, {}", response);
		}
	}

	private ResultStatus updateChannelRiderInfo(UpdateDeliveryInfoRequest req) throws TException {
		if(!MccConfigUtils.openChannelDeliveryDockingThriftService()){
			return channelOrderDockingThriftServiceClient.updateRiderInfo(req);
		}
		// 切换到新thriftService
		RiderUpdateResponse riderUpdateResponse = channelDeliveryDockingThriftService.updateRiderInfo(convertUpdateRiderRequest(req));
		return convertRiderUpdateResponse(riderUpdateResponse);
	}


	public UpdateChannelDeliveryInfoRequest convertUpdateRiderRequest(UpdateDeliveryInfoRequest request) {
		UpdateChannelDeliveryInfoRequest updateChannelDeliveryInfoRequest = new UpdateChannelDeliveryInfoRequest();
		updateChannelDeliveryInfoRequest.setTenantId(request.getTenantId());
		updateChannelDeliveryInfoRequest.setChannelId(request.getChannelId());
		updateChannelDeliveryInfoRequest.setShopId(request.getShopId());
		updateChannelDeliveryInfoRequest.setOrderId(request.getOrderId());
		updateChannelDeliveryInfoRequest.setRiderName(request.getRiderName());
		updateChannelDeliveryInfoRequest.setRiderPhone(request.getRiderPhone());
		updateChannelDeliveryInfoRequest.setStatus(request.getStatus());
		updateChannelDeliveryInfoRequest.setLatitude(request.getLatitude());
		updateChannelDeliveryInfoRequest.setLongitude(request.getLongitude());
		updateChannelDeliveryInfoRequest.setDeliveryChannelId(request.getDeliveryChannelId());
		updateChannelDeliveryInfoRequest.setOnlySyncRiderPosition(request.isOnlySyncRiderPosition());
		updateChannelDeliveryInfoRequest.setDeliveryOrderId(request.getDeliveryOrderId());
		updateChannelDeliveryInfoRequest.setChangeType(request.getChangeType());
		updateChannelDeliveryInfoRequest.setExtJson(request.getExtJson());
		updateChannelDeliveryInfoRequest.setOrderBizType(request.getOrderBizType());
		updateChannelDeliveryInfoRequest.setCancelReason(request.getCancelReason());
		updateChannelDeliveryInfoRequest.setCancelReasonCode(request.getCancelReasonCode());
		return updateChannelDeliveryInfoRequest;
	}

	public ResultStatus convertRiderUpdateResponse(RiderUpdateResponse riderUpdateResponse) {
		ResultStatus resultStatus = new ResultStatus();
		resultStatus.setCode(riderUpdateResponse.getCode());
		resultStatus.setMsg(riderUpdateResponse.getMsg());
		resultStatus.setData(riderUpdateResponse.getData());
		return resultStatus;
	}

	@Override
	@Degrade(rhinoKey = "onlySyncRiderInfo", fallBackMethod = "onlySyncRiderInfoFallback", timeoutInMilliseconds = 2000)
	// @MethodLog(logResponse = true, logRequest = false)
	public void onlySyncRiderInfo(DeliveryOrder deliveryOrder) {

		UpdateDeliveryInfoRequest req = buildUpdateDeliverReq(deliveryOrder, null);
		log.info("syncRiderInfo start, request: {}", req);
		ResultStatus response;
		try {
			response = channelOrderDockingThriftServiceClient.onlyUpdateRiderInfo(req);
		} catch (Exception e) {
			log.error("syncRiderInfo error", e);
			throw new SystemException("同步骑手信息发生错误");
		}

		log.info("syncRiderInfo end, response: {}", response);

		if (Objects.isNull(response) || response.getCode() != SUCCESS) {
			log.error("syncRiderInfo fail, response: {}", response);
			throw new BizException("同步骑手信息失败");
		}
	}

	public void onlySyncRiderInfoFallback(DeliveryOrder deliveryOrder) {
		log.info("onlySyncRiderInfo 已被降级");
	}

	private void syncRiderLocationForQnhOrder(DeliveryOrder deliveryOrder, CoordinatePoint riderLocationPoint) throws TException {
		UpdateDeliveryInfoRequest req = buildUpdateDeliverReq(deliveryOrder, riderLocationPoint);
		log.info("syncRiderLocation for qnh order, {}", req);
		ResultStatus response = qnhOrderDockingThriftServiceClient.updateDeliveryInfo(req);
		if (Objects.isNull(response) || response.getCode() != SUCCESS) {
			log.error("updateDeliveryInfo for qnh order error, {}", response);
		}
	}

	private void syncRiderLocationFallback(DeliveryOrder deliveryOrder, CoordinatePoint riderLocationPoint) {
		log.warn("同步骑手已降级");
	}

	public List<PoiShippingInfoDTO> queryPoiShippingInfoFallback(Long tenantId, Long storeId, Integer channelId) {
		log.warn("查询渠道门店配送信息接口已降级");
		return Collections.emptyList();
	}

	private UpdateDeliveryInfoRequest buildUpdateDeliverReq(DeliveryChangeSyncOutMessage syncOutMessage) {
		DynamicChannelType channelType = DynamicOrderBizType.orderBizTypeValue2ChannelType(
				syncOutMessage.getHead().getOrderBizType());
		if (Objects.isNull(channelType)) {
			throw new IllegalArgumentException("不识别的渠道");
		}
		UpdateDeliveryInfoRequest req = new UpdateDeliveryInfoRequest();
		req.setTenantId(syncOutMessage.getHead().getTenantId());
		req.setChannelId(channelType.getChannelId());
		//去订单orderInfo拿shopId
		req.setShopId(syncOutMessage.getHead().getShopId());
		Result<OrderInfo> orderInfo = orderSystemClient.getOrderInfo(new OrderKey(
				syncOutMessage.getHead().getTenantId(),
				syncOutMessage.getHead().getShopId(), syncOutMessage.getHead().getOrderId()), false);
		if(orderInfo.getInfo()!=null && orderInfo.getInfo().getWarehousePoiId()!=null){
			req.setShopId(orderInfo.getInfo().getWarehousePoiId());
		}
		req.setOrderId(syncOutMessage.getHead().getChannelOrderId());
		req.setDeliveryOrderId(String.valueOf(syncOutMessage.getHead().getDeliveryId()));

		req.setStatus(syncOutMessage.getHead().getDeliveryStatus());
		//变更类型
		req.setChangeType(syncOutMessage.getChangeType());

		return req;
	}

	private UpdateDeliveryInfoRequest buildUpdateDeliverReq(DeliveryOrder deliveryOrder, CoordinatePoint riderLocationPoint) {
		DynamicChannelType channelType = DynamicOrderBizType.orderBizTypeValue2ChannelType(deliveryOrder.getOrderBizType());
		if (Objects.isNull(channelType)) {
			throw new RuntimeException("不识别的渠道");
		}
		UpdateDeliveryInfoRequest req = new UpdateDeliveryInfoRequest();
		req.setTenantId(deliveryOrder.getTenantId());
		req.setChannelId(channelType.getChannelId());
		//去订单orderInfo拿shopId
		req.setShopId(deliveryOrder.getStoreId());
		Result<OrderInfo> orderInfo = orderSystemClient.getOrderInfo(deliveryOrder.getOrderKey(), false);
		if(orderInfo.getInfo()!=null && orderInfo.getInfo().getWarehousePoiId()!=null){
			req.setShopId(orderInfo.getInfo().getWarehousePoiId());
		}
		if(deliveryOrder.getPlatformSourceEnum()== PlatformSourceEnum.OFC){
			if(orderInfo.getInfo()!=null){
				req.setShopId(orderInfo.getInfo().getOrderKey().getStoreId());
			}
		}
		req.setOrderId(deliveryOrder.getChannelOrderId());
		req.setDeliveryOrderId(deliveryOrder.getId().toString());
		if (channelType == DynamicChannelType.MT_DRUNK_HORSE) {
			req.setOnlySyncRiderPosition(true);
		}
		if (deliveryOrder.getRiderInfo()!=null && StringUtils.isNotBlank(deliveryOrder.getRiderInfo().getRiderName())) {
			req.setRiderName(deliveryOrder.getRiderInfo().getRiderName());
		}
		if (deliveryOrder.getRiderInfo()!=null && StringUtils.isNotBlank(deliveryOrder.getRiderInfo().getRiderPhone())) {
			req.setRiderPhone(deliveryOrder.getRiderInfo().getRiderPhone());
		}

		if(deliveryOrder.getDeliveryChannel()!=null){
			req.setDeliveryChannelId(deliveryOrder.getDeliveryChannel());
		}

		req.setStatus(deliveryOrder.getStatus().getCode());
		if (Objects.nonNull(riderLocationPoint)) {
			req.setLatitude(Double.valueOf(riderLocationPoint.getLatitude()));
			req.setLongitude(Double.valueOf(riderLocationPoint.getLongitude()));
		}

		return req;
	}
	@Override
	@MethodLog(logResponse = false, logRequest = true)
	@CatTransaction
	@Degrade(rhinoKey = "farm_paotui_preDelivery", fallBackMethod = "preDeliveryFallback", timeoutInMilliseconds = 1000)
	public FarmPaoTuiPreDeliveryDetailDTO preDelivery(Long tenantId, Long storeId, String channelOrderId){

		RetryTemplate retryTemplate= RetryTemplateUtil.simpleWithFixedRetry(2,100);

		try {
			return retryTemplate.execute(new RetryCallback<FarmPaoTuiPreDeliveryDetailDTO, Exception>() {
				@Override
				public FarmPaoTuiPreDeliveryDetailDTO doWithRetry(RetryContext retryContext) throws Exception {
					FarmPaoTuiPreDeliveryInnerRequest request=new FarmPaoTuiPreDeliveryInnerRequest();
					request.setTenantId(tenantId);
					request.setStoreId(storeId);
					request.setChannelOrderId(channelOrderId);
					FarmPaoTuiPreDeliveryResponse response=farmPaoTuiInnerThriftService.preDelivery(request);
					log.info("preDelivery tenantId:{},storeId:{},channelOrderId:{},response:{}",tenantId,storeId,channelOrderId,response);
					if(response!=null){
						return response.getData();
					}
					return null;
				}
			});
		}catch (Exception e){
			log.error("OcmsChannelRemoteService.preDelivery error tenantId:{},storeId:{},channelOrderId:{}",tenantId,storeId,channelOrderId,e);
		}
		return null;
	}

	@Override
	@MethodLog(logResponse = false, logRequest = true)
	@CatTransaction
	@Degrade(rhinoKey = "farm_paotui_cancelDelivery", fallBackMethod = "cancelDeliveryFallback", timeoutInMilliseconds = 1000)
	public FarmPaotuiCancelInnerResponse cancelDelivery(Long tenantId, Long storeId, String channelOrderId){

		RetryTemplate retryTemplate= RetryTemplateUtil.simpleWithFixedRetry(2,100);

		try {
			return retryTemplate.execute(new RetryCallback<FarmPaotuiCancelInnerResponse, Exception>() {
				@Override
				public FarmPaotuiCancelInnerResponse doWithRetry(RetryContext retryContext) throws Exception {
					FarmPaotuiCancelDeliveryInnerRequest request=new FarmPaotuiCancelDeliveryInnerRequest();
					request.setTenantId(tenantId);
					request.setStoreId(storeId);
					request.setChannelOrderId(channelOrderId);
					FarmPaotuiCancelInnerResponse response=farmPaoTuiInnerThriftService.cancelDelivery(request);
					log.info("cancelDelivery tenantId:{},storeId:{},channelOrderId:{},response:{}",tenantId,storeId,channelOrderId,response);
					if (response != null) {
						return response;
					}
					return null;
				}
			});
		}catch (Exception e){
			log.error("OcmsChannelRemoteService.cancelDelivery error tenantId:{},storeId:{},channelOrderId:{}",tenantId,storeId,channelOrderId,e);
		}
		return null;
	}

	private FarmPaoTuiPreDeliveryDetailDTO preDeliveryFallback(Long tenantId, Long storeId, String channelOrderId) {
		log.info("preDeliveryFallback tenantId:{},storeId:{},channelOrderId:{}",tenantId,storeId,channelOrderId);
		return null;
	}

	private FarmPaotuiCancelInnerResponse cancelDeliveryFallback(Long tenantId, Long storeId, String channelOrderId) {
		log.info("cancelDeliveryFallback tenantId:{},storeId:{},channelOrderId:{}",tenantId,storeId,channelOrderId);
		return null;
	}

	public void syncDrunkHorseRiderLocationFallback(DeliveryOrder deliveryOrder, CoordinatePoint riderLocationPoint) {
		log.error("sync drunk horse rider location degrade");
	}

	private RiderLocationSyncToChannelMessage buildSyncToChannelMessage(DeliveryOrder deliveryOrder, CoordinatePoint riderLocationPoint) {
		DynamicChannelType channelType = DynamicOrderBizType.orderBizTypeValue2ChannelType(deliveryOrder.getOrderBizType());
		if (Objects.isNull(channelType)) {
			throw new RuntimeException("不识别的渠道");
		}
		RiderLocationSyncToChannelMessage message = new RiderLocationSyncToChannelMessage();
		message.setTenantId(deliveryOrder.getTenantId());
		message.setChannelId(channelType.getChannelId());
		//去订单orderInfo拿shopId
		message.setShopId(deliveryOrder.getStoreId());
		Result<OrderInfo> orderInfo = orderSystemClient.getOrderInfo(deliveryOrder.getOrderKey(), false);
		if(orderInfo.getInfo()!=null && orderInfo.getInfo().getWarehousePoiId()!=null){
			message.setShopId(orderInfo.getInfo().getWarehousePoiId());
		}
		message.setViewOrderId(deliveryOrder.getChannelOrderId());
		message.setDeliveryOrderId(deliveryOrder.getId().toString());
		if (channelType == DynamicChannelType.MT_DRUNK_HORSE) {
			message.setOnlySyncRiderPosition(true);
		}
		if (deliveryOrder.getRiderInfo()!=null && StringUtils.isNotBlank(deliveryOrder.getRiderInfo().getRiderName())) {
			message.setRiderName(deliveryOrder.getRiderInfo().getRiderName());
		}
		if (deliveryOrder.getRiderInfo()!=null && StringUtils.isNotBlank(deliveryOrder.getRiderInfo().getRiderPhone())) {
			message.setRiderPhone(deliveryOrder.getRiderInfo().getRiderPhone());
		}

		if(deliveryOrder.getDeliveryChannel()!=null){
			message.setDeliveryChannelId(deliveryOrder.getDeliveryChannel());
		}

		message.setStatus(deliveryOrder.getStatus().getCode());
		message.setLatitude(Double.valueOf(riderLocationPoint.getLatitude()));
		message.setLongitude(Double.valueOf(riderLocationPoint.getLongitude()));

		return message;
	}

	@Override
	@CatTransaction
	public void syncRiderInfoChange(DeliveryOrder deliveryOrder, Rider rider, String channelOrderId) {
		RiderInfoChangeBo riderInfoChangeBo = new RiderInfoChangeBo();
		riderInfoChangeBo.setTenantId(deliveryOrder.getTenantId());
		riderInfoChangeBo.setStoreId(deliveryOrder.getStoreId());
		riderInfoChangeBo.setOrderBizType(deliveryOrder.getOrderBizType());
		riderInfoChangeBo.setChannelOrderId(channelOrderId);
		riderInfoChangeBo.setDeliveryChannelId(deliveryOrder.getDeliveryChannel());
		riderInfoChangeBo.setDeliveryOrderId(deliveryOrder.getId());
		if (Objects.nonNull(rider)) {
			riderInfoChangeBo.setRiderName(rider.getRiderName());
			riderInfoChangeBo.setRiderPhone(rider.getRiderPhone());
		}

		ocmsChannelService.syncRiderInfoChange(riderInfoChangeBo);
	}

	@Override
	public boolean syncRiderBatchLocationToChanel(DeliveryOrder deliveryOrder, List<RiderLocationDetail> list) {
		if (CollectionUtils.isEmpty(list) || Objects.isNull(deliveryOrder)) {
			return false;
		}
		try {
			//封装参数
			RiderPointBatchSyncRequest req = buildChannelSyncReq(deliveryOrder, list);
			if (Objects.isNull(req)) {
				return false;
			}
			RiderUpdateResponse riderUpdateResponse = channelDeliveryDockingThriftService.batchSyncRiderPoint(req);
			return Optional.ofNullable(riderUpdateResponse).map(it -> Objects.equals(it.getCode(), 0)).orElse(false);
		} catch (Exception e) {
			log.error("syncRiderBatchLocationToChanel error", e);
			return false;
		}
	}

	@Override
	public boolean syncRiderAllLocationToChanel(DeliveryOrder deliveryOrder, List<RiderLocationDetail> list) {
		if (CollectionUtils.isEmpty(list) || Objects.isNull(deliveryOrder)) {
			return false;
		}
		try {
			//封装参数
			RiderPointBatchSyncRequest req = buildChannelSyncReq(deliveryOrder, list);
			if (Objects.isNull(req)) {
				return false;
			}
			RiderUpdateResponse riderUpdateResponse = channelDeliveryDockingThriftService.batchSyncRiderAllPoint(req);
			return Optional.ofNullable(riderUpdateResponse).map(it -> Objects.equals(it.getCode(), 0)).orElse(false);

		} catch (Exception e) {
			log.error("syncRiderAllLocationToChanel error", e);
			return false;
		}
	}

	/**
	 * 构建渠道批量同步位置参数
	 */
	private RiderPointBatchSyncRequest buildChannelSyncReq(DeliveryOrder deliveryOrder, List<RiderLocationDetail> list) {
		try {
			DynamicChannelType channelType = DynamicOrderBizType.orderBizTypeValue2ChannelType(deliveryOrder.getOrderBizType());
			if (Objects.isNull(channelType)) {
				return null;
			}
			RiderPointBatchSyncRequest req = new RiderPointBatchSyncRequest();
			req.setTenantId(deliveryOrder.getTenantId());
			req.setChannelId(channelType.getChannelId());
			req.setStoreId(deliveryOrder.getStoreId());
			req.setOrderId(deliveryOrder.getOrderId());
			req.setChannelOrderId(deliveryOrder.getChannelOrderId());
			req.setDeliveryOrderId(deliveryOrder.getId());
			req.setDeliveryChannelId(deliveryOrder.getDeliveryChannel());
			List<RiderPointInfoRequest> riderPointInfoRequestList = list.stream().map(detail -> {
				RiderPointInfoRequest pointInfo = new RiderPointInfoRequest();
				pointInfo.setRiderName(detail.getRiderName());
				pointInfo.setLatitude(Double.parseDouble(detail.getLatitude()));
				pointInfo.setLongitude(Double.parseDouble(detail.getLongitude()));
				pointInfo.setBackFlowTime(detail.getLongTypeTime());
				return pointInfo;
			}).collect(Collectors.toList());
			req.setRiderPointInfoRequestList(riderPointInfoRequestList);
			return req;
		} catch (Exception e) {
			log.error("构建参数失败", e);
			return null;
		}
	}


}
