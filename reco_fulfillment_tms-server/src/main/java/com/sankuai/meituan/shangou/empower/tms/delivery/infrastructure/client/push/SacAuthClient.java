package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.client.push;

import com.google.common.collect.Maps;
import com.meituan.shangou.sac.dto.request.authenticate.AccountAuthPermissionsRequest;
import com.meituan.shangou.sac.dto.response.SacStatus;
import com.meituan.shangou.sac.dto.response.authenticate.AccountAuthPermissionsResponse;
import com.meituan.shangou.sac.thrift.authenticate.AuthenticateService;
import com.sankuai.drunkhorsemgmt.labor.exception.SystemException;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025-09-10
 * @email <EMAIL>
 */
@Slf4j
@Service
public class SacAuthClient {

    @Resource
    private AuthenticateService authenticateService;

    @MethodLog(logRequest = true, logResponse = true)
    public Map<String,Boolean> queryAccountAuthPermissions(long accountId, int appId, List<String> authCodes) {
        AccountAuthPermissionsRequest request = new AccountAuthPermissionsRequest();
        request.setAccountId(accountId);
        request.setPermissionCodes(authCodes);
        request.setAppId(appId);
        AccountAuthPermissionsResponse response = authenticateService.accountAuthPermissions(request);
        if(!response.getSacStatus().getCode().equals(SacStatus.CODE_SUCCESS)) {
            throw new SystemException(response.getSacStatus().getMessage());
        }
        return Optional.ofNullable(response.getAuthResult()).orElse(Maps.newHashMap());
    }
}
