package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.client.push;

import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.meituan.shangou.saas.tenant.thrift.DepartmentV2ThriftService;
import com.meituan.shangou.saas.tenant.thrift.dto.department.v2.dto.EmployeeInfoV2Dto;
import com.meituan.shangou.saas.tenant.thrift.dto.department.v2.request.EmployeeInfoInDepartmentQueryV2Request;
import com.meituan.shangou.saas.tenant.thrift.dto.department.v2.response.EmployeeInfoInDepartmentQueryV2Response;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.AccountInfoVo;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.QueryAccountInfoListResponse;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.LionConfigNameEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.client.auth.AuthClientImpl;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.PushInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.RiderDeliveryOrder;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.client.push.RiderPushClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.task.exception.CommonRuntimeException;
import com.sankuai.meituan.shangou.empower.tms.delivery.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.saas.common.cat.CatTransaction;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 骑手消息推送.
 *
 * <AUTHOR>
 * @since 2021/8/2 17:12
 */
@Slf4j
@Service
public class RiderPushClientImpl implements RiderPushClient {

    @Resource
    BasePushClient basePushClient;
    @Resource
    private DepartmentV2ThriftService departmentV2ThriftService;
    @Resource
    private AuthClientImpl authClient;

    private static final int SUCCESS_CODE = 0;

    private static final int ACCOUNT_VALID = 1;

    // 牵牛花 push appId
    private final Integer SHU_GUO_PAI_PUSH_APP_ID = 5;

    // 自营骑手待取货 tab 页的 key
    private final String RIDER_WAIT_TAKE_GOODS_TAB_KEY = "RIDER_WAIT_TAKE";
    // 自营骑手配送中 tab 页的 key
    private final String RIDER_IN_DELIVERY_TAB_KEY = "RIDER_DELIVERING";

    @Override
    @CatTransaction
    public void pushSelfRiderNewTransferOrder(RiderDeliveryOrder deliveryOrder, Long targetRiderAccountId) {
        Long tenantId = deliveryOrder.getTenantId();
        Long storeId = deliveryOrder.getStoreId();
        List<Long> accountIds = Lists.newArrayList(targetRiderAccountId);
        DeliveryStatusEnum status = deliveryOrder.getStatus();
        String subTab = null;
        switch (status) {
            case RIDER_ASSIGNED:
                subTab = RIDER_WAIT_TAKE_GOODS_TAB_KEY;
                break;
            case RIDER_TAKEN_GOODS:
                subTab = RIDER_IN_DELIVERY_TAB_KEY;
                break;
            default:
                // 其余物流单状态，不支持发该 push
                return;
        }
        ImmutableMap<String, String> msgPropertyMap = ImmutableMap.of("tenantId", String.valueOf(tenantId),
                "storeId", String.valueOf(storeId), "subTab", subTab);
        log.info("RiderPushClient.pushSelfRiderNewTransferOrder 消息 push. tenantId:{}, storeId:{}, accountIds:{}, appId:{}, " +
                "pushConfigId:{}, pushMsg:{}", tenantId, storeId, accountIds, SHU_GUO_PAI_PUSH_APP_ID.toString(), 1044L, msgPropertyMap);
        basePushClient.unifiedSendFusionPush(tenantId, storeId, accountIds, SHU_GUO_PAI_PUSH_APP_ID.toString(), 1044L, msgPropertyMap);
    }


    @Override
    @CatTransaction
    public void pushFrontEndToGetRiderLocation(RiderDeliveryOrder deliveryOrder, Long targetRiderAccountId) {
        Long tenantId = deliveryOrder.getTenantId();
        Long storeId = deliveryOrder.getStoreId();
        List<Long> accountIds = Collections.singletonList(targetRiderAccountId);
        String sharkPushContent = getPushFrontEndSharkPushContent(tenantId, storeId, targetRiderAccountId, "location");
        log.info("通知前端获取转单后骑手位置 push. tenantId:{}, storeId:{}, accountIds:{}, appId:{}, " +
                "sharkPushContent:{}", tenantId, storeId, accountIds, SHU_GUO_PAI_PUSH_APP_ID.toString(), sharkPushContent);
        basePushClient.unifiedSendSharkPush(tenantId, Collections.singletonList(targetRiderAccountId), SHU_GUO_PAI_PUSH_APP_ID.toString(), sharkPushContent);
    }

    @Override
    @CatTransaction
    public void pushSelfRiderTransferOutOrder(RiderDeliveryOrder riderDeliveryOrder, Long sourceRiderAccountId) {
        Long tenantId = riderDeliveryOrder.getTenantId();
        Long storeId = riderDeliveryOrder.getStoreId();
        List<Long> accountIds = Collections.singletonList(sourceRiderAccountId);
        String sharkPushContent = getPushFrontEndSharkPushContent(tenantId, storeId, sourceRiderAccountId, "rider_order_out");
        log.info("通知前端骑手运单被转走 push. tenantId:{}, storeId:{}, accountIds:{}, appId:{}, " +
                "sharkPushContent:{}", tenantId, storeId, accountIds, SHU_GUO_PAI_PUSH_APP_ID.toString(), sharkPushContent);
        basePushClient.unifiedSendSharkPush(tenantId, Collections.singletonList(sourceRiderAccountId), SHU_GUO_PAI_PUSH_APP_ID.toString(), sharkPushContent);
    }

    @Override
    @MethodLog(logRequest = true, logResponse = true)
    public PushInfo queryPushInfoByPositions(long tenantId, long storeId, List<Long> positionIds) {
        try {
            EmployeeInfoInDepartmentQueryV2Response employeeResponse = getEmployeePositionResponse(tenantId, storeId, positionIds);
            Map<Long, List<EmployeeInfoV2Dto>> positionAndEmployeeInfoMap = employeeResponse.getPositionAndEmployeeInfoMap();
            if(MapUtils.isEmpty(positionAndEmployeeInfoMap)) {
                return PushInfo.empty();
            }
            QueryAccountInfoListResponse accountResponse = authClient.getQueryAccountInfoListResponse(tenantId, positionAndEmployeeInfoMap);
            List<AccountInfoVo> validAccountInfoList =
                    Optional.ofNullable(accountResponse.getAccountInfoList())
                            .orElse(Lists.newArrayList())
                            .stream()
                            .filter(accountInfoVo -> Objects.equals(accountInfoVo.getValid(), ACCOUNT_VALID))
                            .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(validAccountInfoList)) {
                return PushInfo.empty();
            }

            //整体逻辑，有mis取mis，没有mis取accountName
            //对于美团员工和加盟来说，其大象账号是accountVO的美团体系misId
            List<String> meituanMisIdList = validAccountInfoList.stream().filter(accountVO -> StringUtils.isNotBlank(accountVO.getMisId())).map(AccountInfoVo::getMisId).distinct().collect(Collectors.toList());
            //对于直营来说，其大象账号是用accountName来开通的外部mis
            List<String> externalMisIdList = validAccountInfoList.stream().filter(accountVO -> StringUtils.isBlank(accountVO.getMisId())).map(AccountInfoVo::getAccountName).distinct().collect(Collectors.toList());

            List<Long> accountList = validAccountInfoList.stream().map(AccountInfoVo::getAccountId).distinct().collect(Collectors.toList());
            return new PushInfo(meituanMisIdList, externalMisIdList, accountList);

        } catch (Exception e) {
            log.error("queryPushInfoByPositions error", e);
            throw new CommonRuntimeException("查询push信息失败");
        }
    }

    /**
     * 获取推送给前端的 SharkPush 内容.
     *
     * @return SharkPush 内容
     */
    private String getPushFrontEndSharkPushContent(Long tenantId, Long storeId, Long targetRiderAccountId, String operation) {
        ObjectNode contentJson = JsonUtil.generateObjectNode();
        contentJson.put("serverTS", System.currentTimeMillis());
        contentJson.put("operation", operation);
        contentJson.put("riderAccountId", targetRiderAccountId.toString());
        contentJson.put("storeId", storeId);
        contentJson.put("tenantId", tenantId);
        if (StringUtils.equals(operation, "location")) {
            contentJson.put("reportUrl", MccConfigUtils.getReportLocationUrl());
            //批量上报地址
            contentJson.put("batchReportUrl", com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils.getLionStrConf(LionConfigNameEnum.BATCH_REPORT_RIDER_LOCATION_URL.getName(), "/api/orderfulfill/app/rider/delivery/location/batch"));
            //上报距离
            contentJson.put("distanceFilter", com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils.getLionIntConf(LionConfigNameEnum.BATCH_REPORT_RIDER_LOCATION_URL.getName(), 10));

            if (MccConfigUtils.isLocationIntervalGrayStore(storeId)) {
                contentJson.put("gpsLocationInterval", MccConfigUtils.getReportLocationInterval());
            }
            if (MccConfigUtils.isLocationShorterIntervalGrayStore(storeId)) {
                contentJson.put("gpsLocationInterval", MccConfigUtils.getShorterReportLocationInterval());
            }
        }

        return contentJson.toString();
    }



    private EmployeeInfoInDepartmentQueryV2Response getEmployeePositionResponse(long tenantId, long storeId, List<Long> positionIds) {
        EmployeeInfoInDepartmentQueryV2Request request = new EmployeeInfoInDepartmentQueryV2Request();
        request.setTenantId(tenantId);
        request.setResourceType("poi");
        request.setResourceId(String.valueOf(storeId));
        request.setFilterPositionIdList(positionIds);
        request.setAlsoQueryInParentDepartment(true);
        request.setAlsoQueryInChildDepartment(false);
        EmployeeInfoInDepartmentQueryV2Response employeeResponse = departmentV2ThriftService.queryEmployeeInfoListInDepartmentByCondition(request);
        if (!Objects.equals(employeeResponse.getStatus().getCode(), SUCCESS_CODE)) {
            throw new CommonRuntimeException("departmentV2ThriftService.queryEmployeeInfoListInDepartmentByCondition fail");
        }
        return employeeResponse;
    }


}
