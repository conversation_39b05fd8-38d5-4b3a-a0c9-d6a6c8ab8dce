package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.thrift;

import com.dianping.cat.Cat;
import com.dianping.rhino.onelimiter.LimitResult;
import com.google.common.collect.Lists;
import com.sankuai.meituan.shangou.dms.base.model.value.CoordinatePoint;
import com.sankuai.meituan.shangou.empower.rider.client.common.Status;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.operation.RiderOperateThriftService;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.operation.request.*;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.operation.response.RiderChangeResponse;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.operation.response.RiderOperateTResponse;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.DeliveryOperationApplicationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrder;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrderRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderKey;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.channel.OcmsChannelClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.OneLimiterHolder;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.FailureCodeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Failure;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.application.RiderOperateApplicationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.client.sync.RiderDeliveryOrderSyncOutClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.utils.DeliveryRiderMccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.utils.ConvertUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.saas.common.cat.CatTransaction;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;

import static com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.FailureCodeEnum.*;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/6/15
 */
@Slf4j
@Service
public class RiderOperateThriftServiceImpl implements RiderOperateThriftService {

    @Resource
    private RiderOperateApplicationService riderOperateApplicationService;

    @Resource
    private DeliveryOperationApplicationService deliveryOperationApplicationService;

    @Resource
    private OcmsChannelClient ocmsChannelClient;

    @Resource
    private RiderDeliveryOrderSyncOutClient riderDeliveryOrderSyncOutClient;

    @Resource
    private DeliveryOrderRepository deliveryOrderRepository;

    @Resource
    private RiderDeliveryOrderRepository riderDeliveryOrderRepository;

    private final RiderChangeResponse RIDER_CHANGE_SUCCESS = new RiderChangeResponse(Status.SUCCESS);

    @Override
    @MethodLog(logRequest = false, logResponse = true)
    public RiderOperateTResponse accept(RiderOperateTRequest request) {
        try {
            String requestErrorMsg = request.validate();
            if (requestErrorMsg != null) {
                return new RiderOperateTResponse(new Status(FailureCodeEnum.INVALID_PARAM.getCode(), requestErrorMsg));
            }
            Optional<Failure> failure = riderOperateApplicationService.accept(request.getDeliveryOrderId(), translateRider(request),
                    new Coordination(request.getLongitude(), request.getLatitude()),request.getTenantId(),request.getStoreId());
            if (failure.isPresent()){
                log.error("骑手接单失败 req={}， failure={}", request, failure);
                Cat.logEvent("SELF_RIDER_ACCEPT", "FAIL_BY_CODE_" + failure.get().getFailureCode());
                return new RiderOperateTResponse(new Status(failure.get().getFailureCode(), failure.get().getFailureMessage()));
            }
            Cat.logEvent("SELF_RIDER_ACCEPT", "SUCCESS");
            return new RiderOperateTResponse(Status.SUCCESS);
        } catch (Exception e) {
            Cat.logEvent("SELF_RIDER_ACCEPT", "ERROR");
            log.error("骑手接单失败 req={}", request, e);
        }
        return new RiderOperateTResponse(new Status(SYSTEM_ERROR.getCode(), SYSTEM_ERROR.getMessage()));
    }

    @Override
    @MethodLog(logRequest = false, logResponse = true)
    public RiderOperateTResponse takeAway(RiderOperateTRequest request) {
        try {
            String requestErrorMsg = request.validate();
            if (requestErrorMsg != null) {
                return new RiderOperateTResponse(new Status(FailureCodeEnum.INVALID_PARAM.getCode(), requestErrorMsg));
            }
            Optional<Failure> failure = riderOperateApplicationService.takeAway(request.getDeliveryOrderId(), translateRider(request),
                    new Coordination(request.getLongitude(), request.getLatitude()),request.getTenantId(),request.getStoreId());
            if (failure.isPresent()){
                log.error("骑手取货失败 req={}， failure={}", request, failure);
                Cat.logEvent("SELF_RIDER_TAKE_AWAY", "FAIL_BY_CODE_" + failure.get().getFailureCode());
                return new RiderOperateTResponse(new Status(failure.get().getFailureCode(), failure.get().getFailureMessage()));
            }
            Cat.logEvent("SELF_RIDER_TAKE_AWAY", "SUCCESS");
            return new RiderOperateTResponse(Status.SUCCESS);
        } catch (Exception e) {
            Cat.logEvent("SELF_RIDER_TAKE_AWAY", "ERROR");
            log.error("骑手取货失败 req={}", request, e);
        }
        return new RiderOperateTResponse(new Status(SYSTEM_ERROR.getCode(), SYSTEM_ERROR.getMessage()));
    }


    @Override
    @MethodLog(logRequest = false, logResponse = true)
    public RiderOperateTResponse complete(RiderOperateTRequest request) {
        try {
            String requestErrorMsg = request.validate();
            if (requestErrorMsg != null) {
                return new RiderOperateTResponse(new Status(FailureCodeEnum.INVALID_PARAM.getCode(), requestErrorMsg));
            }
            Optional<Failure> failure = riderOperateApplicationService.complete(request.getDeliveryOrderId(), translateRider(request),
                    new Coordination(request.getLongitude(), request.getLatitude()),request.getTenantId(),request.getStoreId());
            if (failure.isPresent()){
                log.error("骑手送达失败 req={}， failure={}", request, failure);
                Cat.logEvent("SELF_RIDER_COMPLETE", "FAIL_BY_CODE_" + failure.get().getFailureCode());
                return new RiderOperateTResponse(new Status(failure.get().getFailureCode(), failure.get().getFailureMessage()));
            }
            Cat.logEvent("SELF_RIDER_COMPLETE", "SUCCESS");
            return new RiderOperateTResponse(Status.SUCCESS);
        } catch (Exception e) {
            Cat.logEvent("SELF_RIDER_COMPLETE", "ERROR");
            log.error("骑手送达失败 req={}", request, e);
        }
        return new RiderOperateTResponse(new Status(SYSTEM_ERROR.getCode(), SYSTEM_ERROR.getMessage()));
    }

    @Override
    @MethodLog(logRequest = false, logResponse = true)
    public RiderOperateTResponse completeWithProofPhoto(CompleteWithProofPhotoTRequest request) {
        try {
            String requestErrorMsg = request.validate();
            if (requestErrorMsg != null) {
                return new RiderOperateTResponse(new Status(FailureCodeEnum.INVALID_PARAM.getCode(), requestErrorMsg));
            }
            Optional<Failure> failure = riderOperateApplicationService.completeWithProofPhoto(request.getDeliveryOrderId(), translateRider(request),
                    request.getProofPhotoList(), request.getSignType(), request.getIsWeakNetWork(), new Coordination(request.getLongitude(), request.getLatitude()),request.getTenantId(),request.getStoreId());
            if (failure.isPresent()){
                log.error("骑手送达失败 req={}， failure={}", request, failure);
                Cat.logEvent("SELF_RIDER_COMPLETE_WITH_PHOTO", "FAIL_BY_CODE_" + failure.get().getFailureCode());
                return new RiderOperateTResponse(new Status(failure.get().getFailureCode(), failure.get().getFailureMessage()));
            }
            Cat.logEvent("SELF_RIDER_COMPLETE_WITH_PHOTO", "SUCCESS");
            return new RiderOperateTResponse(Status.SUCCESS);
        } catch (Exception e) {
            Cat.logEvent("SELF_RIDER_COMPLETE_WITH_PHOTO", "ERROR");
            log.error("骑手送达失败 req={}", request, e);
        }
        return new RiderOperateTResponse(new Status(SYSTEM_ERROR.getCode(), SYSTEM_ERROR.getMessage()));
    }


    @Override
    @MethodLog(logRequest = false, logResponse = true)
    public RiderChangeResponse riderChange(RiderChangeRequest request) {
        String checkErrorMsg = request.validate();
        if (checkErrorMsg != null) {
            return new RiderChangeResponse(new Status(INVALID_PARAM.getCode(), checkErrorMsg));
        }

        try {
            Long[] olderRiderAccountId = new Long[1];
            String[] olderRiderAccountName = new String[1];
            return riderOperateApplicationService
                    .riderChange(
                            request.getTenantId(), request.getStoreId(), request.getOrderId(),
                            new StaffRider(request.getRiderName(), request.getRiderPhone(), request.getRiderPhoneToken(), request.getRiderAccountId()), olderRiderAccountId, olderRiderAccountName)
                    .map(value -> {
                        if (Objects.equals(value.getFailureCode(), FailureCodeEnum.RIDER_CHANGE_SAME.getCode())) {
                            RiderChangeResponse riderChangeResponse = new RiderChangeResponse(Status.SUCCESS);
                            riderChangeResponse.setIsSameRider(true);
                            return riderChangeResponse;
                        }
                        return new RiderChangeResponse(new Status(value.getFailureCode(), value.getFailureMessage()));
                    })
                    .orElse(new RiderChangeResponse(Status.SUCCESS, false, olderRiderAccountId[0], olderRiderAccountName[0]));

        } catch (Exception e) {
            log.error("DeliveryOperationThriftService.riderChange error", e);
            return new RiderChangeResponse(new Status(SYSTEM_ERROR.getCode(), SYSTEM_ERROR.getMessage()));
        }
    }

    @Override
    @MethodLog(logRequest = false, logResponse = true)
    public RiderOperateTResponse riderLocation(RiderLocationRequest request) {

        //先处理限流,用账号id来
        Map<String, String> params = new HashMap<>();
        // 集群限频需要将UUID传入params
        // key: "uuid": String, value: "限频id": String
        // uuid 这个 key 是写死的，无法自定义，使用限频必须传入 uuid 这个 key
        params.put("uuid", String.valueOf(request.getRiderAccountId()));
        LimitResult limitResult = OneLimiterHolder.getOneLimiter().run("riderLocation", params);
        if (limitResult.isPass()) {
            if(!MccConfigUtils.checkRiderLocationSyncGreySwitch(request.getStoreId())){
                return new RiderOperateTResponse(Status.SUCCESS);
            }

            String checkErrorMsg = request.validate();
            if (checkErrorMsg != null) {
                return new RiderOperateTResponse(new Status(INVALID_PARAM.getCode(), checkErrorMsg));
            }
            try {
                RiderLocationDetail locationDetail= ConvertUtil.convertToLocationDetail(request);
                Optional<Failure> result = riderOperateApplicationService.riderLocation(request.getTenantId(), request.getStoreId(), locationDetail);
                if (result.isPresent()){
                    return new RiderOperateTResponse(new Status(result.get().getFailureCode(), result.get().getFailureMessage()));
                }
                return new RiderOperateTResponse(Status.SUCCESS);
            }catch (Exception e){
                log.error("DeliveryOperationThriftService.riderLocation error", e);
            }
            return new RiderOperateTResponse(new Status(SYSTEM_ERROR.getCode(), SYSTEM_ERROR.getMessage()));
        } else {
            Cat.logEvent("RIDER_LOCATION", "FLOW_LIMIT");
            return new RiderOperateTResponse(new Status(INTERFACE_LIMITED.getCode(), INTERFACE_LIMITED.getMessage()));
        }

    }

    @Override
    @MethodLog(logRequest = false, logResponse = true)
    public RiderOperateTResponse riderArrivalLocation(RiderArrivalLocationTRequest request) {
        String checkErrorMsg = request.validate();
        if (checkErrorMsg != null) {
            return new RiderOperateTResponse(new Status(INVALID_PARAM.getCode(), checkErrorMsg));
        }

        try {
            Optional<Failure> result = riderOperateApplicationService.riderArrivalLocation(request);

            if (result.isPresent()) {
                return new RiderOperateTResponse(new Status(result.get().getFailureCode(), result.get().getFailureMessage()));
            }
            return new RiderOperateTResponse(Status.SUCCESS);
        }catch (Exception e){
            log.error("DeliveryOperationThriftService.riderArrivalLocation error", e);
            return new RiderOperateTResponse(new Status(SYSTEM_ERROR.getCode(), SYSTEM_ERROR.getMessage()));
        }

    }


    @Override
    @MethodLog(logRequest = false,logResponse = true)
    public RiderOperateTResponse riderLocatingException(RiderLocatingExceptionTRequest request) {
        String checkErrorMsg = request.validate();
        if (checkErrorMsg != null) {
            return new RiderOperateTResponse(new Status(INVALID_PARAM.getCode(), checkErrorMsg));
        }

        try {
            RiderLocatingExceptionDetail exceptionDetail= ConvertUtil.convertToExceptionDetail(request);
            Optional<Failure> result = riderOperateApplicationService.riderLocatingException(exceptionDetail);
            if (result.isPresent()) {
                return new RiderOperateTResponse(new Status(result.get().getFailureCode(), result.get().getFailureMessage()));
            }

            return new RiderOperateTResponse(Status.SUCCESS);
        } catch (Exception e) {
            log.error("DeliveryOperationThriftService.riderLocatingException error, req={}", request, e);
            return new RiderOperateTResponse(new Status(SYSTEM_ERROR.getCode(), SYSTEM_ERROR.getMessage()));
        }
    }

    @Override
    @MethodLog(logRequest = false,logResponse = true)
    public RiderOperateTResponse batchPostRiderLocatingLog(BatchPostRiderLocatingLogTRequest request) {
        try {
            riderOperateApplicationService.batchPostRiderLocatingLog(request);
            Cat.logEvent("BATCH_POST_LOCATING_LOG","SUCCESS");
            return new RiderOperateTResponse(Status.SUCCESS);
        } catch (Exception e) {
            Cat.logEvent("BATCH_POST_LOCATING_LOG","FAIL");
            log.error("DeliveryOperationThriftService.batchPostRiderLocatingLog error, req={}", request, e);
            return new RiderOperateTResponse(new Status(SYSTEM_ERROR.getCode(), SYSTEM_ERROR.getMessage()));
        }
    }

    @Override
    @MethodLog(logRequest = false, logResponse = true)
    public RiderOperateTResponse immediatelyDelivery(RiderImmediatelyDeliveryTRequest request) {
        String checkErrorMsg = request.validate();
        if (checkErrorMsg != null) {
            return new RiderOperateTResponse(new Status(INVALID_PARAM.getCode(), checkErrorMsg));
        }

        try {
            OrderKey orderKey = new OrderKey(request.getTenantId(), request.getStoreId(), request.getOrderId());
            Optional<Failure> failure = deliveryOperationApplicationService.immediatelyDelivery4DrunkHorse(orderKey);

            if (failure.isPresent()) {
                //歪马场景下的线下地推单不发配送也不报错
                if (Objects.equals(failure.get().getFailureCode(), OFFLINE_PROMOTE_CREATE_DELIVERY_ERROR.getCode())) {
                    return new RiderOperateTResponse(Status.SUCCESS);
                }
                Cat.logEvent("SELF_RIDER_IMMEDIATELY_DELIVERY", "FAIL");
                return new RiderOperateTResponse(new Status(failure.get().getFailureCode(), failure.get().getFailureMessage()));
            }

            Cat.logEvent("SELF_RIDER_IMMEDIATELY_DELIVERY", "SUCCESS");
            return new RiderOperateTResponse(Status.SUCCESS);
        } catch (Exception e) {
            log.error("DeliveryOperationThriftService.immediatelyDelivery error", e);
            Cat.logEvent("SELF_RIDER_IMMEDIATELY_DELIVERY", "FAIL");
            return new RiderOperateTResponse(new Status(SYSTEM_ERROR.getCode(), SYSTEM_ERROR.getMessage()));
        }
    }

    @Override
    public RiderOperateTResponse lockDeliveryStatus(RiderOperateTRequest request) {
        String checkErrorMsg = request.validate();
        if (checkErrorMsg != null) {
            return new RiderOperateTResponse(new Status(INVALID_PARAM.getCode(), checkErrorMsg));
        }
        try {
            Optional<Failure> failure = riderOperateApplicationService.lockDeliveryStatus(request.getDeliveryOrderId(), translateRider(request),request.getTenantId(),request.getStoreId());
            if (failure.isPresent()) {
                Cat.logEvent("RIDER_LOCK_ORDER_STATUS", "FAIL");
                return new RiderOperateTResponse(new Status(failure.get().getFailureCode(), failure.get().getFailureMessage()));
            }

            Cat.logEvent("RIDER_LOCK_ORDER_STATUS", "SUCCESS");
            return new RiderOperateTResponse(Status.SUCCESS);
        } catch (Exception e) {
            log.error("DeliveryOperationThriftService.lockDeliveryStatus error", e);
            Cat.logEvent("RIDER_LOCK_ORDER_STATUS", "FAIL");
            return new RiderOperateTResponse(new Status(SYSTEM_ERROR.getCode(), SYSTEM_ERROR.getMessage()));
        }
    }

    @Override
    public RiderOperateTResponse unlockDeliveryStatus(RiderOperateTRequest request) {
        String checkErrorMsg = request.validate();
        if (checkErrorMsg != null) {
            return new RiderOperateTResponse(new Status(INVALID_PARAM.getCode(), checkErrorMsg));
        }
        try {
            Optional<Failure> failure = riderOperateApplicationService.unlockDeliveryStatus(request.getDeliveryOrderId(), translateRider(request),request.getTenantId(),request.getStoreId());
            if (failure.isPresent()) {
                Cat.logEvent("RIDER_UNLOCK_ORDER_STATUS", "FAIL");
                return new RiderOperateTResponse(new Status(failure.get().getFailureCode(), failure.get().getFailureMessage()));
            }

            Cat.logEvent("RIDER_UNLOCK_ORDER_STATUS", "SUCCESS");
            return new RiderOperateTResponse(Status.SUCCESS);
        } catch (Exception e) {
            log.error("DeliveryOperationThriftService.unlockDeliveryStatus error", e);
            Cat.logEvent("RIDER_UNLOCK_ORDER_STATUS", "FAIL");
            return new RiderOperateTResponse(new Status(SYSTEM_ERROR.getCode(), SYSTEM_ERROR.getMessage()));
        }
    }

    @Override
    @CatTransaction
    public RiderOperateTResponse postDeliveryProofPhoto(RiderPostDeliveryProofPhotoTRequest request) {
        String checkErrorMsg = request.validate();
        if (checkErrorMsg != null) {
            return new RiderOperateTResponse(new Status(INVALID_PARAM.getCode(), checkErrorMsg));
        }

        Optional<Failure> failure = riderOperateApplicationService.postDeliveryProofPhoto(request.getDeliveryOrderId(),
                request.getOperatorAccountId(), request.getDeliveryProofPhotoUrls(), request.getRiderIP(), request.getTenantId(), request.getStoreId());
        if (failure.isPresent()) {
            Cat.logEvent("DELIVERY_PROOF_PHOTO", "POST_PHOTO_FAIL");
            return new RiderOperateTResponse(new Status(failure.get().getFailureCode(), failure.get().getFailureMessage()));
        }

        return new RiderOperateTResponse(Status.SUCCESS);

    }


    @Override
    @MethodLog(logRequest = false, logResponse = true)
    public RiderOperateTResponse markPickDeliverySplit(RiderOperateTRequest request) {
        String errMsg = request.validate();
        if (StringUtils.isNotBlank(errMsg)) {
            return new RiderOperateTResponse(new Status(INVALID_PARAM.getCode(), errMsg));
        }
        Optional<Failure> failure = riderOperateApplicationService.markPickDeliverySplit(request.getDeliveryOrderId());
        if (failure.isPresent()) {
            Cat.logEvent("MARK_PICK_DELIVERY_SPLIT", "FAIL");
            return new RiderOperateTResponse(new Status(failure.get().getFailureCode(), failure.get().getFailureMessage()));
        }

        Cat.logEvent("MARK_PICK_DELIVERY_SPLIT", "SUCCESS");
        return new RiderOperateTResponse(Status.SUCCESS);
    }

    @Override
    public RiderOperateTResponse riderLocationBatchReport(RiderLocationBatchReportThriftRequest request) {
        log.info("骑手点位批量上报 riderLocationBatchReport request:{}", request);

        //参数校验
        if (!request.paramsValid()) {
            return new RiderOperateTResponse(Status.INVALID_PARAM);
        }

        //检查是否开启骑手点位批量上传
        if (!riderDeliveryOrderSyncOutClient.checkOpenBatchRiderPointSync(request.getStoreId(), request.getTenantId())) {
            return new RiderOperateTResponse(Status.SUCCESS);
        }

        try {
            List<RiderLocationDetail> riderLocationDetails = ConvertUtil.riderBatchToDetails(request);
            // 批量保存
            Set<Long> deliveryOrderIdSet = riderOperateApplicationService.riderLocationBatchSave(request, riderLocationDetails);

            //最后一个骑手点位上报
            riderEndPointReport(request, deliveryOrderIdSet, riderLocationDetails);

        } catch (Exception e) {
            log.error("riderOperateApplicationService.riderLocationBatchSaveAndSingleReport 批量上传骑手轨迹异常", e);
        }

        return new RiderOperateTResponse(Status.SUCCESS);
    }

    /**
     * 最后一条骑手点位上报
     */
    private void riderEndPointReport(RiderLocationBatchReportThriftRequest request, Set<Long> deliveryOrderIdSet, List<RiderLocationDetail> riderLocationDetails) {
        if (CollectionUtils.isEmpty(deliveryOrderIdSet)) {
            return;
        }
        //最后点位
        CoordinatePoint lastPoint;
        AtomicReference<RiderLocationDetail> maxData = new AtomicReference<>();
        try {
            lastPoint = riderLocationDetails.stream()
                    .max(Comparator.comparing(RiderLocationDetail::getLongTypeTime))
                    .map(item -> {
                        item.setTime(Instant.ofEpochSecond(Long.parseLong(item.getTime()))
                                .atZone(ZoneId.systemDefault())
                                .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

                        maxData.set(item);
                        return new CoordinatePoint(item.getLongitude(), item.getLatitude(), Long.parseLong(item.getTime()) * 1000);
                    })
                    .orElse(null);
        } catch (Exception e) {
            return;
        }

        if (Objects.isNull(lastPoint)) {
            return;
        }

        for (Long deliveryOrderId : deliveryOrderIdSet) {
            //获取配送订单
            DeliveryOrder deliveryOrder = deliveryOrderRepository.getDeliveryOrderWithTenant(deliveryOrderId, request.getTenantId(), request.getStoreId());
            //当配送订单存在
            if (Objects.nonNull(deliveryOrder)) {
                //同步渠道
                ocmsChannelClient.syncRiderLocation(deliveryOrder, lastPoint);

            }
        }

        //日志打点,兼容老点位上传
        locationUploadLog(request, maxData);
    }

    /**
     * 日志打点
     */
    private void locationUploadLog(RiderLocationBatchReportThriftRequest request, AtomicReference<RiderLocationDetail> maxData) {
        try {
            if (Objects.isNull(maxData.get())) {
                return;
            }
            if (DeliveryRiderMccConfigUtils.checkIsDHTenant(request.getTenantId()) || (!DeliveryRiderMccConfigUtils.fusionSelfDegreeSwitch())) {
                RiderLocationDetail riderLocationDetail = maxData.get();
                List<RiderDeliveryOrder> needSyncDeliveryOrders = riderDeliveryOrderRepository.batchQueryDeliveryOrder(request.getStoreId(),
                        Lists.newArrayList(DeliveryStatusEnum.RIDER_ASSIGNED, DeliveryStatusEnum.RIDER_TAKEN_GOODS),
                        riderLocationDetail.getRiderAccountId(),
                        request.getTenantId());
                //配送中的订单为空,不处理，防止后续异常
                if (CollectionUtils.isEmpty(needSyncDeliveryOrders)) {
                    return;
                }
                riderOperateApplicationService.reportMerchantSelfDeliveryRiderPostLocationLog(request.getTenantId(),
                        request.getStoreId(),
                        riderLocationDetail,
                        needSyncDeliveryOrders);
            }
        } catch (Exception e) {
            log.error("locationUploadLog error", e);
        }
    }

    private StaffRider translateRider(RiderOperateTRequest request) {
        return new StaffRider(request.getOperatorName(), request.getOperatorPhone(), null, request.getOperatorId());
    }

    private StaffRider translateRider(CompleteWithProofPhotoTRequest request) {
        return new StaffRider(request.getOperatorName(), request.getOperatorPhone(), null, request.getOperatorId());
    }

    @Override
    public RiderOperateTResponse reportDeliveryException(RiderDeliveryExceptionTRequest request) {
        String checkErrorMsg = request.validate();
        if (checkErrorMsg != null) {
            return new RiderOperateTResponse(new Status(INVALID_PARAM.getCode(), checkErrorMsg));
        }

        try {
            RiderDeliveryException riderDeliveryException = ConvertUtil.convertToRiderDeliveryException(request);
            Optional<Failure> failure = riderOperateApplicationService.reportDeliveryException(riderDeliveryException);
            if (failure.isPresent()) {
                return new RiderOperateTResponse(new Status(failure.get().getFailureCode(), failure.get().getFailureMessage()));
            }

            return new RiderOperateTResponse(Status.SUCCESS);
        } catch (Exception e) {
            log.error("DeliveryOperationThriftService.reportDeliveryException error", e);
            return new RiderOperateTResponse(new Status(SYSTEM_ERROR.getCode(), SYSTEM_ERROR.getMessage()));
        }

    }
}
