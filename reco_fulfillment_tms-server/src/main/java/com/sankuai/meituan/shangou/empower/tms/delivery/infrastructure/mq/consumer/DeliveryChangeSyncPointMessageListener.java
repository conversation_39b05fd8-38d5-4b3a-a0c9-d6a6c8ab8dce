package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.consumer;

import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrder;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrderRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryChannelEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.exception.CommonLogicException;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.cache.MqMsgIdempotentOperateService;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.DeliveryChangeNotifyMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.client.sync.RiderDeliveryOrderSyncOutClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.Optional;

import static com.meituan.mafka.client.consumer.ConsumeStatus.CONSUME_SUCCESS;
import static com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum.MALT_FARM_DELIVERY_PLATFORM;
import static com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum.MERCHANT_SELF_DELIVERY;

/**
 * 配送订单完成和取消消息监听,发送同步骑手轨迹消息
 */
@Slf4j
@Component
@SuppressWarnings("all")
public class DeliveryChangeSyncPointMessageListener extends AbstractDeadLetterConsumer {

    @Resource
    private RiderDeliveryOrderSyncOutClient riderDeliveryOrderSyncOutClient;

    @Resource
    private DeliveryOrderRepository deliveryOrderRepository;

    @Resource
    private MqMsgIdempotentOperateService mqMsgIdempotentOperateService;

    @Override
    protected MQConsumerEnum consumerConfig() {
        return MQConsumerEnum.DELIVERY_CHANGE_RIDER_POINT_SYNC_CUSTOMER;
    }

    /**
     * 同步骑手轨迹到渠道
     */
    @Override
    protected ConsumeStatus consume(MafkaMessage mafkaMessage) {
        try {
            log.info("开始消费配送单状态变更同步骑手点位MQ: {}", mafkaMessage);

            boolean lockStatus = mqMsgIdempotentOperateService.tryLock(mafkaMessage.getMessageID(), MqMsgIdempotentOperateService.MsgIdempotentBusinessEnum.DELIVERY_COMPLETE_AND_CANCEL_POINT_SYNC);
            if (!lockStatus) {
                log.info("DrunkHorseRiderPositionSyncMessageListener 消息幂等加锁失败 msgId:{}", mafkaMessage.getMessageID());
                return CONSUME_SUCCESS;
            }


            DeliveryChangeNotifyMessage msg = translateMessage(mafkaMessage);
            if (Objects.isNull(msg)) {
                log.error("DeliveryCompleteAndCancelMessageListener, message is null");
                return ConsumeStatus.CONSUME_SUCCESS;
            }

            //查看是否开启配置
            boolean openSync = riderDeliveryOrderSyncOutClient.checkOpenBatchRiderPointSync(msg.getShopId(), msg.getTenantId());
            if (!openSync) {
                return ConsumeStatus.CONSUME_SUCCESS;
            }

            if (Objects.equals(DeliveryStatusEnum.RIDER_ASSIGNED.getCode(), msg.getDeliveryStatus())) {
                //骑手已接单处理
                doRiderAssigned(msg);
            } else if (Objects.equals(DeliveryStatusEnum.DELIVERY_CANCELLED.getCode(), msg.getDeliveryStatus())
                    || Objects.equals(DeliveryStatusEnum.DELIVERY_DONE.getCode(), msg.getDeliveryStatus())) {
                //配送完成和配送取消处理
                doDeliveryCompleteAndCancel(msg);
            }
        } catch (CommonLogicException e) {
            log.error("DeliveryCompleteAndCancelMessageListener failed", e);
            mqMsgIdempotentOperateService.unLock(mafkaMessage.getMessageID(), MqMsgIdempotentOperateService.MsgIdempotentBusinessEnum.DELIVERY_COMPLETE_AND_CANCEL_POINT_SYNC);
        }
        return ConsumeStatus.CONSUME_SUCCESS;
    }

    /**
     * 配送完成和配送取消
     */
    private void doDeliveryCompleteAndCancel(DeliveryChangeNotifyMessage msg) {
        //获取配送订单
        DeliveryOrder deliveryOrder = deliveryOrderRepository.getDeliveryOrderWithTenant(msg.getDeliveryOrderId(), msg.getTenantId(), msg.getShopId());

        DeliveryChannelEnum deliveryChannelEnum = DeliveryChannelEnum.valueOf(deliveryOrder.getDeliveryChannel());
        if (Objects.isNull(deliveryChannelEnum)) {
            log.error("DeliveryChangeSyncPointMessageListener, deliveryChannelEnum is null");
            return;
        }

        if (!Objects.equals(deliveryChannelEnum.getDeliveryPlatform(), MALT_FARM_DELIVERY_PLATFORM)
                && !Objects.equals(deliveryChannelEnum.getDeliveryPlatform(), MERCHANT_SELF_DELIVERY)) {
            log.info("DeliveryChangeSyncPointMessageListener doDeliveryCompleteAndCancel, deliveryOrder is not merchant or MALT_FARM delivery,deliveryChannel:{}", deliveryOrder.getDeliveryChannel());
            return;
        }

        //发送消息
        riderDeliveryOrderSyncOutClient.sendRiderAllPointSyncMsg(deliveryOrder.getTenantId(), deliveryOrder.getStoreId(), deliveryOrder.getId(), deliveryOrder.getOrderId());

    }

    /**
     * 骑手已接单
     */
    private void doRiderAssigned(DeliveryChangeNotifyMessage msg) {
        //获取配送订单
        DeliveryOrder deliveryOrder = deliveryOrderRepository.getDeliveryOrderWithTenant(msg.getDeliveryOrderId(), msg.getTenantId(), msg.getShopId());

        DeliveryChannelEnum deliveryChannelEnum = DeliveryChannelEnum.valueOf(deliveryOrder.getDeliveryChannel());
        if (Objects.isNull(deliveryChannelEnum)) {
            log.error("DeliveryChangeSyncPointMessageListener, deliveryChannelEnum is null");
            return;
        }
        if (!Objects.equals(deliveryChannelEnum.getDeliveryPlatform(), MALT_FARM_DELIVERY_PLATFORM)
                && !Objects.equals(deliveryChannelEnum.getDeliveryPlatform(), MERCHANT_SELF_DELIVERY)) {
            log.info("DeliveryChangeSyncPointMessageListener doRiderAssigned, deliveryOrder is not MALT_FARM or MERCHANT_DELIVERY,deliveryChannel:{}", deliveryOrder.getDeliveryChannel());
            return;
        }

        //发送消息
        riderDeliveryOrderSyncOutClient.sendRiderPointBatchSyncMsg(deliveryOrder.getTenantId(), deliveryOrder.getStoreId(), deliveryOrder.getId(), deliveryOrder.getOrderId(), msg.getUpdateTime());

    }

    /**
     * 转换对象
     */
    private DeliveryChangeNotifyMessage translateMessage(MafkaMessage mafkaMessage) {
        try {
            return Optional.ofNullable(mafkaMessage)
                    .map(MafkaMessage::getBody)
                    .map(Object::toString)
                    .map(it -> JsonUtil.fromJson(it, DeliveryChangeNotifyMessage.class))
                    .orElse(null);
        } catch (Exception e) {
            log.error("DeliveryChangeNotifyTmsListener translateMessage error", e);
            return null;
        }
    }

}
