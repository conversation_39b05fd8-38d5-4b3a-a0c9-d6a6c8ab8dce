package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.consumer;

import com.alibaba.fastjson.JSON;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrder;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrderRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.channel.OcmsChannelClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryChannelEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.cache.BillAllRiderPointRecordOperateService;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.cache.MqMsgIdempotentOperateService;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.client.platform.MaltFarmDeliveryPlatformClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.message.RiderPointBatchSyncMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.RiderLocationDetail;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.client.sync.RiderDeliveryOrderSyncOutClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static com.meituan.mafka.client.consumer.ConsumeStatus.CONSUME_SUCCESS;
import static com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum.MALT_FARM_DELIVERY_PLATFORM;
import static com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum.MERCHANT_SELF_DELIVERY;

/**
 * 订单骑手所有点位数据同步
 */
@Slf4j
@Component
@SuppressWarnings("all")
public class RiderAllPointSyncMessageListener extends AbstractDeadLetterConsumer {

    @Resource
    private RiderDeliveryOrderSyncOutClient riderDeliveryOrderSyncOutClient;

    @Resource
    private BillAllRiderPointRecordOperateService billAllRiderPointRecordOperateService;

    @Resource
    private DeliveryOrderRepository deliveryOrderRepository;

    @Resource
    private MqMsgIdempotentOperateService mqMsgIdempotentOperateService;

    @Resource
    private OcmsChannelClient ocmsChannelClient;

    @Resource
    private MaltFarmDeliveryPlatformClient maltFarmDeliveryPlatformClient;

    @Override
    protected MQConsumerEnum consumerConfig() {
        return MQConsumerEnum.AFTER_UNIFY_SYNC_RIDER_POINT_TO_CHANNEL_CONSUMER;
    }

    /**
     * 同步骑手轨迹到渠道
     */
    @Override
    protected ConsumeStatus consume(MafkaMessage mafkaMessage) {
        try {
            log.info("开始消费配送单完结后同步骑手配送所有点位消息: {}", mafkaMessage);
            RiderPointBatchSyncMessage riderPointBatchSyncMessage = analysisAndValidParams(mafkaMessage);
            //校验参数
            if (Objects.isNull(riderPointBatchSyncMessage)) {
                log.error("消费结束，解析参数失败");
                return CONSUME_SUCCESS;
            }
            // 幂等处理
            boolean lockStatus = mqMsgIdempotentOperateService.tryLock(mafkaMessage.getMessageID(), MqMsgIdempotentOperateService.MsgIdempotentBusinessEnum.RIDER_ALL_POINT_SYNC);
            if (!lockStatus) {
                log.info("RiderAllPointSyncMessageListener 消息幂等加锁失败，已经被消费 msgId:{}", mafkaMessage.getMessageID());
                return CONSUME_SUCCESS;
            }

            //查看是否开启批量上传功能
            if (!riderDeliveryOrderSyncOutClient.checkOpenBatchRiderPointSync(riderPointBatchSyncMessage.getStoreId(), riderPointBatchSyncMessage.getTenantId())) {
                return CONSUME_SUCCESS;
            }

            //获取配送订单
            DeliveryOrder deliveryOrder = deliveryOrderRepository.getDeliveryOrderWithTenant(riderPointBatchSyncMessage.getDeliveryId(), riderPointBatchSyncMessage.getTenantId(), riderPointBatchSyncMessage.getStoreId());
            if (Objects.isNull(deliveryOrder)) {
                log.error("配送订单不存在 deliveryOrderId={}", riderPointBatchSyncMessage.getDeliveryId());
                return CONSUME_SUCCESS;
            }
            DeliveryChannelEnum deliveryChannelEnum = DeliveryChannelEnum.valueOf(deliveryOrder.getDeliveryChannel());
            if (Objects.isNull(deliveryChannelEnum)) {
                log.error("RiderPointBatchSyncMessageListener DeliveryChangeSyncPointMessageListener, deliveryChannelEnum is null");
                return CONSUME_SUCCESS;
            }

            Integer deliveryChannel = deliveryOrder.getDeliveryChannel();
            if (!Objects.equals(deliveryChannelEnum.getDeliveryPlatform(), MALT_FARM_DELIVERY_PLATFORM)
                    && !Objects.equals(deliveryChannelEnum.getDeliveryPlatform(), MERCHANT_SELF_DELIVERY)
            ) {
                log.error("配送订单需要商家自配送/麦芽田配送 deliveryOrderId={}，deliveryChannel={}", riderPointBatchSyncMessage.getDeliveryId(), deliveryChannel);
                return CONSUME_SUCCESS;
            }

            List<RiderLocationDetail> list;
            if (Objects.equals(deliveryChannelEnum.getDeliveryPlatform(), MERCHANT_SELF_DELIVERY)) {
                list = billAllRiderPointRecordOperateService.getAllListAndRemove(riderPointBatchSyncMessage.getTenantId(),
                        riderPointBatchSyncMessage.getStoreId(),
                        riderPointBatchSyncMessage.getDeliveryId(),
                        RiderLocationDetail.class);
            } else if (Objects.equals(deliveryChannelEnum.getDeliveryPlatform(), MALT_FARM_DELIVERY_PLATFORM)) {
                list = maltFarmDeliveryPlatformClient.queryRiderPointTrace(deliveryOrder.getOrderId(), 2, null, null);
            } else {
                list = Collections.emptyList();
            }

            if (CollectionUtils.isEmpty(list)) {
                log.info("骑手位置数据不存在");
                return CONSUME_SUCCESS;
            }

            //ocms渠道批量同步点位数据
            ocmsChannelClient.syncRiderAllLocationToChanel(deliveryOrder, list);
        } catch (Exception e) {
            log.error("批量同步骑手位置数据同步失败", e);
            mqMsgIdempotentOperateService.unLock(mafkaMessage.getMessageID(), MqMsgIdempotentOperateService.MsgIdempotentBusinessEnum.RIDER_ALL_POINT_SYNC);
        }

        return CONSUME_SUCCESS;
    }


    /**
     * 校验参数及解析
     */
    private RiderPointBatchSyncMessage analysisAndValidParams(MafkaMessage mafkaMessage) {

        //解析参数
        RiderPointBatchSyncMessage riderPointBatchSyncMessage = Optional.ofNullable(mafkaMessage)
                .map(MafkaMessage::getBody)
                .map(Object::toString)
                .map(it -> JSON.parseObject(it, RiderPointBatchSyncMessage.class))
                .orElse(null);
        //订单ID不能为空
        if (Objects.nonNull(riderPointBatchSyncMessage)) {
            if (Objects.isNull(riderPointBatchSyncMessage.getOrderId())) {
                log.error("消费订单id为空");
                return null;
            }
            if (Objects.isNull(riderPointBatchSyncMessage.getStoreId())) {
                log.error("门店id为空");
                return null;
            }
        }
        return riderPointBatchSyncMessage;

    }


}
