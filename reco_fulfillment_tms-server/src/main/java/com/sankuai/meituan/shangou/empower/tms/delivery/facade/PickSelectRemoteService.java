package com.sankuai.meituan.shangou.empower.tms.delivery.facade;

import com.dianping.cat.Cat;
import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.sankuai.meituan.reco.pickselect.consts.ResultCodeEnum;
import com.sankuai.meituan.reco.pickselect.ebase.thrift.ChannelTimeOutConfig;
import com.sankuai.meituan.reco.pickselect.ebase.thrift.FulfillConfig;
import com.sankuai.meituan.reco.pickselect.ebase.thrift.FulfillConfigQueryResponse;
import com.sankuai.meituan.reco.pickselect.ebase.thrift.StoreFulfillConfigThriftService;
import com.sankuai.meituan.reco.pickselect.query.thrift.lackLocked.OutboundOrderLackLockedThriftService;
import com.sankuai.meituan.reco.pickselect.query.thrift.lackLocked.dto.GoodsLackLockedDetailDTO;
import com.sankuai.meituan.reco.pickselect.query.thrift.lackLocked.request.QueryOrderLackLockedDetailRequest;
import com.sankuai.meituan.reco.pickselect.thrift.picking.rider.RiderPickingThriftService;
import com.sankuai.meituan.reco.pickselect.thrift.picking.rider.dto.RiderPickWorkOrderDTO;
import com.sankuai.meituan.reco.pickselect.thrift.picking.rider.request.QueryRiderPickWorkOrderRequest;
import com.sankuai.meituan.reco.pickselect.thrift.picking.rider.response.RiderPickWorkOrderResponse;
import com.sankuai.meituan.shangou.empower.tms.delivery.model.config.PickSelectStoreConfig;
import com.sankuai.meituan.shangou.empower.tms.delivery.task.exception.CommonRuntimeException;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@Slf4j
@Rhino
public class PickSelectRemoteService {

    @Resource
    private StoreFulfillConfigThriftService.Iface storeFulfillConfigThriftService;

    @Resource
    private RiderPickingThriftService riderPickingThriftService;

    @Resource
    private OutboundOrderLackLockedThriftService outboundOrderLackLockedThriftService;

    @MethodLog(logRequest = false, logResponse = true)
    @Degrade(
            rhinoKey = "PickSelectRemoteService.queryFulfillStoreConfig",
            fallBackMethod = "queryFulfillStoreConfigFallback", timeoutInMilliseconds = 1000
    )
    public PickSelectStoreConfig queryFulfillStoreConfig(Long tenantId, Long storeId, Integer orderBizType) {
        try {
            FulfillConfigQueryResponse response = storeFulfillConfigThriftService.batchQuery(tenantId,
                    Arrays.asList(storeId), 1L);
            if (response == null) {
                return null;
            }
            List<FulfillConfig> fulfillConfigList = response.getFulfillConfigList();
            if (CollectionUtils.isEmpty(fulfillConfigList)) {
                return null;
            }
            FulfillConfig fulfillConfig = fulfillConfigList.get(0);
            if (fulfillConfig == null) {
                return null;
            }
            PickSelectStoreConfig config = new PickSelectStoreConfig();
            config.setTenantId(tenantId);
            config.setStoreId(storeId);
            config.setBookingPushDownTime(getMinutesOfPushDownInAdvanceByOrderBizType(fulfillConfig, orderBizType));
            return config;
        } catch (TException e) {
            log.error("PickSelectRemoteService.queryFulfillStoreConfig tenantId:{},storeId:{}", tenantId, storeId, e);
            return null;
        }
    }

    @Degrade(
            rhinoKey = "PickSelectRemoteService.queryPickOrderIsContainSnProduct",
            fallBackMethod = "queryPickOrderIsContainSnProductFallback", timeoutInMilliseconds = 1000
    )
    public boolean queryPickOrderIsContainSnProduct(Long tenantId, Long storeId, Integer orderBizType, String channelOrderId) {
        try {
            QueryRiderPickWorkOrderRequest request = new QueryRiderPickWorkOrderRequest();
            request.setTenantId(tenantId);
            request.setOrderSource(orderBizType);
            request.setUnifyOrderId(channelOrderId);
            request.setEmpowerStoreId(storeId);
            log.info("invoke riderPickingThriftService.queryPickWorkOrderDetail end, request:{}", request);
            RiderPickWorkOrderResponse response = riderPickingThriftService.queryPickWorkOrderDetail(request);
            log.info("invoke riderPickingThriftService.queryPickWorkOrderDetail end, response:{}", response);
            if (response == null || response.getStatus() == null) {
                throw new CommonRuntimeException("查询拣货单明细失败");
            }

            if(!Objects.equals(response.getStatus().getCode(), ResultCodeEnum.SUCCESS.getCode())) {
                throw new CommonRuntimeException("查询拣货单明细失败");
            }

           return response.getPickWorkOrderDto().getPickTasks()
                   .stream()
                   .anyMatch(task -> task.getIsManagementSnCode() != null && task.getIsManagementSnCode() && task.getCount() > 0);
        } catch (Exception e) {
            Cat.logEvent("DH_ADAPT_SN", "QUERY_PICK_DETAIL_FAIL");
            log.error("PickSelectRemoteService.queryPickOrderIsContainSnProduct, tenantId:{},storeId:{}", tenantId, storeId, e);
            return false;
        }
    }

    public boolean queryPickOrderIsContainSnProductFallback(Long tenantId, Long storeId, Integer orderBizType, String channelOrderId) {
        log.warn("PickSelectRemoteService.queryPickOrderIsContainSnProduct 已被降级, channelOrderId:{}", channelOrderId);
        return false;
    }

    private PickSelectStoreConfig queryFulfillStoreConfigFallback(Long tenantId, Long storeId, Integer orderBizType) {
        log.info("PickSelectRemoteService.queryFulfillStoreConfig is degrade tenantId:{},storeId:{}", tenantId,
                storeId);
        return null;
    }

    private Integer getMinutesOfPushDownInAdvanceByOrderBizType(FulfillConfig fulfillConfig, Integer orderBizType) {
        try {
            if (fulfillConfig.getTimeoutType() == 0) {
                return fulfillConfig.getMinutesOfPushDownInAdvance();
            } else {
                // orderBiz相关的渠道 取不到，取美团的，美团的取不到，取默认
                final Integer orderBizTypeEnum = Optional.ofNullable(orderBizType)
                        .orElse(DynamicOrderBizType.MEITUAN_WAIMAI.getValue());
                final Map<Integer, ChannelTimeOutConfig> timeOutConfigInfoMap = Optional.ofNullable(fulfillConfig.getChannelTimeOutConfig()).orElse(Collections.emptyList())
                        .stream().filter(item -> Objects.nonNull(item.getOrderBizType()))
                        .collect(Collectors.toMap(ChannelTimeOutConfig::getOrderBizType, it -> it, (a, b) -> a));
                final ChannelTimeOutConfig channelTimeOutConfigInfo = Optional
                        .ofNullable(timeOutConfigInfoMap.get(orderBizTypeEnum))
                        .orElse(timeOutConfigInfoMap.get(DynamicOrderBizType.MEITUAN_WAIMAI.getValue()));
                if (channelTimeOutConfigInfo == null) {
                    // 默认为90分钟下发
                    return null;
                } else {
                    return channelTimeOutConfigInfo.getMinutesOfPushDownInAdvance();
                }
            }
        } catch (Exception e) {
            Cat.logEvent("GET_TIMEOUT_ERROR", "ERROR");
            log.error("getMinutesOfPushDownInAdvanceByOrderBizType error", e);
            return fulfillConfig.getMinutesOfPushDownInAdvance();
        }
    }

    public List<GoodsLackLockedDetailDTO>  queryOrderLackLockedDetail(Long tenantId,Long warehouseId,String channelOrderId,Integer orderBizType) {


        try {
            QueryOrderLackLockedDetailRequest request = new QueryOrderLackLockedDetailRequest();
            request.setTenantId(tenantId);
            request.setOfflineStoreId(warehouseId);
            request.setChannelOrderId(channelOrderId);
            request.setOrderBizType(orderBizType);


            TResult<List<GoodsLackLockedDetailDTO>>  result = outboundOrderLackLockedThriftService.queryOrderLackLockedDetail(request);
            log.info("queryOrderLackLockedDetail end, result:{}", result);
            if(result==null || CollectionUtils.isEmpty(result.getData())) {
                return Collections.emptyList();
            }
            return result.getData();
        }catch (Exception e) {
            log.error("queryOrderLackLockedDetail error", e);
        }
        return Collections.emptyList();
    }

}
