package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.repository;

import com.dianping.squirrel.client.StoreKey;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.LionConfigNameEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.RiderLocationDataDO;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.cache.BillCurrentRiderPointRecordOperateService;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.cache.DrunkHorseRiderLocationSquirrelOperateService;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.cache.RiderLocationSquirrelOperateService;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.RiderLocationDetail;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.RiderLocationRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.ThirdRiderKey;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.ThirdRiderPoint;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.utils.TimeUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.utils.ConvertUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.sankuai.meituan.shangou.empower.tms.delivery.rider.utils.CatLogEventReportUtils.reportSelfDeliveryRiderLocationSaveOrGetLogEvent;

@Slf4j
@Service
public class SquirrelRiderLocationRepository implements RiderLocationRepository {

    @Resource
    private RiderLocationSquirrelOperateService squirrelOperateService;

    @Resource
    private DrunkHorseRiderLocationSquirrelOperateService drunkHorseRiderLocationSquirrelOperateService;

    @Resource
    private BillCurrentRiderPointRecordOperateService billCurrentRiderPointRecordOperateService;

    @Override
    @MethodLog(logRequest = false, logResponse = true)
    public void save(RiderLocationDetail riderLocationDetail) {
        if(riderLocationDetail==null){
            reportSelfDeliveryRiderLocationSaveOrGetLogEvent("save",  false);
            return;
        }

        RiderLocationDataDO riderLocationData = ConvertUtil.convertToRiderLocation(riderLocationDetail);
        if (MccConfigUtils.getRiderLocationRedisMigrateSwitch()) {
            drunkHorseRiderLocationSquirrelOperateService.set(riderLocationDetail.getRiderAccountId() + "", riderLocationData);
        } else {
            squirrelOperateService.set(riderLocationDetail.getRiderAccountId()+"",riderLocationData);
        }

        reportSelfDeliveryRiderLocationSaveOrGetLogEvent("save",  true);
    }

    @Override
    @MethodLog(logRequest = false, logResponse = true)
    public void saveThirdLocation(ThirdRiderPoint thirdRiderPoint) {
        if (MccConfigUtils.getRiderLocationRedisMigrateSwitch()) {
            drunkHorseRiderLocationSquirrelOperateService.set(thirdRiderPoint.getThirdRiderKey().toKey(), thirdRiderPoint);
        } else {
            squirrelOperateService.set(thirdRiderPoint.getThirdRiderKey().toKey(), thirdRiderPoint);
        }
        reportSelfDeliveryRiderLocationSaveOrGetLogEvent("saveThird",  true);
    }


    @Override
    public RiderLocationDetail getStaffRiderLocation(long riderAccountId) {
        try {
            Optional<RiderLocationDataDO> dataOptional;
            if (MccConfigUtils.getRiderLocationRedisMigrateSwitch()) {
                dataOptional = drunkHorseRiderLocationSquirrelOperateService.get(riderAccountId + "", RiderLocationDataDO.class);
            } else {
                dataOptional = squirrelOperateService.get(riderAccountId + "", RiderLocationDataDO.class);
            }
            if(!dataOptional.isPresent()){
                reportSelfDeliveryRiderLocationSaveOrGetLogEvent("get",false);
                return null;
            }
            RiderLocationDataDO locationData=dataOptional.get();
            //缓存内数据超过设定的时间则为无效数据
            if(!isValueLocationData(locationData)){
                reportSelfDeliveryRiderLocationSaveOrGetLogEvent("get", false);
                return null;
            }
            reportSelfDeliveryRiderLocationSaveOrGetLogEvent("get", true);
            return ConvertUtil.convertToLocationDetail(locationData);
        } catch (Exception e) {
            log.error("查询骑手位置失败", e);
            return null;
        }

    }

    @Override
    public RiderLocationDataDO getLatestStaffRiderLocation(long riderAccountId) {
        try {
            Optional<RiderLocationDataDO> dataOptional;
            if (MccConfigUtils.getRiderLocationRedisMigrateSwitch()) {
                dataOptional = drunkHorseRiderLocationSquirrelOperateService.get(riderAccountId + "", RiderLocationDataDO.class);
            } else {
                dataOptional = squirrelOperateService.get(riderAccountId + "", RiderLocationDataDO.class);
            }

            return dataOptional.orElse(null);
        } catch (Exception e) {
            log.error("查询骑手位置失败", e);
            return null;
        }
    }

    @Override
    public Map<Long, RiderLocationDetail> getStaffRiderLocations(List<Long> riderAccountIds) {
        if (CollectionUtils.isEmpty(riderAccountIds)) {
            return Collections.emptyMap();
        }

        List<String> riderAccountIdList = riderAccountIds.stream().map(Object::toString).collect(Collectors.toList());
        Map<StoreKey, Optional<RiderLocationDataDO>> locationMap;
        if (MccConfigUtils.getRiderLocationRedisMigrateSwitch()) {
            locationMap = drunkHorseRiderLocationSquirrelOperateService.multiGet(riderAccountIdList, RiderLocationDataDO.class);
        } else {
            locationMap = squirrelOperateService.multiGet(riderAccountIdList, RiderLocationDataDO.class);
        }


        if (locationMap.isEmpty()) {
            return Collections.emptyMap();
        }

        HashMap<Long, RiderLocationDetail> result = new HashMap<>();
        locationMap.forEach(((storeKey, riderLocationDataOpt) -> {
            if (riderLocationDataOpt.isPresent()) {
                RiderLocationDataDO riderLocationDataDO = riderLocationDataOpt.get();
                if (isValueLocationData(riderLocationDataDO)) {
                    result.put(riderLocationDataDO.getRiderAccountId(), ConvertUtil.convertToLocationDetail(riderLocationDataDO));
                }
            }
        }));

        return result;
    }

    @Override
    public Map<ThirdRiderKey, ThirdRiderPoint> getThirdRiderLocations(List<String> thirdRiderKeys) {
        if (CollectionUtils.isEmpty(thirdRiderKeys)) {
            return Collections.emptyMap();
        }

        Map<StoreKey, Optional<ThirdRiderPoint>> locationMap;
        if (MccConfigUtils.getRiderLocationRedisMigrateSwitch()) {
            locationMap = drunkHorseRiderLocationSquirrelOperateService.multiGet(thirdRiderKeys, ThirdRiderPoint.class);
        } else {
            locationMap = squirrelOperateService.multiGet(thirdRiderKeys, ThirdRiderPoint.class);
        }


        if (locationMap.isEmpty()) {
            return Collections.emptyMap();
        }

        return locationMap.values()
                .stream()
                .filter(Optional::isPresent)
                .map(Optional::get)
                .filter(thirdRiderPoint -> isValueLocationData(thirdRiderPoint.getTime()))
                .collect(Collectors.toMap(
                        ThirdRiderPoint::getThirdRiderKey,
                        Function.identity(),
                        (older, newer) -> newer
                ));
    }

    @Override
    public void batchIncrementSave(Long tenantId, Long storeId, Long orderId, List<RiderLocationDetail> list) {
        if (Objects.isNull(tenantId)
                || Objects.isNull(storeId)
                || Objects.isNull(orderId)
                || CollectionUtils.isEmpty(list)) {
            return;
        }
        Integer expireInSeconds = com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils.getLionIntConf(LionConfigNameEnum.RIDER_CURRENT_POINT_REDIS_EXPIRE_TIME.getName(), BillCurrentRiderPointRecordOperateService.expireSeconds);
        billCurrentRiderPointRecordOperateService.addList(tenantId, storeId, orderId, list, expireInSeconds);
    }

    @Deprecated
    public Boolean isValueLocationData(RiderLocationDataDO riderLocationDataDO) {
        try {
            long now = System.currentTimeMillis();
            int subDta=(int)(now-Optional.ofNullable(TimeUtil.toMilliSeconds(riderLocationDataDO.getTime())).orElse(0L))/1000;
            return subDta < MccConfigUtils.getRiderLocationDataInvalidTime();
        } catch (Exception e) {
            log.error("解析时间出错", e);
            return false;
        }
    }

    public boolean isValueLocationData(Long millTime) {
        try {
            long now = System.currentTimeMillis();
            int subDta=(int)(now-Optional.ofNullable(millTime).orElse(0L))/1000;
            return subDta < MccConfigUtils.getRiderLocationDataInvalidTime();
        } catch (Exception e) {
            return false;
        }
    }
}
