package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.producer;

import com.google.common.base.Preconditions;
import com.meituan.mafka.client.MafkaClient;
import com.meituan.mafka.client.consumer.ConsumerConstants;
import com.meituan.mafka.client.producer.IProducerProcessor;
import com.meituan.mafka.client.producer.ProducerResult;
import com.meituan.mafka.client.producer.ProducerStatus;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.PreDestroy;
import java.util.Properties;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/10
 */
@Slf4j
@SuppressWarnings("SpellCheckingInspection")
public class MafkaDelayMessageProducer<T> {
	private static final String DEFAULT_NAME_SPACE = "waimai";

	private final IProducerProcessor<Object, String> processor;

	public MafkaDelayMessageProducer(MQProducerEnum producer) throws Exception {
		Preconditions.checkNotNull(producer, "producer is null");

		Properties properties = new Properties();

		properties.setProperty(ConsumerConstants.MafkaBGNamespace, producer.getNameSpace());

		properties.setProperty(ConsumerConstants.MafkaClientAppkey, producer.getAppkey());

		processor = MafkaClient.buildDelayProduceFactory(properties, producer.getTopic(), false);
	}

	@PreDestroy
	public void destroy() {
		try {
			this.processor.close();
		} catch (Exception e) {
			log.error("Shut down mq producer failed", e);
		}
	}

	/**
	 * 发送Mafka消息
	 *
	 * @param message 消息体
	 */
	public void sendDelayMessage(T message, Integer delayMinutes) {
		Preconditions.checkNotNull(message, "message is null");
		Preconditions.checkArgument(delayMinutes > 0, "delayMinutes must bigger than 0");

		ProducerResult produceResult;
		String messageJson = JsonUtil.toJson(message);
		try {
			produceResult = processor.sendDelayMessage(messageJson, delayMinutes * 60 * 1000L);
		} catch (Exception e) {
			log.error("发送Mafka延时消息失败, type=[{}] message=[{}]", message.getClass().getName(), messageJson, e);
			throw new MessageSendFailedException(e);
		}

		if (produceResult != null && ProducerStatus.SEND_OK == produceResult.getProducerStatus()) {
			log.info("发送Mafka延时消息成功, type=[{}] message=[{}]", message.getClass().getName(), messageJson);
		} else {
			log.error("发送Mafka延时消息失败, type=[{}] message=[{}]", message.getClass().getName(), messageJson);
			throw new MessageSendFailedException("发送Mafka延时消息失败");
		}
	}

	/**
	 * 发送 Mafka 延时消息（毫秒）.
	 *
	 * @param message     消息
	 * @param delayMillis 延时毫秒数
	 */
	public void sendDelayMessageInMillis(T message, long delayMillis) {
		Preconditions.checkNotNull(message, "message is null");
		Preconditions.checkArgument(delayMillis >= 5000, "delayMillis must bigger than 5000");
		ProducerResult producerResult;
		String messageJson = JsonUtil.toJson(message);
		try {
			producerResult = processor.sendDelayMessage(messageJson, delayMillis);
		} catch (Exception e) {
			log.error("发送Mafka延时消息失败, type=[{}] message=[{}]", message.getClass().getName(), messageJson, e);
			throw new MessageSendFailedException(e);
		}

		if (producerResult != null && ProducerStatus.SEND_OK == producerResult.getProducerStatus()) {
			log.info("发送Mafka延时消息成功, type=[{}] message=[{}]", message.getClass().getName(), messageJson);
		} else {
			log.error("发送Mafka延时消息失败, type=[{}] message=[{}]", message.getClass().getName(), messageJson);
			throw new MessageSendFailedException("发送Mafka延时消息失败");
		}
	}
}
