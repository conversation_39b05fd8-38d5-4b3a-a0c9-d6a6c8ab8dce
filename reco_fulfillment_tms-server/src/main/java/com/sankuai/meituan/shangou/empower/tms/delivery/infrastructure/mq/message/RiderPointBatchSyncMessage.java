package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.message;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 骑手点位批量同步消息题
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class RiderPointBatchSyncMessage {

    /**
     * 运单Id
     */
    private Long deliveryId;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 店铺id
     */
    private Long storeId;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 订单id
     */
    private Long lastSyncEndTime;

}
