package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.consumer;

import com.sankuai.meituan.util.ConfigUtilAdapter;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/11
 */
@Slf4j
@Getter
public enum MQConsumerEnum {

	/**
	 * 监听新门店创建消息
	 */
	NEW_STORE_CONSUMER("shangou_saastenant_poi", "saas_create_store_comsumer_tms", "com.sankuai.sgfulfillment.tms"),
	/**
	 * 监听订单状态变更
	 */
	ORDER_STATUS_CHANGE("shangou_empower_order_status_topic", "shangou_empower_delivery_service", "com.sankuai.shangou.empower.orderbiz"),

	/**
	 * 订单收货人信息变更
	 */
	ORDER_RECEIVER_UPDATE("shangou_empower_delivery_userphone_retrieved_topic", "shangou_empower_mt_famous_tavern_order_trans_consumer", "com.sankuai.sgfulfillment.tms", "com.sankuai.mafka.castle.daojiacommon"),

	/**
	 * 监听订单状态变更
	 */
	ORDER_STATUS_CHANGE_FOR_ES("shangou_empower_order_status_topic", "shangou_empower_delivery_service_es", "com.sankuai.sgfulfillment.tms"),

	/**
	 * 拣货完成
	 */
	PICK_FINISH("shangou_empower_picked_finish_topic", "shangou_empower_delivery_service", "com.sankuai.shangou.empower.orderbiz"),
	/**
	 * 监听发起配送指令
	 */
	DELIVERY_LAUNCH_COMMAND("shangou_empower_delivery_launch_delivery_topic", "shangou_empower_delivery_service", "com.sankuai.shangou.empower.orderbiz"),

	/**
	 * 新供给监听发起配送指令
	 */
	NEW_SUPPLY_DELIVERY_LAUNCH_COMMAND("shangou_empower_new_supply_delivery_launch_delivery_topic", "shangou_empower_new_supply_delivery_service", "com.sankuai.sgfulfillment.tms"),

	/**
	 * 监听取消配送指令
	 *
	 * @since 1.4
	 */
	DELIVERY_CANCEL_COMMAND("shangou_empower_delivery_cancel_topic", "shangou_empower_delivery_service", "com.sankuai.shangou.empower.orderbiz"),

	/**
	 * 通用运单变更同步通知，订单、cbizmanagement、tms等服务都可监听
	 */
	DELIVERY_CHANGE_ASYNC_OUT("shangou_empower_delivery_change_async_out_topic", "shangou_empower_delivery_service","com.sankuai.sgfulfillment.tms"),

	/**
	 * 监听骑手分配超时
	 */
	RIDER_ASSIGN_TIME_OUT("shangou_empower_delivery_overtime_check_topic", "order_biz_delivery_check", "com.sankuai.shangou.empower.orderbiz"),
	/**
	 * 监听配送事件指令
	 *
	 * @since 1.4
	 */
	DELIVERY_EVENT("shangou_empower_delivery_event_topic", "shangou_empower_delivery_service", "com.sankuai.shangou.empower.orderbiz"),
	/**
	 * 监听订单配送进度更新
	 * (主要监听订单平台配送的场景，目前是有订单系统对接订单配送回调)
	 */
	ORDER_DELIVERY_UPDATE("shangou_empower_delivery_status_update_topic", "com.sankuai.shangou.empower.orderbiz", "com.sankuai.shangou.empower.orderbiz"),
	/**
	 * 监听渠道配送回调
	 *
	 * @since 1.4
	 */
	DELIVERY_CALLBACK("shangou_empower_delivery_callback_topic", "shangou_empower_delivery_service", "com.sankuai.shangou.empower.orderbiz"),
	/**
	 * 监听渠道配送门店变更
	 *
	 * @since 1.4
	 */
	DELIVERY_STORE_UPDATE("shangou_empower_delivery_store_update_topic", "shangou_empower_delivery_service", "com.sankuai.shangou.empower.orderbiz"),
	/**
	 * 监听配送流水
	 *
	 * @since 1.4
	 */
	DELIVERY_LOG_COMMAND("shangou_empower_delivery_log_topic", "shangou_empower_delivery_service", "com.sankuai.shangou.empower.orderbiz"),
	/**
	 * 监听同步骑手位置消息
	 *
	 * @since 1.4
	 */
	RIDER_LOCATION_SYNC_CONSUMER("rider_location_sync", "rider_location_sync_consumer", "com.sankuai.sgfulfillment.tms"),
	/**
	 * 骑手位置批量同步监听器
	 */
	BATCH_SYNC_RIDER_POINT_TO_CHANNEL_CONSUMER("batch_sync_rider_point_to_channel", "batch_sync_rider_point_to_channel_consumer", "com.sankuai.sgfulfillment.tms", "com.sankuai.mafka.castle.daojiacommon"),
	/**
	 * 配送单结束骑手位置批量同步监听器
	 */
	AFTER_UNIFY_SYNC_RIDER_POINT_TO_CHANNEL_CONSUMER("after_unify_sync_rider_point_to_channel", "after_unify_sync_rider_point_to_channel_consumer", "com.sankuai.sgfulfillment.tms", "com.sankuai.mafka.castle.daojiacommon"),
	/**
	 * 订单状态变更骑手位置同步监听器
	 */
	DELIVERY_CHANGE_RIDER_POINT_SYNC_CUSTOMER("shangou_empower_delivery_change_topic", "delivery_change_rider_point_sync_customer", "com.sankuai.sgfulfillment.tms"),

	/**
	 * 清除配送异常
	 * (用于兜底清除配送失败异常)
	 */
	CLEAR_DELIVERY_EXCEPTION_CONSUMER("shangou_empower_delivery_clear_exception", "shangou_empower_delivery_clear_exception_consumer", "com.sankuai.sgfulfillment.tms"),
	/**
	 * 检查配送是否超时
	 */
	DELIVERY_TIMEOUT_CHECK_CONSUMER("shangou_empower_delivery_timeout_check_topic", "shangou_empower_delivery_service", "com.sankuai.sgfulfillment.tms"),

	/**
	 * 配送统一回调消费组
	 */
	DELIVERY_UNIFIED_CALLBACK_CONSUMER("shangou_empower_delivery_unify_callback", "shangou_empower_delivery_service", "com.sankuai.sgfulfillment.tms"),

	/**
	 * 歪马配送状态检查消费组
	 */
	DH_DELIVERY_STATUS_CHECK_CONSUMER("shangou_empower_delivery_dh_status_check", "shangou_empower_delivery_service", "com.sankuai.sgfulfillment.tms"),

	/**
	 * 压测-订单状态变更消息
	 */
	PRESSURE_ORDER_STATUS_CONSUMER("shangou_empower_order_status_pressure_topic","shangou_empower_delivery_service_pressure", "com.sankuai.sgfulfillment.tms"),
	/**
	 * 新供给配送超时检查消费组
	 */
	NEW_SUPPLY_DELIVERY_TIMEOUT_CHECK("shangou_empower_delivery_new_supply_status_check", "shangou_empower_delivery_service", "com.sankuai.sgfulfillment.tms"),

	DELIVERY_ORDER_TRANS_CONSUMER("shangou_empower_delivery_order_transform_topic","shangou_empower_delivery_order_trans_consumer", "com.sankuai.sgfulfillment.tms"),

	/**
	 * 延迟通知配送状态变更消费组
	 */
	DELAY_NOTIFY_DELIVERY_STATUS_CONSUMER("delay_notify_delivery_status_change", "delay_notify_delivery_status_change_consumer",  "com.sankuai.sgfulfillment.tms"),
	/**
	 * 同城配送包裹状态变更消费组
	*/
	YZ_DELIVERY_ORDER_CHANGE_CONSUMER("shangou_empower_yz_delivery_order_change_topic", "shangou_empower_yz_delivery_order_change_consumer", "com.sankuai.sgfulfillment.tms"),

	/**
	 * 抖音平台配送状态变更消费组
	 */
	DY_DELIVERY_ORDER_CHANGE_CONSUMER("shangou_empower_dy_delivery_order_change_topic", "shangou_empower_dy_delivery_order_change_consumer", "com.sankuai.sgfulfillment.tms"),

	CREDIT_AUDIT_BATCH_NOTIFY_CONSUMER("credit.audit.batch.notify", "shangou_empower_delivery_service", "com.sankuai.sgfulfillment.tms", "common"),

	/**
	 * 送达照片送审自动审批触发消息
	 */
	CREDIT_AUDIT_AUTOMATIC_PASS_CONSUMER("delivery.proof.photo.credit.audit.automatic.pass.topic", "shangou_empower_delivery_service", "com.sankuai.sgfulfillment.tms"),


	/**
	 * 歪马专用订单状态变更消息消费组
	 */
	DRUNK_HORSE_ORDER_STATUS_CONSUMER("shangou_empower_order_status_topic", "drunk_horse_order_status_consumer", "com.sankuai.sgfulfillment.tms"),

	SG_EMPOWER_DELIVERY_HU_SYNC_CONSUMER("shangou_empower_delivery_hu_sync_topic","shangou_empower_delivery_hu_sync_consumer","com.sankuai.sgfulfillment.tms"),

    OCMS_STORE_CHANGE_TMS_CONSUMER("shangou_ocms_store_change_topic", "shangou_ocms_store_change_tms_comsumer",
            "com.sankuai.sgfulfillment.tms"),

	DTS_DELIVERY_ORDER_CONSUMER("dts_topic.sgfulfillment.tms.delivery_order", "shangou_empower_delivery_dts_consumer", "com.sankuai.sgfulfillment.tms"),

	DELIVERY_ORDER_REPORT_CONSUMER("dts_topic.sgfulfillment.tms.delivery_order", "shangou_empower_delivery_report_consumer", "com.sankuai.sgfulfillment.tms"),

	DELIVERY_ORDER_REPORT_COMPENSATE_CONSUMER("full.fill.message.topic", "tms_report_consumer", "com.sankuai.sgfulfillment.tms"),

	OFFLINE_PROMOTE_PICK_PUSH_DOWN_CONSUMER("notify_offline_promote_pick_order_push_down","offline_promote_pick_order_push_down_consumer","com.sankuai.sgfulfillment.tms"),

	RIDER_CHANGE_MESSAGE_CONSUMER("shangou_empower_delivery_change_async_out_topic","rider_change_message_listener","com.sankuai.sgfulfillment.tms"),

	OFC_OFW_FULFILLMENT_DOWN_CONSUMER("ofc_fulfill_down_stream_msg","ofc_fulfill_down_stream_delivery_consumer","com.sankuai.sgfulfillment.tms"),

	OPEN_API_DELIVERY_ORDER_CHANGE_CONSUMER("shangou_empower_open_api_delivery_status_update_topic", "shangou_empower_open_api_delivery_order_change_consumer", "com.sankuai.sgfulfillment.tms"),

	/**
	 * 跑腿锁单结束消息
	 */
	PAO_TUI_ORDER_UNLOCK_CONSUMER("shangou_empower_order_lock_end_topic", "shangou_empower_pao_tui_order_unlock_consumer", "com.sankuai.sgfulfillment.tms"),

	/**
	 * 监听同步歪马骑手位置消息
	 *
	 * @since 1.4
	 */
	DRUNK_HORSE_RIDER_LOCATION_SYNC_CONSUMER("rider_location_sync", "drunk_horse_rider_location_sync_consumer", "com.sankuai.sgfulfillment.tms"),


	DELIVERY_ORDER_STATUS_DELAY_CHECK_CONSUMER("delivery.order.status.delay.check.topic","delivery_order_status_delay_check_consumer","com.sankuai.sgfulfillment.tms"),

	DELIVERY_OFC_STATUS_CHANGE_CONSUMER("ofc_multi_channel_fulfill_down_stream_topic","delivery_ofc_status_change_consumer","com.sankuai.sgfulfillment.tms"),

	ORDER_COMPENSATION_CONSUMER("shangou_empower_order_event_channel_topic", "shangou_empower_delivery_service", "com.sankuai.sgfulfillment.tms"),

	DRUNK_HORSE_DELIVERY_DONE_OR_CANCEL_CONSUMER("shangou_empower_delivery_change_async_out_topic", "dh_delivery_done_or_cancel_consumer", "com.sankuai.sgfulfillment.tms"),

	SEAL_CONTAINER_RETURN_TIMEOUT_CONSUMER("seal_container_return_timeout_topic", "seal_container_return_timeout_consumer", "com.sankuai.sgfulfillment.tms", "com.sankuai.mafka.castle.daojiacommon"),

	COMPENSATE_FULFILLMENT_ORDER_CONSUMER("compensate_fulfillment_order_message", "compensate_fulfillment_order_tms_consumer", "com.sankuai.sgfulfillment.tms", "com.sankuai.mafka.castle.daojiacommon"),

	SELF_DELIVERY_ORDER("self_delivery_order_topic", "self_delivery_order_consumer", "com.sankuai.sgfulfillment.tms", "com.sankuai.mafka.castle.daojiacommon"),

	SEND_DELIVERY_ORDER_CHANGE_SHARK_PUSH_CONSUMER("shangou_empower_delivery_change_async_out_topic", "send_delivery_order_change_shark_push_consumer", "com.sankuai.sgfulfillment.tms"),


	PLATFORM_DELIVERY_CHECK_CONSUMER("shangou_empower_platform_delivery_check_topic","shangou_empower_platform_delivery_check_consumer","com.sankuai.sgfulfillment.tms","com.sankuai.mafka.castle.daojiacommon"),

	RIDER_CHANGE_TRANS_MATERIAL_STOCK_CONSUMER("rider_change_trans_material_stock_topic", "rider_change_trans_material_stock_consumer", "com.sankuai.sgfulfillment.tms", "com.sankuai.mafka.castle.daojiacommon"),

	;

	private static final int DEFAULT_MAX_RETRY_TIMES = 3;

	private final String topic;
	private final String consumerGroup;
	private final String appkey;
	private final String nameSpace;

	MQConsumerEnum(String topic, String consumerGroup, String appkey) {
		this.topic = topic;
		this.consumerGroup = consumerGroup;
		this.appkey = appkey;
		this.nameSpace = "waimai";
	}

	MQConsumerEnum(String topic, String consumerGroup, String appkey, String nameSpace) {
		this.topic = topic;
		this.consumerGroup = consumerGroup;
		this.appkey = appkey;
		this.nameSpace = nameSpace;
	}

	public int getMaxRetryTimes() {
		String mccKey = "";
		try {
			mccKey = String.format("mq.max_retry_times.%s", this.topic);
			return ConfigUtilAdapter.getInt(mccKey, DEFAULT_MAX_RETRY_TIMES);
		} catch (Exception e) {
			log.error("MCC[{}] parse error, will use default max retry times", mccKey, e);
			return DEFAULT_MAX_RETRY_TIMES;
		}
	}
}
