package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.client.order;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.ImmutableSet;
import com.meituan.shangou.saas.dto.StatusCodeEnum;
import com.meituan.shangou.saas.o2o.dto.model.BizOrderDeliveryModel;
import com.meituan.shangou.saas.o2o.dto.model.BizOrderItemModel;
import com.meituan.shangou.saas.o2o.dto.model.BizOrderModel;
import com.meituan.shangou.saas.o2o.dto.request.BizOrderQuery4Request;
import com.meituan.shangou.saas.o2o.dto.request.BizOrderQueryByOrderIdRequest;
import com.meituan.shangou.saas.o2o.dto.request.BizOrderQueryByViewOrderIdRequest;
import com.meituan.shangou.saas.o2o.dto.request.BizOrderThirdDeliveryInfoUpdateRequest;
import com.meituan.shangou.saas.o2o.dto.response.BizOrderQueryResponse;
import com.meituan.shangou.saas.o2o.dto.response.BizOrderThirdDeliveryInfoUpdateResponse;
import com.meituan.shangou.saas.o2o.service.BizOrderThriftService;
import com.meituan.shangou.saas.order.management.client.dto.request.BatchQueryOrderRequest;
import com.meituan.shangou.saas.order.management.client.dto.request.online.OCMSListViewIdConditionRequest;
import com.meituan.shangou.saas.order.management.client.dto.request.online.ViewIdCondition;
import com.meituan.shangou.saas.order.management.client.dto.response.OrderDetailBatchQueryResponse;
import com.meituan.shangou.saas.order.management.client.dto.response.online.OCMSListViewIdConditionResponse;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OCMSOrderVO;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OnlineOrderVO;
import com.meituan.shangou.saas.order.management.client.enums.OrderBatchQueryFieldModelEnum;
import com.meituan.shangou.saas.order.management.client.service.online.OCMSQueryThriftService;
import com.meituan.shangou.saas.order.management.client.service.online.OnlineOrderQueryThriftService;
import com.meituan.shangou.saas.order.platform.client.dto.model.DeliveryInfoExtModel;
import com.meituan.shangou.saas.order.platform.client.dto.request.DeliveryInfoUpdateRequest;
import com.meituan.shangou.saas.order.platform.client.dto.response.DeliveryInfoUpdateResponse;
import com.meituan.shangou.saas.order.platform.client.service.DeliveryInfoThriftService;
import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.sankuai.meituan.shangou.dms.base.model.value.DeliveryChannel;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.ResponseStatus;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.SelfDeliveryRequest;
import com.sankuai.meituan.shangou.empower.ocms.thrift.service.BizOrderTenantThriftService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.DeliveryChannelApplicationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderInfo.GoodsInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.order.OrderSystemClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.platform.DeliveryPlatformClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.TimeUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.facade.SkuRemoteService;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.DeliveryChangeNotifyMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.DeliveryExceptionNotifyMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.DeliveryTransNotifyMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.OrderLockEndNotifyMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.producer.MafkaMessageProducer;
import com.sankuai.meituan.shangou.dms.base.model.value.Address;
import com.sankuai.meituan.shangou.dms.base.model.value.CoordinatePoint;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoi;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoiRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Failure;
import com.sankuai.meituan.shangou.dms.base.model.value.Receiver;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Result;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.client.osw.OswClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.utils.CoordinateUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.utils.RetryTemplateUtil;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.domain.SkuInfoWithGroup;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.domain.SkuSizeInfo;
import com.sankuai.meituan.shangou.saas.common.cat.CatTransaction;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.shangou.infra.osw.api.poi.warehouse.dto.response.RegionDTO;
import com.sankuai.shangou.infra.osw.api.poi.warehouse.dto.response.WarehouseDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.assertj.core.util.Lists;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.retry.RetryCallback;
import org.springframework.retry.RetryContext;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.meituan.shangou.saas.order.platform.enums.DistributeStatusEnum.*;
import static com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.CoordinateUtil.translateToCoordinatePoint;

/**
 * 订单系统客户端实现
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/9
 */
@Slf4j
@Service
@Order(Ordered.HIGHEST_PRECEDENCE)
@Rhino
public class OrderSystemClientImpl implements OrderSystemClient {

	private static final int SUCCESS = 0;
	private static final Set<Integer> IN_DELIVERY_STATUS_SET = ImmutableSet.of(
			WAIT_FOR_ASSIGN_RIDER.getValue(), RIDER_ASSIGNED.getValue(), RIDER_REACH_SHOP.getValue(),
			RIDER_TAKE_GOODS.getValue(), RIDER_DELIVERED.getValue()
	);

	@Resource
	private BizOrderThriftService bizOrderThriftServiceClient;
	@Resource
	private BizOrderTenantThriftService.Iface bizOrderTenantThriftServiceClient;
	@Resource
	private MafkaMessageProducer<DeliveryChangeNotifyMessage> deliveryChangeNotifyMessageProducer;

	@Resource
	private MafkaMessageProducer<DeliveryExceptionNotifyMessage> deliveryExceptionNotifyMessageProducer;

	@Resource
	private MafkaMessageProducer<DeliveryTransNotifyMessage> deliveryTransNotifyMessageMafkaMessageProducer;

	@Resource
	private MafkaMessageProducer<OrderLockEndNotifyMessage> orderLockEndNotifyMessageMafkaMessageProducer;
	@Resource
	private DeliveryInfoThriftService deliveryInfoThriftServiceClient;
	@Resource
	private SkuRemoteService skuRemoteService;
	@Resource
	private DeliveryPoiRepository deliveryPoiRepository;
	@Resource
	private DeliveryPlatformClient deliveryPlatformClient;

	@Resource
	private OCMSQueryThriftService ocmsQueryThriftService;

	@Resource
	private DeliveryChannelApplicationService deliveryChannelApplicationService;

	@Resource
	private OnlineOrderQueryThriftService onlineOrderQueryThriftService;

	@Resource
	private NewSupplyRiderLocationRepository newSupplyRiderLocationRepository;

	@Resource
	private OswClient oswClient;

	@Override
	@CatTransaction
	public Result<OrderInfo> getOrderInfo(OrderKey orderKey, boolean needWeightInfo) {
		return this.getOrderInfo(orderKey.getTenantId(), orderKey.getOrderId(), needWeightInfo);
	}

	@Override
	@CatTransaction
	public Result<OrderInfo> getOrderInfo(long tenantId, long poiId, String channelOrderId, int channelId, boolean needWeightInfo) {
		try {
			BizOrderQueryByViewOrderIdRequest request = new BizOrderQueryByViewOrderIdRequest();
			DynamicOrderBizType orderBizType = DynamicOrderBizType.channelId2OrderBizType(channelId);
			if(orderBizType == null){
				log.error("invoke getOrderInfo error, orderBizType is null");
				return new Result<>(new Failure(true, FailureCodeEnum.SYSTEM_ERROR));
			}
			request.setOrderBizType(orderBizType.getValue());
			request.setViewOrderId(channelOrderId);
			request.setTenantId(tenantId);
			request.setShopId(poiId);
			log.info("BizOrderThriftService.queryByViewOrderId begin, request={}", request);
			//先查从库
			BizOrderQueryResponse response = bizOrderThriftServiceClient.queryByViewOrderId(request);

			return getOrderInfoResult(needWeightInfo, response);

		} catch (Exception e) {
			log.error("Call BizOrderThriftService.queryByOrderId failed", e);
			return new Result<>(new Failure(true, FailureCodeEnum.SYSTEM_ERROR));
		}
	}

	@Override
	@CatTransaction
	public Result<OrderInfo> getOrderInfo(Long tenantId, Long orderId, boolean needWeightInfo) {
		try {
			BizOrderQueryByOrderIdRequest request = new BizOrderQueryByOrderIdRequest(tenantId, orderId);
			log.info("BizOrderThriftService.queryByOrderId begin, request={}", request);
			//先查从库
			BizOrderQueryResponse response = bizOrderThriftServiceClient.queryByOrderId(request);
			//从库查不到再查主库
            if (needQueryOrderFromMasterDB(response)) {
				request.setFromMaster(true);
				response = bizOrderThriftServiceClient.queryByOrderId(request);
	            log.info("从主库查询订单数据，request={}", request);
				Cat.logEvent("ORDER_SYSTEM_CLIENT","QUERY_ORDER_INFO_FORM_MASTER");
			}
			return getOrderInfoResult(needWeightInfo, response);

		} catch (Exception e) {
			log.error("Call BizOrderThriftService.queryByOrderId failed", e);
			return new Result<>(new Failure(true, FailureCodeEnum.SYSTEM_ERROR));
		}
	}

    private static boolean needQueryOrderFromMasterDB(BizOrderQueryResponse response) {
	    if (!MccConfigUtils.queryOrderInfoFromMasterSwitch()) {
		    return false;
	    }
	    if (response == null || response.getStatus() == null) {
		    return true;
	    }
        BizOrderModel bizOrderModel = response.getBizOrderModel();
        List<BizOrderItemModel> bizOrderItemModelList = Objects.nonNull(bizOrderModel) ? bizOrderModel.getBizOrderItemModelList() : null;
        return response.getStatus().getCode() == StatusCodeEnum.ORDER_NOT_FOUNT.getCode()
                        || Objects.isNull(bizOrderModel)
                        || CollectionUtils.isEmpty(bizOrderItemModelList);
    }


	@Override
	public Result<OrderInfo> getOrderInfo(Long orderId, boolean needWeightInfo) {
		try {
			log.info("BizOrderThriftService.queryByOrderId begin, request={}", orderId);
			BizOrderQueryResponse response = bizOrderThriftServiceClient.query4OrderId(orderId);
			//从库查不到再查主库
			if (needQueryOrderFromMasterDB(response)) {
				log.info("从主库查询订单数据，orderId={}", orderId);
				response = bizOrderThriftServiceClient.query4OrderIdV2(BizOrderQuery4Request.builder().orderId(orderId).fromMaster(true).build());
				Cat.logEvent("ORDER_SYSTEM_CLIENT", "QUERY_ORDER_INFO_FORM_MASTER");
			}
			return getOrderInfoResult(needWeightInfo, response);
		} catch (Exception e) {
			log.error("Call BizOrderThriftService.queryByOrderId failed", e);
			return new Result<>(new Failure(true, FailureCodeEnum.SYSTEM_ERROR));
		}
	}

	private Result<OrderInfo> getOrderInfoResult(boolean needWeightInfo, BizOrderQueryResponse response) {
		log.info("BizOrderThriftService.queryByOrderId finish, response={}", response);
		if (response.getStatus().getCode() == SUCCESS) {
			return new Result<>(translate(response.getBizOrderModel(), needWeightInfo));
		} else {
			log.error("Call BizOrderThriftService.queryByOrderId failed will status[{}]", response.getStatus());
			return new Result<>(new Failure(false, FailureCodeEnum.OTHER_SYSTEM_CALL_FAILED, response.getStatus().getMessage()));
		}
	}

	@Override
	@CatTransaction
	public Optional<Failure> turnToSelfDelivery(Long tenantId, Long orderId, Integer orderBizType, String channelOrderId) {
		DynamicChannelType channelType = DynamicOrderBizType.orderBizTypeValue2ChannelType(orderBizType);
		if (channelType == null) {
			return Optional.of(new Failure(false, FailureCodeEnum.ORDER_NOT_SUPPORT_ERROR));
		}

		try {
			//调用线上订单平台接口转自配送
			SelfDeliveryRequest request = new SelfDeliveryRequest(channelType.getChannelId(), channelOrderId, tenantId);
			log.info("BizOrderTenantThriftService.selfDelivery begin, request={}", request);
			ResponseStatus response = bizOrderTenantThriftServiceClient.selfDelivery (request);
			log.info("BizOrderTenantThriftService.selfDelivery finish, response={}", response);
			if (response.getCode() != SUCCESS) {
				return Optional.of(new Failure(false, FailureCodeEnum.OTHER_SYSTEM_CALL_FAILED, response.getMsg()));
			}

			return Optional.empty();

		} catch (Exception e) {
			log.error("Call BizOrderTenantThriftService.selfDelivery failed", e);
			return Optional.of(new Failure(true, FailureCodeEnum.SYSTEM_ERROR));
		}
	}

	@Override
	@CatTransaction
	public void turnOrderDeliveryStatusToSelfDelivery(Long tenantId, Long orderId, Integer orderBizType,
													  Pair<DeliveryChannel, String> deliveryChannelWithId) {
		try {
			//查询订单当前配送状态
			BizOrderQueryByOrderIdRequest orderQueryRequest = new BizOrderQueryByOrderIdRequest(tenantId, orderId);
			log.info("BizOrderThriftService.queryByOrderId begin, request={}", orderQueryRequest);
			BizOrderQueryResponse orderQueryResponse = bizOrderThriftServiceClient.queryByOrderId(orderQueryRequest);
			log.info("BizOrderThriftService.queryByOrderId finish, response={}", orderQueryResponse);
			if (orderQueryResponse.getStatus().getCode() != SUCCESS) {
				log.error("尝试更新订单配送状态失败，原因：查询订单当前配送状态失败");
				return;
			}

			DeliveryInfoExtModel extModel = Optional.ofNullable(deliveryChannelWithId).map(
					pair -> DeliveryInfoExtModel.builder()
							.deliveryChannelId(pair.getLeft().getCarrierCode())
							.channelDeliveryId(pair.getRight())
							.build())
					.orElse(null);


			//修改订单配送状态到自配送
			DeliveryInfoUpdateRequest request = DeliveryInfoUpdateRequest
					.builder()
					.orderBizType(orderBizType)
					.orderId(orderId)
					.previousDistributeStatus(orderQueryResponse.getBizOrderModel().getDeliveryModel().getDistributeStatus())
					.distributeStatus(SELF_DISTRIBUTE.getValue())
					.extModel(extModel)
					.build();
			// 针对麦芽田转为商家自己配送的场景，对骑手信息进行清除
			if (deliveryChannelWithId != null && Objects.nonNull(deliveryChannelWithId.getLeft()) && deliveryChannelWithId.getLeft().getCarrierCode() == DeliveryChannelEnum.FARM_DELIVERY_MERCHANT.getCode()) {
				request.setRiderName(StringUtils.EMPTY);
				request.setRiderPhone(StringUtils.EMPTY);
			}
			log.info("DeliveryInfoThriftService.updateDeliveryInfo begin, request={}", request);
			DeliveryInfoUpdateResponse updateResponse = deliveryInfoThriftServiceClient.updateDeliveryInfo(request);
			log.info("DeliveryInfoThriftService.updateDeliveryInfo finish, response={}", updateResponse);
			if (updateResponse.getStatus().getCode() != SUCCESS) {
				log.error("尝试更新订单配送状态失败，原因：修改订单配送状态失败");
			}

		} catch (Exception e) {
			log.error("尝试更新订单配送状态失败", e);
		}
	}

	@Override
	@CatTransaction
	public void syncDeliveryChangeToOrderSystem(DeliveryOrder deliveryOrder) {
		syncDeliveryChangeToOrderSystem(deliveryOrder, LocalDateTime.now());
	}

	@Override
	@CatTransaction
	public void syncDeliveryExceptionToOrderSystem(DeliveryOrder deliveryOrder) {
		if(deliveryOrder == null){
			return;
		}
		try {
			DeliveryExceptionNotifyMessage deliveryExceptionNotifyMessage = new DeliveryExceptionNotifyMessage();
			deliveryExceptionNotifyMessage.setTenantId(deliveryOrder.getTenantId());
			deliveryExceptionNotifyMessage.setWarehouseId(deliveryOrder.getStoreId());
			deliveryExceptionNotifyMessage.setOrderId(deliveryOrder.getOrderId());
			deliveryExceptionNotifyMessage.setExceptionType(deliveryOrder.getExceptionType().getCode());
			deliveryExceptionNotifyMessage.setExceptionCode(deliveryOrder.getDeliveryExceptionCode());
			deliveryExceptionNotifyMessage.setExceptionDesc(deliveryOrder.getExceptionDescription());
			deliveryExceptionNotifyMessage.setStatus(deliveryOrder.getStatus().getCode());
			deliveryExceptionNotifyMessageProducer.sendMessage(deliveryExceptionNotifyMessage,deliveryOrder.getOrderId());
		}catch (Exception e){
			log.error("syncDeliveryExceptionToOrderSystem error,orderId:{}",deliveryOrder.getOrderId(),e);
		}

	}

	@Override
	@CatTransaction
	public void syncDeliveryChangeToOrderSystem(DeliveryOrder deliveryOrder, LocalDateTime updateTime) {
		DeliveryChannel deliveryChannelDto = deliveryChannelApplicationService.queryDeliveryChannelByCarrierCode(deliveryOrder.getDeliveryChannel());
		//只同步三方配送
		if (needSyncDeliveryChangeToOrderSystem(deliveryChannelDto)) {
			// 针对麦芽田自己送，需将"配送状态同步订单"做特殊处理
			if (deliveryOrder.getDeliveryChannel() == DeliveryChannelEnum.FARM_DELIVERY_MERCHANT.getCode()) {
				if (deliveryOrder.getStatus() == DeliveryStatusEnum.MERCHANT_DELIVERING) {
					// 转自己送的状态变更，需通过接口变更。
					turnOrderDeliveryStatusToSelfDelivery(deliveryOrder.getTenantId(), deliveryOrder.getOrderId(),
							deliveryOrder.getOrderBizType(),
							Pair.of(deliveryChannelDto, deliveryOrder.getChannelDeliveryId()));
				}
				return;
			}
			//对于配送发起状态，因为订单侧不消费这个状态变更，为了尽早将配送渠道信息同步，需要手动同步一次，后续迁移查询逻辑后可干掉
			if (deliveryOrder.getStatus() == DeliveryStatusEnum.DELIVERY_LAUNCHED) {
				syncChannelInfo(deliveryOrder);
			}
			if(Objects.equals(deliveryOrder.getDeliveryExceptionCode(), DeliveryExceptionCodeEnum.DELIVERY_STATUS_ROLLBACK.getCode())){
				// 状态回退(转单)同步渠道会失败、是因为渠道不支持配送状态回退
				// 直接通过接口调用同步最新配送信息到订单系统
				syncDeliveryRollbackStatus2OrderSystem(deliveryOrder);
				return;
			}

			CoordinatePoint riderLocation = null;
			// 平台配送暂不向订单侧同步骑手坐标信息、三方自配送暂没有骑手坐标
			if (deliveryChannelDto.getDeliveryPlatFormCode() != DeliveryPlatformEnum.ORDER_CHANNEL_DELIVERY_PLATFORM.getCode()
					&& deliveryChannelDto.getDeliveryPlatFormCode() != DeliveryPlatformEnum.OTHER_SELF_DELIVERY_PLATFORM.getCode()) {
				riderLocation = queryRiderLocation(deliveryOrder, deliveryChannelDto);
				if (riderLocation == null) {
					riderLocation = buildDefaultRiderLocation(deliveryOrder, deliveryChannelDto);
				}
			}
			deliveryChangeNotifyMessageProducer.sendMessage(new DeliveryChangeNotifyMessage(deliveryOrder, riderLocation, updateTime, deliveryChannelDto.getDeliveryPlatFormCode()));
		}
	}

	private boolean needSyncDeliveryChangeToOrderSystem(DeliveryChannel deliveryChannel) {
		if (Objects.isNull(deliveryChannel) || Objects.isNull(deliveryChannel.getDeliveryPlatFormCode()) || Objects.isNull(deliveryChannel.getCarrierCode())) {
			return false;
		}

		//配送平台需同步
		DeliveryPlatformEnum deliveryPlatformEnum = DeliveryPlatformEnum.enumOf(deliveryChannel.getDeliveryPlatFormCode());
		if (needSyncOut(deliveryPlatformEnum)) {
			return true;
		}

		//商家自配送门店的自配送运单需同步，平台配送转自送的单也需要同步
		if (DeliveryChannelEnum.MERCHANT_DELIVERY.getCode() == deliveryChannel.getCarrierCode()) {
			return true;
		}
		return false;
	}

	private Optional<DeliveryPoi> getDeliveryPoi(Long tenantId,Long storeId,Integer orderBizType){
		Optional<DeliveryPoi> opDeliveryPoi ;
		DynamicOrderBizType dynamicOrderBizType = ObjectUtils.defaultIfNull(
				DynamicOrderBizType.findOf(orderBizType), DynamicOrderBizType.MEITUAN_WAIMAI);
		opDeliveryPoi = deliveryPoiRepository.queryDeliveryPoiWithDefault(tenantId, storeId,
				dynamicOrderBizType.getChannelId());
		return opDeliveryPoi;
	}

	private boolean needSyncOut(DeliveryPlatformEnum deliveryPlatform) {
		if (deliveryPlatform == null) {
			return false;
		}

		return deliveryPlatform == DeliveryPlatformEnum.SELF_BUILT_DELIVERY_PLATFORM
				|| deliveryPlatform == DeliveryPlatformEnum.AGGREGATION_DELIVERY_PLATFORM
				|| deliveryPlatform == DeliveryPlatformEnum.MALT_FARM_DELIVERY_PLATFORM
				|| deliveryPlatform == DeliveryPlatformEnum.DAP_DELIVERY_PLATFORM
				|| deliveryPlatform == DeliveryPlatformEnum.ORDER_CHANNEL_DELIVERY_PLATFORM
				|| deliveryPlatform == DeliveryPlatformEnum.OTHER_SELF_DELIVERY_PLATFORM;
	}

	private void syncChannelInfo(DeliveryOrder deliveryOrder) {
		try {
			BizOrderThirdDeliveryInfoUpdateRequest request = new BizOrderThirdDeliveryInfoUpdateRequest();
			request.setOrderBizType(deliveryOrder.getOrderBizType());
			request.setViewOrderId(deliveryOrder.getChannelOrderId());
			request.setTenantId(deliveryOrder.getTenantId());
			request.setShopId(deliveryOrder.getStoreId());
			request.setOrderSource(deliveryOrder.getOrderSource());
			request.setChannelDeliveryId(deliveryOrder.getChannelDeliveryId());
			deliveryChannelHandle(deliveryOrder.getDeliveryChannel(), request);
			log.info("BizOrderThriftService.thirdDeliveryInfoUpdate begin, request={}", request);
			BizOrderThirdDeliveryInfoUpdateResponse response = bizOrderThriftServiceClient.thirdDeliveryInfoUpdate(request);
			log.info("BizOrderThriftService.thirdDeliveryInfoUpdate finish, response={}", response);
			if (response.getStatus().getCode() != SUCCESS) {
				log.error("同步配送渠道到订单失败");
			}

		} catch (Exception e) {
			log.error("同步配送渠道到订单失败");
		}
	}

	private void deliveryChannelHandle(Integer deliveryChannel, BizOrderThirdDeliveryInfoUpdateRequest request) {
		if (deliveryChannel == null) {
			return;
		}
		DeliveryChannel deliveryChannelDto = deliveryChannelApplicationService.queryDeliveryChannelByCarrierCode(deliveryChannel);
		if (Objects.equals(DeliveryPlatformEnum.ORDER_CHANNEL_DELIVERY_PLATFORM.getCode(), deliveryChannelDto.getDeliveryPlatFormCode())) {
			Map<String, Integer> typeMappingMap = MccConfigUtils.orderChannelDistributeTypeMappingMap();
			String orderChannelCode = String.valueOf(deliveryChannelDto.getOrderChannelCode());
			if (typeMappingMap.containsKey(orderChannelCode)) {
				request.setPlatformDistributeTypeId(typeMappingMap.get(orderChannelCode));
				return;
			}
		}
		request.setDeliveryChannelId(deliveryChannel);
	}

	private CoordinatePoint queryRiderLocation(DeliveryOrder deliveryOrder, DeliveryChannel deliveryChannelDto) {
		if (!deliveryOrder.getStatus().needQueryRiderLocation()) {
			return null;
		}
		if(MccConfigUtils.getPaoTuiCode().contains(deliveryChannelDto.getCarrierCode()+"")){
			if (MccConfigUtils.paoTuiUploadFilterChannel(deliveryOrder.getOrderBizType())) {
				return null;
			}
			if (!MccConfigUtils.paoTuiUploadTenantIds(deliveryOrder.getTenantId())) {
				return null;
			}
		}
		Optional<DeliveryPoi> deliveryPoiOptional = getDeliveryPoi(deliveryOrder.getTenantId(), deliveryOrder.getStoreId(),deliveryOrder.getOrderBizType());
		return deliveryPoiOptional
				.flatMap(deliveryPoi -> deliveryPlatformClient.queryRiderLocation(deliveryOrder, deliveryChannelDto, deliveryPoi))
				.orElse(null);
	}

	/**
	 * 回传配送状态骑手位置兜底
	 */
	private CoordinatePoint buildDefaultRiderLocation(DeliveryOrder deliveryOrder, DeliveryChannel deliveryChannelDto) {
		try {
			// 聚合平台
			if (!Objects.equals(DeliveryPlatformEnum.MALT_FARM_DELIVERY_PLATFORM.getCode(), deliveryChannelDto.getDeliveryPlatFormCode())
					&& !Objects.equals(DeliveryPlatformEnum.DAP_DELIVERY_PLATFORM.getCode(), deliveryChannelDto.getDeliveryPlatFormCode())) {
				return null;
			}
			DeliveryStatusEnum status = deliveryOrder.getStatus();
			if (!status.needQueryRiderLocation()) {
				return null;
			}
			if (!MccConfigUtils.deliveryStatusDefaultRiderLocationSwitch()) {
				return null;
			}
			if (MccConfigUtils.getPaoTuiCode().contains(deliveryChannelDto.getCarrierCode() + "")) {
				if (MccConfigUtils.paoTuiUploadFilterChannel(deliveryOrder.getOrderBizType())) {
					return null;
				}
				if (!MccConfigUtils.paoTuiUploadTenantIds(deliveryOrder.getTenantId())) {
					return null;
				}
			}
			Double longitude = null;
			Double latitude = null;
			if (Objects.equals(DeliveryStatusEnum.DELIVERY_DONE, status)) {
				// 取用户地址位置
				CoordinatePoint receiverCoordinate = deliveryOrder.getReceiver().getReceiverAddress().getCoordinatePoint();
				if (receiverCoordinate != null && StringUtils.isNoneBlank(receiverCoordinate.getLongitude(), receiverCoordinate.getLatitude())) {
					longitude = Double.parseDouble(receiverCoordinate.getLongitude());
					latitude = Double.parseDouble(receiverCoordinate.getLatitude());
				} else {
					log.info("receiver coordinate point is null.  channelOrderId: {}", deliveryOrder.getChannelOrderId());
				}
			} else {
				// 取中台门店位置
				Optional<WarehouseDTO> warehouseDTOOpt = oswClient.queryWarehouseById(deliveryOrder.getTenantId(), deliveryOrder.getStoreId());
				if (warehouseDTOOpt.isPresent() && Objects.nonNull(warehouseDTOOpt.get().getRegion())) {
					RegionDTO region = warehouseDTOOpt.get().getRegion();
					if (Objects.nonNull(region.getLongitude()) && Objects.nonNull(region.getLatitude())) {
						longitude = region.getLongitude();
						latitude = region.getLatitude();
					}
				}
			}
			if (latitude == null || longitude == null) {
				return null;
			}
			Map<String, Double[]> stringMap = MccConfigUtils.deliveryStatusDefaultRiderLocationConfig();
			Double[] locationConfig = stringMap.get(String.valueOf(status.getCode()));
			if (locationConfig == null) {
				return new CoordinatePoint(String.valueOf(longitude), String.valueOf(latitude));
			}
			Cat.logEvent("AGG_DEFAULT_RIDER_LOCATION", "BUILD_RIDER_LOCATION");
			double[] newCoordinates = CoordinateUtils.calculateNewCoordinates(longitude, latitude, locationConfig[0], locationConfig[1]);
			return new CoordinatePoint(String.valueOf(newCoordinates[0]), String.valueOf(newCoordinates[1]));
		} catch (NumberFormatException e) {
			log.error("buildDefaultRiderLocation is error:  ", e);
			return null;
		}
	}

	@Override
	@CatTransaction
	public Optional<Failure> setOrderDeliveryTimeout(DeliveryOrder deliveryOrder) {
		try {
			DeliveryInfoUpdateRequest request = DeliveryInfoUpdateRequest
					.builder()
					.orderBizType(deliveryOrder.getOrderBizType())
					.orderId(deliveryOrder.getOrderId())
					.extModel(new DeliveryInfoExtModel())
					.isDeliveryOvertime(1)
					.build();
			log.info("DeliveryInfoThriftService.updateDeliveryInfo begin, request={}", request);
			DeliveryInfoUpdateResponse response = deliveryInfoThriftServiceClient.updateDeliveryInfo(request);
			log.info("DeliveryInfoThriftService.updateDeliveryInfo finish, response={}", response);

			if (response.getStatus().getCode() != SUCCESS) {
				return Optional.of(new Failure(false, FailureCodeEnum.OTHER_SYSTEM_CALL_FAILED, response.getStatus().getMessage()));
			}
			return Optional.empty();

		} catch (Exception e) {
			log.error("设置订单配送超时标记失败", e);
			return Optional.of(new Failure(true, FailureCodeEnum.SYSTEM_ERROR));
		}
	}

	@Override
	public void syncDeliveryTransToOrderSystem(DeliveryOrder deliveryOrder, Integer lastDeliveryPlatform) {
		try{
			DeliveryTransNotifyMessage message = new DeliveryTransNotifyMessage(
					deliveryOrder.getOrderBizType(),deliveryOrder.getStoreId(),
					deliveryOrder.getTenantId(),deliveryOrder.getChannelOrderId(),
					deliveryOrder.getOrderId(),lastDeliveryPlatform,1,deliveryOrder.getDeliveryChannel(), System.currentTimeMillis());

			deliveryTransNotifyMessageMafkaMessageProducer.sendMessage(message);

		} catch (Exception e) {
			log.error("同步配送转单到订单失败, e:{}", e);
		}
	}

	@Override
	public Result<OrderInfo> queryByViewOrderId(int orderBizType,String viewOrderId, boolean needWeightInfo) {
		BizOrderQueryByViewOrderIdRequest request=new BizOrderQueryByViewOrderIdRequest();
		request.setOrderBizType(orderBizType);
		request.setViewOrderId(viewOrderId);
		RetryTemplate retryTemplate= RetryTemplateUtil.simpleWithFixedRetry(2,100);
		try {
			BizOrderQueryResponse response=retryTemplate.execute(new RetryCallback<BizOrderQueryResponse, Exception>() {
				@Override
				public BizOrderQueryResponse doWithRetry(RetryContext retryContext) throws Exception {
					return bizOrderThriftServiceClient.queryByViewOrderId(request);
				}
			});
			if(response==null || response.getBizOrderModel()==null){
				return new Result<>(new Failure(false, FailureCodeEnum.OTHER_SYSTEM_CALL_FAILED, "订单返回为空"));
			}
			return getOrderInfoResult(needWeightInfo, response);
		}catch (Exception e){
			log.error("queryByViewOrderId error orderBizTypeEnum:{},viewOrderId:{},needWeightInfo:{}", orderBizType,viewOrderId,needWeightInfo,e);
			return new Result<>(new Failure(false, FailureCodeEnum.OTHER_SYSTEM_CALL_FAILED, "请求异常"));
		}

	}

	private OrderInfo translate(BizOrderModel order, boolean needWeightInfo) {
		List<GoodsInfo> goodsList = new ArrayList<>();
		List<OrderInfo.GiftGoodsInfo> giftGoodsList = new ArrayList<>();
		Map<String, List<GoodsInfo>> noWeightGoodsMap = new HashMap<>();
		Optional.ofNullable(order.getBizOrderItemModelList())
				.orElse(new ArrayList<>())
				.forEach(it -> {

					//默认商品名称, 存在货号且解析extData成功替换为货号, 如果json字符串格式错误不可阻断。
					OrderGoodsInfoExtInfo orderGoodsInfoExtInfo=new OrderGoodsInfoExtInfo(it.getSkuName());
					try {
						Optional<OrderGoodsInfoExtInfo> extDataOptional = Optional.ofNullable(it.getExtData()).filter(k->StringUtils.isNotBlank(k)).map(k -> JsonUtil.fromJson(k, OrderGoodsInfoExtInfo.class));
						if(extDataOptional.isPresent()){
							orderGoodsInfoExtInfo=extDataOptional.get();
						}
					}catch (Exception e){
						log.error("translate extData error, orderId:{}, instoreSkuId2:{},skuName:{},extData:{}", order.getOrderId(),it.getInstoreSkuId2(),it.getSkuName(),it.getExtData(),e);
					}

					GoodsInfo goodsInfo = new GoodsInfo(
							it.getInstoreSkuId2(),
							it.getSkuName(),
							it.getQuantity(),
							it.getCurrentPrice(),
							it.getSellUnit(),
							Optional.ofNullable(it.getChannelWeight()).orElse(0),
							orderGoodsInfoExtInfo,
							false
							);

					if (it.getChannelWeight() == null) {
						List<GoodsInfo> noWeightGoodsList = noWeightGoodsMap.getOrDefault(goodsInfo.getSkuId(), new ArrayList<>());
						noWeightGoodsList.add(goodsInfo);
						noWeightGoodsMap.put(goodsInfo.getSkuId(), noWeightGoodsList);
					}
					goodsList.add(goodsInfo);
				});

		//中台订单可能会配置赠品
		Optional.ofNullable(order.getBizOrderGiftModelList())
				.orElse(new ArrayList<>())
				.forEach(it -> {
					//默认商品名称, 存在货号且解析extData成功替换为货号, 如果json字符串格式错误不可阻断。
					OrderInfo.GiftGoodsInfo goodsInfo = new OrderInfo.GiftGoodsInfo(
							it.getSkuId(),
							it.getName(),
							it.getQuantity(),
							StringUtils.isBlank(it.getGoodsCode())?it.getName():it.getGoodsCode()
					);
					giftGoodsList.add(goodsInfo);
				});


		if (needWeightInfo) {
			makeUpMissingWeightInfo(order.getTenantId(), order.getShopId(), noWeightGoodsMap);
		}
		Long shopId=order.getShopId();
		Long warehousePoiId=null;
		if(order.getWarehouseId()!=null){
			shopId=order.getWarehouseId();
			warehousePoiId=order.getShopId();
		}

		OrderExtInfo extInfo = getOrderExtInfo(order);

		OrderTransInfo orderTransInfo = null;
		if(order.getDispatchOrderExtDataModel()!=null){
			orderTransInfo = new OrderTransInfo(order.getDispatchOrderExtDataModel().getDispatchShopId(),order.getDispatchOrderExtDataModel().getDispatchTenantId(),order.getDispatchOrderExtDataModel().getDispatchTime());
		}

		Boolean isOneYuanOrder = parseOneYuanOrderTag(order);

		return new OrderInfo(
				new OrderKey(order.getTenantId(), shopId, order.getOrderId()),
				order.getViewOrderId(),
				order.getOrderSerialNumber(),
				order.getOrderSerialNumberStr(),
				order.getOrderBizType(),
				order.getOrderSource(),
				order.getOrderStatus(),
				order.getDeliveryModel().getIsSelfDelivery() != null && order.getDeliveryModel().getIsSelfDelivery() == 1,
				order.getDeliveryModel().getDistributeMethod(),
				new Receiver(
						order.getDeliveryModel().getUserName(),
						order.getDeliveryModel().getUserPhone(),
						order.getDeliveryModel().getUserPrivacyPhone(),
						new Address(
								order.getDeliveryModel().getUserAddress(),
								Optional.ofNullable(order.getDeliveryModel().getCoordinateType())
										.map(CoordinateTypeEnum::valueOf)
										.orElse(CoordinateTypeEnum.UNKNOWN),
								getReceiverAddressPoint(order.getShopId(), order.getDeliveryModel())
						)
				),
				TimeUtil.fromMilliSeconds(order.getDeliveryModel().getArrivalTime()),
				TimeUtil.fromMilliSeconds(order.getDeliveryModel().getArrivalEndTime()),
				order.getIsBooking() != null && order.getIsBooking() == 1,
				order.getDeliveryModel().getOriginalDistributeType(),
				order.getOriginalAmt(),
				goodsList,
				order.getComments(),
				order.getInvoiceTitle(),
				order.getDeliveryModel().getDeliveryChannelId(),
				order.getDeliveryModel().getChannelDeliveryId(),
				IN_DELIVERY_STATUS_SET.contains(order.getDeliveryModel().getDistributeStatus()),
				order.getDeliveryModel().getRiderName(),
				order.getDeliveryModel().getRiderPhone(),
				order.getActualPayAmt(),
				TimeUtil.fromMilliSeconds(order.getCreateTime()),
				TimeUtil.fromMilliSeconds(order.getPayTime()),
				TimeUtil.fromMilliSeconds(order.getMerchantTakeOrderTime()),
				warehousePoiId,
				order.getDiscountAmt(),
				null,
				order.getDeliveryModel().getDistributeStatus(),
				TimeUtil.fromMilliSeconds(order.getDeliveryModel().getCompleteTime()),
				order.getUserId(),
				extInfo,
				giftGoodsList,
				orderTransInfo,
				order.getDeliveryPosition(),
				isOneYuanOrder,
				getOrderCategory(order),
				order.getDeliveryModel().getDeliveryStatus(),
				order.getDeliveryModel().getLockOrderPackageId(),
				order.getDeliveryModel().getLockOrderLabel(),
				order.getDeliveryModel().getLockOrderState()!= null && order.getDeliveryModel().getLockOrderState() == 1,
				Objects.equals(order.getIsFastOrder(), 1),
				order.getIsFastToSelfDelivery(),
				order.getAddressChangeFee(),
				order.getRefundAddressChangeFee(),
                order.getDownFlag(),
                order.getDegradeModules(),
				getOrderExt(order),
				order.getIsMtFamousTavern(),
				order.getPhoneNoPrint(),
				order.getPrivacyGoods(),
				order.getGroupId(),null
		);
	}

	private OrderInfo.OrderExt getOrderExt (BizOrderModel orderModel) {
		if (StringUtils.isEmpty(orderModel.getNewExtData())) {
			return null;
		}
		try {
			OrderInfo.OrderExt orderExt = JsonUtil.fromJson(orderModel.getNewExtData(), OrderInfo.OrderExt.class);
			return orderExt;
		} catch (Exception e) {
			log.error("getBrandId error", e);
		}
		return null;
	}

	private static String getOrderCategory(BizOrderModel order) {
		try{
			String category = null;
			if (Objects.nonNull(order.getDeliveryModel()) && StringUtils.isNotBlank(order.getDeliveryModel().getCategory())) {
				category = order.getDeliveryModel().getCategory();
			}
			return category;
		} catch (Exception e) {
			log.error("getOrderCategory error", e);
			return null;
		}
	}


	private Boolean parseOneYuanOrderTag(BizOrderModel order) {
		try {
			if (order == null) {
				return null;
			}

			JSONObject jsonObject = JSON.parseObject(order.getNewExtData(), JSONObject.class);
			if (Objects.nonNull(jsonObject) && Objects.nonNull(jsonObject.getJSONArray("tagModels"))) {
				for (Object tagModel : jsonObject.getJSONArray("tagModels")) {
					Map<String, Object> tagModelMap = (Map<String, Object>) tagModel;
					if (Objects.equals(tagModelMap.get("type"), 102)) {
						return true;
					}
				}
			}

			return false;

		} catch (Exception e) {
			Cat.logEvent("PARSE_ONE_YUAN_ORDER", "ERROR");
			log.error("解析一元单错误", e);
			return null;
		}
	}
	private OrderExtInfo getOrderExtInfo(BizOrderModel order) {
		try {
			String commonInfo = order.getCommonInfo();
			if (StringUtils.isBlank(commonInfo)) {
				return null;
			}

			return JSON.parseObject(order.getCommonInfo(), OrderExtInfo.class);
		} catch (Exception e) {
			log.error("getOrderExtInfo throws exception",e);
			return null;
		}
	}

	private void makeUpMissingWeightInfo(Long tenantId, Long storeId, Map<String, List<GoodsInfo>> noWeightGoodsMap) {
		try {
			Set<String> toQuerySkuIds = noWeightGoodsMap.keySet().stream().filter(StringUtils::isNotBlank).collect(Collectors.toSet());
			List<SkuInfoWithGroup> skuInfoWithGroups = skuRemoteService.findStoreSkuInfoBySkuIds(tenantId, storeId, toQuerySkuIds, true);
			for (SkuInfoWithGroup skuInfoWithGroup : skuInfoWithGroups) {
				List<GoodsInfo> goodsList = noWeightGoodsMap.get(skuInfoWithGroup.getSkuInfo().getSkuId());
				SkuSizeInfo skuSizeInfo = skuInfoWithGroup.getSkuInfo().getSkuSizeInfo();
				int weight = skuSizeInfo.getOnlineWeight() > 0 ? skuSizeInfo.getOnlineWeight() : (skuSizeInfo.getTare() > 0 ? skuSizeInfo.getTare() : skuSizeInfo.getNetWeight());
				goodsList.forEach(it -> it.setSingleGoodsWeight(weight));
			}
		} catch (Exception e) {
			log.warn("查询商品服务补偿商品重量失败, noWeightGoodsMap={}", noWeightGoodsMap, e);
		}
	}

	private CoordinatePoint getReceiverAddressPoint(Long storeId, BizOrderDeliveryModel deliveryModel) {
		CoordinatePoint realReceiverAddressPoint =
				deliveryModel.getReceiverLongitude() != null && deliveryModel.getReceiverLatitude() != null ?
						translateToCoordinatePoint(deliveryModel.getReceiverLongitude(), deliveryModel.getReceiverLatitude()) : null;

		String configStr = ConfigUtilAdapter.getString("delivery_mock_receiver_address_white_list");
		if (StringUtils.isNotBlank(configStr)) {
			try {
				Map<Long, CoordinatePoint> mockAddressMap = JsonUtil.fromJson(configStr, new TypeReference<Map<Long, CoordinatePoint>>() {
				});
				if (MapUtils.isNotEmpty(mockAddressMap) && mockAddressMap.containsKey(storeId)) {
					CoordinatePoint mockAddressPoint = mockAddressMap.get(storeId);
					log.info("门店[{}]命中mock的收货地址列表，将使用mock的收货地址:{}", storeId, mockAddressPoint);
					return mockAddressPoint;
				}
			} catch (Exception e) {
				log.error("解析mock的收货地址失败，将使用真实地址", e);
			}
		}

		return realReceiverAddressPoint;
	}

	@MethodLog(logRequest = false, logResponse = true)
	@Override
	public Map<String,Integer> queryOrderStatusMap(Map<String,Integer> orderWitBizMap){
		if(MapUtils.isEmpty(orderWitBizMap)){
			return Collections.emptyMap();
		}
		try {
			OCMSListViewIdConditionRequest request = new OCMSListViewIdConditionRequest();
			request.setViewIdConditionList(orderWitBizMap.entrySet().stream().filter(Objects::nonNull)
					.map(to -> new ViewIdCondition(to.getValue(), to.getKey()))
					.collect(Collectors.toList()));
			OCMSListViewIdConditionResponse response = ocmsQueryThriftService.queryOrderByViewIdCondition(request);
			if(response==null || CollectionUtils.isEmpty(response.getOcmsOrderList())){
				return Collections.emptyMap();
			}
			Map<String,Integer> map=response.getOcmsOrderList().stream().collect(Collectors.toMap(OCMSOrderVO::getViewOrderId,OCMSOrderVO::getOrderStatus));
			if(MapUtils.isEmpty(map)){
				return Collections.emptyMap();
			}
			return map;
		}catch (Exception e){
			log.error("queryOrderInfoList error,orderWitBizMap:{}",orderWitBizMap,e);
		}
		return Collections.emptyMap();
	}


	/**
	 * 注意：此接口返回数据不全，使用需要check一下数据源
	 * @param tenantId
	 * @param orderIdList
	 * @return
	 */
	@Degrade(rhinoKey = "OrderSystemClientImpl.batchQueryOrderByEs", fallBackMethod = "batchQueryOrderByEsFallback", timeoutInMilliseconds = 1000)
	@Override
	public List<OrderInfo> batchQueryOrderByEs(Long tenantId, List<Long> orderIdList) {
		if(CollectionUtils.isEmpty(orderIdList)){
			return Collections.emptyList();
		}
		try {
			BatchQueryOrderRequest request = new BatchQueryOrderRequest();
			request.setTenantId(tenantId);
			request.setOrderIdList(orderIdList);
			request.setQueryFieldEnumList(Arrays.asList(OrderBatchQueryFieldModelEnum.DELIVERY_INFO.getFieldCode(),OrderBatchQueryFieldModelEnum.ORDER_AND_DISPATCH_INFO.getFieldCode()));
			request.setExtQueryFieldList(Arrays.asList("actualPayAmt"));
			OrderDetailBatchQueryResponse response = onlineOrderQueryThriftService.batchQueryOrderList(request);
			log.info("batchQueryOrderByEs orderIdList:{},response:{}",orderIdList,response);
			if(response==null || CollectionUtils.isEmpty(response.getOnlineOrderList())){
				return Collections.emptyList();
			}
			return response.getOnlineOrderList().stream().map(this::translate).filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());
		}catch (Exception e){
			log.error("batchQueryOrderByEs query error,tenantId:{},orderIdList:{}",tenantId,orderIdList,e);
		}

		return Collections.emptyList();
	}
	public List<OrderInfo> batchQueryOrderByEsFallback(Long tenantId, List<Long> orderIdList){
		log.info("batchQueryOrderByEsFallback orderIdList:{},tenantId:{}",orderIdList,tenantId);
		return Collections.emptyList();
	}

	@Override
	public void syncDeliveryRollbackStatus2OrderSystem(DeliveryOrder deliveryOrder) {
		try {
			if(deliveryOrder.getStatus() == null){
				return;
			}
			Integer distributeStatus = getDistributeStatus4DeliveryStatus(deliveryOrder.getStatus());
			if(distributeStatus == null || deliveryOrder.getDeliveryChannel() == null){
				return;
			}
			BizOrderQueryByOrderIdRequest orderQueryRequest = new BizOrderQueryByOrderIdRequest(deliveryOrder.getTenantId(), deliveryOrder.getOrderId());
			BizOrderQueryResponse orderQueryResponse = bizOrderThriftServiceClient.queryByOrderId(orderQueryRequest);
			if (orderQueryResponse.getStatus().getCode() != SUCCESS) {
				return;
			}
			DeliveryInfoExtModel extModel = DeliveryInfoExtModel
					.builder()
					.deliveryChannelId(deliveryOrder.getDeliveryChannel())
					.channelDeliveryId(deliveryOrder.getChannelDeliveryId())
					.build();
			// 更新订单配送信息
			DeliveryInfoUpdateRequest request = DeliveryInfoUpdateRequest
					.builder()
					.orderBizType(deliveryOrder.getOrderBizType())
					.orderId(deliveryOrder.getOrderId())
					.previousDistributeStatus(orderQueryResponse.getBizOrderModel().getDeliveryModel().getDistributeStatus())
					.distributeStatus(distributeStatus)
					.extModel(extModel)
					.build();
			// 骑手信息不存在、同步清除订单记录的骑手信息
			if(deliveryOrder.getRiderInfo() == null
					|| StringUtils.isEmpty(deliveryOrder.getRiderInfo().getRiderName())){
				request.setRiderName(StringUtils.EMPTY);
				request.setRiderPhone(StringUtils.EMPTY);
			}
			log.info("DeliveryInfoThriftService.updateDeliveryInfo begin, request={}", request);
			DeliveryInfoUpdateResponse updateResponse = deliveryInfoThriftServiceClient.updateDeliveryInfo(request);
			log.info("DeliveryInfoThriftService.updateDeliveryInfo finish, response={}", updateResponse);
			if (updateResponse.getStatus().getCode() != SUCCESS) {
				log.error("尝试更新订单配送状态失败，原因：修改订单配送状态失败");
			}
		} catch (Exception e) {
			log.error("尝试更新订单配送状态失败", e);
		}
	}

	/**
	 * 注意：此接口返回数据不全，使用需要check一下数据源
	 * @param order
	 * @return
	 */
	private Optional<OrderInfo> translate(OnlineOrderVO order) {

		Long shopId=order.getShopId();
		Long warehousePoiId = null;
		if(order.getWarehouseId()!=null){
			shopId = order.getWarehouseId();
			warehousePoiId = order.getShopId();
		}
		if (Objects.isNull(order.getDeliveryInfoModel())) {
			return Optional.empty();
		}
		OrderTransInfo orderTransInfo = null;
		if(order.getDispatchShopId()!=null){
			orderTransInfo = new OrderTransInfo(order.getDispatchShopId(),order.getDispatchTenantId(),order.getDispatchTime());
		}
		return Optional.of(new OrderInfo(
				new OrderKey(order.getTenantId(), shopId, order.getOrderId()),
				order.getViewOrderId(),
				order.getOrderSerialNumber(),
				order.getOrderSerialNumberStr(),
				order.getOrderBizType(),
				order.getOrderSource(),
				order.getOrderStatus(),
				order.getDeliveryInfoModel().getIsSelfDelivery() != null && order.getDeliveryInfoModel().getIsSelfDelivery() == 1,
				order.getDeliveryInfoModel().getDistributeMethod(),
				new Receiver(
						order.getDeliveryInfoModel().getUserName(),
						order.getDeliveryInfoModel().getUserPhone(),
						order.getDeliveryInfoModel().getUserPhone(),
						new Address(
								order.getDeliveryInfoModel().getUserAddress(),
								CoordinateTypeEnum.UNKNOWN,
								null
						)
				),
				TimeUtil.fromMilliSeconds(order.getDeliveryInfoModel().getArrivalTime()),
				TimeUtil.fromMilliSeconds(order.getDeliveryInfoModel().getArrivalEndTime()),
				order.getIsBooking() != null && order.getIsBooking() == 1,
				order.getDeliveryInfoModel().getOriginalDistributeType(),
				order.getOriginalAmt(),
				new ArrayList<>(),
				order.getComments(),
				null,
				order.getDeliveryInfoModel().getDeliveryChannelId(),
				order.getDeliveryInfoModel().getChannelDeliveryId(),
				IN_DELIVERY_STATUS_SET.contains(order.getDeliveryInfoModel().getDistributeStatus()),
				order.getDeliveryInfoModel().getRiderName(),
				order.getDeliveryInfoModel().getRiderPhone(),
				order.getActualPayAmt(),
				TimeUtil.fromMilliSeconds(order.getCreateTime()),
				TimeUtil.fromMilliSeconds(order.getPayTime()),
				TimeUtil.fromMilliSeconds(order.getMerchantTakeOrderTime()),
				warehousePoiId,
				null,
				null,
				order.getDeliveryInfoModel().getDistributeStatus(),
				TimeUtil.fromMilliSeconds(order.getDeliveryInfoModel().getCompleteTime()),
				0L,
				null, Lists.newArrayList(),orderTransInfo, null, null, null,
				order.getDeliveryInfoModel().getDeliveryStatus(),
				order.getDeliveryInfoModel().getLockOrderPackageId(),
				order.getDeliveryInfoModel().getLockOrderLabel(),
				order.getDeliveryInfoModel().getLockOrderState() != null && order.getDeliveryInfoModel().getLockOrderState() == 1,
				false,
				false,
				null,
				null,
				0,
                null,
                null,
				false,
				null,
				null,
				null,
				order.getLabelList()
		));
	}



	public void replaceGoodsCode2SkuName(OrderKey orderKey, OrderInfo orderInfo) {
		//赠品信息
		if (CollectionUtils.isNotEmpty(orderInfo.getGiftGoodsList())) {
			Optional.ofNullable(orderInfo.getGoodsList())
					.ifPresent(k -> orderInfo.getGoodsList()
							.addAll(
									orderInfo.getGiftGoodsList()
											.stream()
											.map(giftGoodsInfo ->
													new OrderInfo.GoodsInfo(giftGoodsInfo))
											.collect(Collectors.toList()))
					);
		}

		Optional.ofNullable(orderInfo.getGoodsList())
				.orElse(Lists.newArrayList())
				.stream()
				.forEach(
						goods -> {
							String skuNameBefore = goods.getName();
							if(Objects.nonNull(goods.getOrderGoodsInfoExtInfo())
									&&StringUtils.isNotBlank(goods.getOrderGoodsInfoExtInfo().getGoodsCode())){
								goods.setName(goods.getOrderGoodsInfoExtInfo().getGoodsCode());
								goods.setHasReplaceGoodsCode(true);
							}
							log.info("医药无人仓订单[{}]的商品[{}]替换为[{}] ", orderKey.getOrderId(),skuNameBefore,goods.getName());
						}
				);
	}

}
