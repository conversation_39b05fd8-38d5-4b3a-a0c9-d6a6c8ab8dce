package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.rider.repository;

import com.dianping.cat.Cat;
import com.dianping.zebra.group.router.ZebraForceMasterHelper;
import com.google.common.base.Preconditions;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Lists;
import com.meituan.linz.boot.util.Fun;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrderRouteRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryExceptionTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrder;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryChannelEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper.DeliveryOrderDOComplexMapper;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper.DeliveryOrderDOMapper;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.CountGroupExDO;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryOrderDO;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryOrderDOExample;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryOrderRoute;
import com.sankuai.meituan.shangou.empower.tms.delivery.enums.OrderReserveTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.leaf.DeliveryOrderIdLeafService;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.repository.JsonTranslator;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.repository.MySQLOperateFailedException;
import com.sankuai.meituan.shangou.dms.base.model.value.Receiver;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.utils.LionConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.RiderDeliveryExtInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.saas.common.cat.CatTransaction;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import com.sankuai.shangou.commons.utils.collection.IListUtils;
import javafx.util.Pair;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.sankuai.meituan.shangou.dms.base.model.value.CustomerOrderKey;
import com.sankuai.meituan.shangou.dms.base.model.value.DeliveryTimeline;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/7/9
 */
@Slf4j
@Repository
public class MySQLRiderDeliveryOrderRepository implements RiderDeliveryOrderRepository {

	private static final Integer DEFAULT_BATCH_SIZE = 200;

	private static final Integer UNLOCK_STATUS = 0;

	@Resource
	private DeliveryOrderDOMapper deliveryOrderDOMapper;

	@Resource
	private DeliveryOrderDOComplexMapper deliveryOrderDOComplexMapper;

	@Resource
	private DeliveryOrderIdLeafService deliveryOrderIdLeafService;

	@Resource
	private DeliveryOrderRouteRepository deliveryOrderRouteRepository;

	@Override
	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public void save(RiderDeliveryOrder deliveryOrder) {
		Preconditions.checkNotNull(deliveryOrder, "deliveryOrder is null");

		if (deliveryOrder.getId() == null) {
			deliveryOrder.setVersion(0);
			if(MccConfigUtils.getDeliveryLeafSwitch()){
				List<Long> deliveryIdList = deliveryOrderIdLeafService.batchGetLeafId(1);
				if(CollectionUtils.isNotEmpty(deliveryIdList)){
					deliveryOrder.setId(deliveryIdList.get(0));
				}
			}
			DeliveryOrderDO record = translate(deliveryOrder);
			if(record.getId()!=null){
				deliveryOrderDOMapper.insertSelective(record);
			}else {
				deliveryOrderDOComplexMapper.insertSelective(record);
			}

			deliveryOrderRouteRepository.saveRiderDeliveryOrderRoute(deliveryOrder);
			deliveryOrder.setId(record.getId());

		} else {
			Integer oldVersion = deliveryOrder.getVersion();
			deliveryOrder.setVersion(oldVersion + 1);

			DeliveryOrderDOExample example = new DeliveryOrderDOExample();
			example.createCriteria()
					.andIdEqualTo(deliveryOrder.getId())
					.andTenantIdEqualTo(deliveryOrder.getTenantId())
					.andStoreIdEqualTo(deliveryOrder.getStoreId())
					.andVersionEqualTo(oldVersion);

			if (1 != deliveryOrderDOMapper.updateByExampleSelective(translate(deliveryOrder), example)) {
				throw new MySQLOperateFailedException("运单保存失败");
			}
		}
	}


	@Override
	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public Optional<RiderDeliveryOrder> getDeliveryOrderForceMasterWithTenantId(Long id,Long tenantId,Long storeId) {

		DeliveryOrderDOExample example = new DeliveryOrderDOExample();
		example.createCriteria().andIdEqualTo(id).andTenantIdEqualTo(tenantId).andStoreIdEqualTo(storeId);
		List<DeliveryOrderDO> deliveryOrderDOList = deliveryOrderDOMapper.selectByExample(example);
		if(CollectionUtils.isEmpty(deliveryOrderDOList)){
			return Optional.empty();
		}
		return Optional.ofNullable(translate(deliveryOrderDOList.get(0)));
	}

	@Override
	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public Optional<RiderDeliveryOrder> getDeliveryOrderForceMaster(Long id) {
		try {
			ZebraForceMasterHelper.forceMasterInLocalContext();

			return Optional.ofNullable(translate(deliveryOrderDOMapper.selectByPrimaryKey(id)));
		} finally {
			ZebraForceMasterHelper.clearLocalContext();
		}
	}
	@Override
	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public PageResult<RiderDeliveryOrder> pageQueryRiderDeliveryOrdersTenant(PageRequest pageRequest, Long storeId, DeliveryStatusEnum status,Long tenantId) {
		long total = countDeliveryOrderTenant(storeId, status,tenantId);
		if (total == 0) {
			return new PageResult<>(new ArrayList<>(), pageRequest, 0);
		}

		DeliveryOrderDOExample example = new DeliveryOrderDOExample();
		DeliveryOrderDOExample.Criteria criteria = example.createCriteria();
		criteria
				.andStoreIdEqualTo(storeId)
				.andDeliveryStatusEqualTo(status.getCode())
				.andDeliveryChannelEqualTo(DeliveryChannelEnum.MERCHANT_DELIVERY.getCode())
				.andActiveStatusEqualTo(DeliveryOrder.DELIVERY_ORDER_ACTIVE);
		if(com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils.getDeliveryQueryTenantSwitch(tenantId)){
			criteria.andTenantIdEqualTo(tenantId);
		}

		example.setOrderByClause("estimated_delivery_time ASC");
		example.setLimit(pageRequest.getPageSize());
		example.setOffset((pageRequest.getPage() - 1) * pageRequest.getPageSize());

		List<DeliveryOrderDO> records = new ArrayList<>();
		if(MccConfigUtils.checkIsDHTenant(tenantId)){
			records = deliveryOrderDOMapper.selectByExample(example);
		}else {
			records = deliveryOrderDOComplexMapper.selectByExampleSlave(example);
		}

		return new PageResult<>(translate(records), pageRequest, total);
	}

	@Override
	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public PageResult<RiderDeliveryOrder> pageQueryRiderDeliveryOrders(PageRequest pageRequest, Long storeId, DeliveryStatusEnum status, Long riderAccountId,Long tenantId) {
		long total = countDeliveryOrder(storeId, status, riderAccountId,tenantId);
		if (total == 0) {
			return new PageResult<>(new ArrayList<>(), pageRequest, 0);
		}

		DeliveryOrderDOExample example = new DeliveryOrderDOExample();
		DeliveryOrderDOExample.Criteria criteria = example.createCriteria();
		criteria
				.andStoreIdEqualTo(storeId)
				.andDeliveryStatusEqualTo(status.getCode())
				.andActiveStatusEqualTo(DeliveryOrder.DELIVERY_ORDER_ACTIVE)
				.andDeliveryChannelEqualTo(DeliveryChannelEnum.MERCHANT_DELIVERY.getCode())
				.andRiderAccountIdEqualTo(riderAccountId);
		if(com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils.getDeliveryQueryTenantSwitch(tenantId)){
			criteria.andTenantIdEqualTo(tenantId);
		}

		example.setOrderByClause("estimated_delivery_time ASC");
		example.setOffset((pageRequest.getPage() - 1) * pageRequest.getPageSize());
		example.setLimit(pageRequest.getPageSize());

		List<DeliveryOrderDO> records = new ArrayList<>();
		if(MccConfigUtils.checkIsDHTenant(tenantId)){
			records = deliveryOrderDOMapper.selectByExample(example);
		}else {
			records = deliveryOrderDOComplexMapper.selectByExampleSlave(example);
		}

		return new PageResult<>(translate(records), pageRequest, total);
	}

	@Override
	public PageResult<RiderDeliveryOrder> pageQueryCompletedDeliveryOrders(PageRequest pageRequest, Long storeId, Long riderAccountId, LocalDateTime startETA,Long tenantId) {
		DeliveryOrderDOExample example = new DeliveryOrderDOExample();
		DeliveryOrderDOExample.Criteria criteria = example.createCriteria();
		criteria
				.andStoreIdEqualTo(storeId)
				.andActiveStatusEqualTo(DeliveryOrder.DELIVERY_ORDER_ACTIVE)
				.andRiderAccountIdEqualTo(riderAccountId)
				.andDeliveryStatusIn(Lists.newArrayList(DeliveryStatusEnum.DELIVERY_DONE.getCode()))
				.andDeliveryChannelEqualTo(DeliveryChannelEnum.MERCHANT_DELIVERY.getCode())
				.andEstimatedDeliveryTimeGreaterThanOrEqualTo(startETA);

		if(com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils.getDeliveryQueryTenantSwitch(tenantId)){
			criteria.andTenantIdEqualTo(tenantId);
		}

		long total = 0;
		if(MccConfigUtils.checkIsDHTenant(tenantId)){
			total = deliveryOrderDOMapper.countByExample(example);
		}else {
			total = deliveryOrderDOComplexMapper.countByExampleSlave(example);
		}


		example.setOrderByClause("estimated_delivery_time DESC");
		example.setOffset((pageRequest.getPage() - 1) * pageRequest.getPageSize());
		example.setLimit(pageRequest.getPageSize());

		List<DeliveryOrderDO> records = new ArrayList<>();
		if(MccConfigUtils.checkIsDHTenant(tenantId)){
			records = deliveryOrderDOMapper.selectByExample(example);
		}else {
			records = deliveryOrderDOComplexMapper.selectByExampleSlave(example);
		}

		return new PageResult<>(translate(records), pageRequest, total);
	}

	@Override
	public PageResult<RiderDeliveryOrder> pageQueryCompletedDeliveryOrders(PageRequest pageRequest, Long storeId, Long riderAccountId,
																		   LocalDateTime deliveryDoneTimeBegin, LocalDateTime deliveryDoneTimeEnd,Long tenantId) {
		DeliveryOrderDOExample example = new DeliveryOrderDOExample();
		DeliveryOrderDOExample.Criteria criteria = example.createCriteria();
		criteria
				.andStoreIdEqualTo(storeId)
				.andActiveStatusEqualTo(DeliveryOrder.DELIVERY_ORDER_ACTIVE)
				.andRiderAccountIdEqualTo(riderAccountId)
				.andDeliveryStatusIn(Lists.newArrayList(DeliveryStatusEnum.DELIVERY_DONE.getCode()))
				.andDeliveryChannelEqualTo(DeliveryChannelEnum.MERCHANT_DELIVERY.getCode())
				.andDeliveryDoneTimeBetween(deliveryDoneTimeBegin, deliveryDoneTimeEnd);
		if(com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils.getDeliveryQueryTenantSwitch(tenantId)){
			criteria.andTenantIdEqualTo(tenantId);
		}

		long total = deliveryOrderDOMapper.countByExample(example);

		if (LionConfigUtils.checkIsDHTenant(tenantId)) {
			example.setOrderByClause("delivery_done_time DESC");
		} else {
			example.setOrderByClause("estimated_delivery_time DESC");
		}
		example.setOffset((pageRequest.getPage() - 1) * pageRequest.getPageSize());
		example.setLimit(pageRequest.getPageSize());

		List<DeliveryOrderDO> records = deliveryOrderDOMapper.selectByExample(example);
		return new PageResult<>(translate(records), pageRequest, total);
	}

	@Override
	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public long countDeliveryOrderTenant(Long storeId, DeliveryStatusEnum status,Long tenantId) {
		DeliveryOrderDOExample example = new DeliveryOrderDOExample();
		DeliveryOrderDOExample.Criteria criteria = example.createCriteria();
		criteria
				.andStoreIdEqualTo(storeId)
				.andDeliveryStatusEqualTo(status.getCode())
				.andDeliveryChannelEqualTo(DeliveryChannelEnum.MERCHANT_DELIVERY.getCode())
				.andActiveStatusEqualTo(DeliveryOrder.DELIVERY_ORDER_ACTIVE);
		if(com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils.getDeliveryQueryTenantSwitch(tenantId)){
			criteria.andTenantIdEqualTo(tenantId);
		}

		if(MccConfigUtils.checkIsDHTenant(tenantId)){
			return deliveryOrderDOMapper.countByExample(example);
		}
		return deliveryOrderDOComplexMapper.countByExampleSlave(example);
	}

	@Override
	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public long countDeliveryOrder(Long storeId, DeliveryStatusEnum status, Long riderAccountId,Long tenantId) {
		DeliveryOrderDOExample example = new DeliveryOrderDOExample();
		DeliveryOrderDOExample.Criteria criteria = example.createCriteria();
		criteria
				.andStoreIdEqualTo(storeId)
				.andDeliveryStatusEqualTo(status.getCode())
				.andActiveStatusEqualTo(DeliveryOrder.DELIVERY_ORDER_ACTIVE)
				.andDeliveryChannelEqualTo(DeliveryChannelEnum.MERCHANT_DELIVERY.getCode())
				.andRiderAccountIdEqualTo(riderAccountId);
		if(com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils.getDeliveryQueryTenantSwitch(tenantId)){
			criteria.andTenantIdEqualTo(tenantId);
		}

		if(MccConfigUtils.checkIsDHTenant(tenantId)){
			return deliveryOrderDOMapper.countByExample(example);
		}
		return deliveryOrderDOComplexMapper.countByExampleSlave(example);
	}

	@Override
	public long countTimeoutDeliveryOrderTenant(Long storeId, DeliveryStatusEnum status,Long tenantId) {
		DeliveryOrderDOExample example = new DeliveryOrderDOExample();
		DeliveryOrderDOExample.Criteria criteria = example.createCriteria();
		criteria
				.andStoreIdEqualTo(storeId)
				.andDeliveryStatusEqualTo(status.getCode())
				.andActiveStatusEqualTo(DeliveryOrder.DELIVERY_ORDER_ACTIVE)
				.andDeliveryChannelEqualTo(DeliveryChannelEnum.MERCHANT_DELIVERY.getCode())
				.andEstimatedDeliveryEndTimeLessThan(LocalDateTime.now());
		if(com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils.getDeliveryQueryTenantSwitch(tenantId)){
            criteria.andTenantIdEqualTo(tenantId);
		}

		if(MccConfigUtils.checkIsDHTenant(tenantId)){
			return deliveryOrderDOMapper.countByExample(example);
		}
		return deliveryOrderDOComplexMapper.countByExampleSlave(example);
	}

	@Override
	public long countTimeoutDeliveryOrder(Long storeId, DeliveryStatusEnum status, Long riderAccountId,Long tenantId) {
		DeliveryOrderDOExample example = new DeliveryOrderDOExample();
		DeliveryOrderDOExample.Criteria criteria = example.createCriteria();
		criteria
				.andStoreIdEqualTo(storeId)
				.andDeliveryStatusEqualTo(status.getCode())
				.andActiveStatusEqualTo(DeliveryOrder.DELIVERY_ORDER_ACTIVE)
				.andRiderAccountIdEqualTo(riderAccountId)
				.andDeliveryChannelEqualTo(DeliveryChannelEnum.MERCHANT_DELIVERY.getCode())
				.andEstimatedDeliveryEndTimeLessThan(LocalDateTime.now());
		if(com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils.getDeliveryQueryTenantSwitch(tenantId)){
			criteria.andTenantIdEqualTo(tenantId);
		}
		if(MccConfigUtils.checkIsDHTenant(tenantId)){
			return deliveryOrderDOMapper.countByExample(example);
		}
		return deliveryOrderDOComplexMapper.countByExampleSlave(example);
	}

	@Override
	public PageResult<RiderDeliveryOrder> pageQueryRiderDeliveryOrdersWithTenantId(PageRequest pageRequest, Long storeId, List<DeliveryStatusEnum> statusList, Long riderAccountId,Long activeStatus,Long tenantId) {
		long total = countDeliveryOrderWithTenantId(storeId, statusList, riderAccountId,activeStatus,tenantId);
		if (total == 0) {
			return new PageResult<>(new ArrayList<>(), pageRequest, 0);
		}

		DeliveryOrderDOExample example = new DeliveryOrderDOExample();
		DeliveryOrderDOExample.Criteria criteria = example.createCriteria();
		criteria
				.andStoreIdEqualTo(storeId)
				.andDeliveryStatusIn(statusList.stream().map(DeliveryStatusEnum::getCode).collect(Collectors.toList()))
				.andActiveStatusEqualTo(activeStatus)
				.andDeliveryChannelEqualTo(DeliveryChannelEnum.MERCHANT_DELIVERY.getCode())
				.andRiderAccountIdEqualTo(riderAccountId);
		if(com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils.getDeliveryQueryTenantSwitch(tenantId)){
			criteria.andTenantIdEqualTo(tenantId);
		}

		example.setOrderByClause("estimated_delivery_time ASC");
		example.setOffset((pageRequest.getPage() - 1) * pageRequest.getPageSize());
		example.setLimit(pageRequest.getPageSize());

		List<DeliveryOrderDO> records = new ArrayList<>();
		if(MccConfigUtils.checkIsDHTenant(tenantId)){
			records = deliveryOrderDOMapper.selectByExample(example);
		}else {
			records = deliveryOrderDOComplexMapper.selectByExampleSlave(example);
		}
		return new PageResult<>(translate(records), pageRequest, total);
	}

	@Override
	public List<RiderDeliveryOrder> querySelfRiderNewAndEndDeliveryOrders(Long tenantId, Long storeId, List<DeliveryStatusEnum> statusList, Long riderAccountId) {

		DeliveryOrderDOExample example = new DeliveryOrderDOExample();
		DeliveryOrderDOExample.Criteria criteria = example.createCriteria();
		criteria
				.andTenantIdEqualTo(tenantId)
				.andStoreIdEqualTo(storeId)
				.andDeliveryChannelEqualTo(DeliveryChannelEnum.MERCHANT_DELIVERY.getCode())
				.andDeliveryStatusIn(statusList.stream().map(DeliveryStatusEnum::getCode).collect(Collectors.toList()))
				.andActiveStatusEqualTo(DeliveryOrder.DELIVERY_ORDER_ACTIVE)
				.andRiderAccountIdIn(Lists.newArrayList(0L, riderAccountId));
		List<DeliveryOrderDO> records = deliveryOrderDOMapper.selectByExample(example);

		return translate(records);
	}

	@Override
	public List<RiderDeliveryOrder> querySelfRiderInProcessDeliveryOrders(Long tenantId, Long storeId, List<DeliveryStatusEnum> statusList, Long riderAccountId) {
		DeliveryOrderDOExample example = new DeliveryOrderDOExample();
		DeliveryOrderDOExample.Criteria criteria = example.createCriteria();
		criteria
				.andTenantIdEqualTo(tenantId)
				.andStoreIdEqualTo(storeId)
				.andDeliveryChannelEqualTo(DeliveryChannelEnum.MERCHANT_DELIVERY.getCode())
				.andDeliveryStatusIn(statusList.stream().map(DeliveryStatusEnum::getCode).collect(Collectors.toList()))
				.andActiveStatusEqualTo(DeliveryOrder.DELIVERY_ORDER_ACTIVE)
				.andRiderAccountIdEqualTo( riderAccountId);
		List<DeliveryOrderDO> records = deliveryOrderDOMapper.selectByExample(example);
		return translate(records);
	}

	@Override
	public List<RiderDeliveryOrder> queryThirdRiderInProcessDeliveryOrders(Long tenantId, Long storeId, List<DeliveryStatusEnum> statusList, LocalDateTime timeoutPoint) {
		DeliveryOrderDOExample example = new DeliveryOrderDOExample();
		DeliveryOrderDOExample.Criteria criteria = example.createCriteria();
		criteria
				.andTenantIdEqualTo(tenantId)
				.andStoreIdEqualTo(storeId)
				.andDeliveryChannelNotEqualTo(DeliveryChannelEnum.MERCHANT_DELIVERY.getCode())
				.andDeliveryStatusIn(statusList.stream().map(DeliveryStatusEnum::getCode).collect(Collectors.toList()))
				.andActiveStatusEqualTo(DeliveryOrder.DELIVERY_ORDER_ACTIVE);
		if (Objects.nonNull(timeoutPoint)) {
			criteria.andEstimatedDeliveryTimeGreaterThanOrEqualTo(timeoutPoint);
		}

		List<DeliveryOrderDO> records = deliveryOrderDOMapper.selectByExample(example);
		return translateAllTypeDeliveryOrder(records);
	}

	@Override
	public long countDeliveryOrderWithTenantId(Long storeId, List<DeliveryStatusEnum> statusList, Long riderAccountId,Long activeStatus,Long tenantId) {
		if(CollectionUtils.isEmpty(statusList)){
			return 0L;
		}
		DeliveryOrderDOExample example = new DeliveryOrderDOExample();
		DeliveryOrderDOExample.Criteria criteria = example.createCriteria();
		criteria
				.andStoreIdEqualTo(storeId)
				.andDeliveryStatusIn(statusList.stream().map(DeliveryStatusEnum::getCode).collect(Collectors.toList()))
				.andActiveStatusEqualTo(activeStatus)
				.andDeliveryChannelEqualTo(DeliveryChannelEnum.MERCHANT_DELIVERY.getCode())
				.andRiderAccountIdEqualTo(riderAccountId);
		if(com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils.getDeliveryQueryTenantSwitch(tenantId)){
			criteria.andTenantIdEqualTo(tenantId);
		}
		if(MccConfigUtils.checkIsDHTenant(tenantId)){
			return deliveryOrderDOMapper.countByExample(example);
		}
		return deliveryOrderDOComplexMapper.countByExampleSlave(example);
	}

	@Override
	public Optional<RiderDeliveryOrder> getCurrentDeliveryOrderForceMaster(Long orderId) {
		try {
			ZebraForceMasterHelper.forceMasterInLocalContext();

			DeliveryOrderDOExample example = new DeliveryOrderDOExample();
			example.createCriteria().andOrderIdEqualTo(orderId);
			example.setOrderByClause("id desc");

			List<DeliveryOrderDO> records = deliveryOrderDOMapper.selectByExample(example);
			if (CollectionUtils.isEmpty(records)) {
				return Optional.empty();
			} else {
				return Optional.ofNullable(translate(
						records.stream()
								.filter(it -> it.getActiveStatus() == DeliveryOrder.DELIVERY_ORDER_ACTIVE)
								.findAny()
								.orElse(records.get(0))
				));
			}
		} finally {
			ZebraForceMasterHelper.clearLocalContext();
		}
	}

	@Override
	public Optional<RiderDeliveryOrder> getCurrentDeliveryOrderForceMasterWithRoute(Long orderId){

		DeliveryOrderRoute deliveryOrderRoute = deliveryOrderRouteRepository.queryOrderRouteByOrderIdWithMax(orderId);
		if(deliveryOrderRoute == null){
			return Optional.empty();
		}

		DeliveryOrderDOExample example = new DeliveryOrderDOExample();
		example.createCriteria().andOrderIdEqualTo(deliveryOrderRoute.getOrderId()).andTenantIdEqualTo(deliveryOrderRoute.getTenantId()).andStoreIdEqualTo(deliveryOrderRoute.getOfflineStoreId());
		example.setOrderByClause("id desc");

		List<DeliveryOrderDO> records = deliveryOrderDOMapper.selectByExample(example);
		if (CollectionUtils.isEmpty(records)) {
			return Optional.empty();
		} else {
			return Optional.ofNullable(translate(
					records.stream()
							.filter(it -> it.getActiveStatus() == DeliveryOrder.DELIVERY_ORDER_ACTIVE)
							.findAny()
							.orElse(records.get(0))
			));
		}
	}

	@Override
	public Optional<RiderDeliveryOrder> getCurrentDeliveryOrderForceMasterWithTenant(Long orderId,Long tenantId,Long storeId){
		DeliveryOrderDOExample example = new DeliveryOrderDOExample();
		example.createCriteria().andOrderIdEqualTo(orderId).andTenantIdEqualTo(tenantId).andStoreIdEqualTo(storeId);
		example.setOrderByClause("id desc");

		List<DeliveryOrderDO> records = deliveryOrderDOMapper.selectByExample(example);
		if (CollectionUtils.isEmpty(records)) {
			return Optional.empty();
		} else {
			return Optional.ofNullable(translate(
					records.stream()
							.filter(it -> it.getActiveStatus() == DeliveryOrder.DELIVERY_ORDER_ACTIVE)
							.findAny()
							.orElse(records.get(0))
			));
		}
	}

	private List<RiderDeliveryOrder> translate(List<DeliveryOrderDO> records) {
		if (CollectionUtils.isEmpty(records)) {
			return new ArrayList<>();
		}

		return records.stream()
				.map(this::translate)
				.filter(Objects::nonNull)
				.collect(Collectors.toList());
	}

	private List<RiderDeliveryOrder> translateAllTypeDeliveryOrder(List<DeliveryOrderDO> records) {
		if (CollectionUtils.isEmpty(records)) {
			return new ArrayList<>();
		}

		return records.stream()
				.map(this::translateAllType)
				.filter(Objects::nonNull)
				.collect(Collectors.toList());
	}

	private RiderDeliveryOrder translateAllType(DeliveryOrderDO record) {
		if (record == null) {
			return null;
		}

		DeliveryStatusEnum status = DeliveryStatusEnum.valueOf(record.getDeliveryStatus());

		RiderDeliveryExtInfo riderDeliveryExtInfo = null;
		try {
			riderDeliveryExtInfo = JsonUtil.fromJson(record.getExtInfo(), RiderDeliveryExtInfo.class);
		} catch (Exception e) {
			log.error("解析运单扩展字段失败, deliveryOrderDO:{}",record, e);
			Cat.logEvent("DELIVERY_EXT_INFO_PARSE", "ERROR");
		}
		StaffRider changeFromRider = null;
		if (Objects.nonNull(riderDeliveryExtInfo) && Objects.nonNull(riderDeliveryExtInfo.getLastFromRiderAccountId()) && StringUtils.isNotEmpty(riderDeliveryExtInfo.getLastFromRiderName())) {
			changeFromRider = new StaffRider(riderDeliveryExtInfo.getLastFromRiderName(), "-1", null, riderDeliveryExtInfo.getLastFromRiderAccountId());
		}

		CustomerOrderKey customerOrderKey = new CustomerOrderKey(
				record.getOrderId(), record.getChannelOrderId(), record.getOrderBizType(),
				record.getDaySeq(), OrderReserveTypeEnum.convert(record.getReserved()), record.getFulfillmentOrderId(), record.getOrderSource());

		DeliveryTimeline timeline = new DeliveryTimeline(
				record.getLastEventTime(),record.getEstimatedDeliveryTime(), record.getEstimatedDeliveryEndTime(),
				record.getDeliveryDoneTime(), record.getCreateTime());

		Receiver recipient = new Receiver(
				record.getReceiverName(), record.getReceiverPhone(), record.getReceiverPrivacyPhone(),
				JsonTranslator.fromAddressJson(record.getReceiverAddress()));

		StaffRider staffRider = translateRider(record);

		boolean isThirdException = isThirdException(record);

		return new RiderDeliveryOrder(
				record.getId(),
				record.getTenantId(),
				record.getStoreId(),
				DeliveryChannelEnum.valueOf(record.getDeliveryChannel()),
				record.getDeliveryChannel(),
				customerOrderKey,
				status,
				timeline,
				recipient,
				record.getDistance(),
				record.getVersion(),
				staffRider,
				changeFromRider,
				record.getCurrentStatusLock(),
				record.getTotalLockCount(),
				record.getCurrentLockTime(),
				record.getCurrentUnlockTime(),
				riderDeliveryExtInfo,
				isThirdException
		);
	}

	private boolean isThirdException(DeliveryOrderDO record) {
		try {
			boolean isThirdException = false;
			if (!Objects.equals(record.getDeliveryChannel(), DeliveryChannelEnum.MERCHANT_DELIVERY.getCode())
					&& ((record.getDeliveryExceptionType() != DeliveryExceptionTypeEnum.NO_EXCEPTION.getCode()) || Objects.equals(record.getDeliveryStatus(), DeliveryStatusEnum.DELIVERY_CANCELLED.getCode()))) {
				isThirdException = true;
			}
			return isThirdException;
		} catch (Exception e) {
			log.error("isThirdException error", e);
			return false;
		}
	}

	private RiderDeliveryOrder translate(DeliveryOrderDO record) {
		if (record == null) {
			return null;
		}

		DeliveryStatusEnum status = DeliveryStatusEnum.valueOf(record.getDeliveryStatus());
		if (status == null) {
			log.error("不支持运单[{}]当前状态[{}]，将会视为不存在", record.getId(), record.getDeliveryStatus());
			return null;
		}

		RiderDeliveryExtInfo riderDeliveryExtInfo = null;
		try {
			riderDeliveryExtInfo = JsonUtil.fromJson(record.getExtInfo(), RiderDeliveryExtInfo.class);
		} catch (Exception e) {
			log.error("解析运单扩展字段失败, deliveryOrderDO:{}",record, e);
			Cat.logEvent("DELIVERY_EXT_INFO_PARSE", "ERROR");
		}

		//todo: 二期的时候，再删除这里，读取真实的ext。一期上线需要兼容老运单
		if (!MccConfigUtils.checkIsDHTenant(record.getTenantId())) {
			if (riderDeliveryExtInfo == null) {
				riderDeliveryExtInfo = new RiderDeliveryExtInfo();
			}
			riderDeliveryExtInfo.setPickDeliverySplitTag(true);
		}

		StaffRider changeFromRider = null;
		if (Objects.nonNull(riderDeliveryExtInfo) && Objects.nonNull(riderDeliveryExtInfo.getLastFromRiderAccountId()) && StringUtils.isNotEmpty(riderDeliveryExtInfo.getLastFromRiderName())) {
			changeFromRider = new StaffRider(riderDeliveryExtInfo.getLastFromRiderName(), "-1", null, riderDeliveryExtInfo.getLastFromRiderAccountId());
		}

		CustomerOrderKey customerOrderKey = new CustomerOrderKey(
				record.getOrderId(), record.getChannelOrderId(), record.getOrderBizType(),
				record.getDaySeq(), OrderReserveTypeEnum.convert(record.getReserved()), record.getFulfillmentOrderId(), record.getOrderSource());

		DeliveryTimeline timeline = new DeliveryTimeline(
				record.getLastEventTime(),record.getEstimatedDeliveryTime(), record.getEstimatedDeliveryEndTime(),
				record.getDeliveryDoneTime(), record.getCreateTime());

		Receiver recipient = new Receiver(
				record.getReceiverName(), record.getReceiverPhone(), record.getReceiverPrivacyPhone(),
				JsonTranslator.fromAddressJson(record.getReceiverAddress()));

		StaffRider staffRider = translateRider(record);

		return new RiderDeliveryOrder(
				record.getId(),
				record.getTenantId(),
				record.getStoreId(),
				DeliveryChannelEnum.MERCHANT_DELIVERY,
				DeliveryChannelEnum.MERCHANT_DELIVERY.getCode(),
				customerOrderKey,
				status,
				timeline,
				recipient,
				record.getDistance(),
				record.getVersion(),
				staffRider,
				changeFromRider,
				record.getCurrentStatusLock(),
				record.getTotalLockCount(),
				record.getCurrentLockTime(),
				record.getCurrentUnlockTime(),
				riderDeliveryExtInfo,
				false
		);
	}

	private DeliveryOrderDO translate(RiderDeliveryOrder model) {
		if (model == null) {
			return null;
		}

		DeliveryOrderDO record = new DeliveryOrderDO();
		record.setId(model.getId());
		record.setTenantId(model.getTenantId());
		record.setStoreId(model.getStoreId());
		record.setOrderId(model.getCustomerOrderKey().getOrderId());
		if(model.getCustomerOrderKey().getFulfillOrderId()!=null){
			record.setFulfillmentOrderId(model.getCustomerOrderKey().getFulfillOrderId());
		}
		record.setChannelOrderId(model.getCustomerOrderKey().getChannelOrderId());
		record.setOrderBizType(model.getCustomerOrderKey().getOrderBizType());
		record.setDaySeq(model.getCustomerOrderKey().getDaySeq().intValue());
		record.setReserved(OrderReserveTypeEnum.convert(model.getCustomerOrderKey().getReserved()).getCode());
		record.setOrderSource(model.getCustomerOrderKey().getOrderSource());
		record.setReceiverPhone(model.getReceiver().getReceiverPhone());
		record.setReceiverName(model.getReceiver().getReceiverName());
		record.setReceiverPrivacyPhone(model.getReceiver().getReceiverPrivacyPhone());
		record.setReceiverAddress(JsonTranslator.toJson(model.getReceiver().getReceiverAddress()));
		record.setEstimatedDeliveryTime(model.getTimeline().getEstimatedDeliveryTime());
		record.setEstimatedDeliveryEndTime(model.getTimeline().getEstimatedDeliveryEndTime());
		record.setDeliveryChannel(DeliveryChannelEnum.MERCHANT_DELIVERY.getCode());
		record.setChannelDeliveryId(model.getCustomerOrderKey().getChannelOrderId());
		record.setChannelServicePackageCode(StringUtils.EMPTY);
		record.setDeliveryStatus(model.getStatus().getCode());
		record.setDeliveryExceptionType(DeliveryExceptionTypeEnum.NO_EXCEPTION.getCode());
		record.setDeliveryExceptionDescription(StringUtils.EMPTY);
		record.setLastEventTime(model.getTimeline().getLastEventTime());
		if (model.getRiderInfo() != null) {
			record.setRiderName(model.getRiderInfo().getRiderName());
			record.setRiderPhone(model.getRiderInfo().getRiderPhone());
			record.setRiderAccountId(model.getRiderInfo().getRiderAccountId());
		}
		record.setActiveStatus(DeliveryOrder.DELIVERY_ORDER_ACTIVE);
		record.setDeliveryDoneTime(model.getTimeline().getDeliveryDoneTime());
		record.setVersion(model.getVersion());
		record.setCurrentStatusLock(model.getCurrentStatusLocked());
		record.setTotalLockCount(model.getTotalLockCount());
		if (model.getCurrentLockTime() != null) {
			record.setCurrentLockTime(model.getCurrentLockTime());
		}
		if (model.getCurrentUnlockTime() != null) {
			record.setCurrentUnlockTime(model.getCurrentUnlockTime());
		}
		if (model.getRiderDeliveryExtInfo() != null) {
			record.setExtInfo(JsonUtil.toJson(model.getRiderDeliveryExtInfo()));
		}
		return record;
	}

	private StaffRider translateRider(DeliveryOrderDO record) {
		return Optional.ofNullable(record)
				.filter(it -> StaffRider.DEFAULT_ACCOUNT_ID != it.getRiderAccountId())
				.map(it -> new StaffRider(it.getRiderName(), it.getRiderPhone(), it.getRiderPhoneToken(), it.getRiderAccountId()))
				.orElse(null);
	}

	@Override
	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public Optional<RiderDeliveryOrder> getActiveDeliveryOrderForceMaster(Long tenantId, Long storeId, Long orderId) {
		try {
			ZebraForceMasterHelper.forceMasterInLocalContext();

			DeliveryOrderDOExample example = new DeliveryOrderDOExample();
			DeliveryOrderDOExample.Criteria criteria = example.createCriteria();
			criteria
					.andTenantIdEqualTo(tenantId)
					.andStoreIdEqualTo(storeId)
					.andOrderIdEqualTo(orderId)
					.andDeliveryChannelEqualTo(DeliveryChannelEnum.MERCHANT_DELIVERY.getCode())
					.andActiveStatusEqualTo(DeliveryOrder.DELIVERY_ORDER_ACTIVE);

			List<DeliveryOrderDO> records = deliveryOrderDOMapper.selectByExample(example);
			if (CollectionUtils.isEmpty(records)) {
				return Optional.empty();
			} else {
				return Optional.ofNullable(translate(records.get(0)));
			}
		} finally {
			ZebraForceMasterHelper.clearLocalContext();
		}
	}

	@Override
	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public List<RiderDeliveryOrder> batchQueryDeliveryOrder(Long storeId, List<DeliveryStatusEnum> statusList, Long lastQueryResultMaxId, Integer batchSize,Long tenantId) {
		if (storeId == null || CollectionUtils.isEmpty(statusList)) {
			return Collections.emptyList();
		}

		if (lastQueryResultMaxId == null) {
			lastQueryResultMaxId = 0L;
		}

		if (batchSize == null || batchSize < 0) {
			batchSize = DEFAULT_BATCH_SIZE;
		}

		List<Integer> statusCodeList = statusList.stream().map(DeliveryStatusEnum::getCode).collect(Collectors.toList());

		DeliveryOrderDOExample example = new DeliveryOrderDOExample();
		DeliveryOrderDOExample.Criteria criteria = example.createCriteria();
		criteria
				.andStoreIdEqualTo(storeId)
				.andDeliveryStatusIn(statusCodeList)
				.andIdGreaterThan(lastQueryResultMaxId)
				.andDeliveryChannelEqualTo(DeliveryChannelEnum.MERCHANT_DELIVERY.getCode())
				.andActiveStatusEqualTo(DeliveryOrder.DELIVERY_ORDER_ACTIVE);
		if(com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils.getDeliveryQueryTenantSwitch(tenantId)){
			criteria.andTenantIdEqualTo(tenantId);
		}

		example.setOrderByClause("id ASC");
		example.setLimit(batchSize);
		List<DeliveryOrderDO> records = deliveryOrderDOMapper.selectByExample(example);
		return translate(records);
	}


	@Override
	public long countRiderDeliveryOrder(Long tenantId, List<DeliveryStatusEnum> statusEnumList, LocalDateTime start,
										LocalDateTime end, List<Long> storeIds, List<Long> riderAccountIds) {
		DeliveryOrderDOExample example = this.buildDeliveryOrderDOExample(tenantId, statusEnumList,
				start, end, storeIds, riderAccountIds);
		if(MccConfigUtils.checkIsDHTenant(tenantId)){
			return deliveryOrderDOComplexMapper.countByExampleWithStoreList(example);
		}
		return deliveryOrderDOComplexMapper.countByExampleWithStoreListSlave(example);
	}

	@Override
	public long countRiderCompletedDeliveryOrder(Long tenantId, Long storeId, Long riderAccountId, LocalDateTime deliveryDoneTimeBegin, LocalDateTime deliveryDoneTimeEnd) {
		DeliveryOrderDOExample deliveryOrderDOExample = new DeliveryOrderDOExample();
		deliveryOrderDOExample.createCriteria()
				.andTenantIdEqualTo(tenantId)
				.andStoreIdEqualTo(storeId)
				.andRiderAccountIdEqualTo(riderAccountId)
				.andDeliveryStatusEqualTo(DeliveryStatusEnum.DELIVERY_DONE.getCode())
				.andActiveStatusEqualTo(DeliveryOrder.DELIVERY_ORDER_ACTIVE)
				.andDeliveryChannelEqualTo(DeliveryChannelEnum.MERCHANT_DELIVERY.getCode())
				.andDeliveryDoneTimeBetween(deliveryDoneTimeBegin, deliveryDoneTimeEnd);
		if(MccConfigUtils.checkIsDHTenant(tenantId)){
			return deliveryOrderDOMapper.countByExample(deliveryOrderDOExample);
		}
		return deliveryOrderDOComplexMapper.countByExampleSlave(deliveryOrderDOExample);
	}


	@Override
	public long countRiderCompletedAndOneYuanOrder(Long tenantId, Long storeId, Long riderAccountId, LocalDateTime deliveryDoneTimeBegin, LocalDateTime deliveryDoneTimeEnd) {
		DeliveryOrderDOExample deliveryOrderDOExample = new DeliveryOrderDOExample();
		deliveryOrderDOExample.createCriteria()
				.andTenantIdEqualTo(tenantId)
				.andStoreIdEqualTo(storeId)
				.andRiderAccountIdEqualTo(riderAccountId)
				.andDeliveryStatusEqualTo(DeliveryStatusEnum.DELIVERY_DONE.getCode())
				.andActiveStatusEqualTo(DeliveryOrder.DELIVERY_ORDER_ACTIVE)
				.andDeliveryChannelEqualTo(DeliveryChannelEnum.MERCHANT_DELIVERY.getCode())
				.andDeliveryDoneTimeBetween(deliveryDoneTimeBegin, deliveryDoneTimeEnd)
				.andExtInfoLike("%\"isOneYuanOrder\":true%");
		if(MccConfigUtils.checkIsDHTenant(tenantId)){
			return deliveryOrderDOMapper.countByExample(deliveryOrderDOExample);
		}
		return deliveryOrderDOComplexMapper.countByExampleSlave(deliveryOrderDOExample);
	}

	@Override
	public List<RiderDeliveryOrder> queryRiderDeliveryOrderByPage(PageRequest pageRequest, Long tenantId,
																  List<DeliveryStatusEnum> statusEnumList,
																  LocalDateTime start, LocalDateTime end, List<Long> storeIds,
																  List<Long> riderAccountIds) {

		DeliveryOrderDOExample example = this.buildDeliveryOrderDOExample(tenantId, statusEnumList,
				start, end, storeIds, riderAccountIds);
		example.setLimit(pageRequest.getPageSize());
		example.setOffset((pageRequest.getPage() - 1) * pageRequest.getPageSize());

		List<DeliveryOrderDO> records = deliveryOrderDOComplexMapper.selectByExampleWithStoreList(example);
		return translate(records);
	}

	@Override
	@MethodLog(logRequest = false, logResponse = true)
	public Map<Long, RiderDeliveryOrder> queryRiderDeliveringOrderMap(Long storeId, Long riderAccountId,Long tenantId) {
		DeliveryOrderDOExample example = new DeliveryOrderDOExample();
		DeliveryOrderDOExample.Criteria criteria = example.createCriteria();
		criteria
				.andStoreIdEqualTo(storeId)
				.andDeliveryStatusEqualTo(DeliveryStatusEnum.RIDER_TAKEN_GOODS.getCode())
				.andCurrentStatusLockEqualTo(UNLOCK_STATUS)
				.andActiveStatusEqualTo(DeliveryOrder.DELIVERY_ORDER_ACTIVE)
				.andDeliveryChannelEqualTo(DeliveryChannelEnum.MERCHANT_DELIVERY.getCode())
				.andRiderAccountIdEqualTo(riderAccountId);

		if(com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils.getDeliveryQueryTenantSwitch(tenantId)){
			criteria.andTenantIdEqualTo(tenantId);
		}

		List<DeliveryOrderDO> records = deliveryOrderDOMapper.selectByExample(example);

		return translate(records).stream()
				.collect(Collectors.toMap(RiderDeliveryOrder::getId, Function.identity()));
	}

	@Override
	public List<RiderDeliveryOrder> queryDeliveryOrdersByPoiAndStatusList(Long tenantId, List<Long> storeIdList, List<Integer> statusList) {
		DeliveryOrderDOExample example = new DeliveryOrderDOExample();
		DeliveryOrderDOExample.Criteria criteria = example.createCriteria();
		criteria
				.andStoreIdIn(storeIdList)
				.andDeliveryStatusIn(statusList)
				.andDeliveryChannelEqualTo(DeliveryChannelEnum.MERCHANT_DELIVERY.getCode())
				.andActiveStatusEqualTo(DeliveryOrder.DELIVERY_ORDER_ACTIVE);
		if(com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils.getDeliveryQueryTenantSwitch(tenantId)){
			criteria.andTenantIdEqualTo(tenantId);
		}
		example.setOrderByClause("estimated_delivery_time ASC");
		//未分页,兜底limit
		example.setLimit(2000);

		List<DeliveryOrderDO> records = deliveryOrderDOComplexMapper.selectByExampleWithStoreList(example);
		return translate(records);
	}

	@Override
	public List<RiderDeliveryOrder> batchQueryDeliveryOrder(Long tenantId, Long storeId, List<Long> orderIds) {
		if (CollectionUtils.isEmpty(orderIds)) {
			return Collections.emptyList();
		}

		DeliveryOrderDOExample example = new DeliveryOrderDOExample();
		example.createCriteria()
				.andOrderIdIn(orderIds)
				.andTenantIdEqualTo(tenantId)
				.andStoreIdEqualTo(storeId)
				.andActiveStatusEqualTo(DeliveryOrder.DELIVERY_ORDER_ACTIVE);
		example.setOrderByClause("estimated_delivery_time ASC");
		//未分页,兜底limit
		example.setLimit(200);

		List<DeliveryOrderDO> records = deliveryOrderDOMapper.selectByExample(example);
		return translate(records);
	}

	@Override
	public List<RiderDeliveryOrder> batchQueryDeliveryOrderByRoute(List<Long> orderIds, boolean filterSelfDelivery){
		if (CollectionUtils.isEmpty(orderIds)) {
			return Collections.emptyList();
		}

		List<DeliveryOrderRoute> deliveryOrderRouteList = deliveryOrderRouteRepository.queryOrderRouteByOrderIdList(orderIds);
		if(CollectionUtils.isEmpty(deliveryOrderRouteList)){
			return Collections.emptyList();
		}

		ArrayListMultimap<Pair<Long,Long>,Long> multimap = ArrayListMultimap.create();
		deliveryOrderRouteList.forEach(r->{
			multimap.put(new Pair<>(r.getTenantId(),r.getOfflineStoreId()),r.getOrderId());
		});
		List<DeliveryOrderDO> recordList = new ArrayList<>();
		for (Pair<Long,Long> pair:multimap.keySet()){
			DeliveryOrderDOExample example = new DeliveryOrderDOExample();
			example.createCriteria()
					.andTenantIdEqualTo(pair.getKey())
					.andStoreIdEqualTo(pair.getValue())
					.andOrderIdIn(multimap.get(pair))
					.andActiveStatusEqualTo(DeliveryOrder.DELIVERY_ORDER_ACTIVE);

			//未分页,兜底limit
			example.setLimit(200);

			List<DeliveryOrderDO> records = deliveryOrderDOMapper.selectByExample(example);
			if(CollectionUtils.isNotEmpty(records)){
				recordList.addAll(records);
			}
		}
		if (filterSelfDelivery) {
			return translate(recordList);
		} else {
			return translateAllTypeDeliveryOrder(recordList);
		}
	}

	@Override
	public List<RiderDeliveryOrder> batchQueryDeliveryOrderContainUnactivateOrder(Long tenantId, Long storeId, List<Long> orderIds) {
		if (CollectionUtils.isEmpty(orderIds)) {
			return Collections.emptyList();
		}
		DeliveryOrderDOExample example = new DeliveryOrderDOExample();
		example.createCriteria()
				.andOrderIdIn(orderIds)
				.andTenantIdEqualTo(tenantId)
				.andStoreIdEqualTo(storeId);
		example.setOrderByClause("create_time DESC");
		//未分页,兜底limit
		example.setLimit(200);

		List<DeliveryOrderDO> records = deliveryOrderDOMapper.selectByExample(example);

		//如果一个订单对应多个运单 用创建时间最晚的运单
		Map<Long, DeliveryOrderDO> deliveryOrderDOMap = records.stream()
				.collect(Collectors.toMap(DeliveryOrderDO::getOrderId, Function.identity(), (v1, v2) -> v1));
		return translate(new ArrayList<>(deliveryOrderDOMap.values()));
	}


	@Override
	public List<RiderDeliveryOrder> batchQueryDeliveryOrder(List<Long> orderIds) {
		if (CollectionUtils.isEmpty(orderIds)) {
			return Collections.emptyList();
		}

		DeliveryOrderDOExample example = new DeliveryOrderDOExample();
		example.createCriteria()
				.andOrderIdIn(orderIds)
				.andActiveStatusEqualTo(DeliveryOrder.DELIVERY_ORDER_ACTIVE);

		//未分页,兜底limit
		example.setLimit(200);

		List<DeliveryOrderDO> records = deliveryOrderDOMapper.selectByExample(example);
		return translate(records);
	}

	@Override
	public List<RiderDeliveryOrder> batchQueryDeliveryOrderByIds(List<Long> deliveryOrderIds,Long tenantId,Long storeId) {
		if (CollectionUtils.isEmpty(deliveryOrderIds)) {
			return Collections.emptyList();
		}

		DeliveryOrderDOExample example = new DeliveryOrderDOExample();
		DeliveryOrderDOExample.Criteria criteria = example.createCriteria();

		criteria.andIdIn(deliveryOrderIds)
				.andActiveStatusEqualTo(DeliveryOrder.DELIVERY_ORDER_ACTIVE);

		if(tenantId!=null){
			criteria.andTenantIdEqualTo(tenantId);
		}else {
			log.info("getPickSelectQueryJoinSwitch tenantId:{}",tenantId);
		}

		if(storeId!=null){
			criteria.andStoreIdEqualTo(storeId);
		}else {
			log.info("getPickSelectQueryJoinSwitch storeId:{}",storeId);
		}

		//未分页,兜底limit
		example.setLimit(200);

		List<DeliveryOrderDO> records = deliveryOrderDOMapper.selectByExample(example);
		return translate(records);
	}

	@Override
	public List<RiderDeliveryOrder> batchQueryDeliveryOrderWithTenant(Long storeId, DeliveryStatusEnum status,Long tenantId) {

		DeliveryOrderDOExample example = new DeliveryOrderDOExample();
		DeliveryOrderDOExample.Criteria criteria = example.createCriteria();
		criteria
				.andStoreIdEqualTo(storeId)
				.andDeliveryStatusEqualTo(status.getCode())
				.andDeliveryChannelEqualTo(DeliveryChannelEnum.MERCHANT_DELIVERY.getCode())
				.andActiveStatusEqualTo(DeliveryOrder.DELIVERY_ORDER_ACTIVE);
		if(com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils.getDeliveryQueryTenantSwitch(tenantId)){
			criteria.andTenantIdEqualTo(tenantId);
		}

		example.setOrderByClause("estimated_delivery_time ASC");

		List<DeliveryOrderDO> records = deliveryOrderDOMapper.selectByExample(example);
		return translate(records);
	}

	@Override
	public List<RiderDeliveryOrder> batchQueryDeliveryOrder(Long storeId, DeliveryStatusEnum status, Long riderAccountId,Long tenantId) {
		DeliveryOrderDOExample example = new DeliveryOrderDOExample();
		DeliveryOrderDOExample.Criteria criteria = example.createCriteria();
		criteria
				.andStoreIdEqualTo(storeId)
				.andDeliveryStatusEqualTo(status.getCode())
				.andActiveStatusEqualTo(DeliveryOrder.DELIVERY_ORDER_ACTIVE)
				.andDeliveryChannelEqualTo(DeliveryChannelEnum.MERCHANT_DELIVERY.getCode())
				.andRiderAccountIdEqualTo(riderAccountId);
		if(com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils.getDeliveryQueryTenantSwitch(tenantId)){
			criteria.andTenantIdEqualTo(tenantId);
		}

		example.setOrderByClause("estimated_delivery_time ASC");

		List<DeliveryOrderDO> records = deliveryOrderDOMapper.selectByExample(example);
		return translate(records);
	}

	@Override
	public List<RiderDeliveryOrder> batchQueryDeliveryOrder(Long storeId, List<DeliveryStatusEnum> status, Long riderAccountId, Long tenantId) {
		DeliveryOrderDOExample example = new DeliveryOrderDOExample();
		DeliveryOrderDOExample.Criteria criteria = example.createCriteria();
		criteria
				.andStoreIdEqualTo(storeId)
				.andDeliveryStatusIn(IListUtils.mapTo(status, DeliveryStatusEnum::getCode))
				.andActiveStatusEqualTo(DeliveryOrder.DELIVERY_ORDER_ACTIVE)
				.andDeliveryChannelEqualTo(DeliveryChannelEnum.MERCHANT_DELIVERY.getCode())
				.andRiderAccountIdEqualTo(riderAccountId);
		if(com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils.getDeliveryQueryTenantSwitch(tenantId)){
			criteria.andTenantIdEqualTo(tenantId);
		}

		example.setOrderByClause("estimated_delivery_time ASC");

		List<DeliveryOrderDO> records = deliveryOrderDOMapper.selectByExample(example);
		return translate(records);
	}

	@Override
	public List<DeliveryOrderDO> batchQueryThirdDeliveryOrder(Long storeId, List<DeliveryStatusEnum> statusList, Long lastQueryResultMaxId, Integer pageSize,Long tenantId) {
		if (storeId == null || CollectionUtils.isEmpty(statusList)) {
			return Collections.emptyList();
		}

		if (lastQueryResultMaxId == null) {
			lastQueryResultMaxId = 0L;
		}

		if (pageSize == null || pageSize < 0) {
			pageSize = DEFAULT_BATCH_SIZE;
		}

		List<Integer> statusCodeList = statusList.stream().map(DeliveryStatusEnum::getCode).collect(Collectors.toList());

		DeliveryOrderDOExample example = new DeliveryOrderDOExample();
		DeliveryOrderDOExample.Criteria criteria = example.createCriteria();
		criteria
				.andStoreIdEqualTo(storeId)
				.andDeliveryStatusIn(statusCodeList)
				.andIdGreaterThan(lastQueryResultMaxId)
				.andDeliveryChannelNotEqualTo(DeliveryChannelEnum.MERCHANT_DELIVERY.getCode())
				.andActiveStatusEqualTo(DeliveryOrder.DELIVERY_ORDER_ACTIVE);

		if(com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils.getDeliveryQueryTenantSwitch(tenantId)){
			criteria.andTenantIdEqualTo(tenantId);
		}

		example.setOrderByClause("id ASC");
		example.setLimit(pageSize);
		return deliveryOrderDOMapper.selectByExample(example);
	}

	private DeliveryOrderDOExample buildDeliveryOrderDOExample(Long tenantId,
															   List<DeliveryStatusEnum> statusEnumList,
															   LocalDateTime start, LocalDateTime end, List<Long> storeIds,
															   List<Long> riderAccountIds) {
		DeliveryOrderDOExample example = new DeliveryOrderDOExample();
		DeliveryOrderDOExample.Criteria criteria = example.createCriteria();
		if (tenantId != null) {
			criteria.andTenantIdEqualTo(tenantId);
		}else {
			log.info("getPickSelectQueryJoinSwitch tenantId:{}",tenantId);
		}
		if (CollectionUtils.isNotEmpty(statusEnumList)) {
			criteria.andDeliveryStatusIn(statusEnumList.stream().map(DeliveryStatusEnum::getCode)
					.collect(Collectors.toList()));
		}
		if (start != null) {
			criteria.andCreateTimeGreaterThan(start);
		}
		if (end != null) {
			criteria.andCreateTimeLessThanOrEqualTo(end);
		}
		if (CollectionUtils.isNotEmpty(storeIds)) {
			criteria.andStoreIdIn(storeIds);
		}else {
			log.info("getPickSelectQueryJoinSwitch storeIds:{}",storeIds);
		}
		if (CollectionUtils.isNotEmpty(riderAccountIds)) {
			criteria.andRiderAccountIdIn(riderAccountIds);
		}

		criteria.andActiveStatusEqualTo(DeliveryOrder.DELIVERY_ORDER_ACTIVE)
		.andDeliveryChannelEqualTo(DeliveryChannelEnum.MERCHANT_DELIVERY.getCode());

		return example;
	}

	@Override
	public Map<Long, Integer> queryStoreDeliveringOrderCount(List<Long> storeIds, List<DeliveryStatusEnum> statusList,Long tenantId){
		if (CollectionUtils.isEmpty(storeIds) || CollectionUtils.isEmpty(statusList)) {
			return Collections.emptyMap();
		}

		List<CountGroupExDO> countGroupExDOS = deliveryOrderDOComplexMapper.queryStoreDeliveringOrderCount(storeIds, Fun.map(statusList, DeliveryStatusEnum::getCode),tenantId);
		return countGroupExDOS.stream().collect(Collectors.toMap(CountGroupExDO::getGroupColumn, CountGroupExDO::getGroupCount, (k1,k2) -> k2));
	}
}

