package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.cache;

import com.dianping.squirrel.client.StoreClient;
import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.utils.RetryTemplateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.retry.RetryCallback;
import org.springframework.retry.RetryContext;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@SuppressWarnings("all")
@Slf4j
public abstract class SquirrelOperateService {

    @Resource(name = "redisSgCommonProduct")
    protected RedisStoreClient redisClient;

    protected RedisStoreClient getRedisClient(){
        return redisClient;
    }

    public abstract String getCategoryName();

    public <T> boolean set(String key,T data) {
        if(StringUtils.isEmpty(key) || data==null || StringUtils.isEmpty(getCategoryName())){
            log.error("SquirrelOperateService.set param error . key:{},data:{},categoryName:{}",key,data,getCategoryName());
            return false;
        }
        try {

            Boolean result=RetryTemplateUtil.simpleWithFixedRetry(MccConfigUtils.getSquirrelOperateRetryCount(),MccConfigUtils.getSquirrelOperateRetryPeriod()).execute(new RetryCallback<Boolean, Exception>() {
                @Override
                public Boolean doWithRetry(RetryContext retryContext) throws Exception {
                    StoreKey storeKey = new StoreKey(getCategoryName(), key);
                    return getRedisClient().set(storeKey,JsonUtil.toJson(data));
                }
            });
            if(result!=null){
                return result;
            }
        }catch (Exception e){
            log.error("SquirrelOperateService.set error . key:{},data:{},categoryName:{}",key,data,getCategoryName(),e);
        }
        return false;
    }

    public <T> Boolean multiSet(Map<String, T> dataMap) {
        if (MapUtils.isEmpty(dataMap)) {
            log.info("SquirrelOperateService.multiSet dataMap is empty ,dataMap:{}", dataMap);
            return true;
        }

        try {
            Map<StoreKey, String> keyStringMap = dataMap.entrySet().stream()
                    .collect(Collectors.toMap(entry -> new StoreKey(getCategoryName(), entry.getKey()), entry -> JsonUtil.toJson(entry.getValue()), (k1, k2) -> k2));

            Boolean result = RetryTemplateUtil.simpleWithFixedRetry(MccConfigUtils.getSquirrelOperateRetryCount(), MccConfigUtils.getSquirrelOperateRetryPeriod()).execute(new RetryCallback<Boolean, Exception>() {
                @Override
                public Boolean doWithRetry(RetryContext retryContext) {
                    return getRedisClient().multiSet(keyStringMap);
                }
            });

            if (result != null) {
                return result;
            }
        } catch (Exception e) {
            log.error("SquirrelOperateService.multiSet error ,dataMap:{}", dataMap, e);
        }
        return false;
    }

    public <T> Boolean multiSet(Map<String, T> dataMap, int expireSeconds) {
        if (MapUtils.isEmpty(dataMap)) {
            log.info("SquirrelOperateService.multiSet dataMap is empty ,dataMap:{}", dataMap);
            return true;
        }

        try {
            Map<StoreKey, String> keyStringMap = dataMap.entrySet().stream()
                    .collect(Collectors.toMap(entry -> new StoreKey(getCategoryName(), entry.getKey()), entry -> JsonUtil.toJson(entry.getValue()), (k1, k2) -> k2));

            Boolean result = RetryTemplateUtil.simpleWithFixedRetry(MccConfigUtils.getSquirrelOperateRetryCount(), MccConfigUtils.getSquirrelOperateRetryPeriod()).execute(new RetryCallback<Boolean, Exception>() {
                @Override
                public Boolean doWithRetry(RetryContext retryContext) {
                    return getRedisClient().multiSet(keyStringMap, expireSeconds);
                }
            });

            if (result != null) {
                return result;
            }
        } catch (Exception e) {
            log.error("SquirrelOperateService.multiSet error ,dataMap:{}", dataMap, e);
        }
        return false;
    }

    public <T> boolean set(String key, T data, int expireSeconds) {
        if (StringUtils.isEmpty(key) || data == null || StringUtils.isEmpty(getCategoryName())) {
            log.error("SquirrelOperateService.set param error. key:{},data:{}, categoryName:{}", key, data, getCategoryName());
            return false;
        }
        try {
            Boolean result = RetryTemplateUtil.simpleWithFixedRetry(MccConfigUtils.getSquirrelOperateRetryCount(),
                    MccConfigUtils.getSquirrelOperateRetryPeriod()).execute((RetryCallback<Boolean, Exception>) retryContext -> {
                        StoreKey storeKey = new StoreKey(getCategoryName(), key);
                        return getRedisClient().set(storeKey, JsonUtil.toJson(data), expireSeconds);
                    });
            if (result != null) {
                return result;
            }
        } catch (Exception e) {
            log.error("SquirrelOperateService.set error. key:{}, data:{}, categoryName:{}", key, data, getCategoryName(), e);
        }
        return false;
    }

    public <T> Optional<T> get(String key, Class<T> clazz){
        if(StringUtils.isEmpty(key)){
            log.info("SquirrelOperateService.get key is empty ,key:{}",key);
            return Optional.empty();
        }

        try {
            StoreKey storeKey = new StoreKey(getCategoryName(), key);
            String result=RetryTemplateUtil.simpleWithFixedRetry(MccConfigUtils.getSquirrelOperateRetryCount(),MccConfigUtils.getSquirrelOperateRetryPeriod()).execute(new RetryCallback<String, Exception>() {
                @Override
                public String doWithRetry(RetryContext retryContext) throws Exception {
                    return getRedisClient().get(storeKey);
                }
            });
            if(StringUtils.isEmpty(result)){
                return Optional.empty();
            }
            T valObj=JsonUtil.fromJson(result,clazz);
            if(valObj==null){
                return Optional.empty();
            }
            return Optional.of(valObj);
        }catch (Exception e){
            log.info("SquirrelOperateService.get error ,key:{}",key,e);
        }
        return Optional.empty();
    }

    public <T> Map<StoreKey, Optional<T>> multiGet(List<String> keys, Class<T> clazz) {
        if (CollectionUtils.isEmpty(keys)) {
            log.info("SquirrelOperateService.multiGet keys is empty ,keys:{}", keys);
            return Collections.emptyMap();
        }

        try {
            List<StoreKey> storeKeys = keys.stream().map(item -> new StoreKey(getCategoryName(), item)).collect(Collectors.toList());
            Map<StoreKey, String> keyStringMap = RetryTemplateUtil.simpleWithFixedRetry(MccConfigUtils.getSquirrelOperateRetryCount(), MccConfigUtils.getSquirrelOperateRetryPeriod()).execute(new RetryCallback<Map<StoreKey, String>, Exception>() {
                @Override
                public Map<StoreKey, String> doWithRetry(RetryContext retryContext) {
                    return getRedisClient().multiGet(storeKeys);
                }
            });

            if (MapUtils.isEmpty(keyStringMap)) {
                return Collections.emptyMap();
            }

            return keyStringMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey,
                    entry -> {

                        if (StringUtils.isBlank(entry.getValue())) {
                            return Optional.empty();
                        }

                        T valObj = JsonUtil.fromJson(entry.getValue(), clazz);

                        return Optional.ofNullable(valObj);
                    }, (k1, k2) -> k1));

        } catch (Exception e) {
            log.error("SquirrelOperateService.multiGet error ,keys:{}", keys, e);
            return Collections.emptyMap();
        }
    }

    public <T> Map<StoreKey, Optional<T>> multiGetWithMiss(List<String> keys, Class<T> clazz) {
        if (CollectionUtils.isEmpty(keys)) {
            log.info("SquirrelOperateService.multiGetWithMiss keys is empty ,keys:{}", keys);
            return Collections.emptyMap();
        }

        try {
            List<StoreKey> storeKeys = keys.stream().map(item -> new StoreKey(getCategoryName(), item)).collect(Collectors.toList());
            Map<StoreKey, String> keyStringMap = RetryTemplateUtil.simpleWithFixedRetry(MccConfigUtils.getSquirrelOperateRetryCount(), MccConfigUtils.getSquirrelOperateRetryPeriod()).execute(new RetryCallback<Map<StoreKey, String>, Exception>() {
                @Override
                public Map<StoreKey, String> doWithRetry(RetryContext retryContext) {
                    return getRedisClient().multiGet(storeKeys, StoreClient.DeliverOption.BEST_EFFORT_DELIVER_WHIT_MISS_KEYS);
                }
            });

            if (MapUtils.isEmpty(keyStringMap)) {
                return Collections.emptyMap();
            }

            return keyStringMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey,
                    entry -> {
                        if (StringUtils.isBlank(entry.getValue())) {
                            return Optional.empty();
                        }
                        T valObj = JsonUtil.fromJson(entry.getValue(), clazz);
                        return Optional.ofNullable(valObj);
                    }, (k1, k2) -> k1));

        } catch (Exception e) {
            log.error("SquirrelOperateService.multiGetWithMiss error ,keys:{}", keys, e);
            return Collections.emptyMap();
        }
    }

    public boolean delete(String key) {
        if (StringUtils.isEmpty(key) || StringUtils.isEmpty(getCategoryName())) {
            log.error("SquirrelOperateService.delete param error. key:{}, categoryName:{}", key, getCategoryName());
            return false;
        }
        try {

            Boolean result = RetryTemplateUtil.simpleWithFixedRetry(MccConfigUtils.getSquirrelOperateRetryCount(), MccConfigUtils.getSquirrelOperateRetryPeriod()).execute(new RetryCallback<Boolean, Exception>() {
                @Override
                public Boolean doWithRetry(RetryContext retryContext) throws Exception {
                    StoreKey storeKey = new StoreKey(getCategoryName(), key);
                    return getRedisClient().delete(storeKey);
                }
            });
            if (result != null) {
                return result;
            }
        } catch (Exception e) {
            log.error("SquirrelOperateService.delete error. key:{}, categoryName:{}", key, getCategoryName(), e);
        }
        return false;
    }

    public boolean setnx(String key, String data) {
        if (StringUtils.isEmpty(key) || StringUtils.isEmpty(getCategoryName())) {
            log.error("SquirrelOperateService.setnx param error. key:{}, categoryName:{}", key, getCategoryName());
            return false;
        }
        try {
            Boolean result = RetryTemplateUtil.simpleWithFixedRetry(MccConfigUtils.getSquirrelOperateRetryCount(), MccConfigUtils.getSquirrelOperateRetryPeriod())
                    .execute((RetryCallback<Boolean, Exception>) retryContext -> {
                        StoreKey storeKey = new StoreKey(getCategoryName(), key);
                        return getRedisClient().setnx(storeKey, data);
                    });
            if (result != null) {
                return result;
            }
        } catch (Exception e) {
            log.error("SquirrelOperateService.setnx error. key:{}, categoryName:{}", key, getCategoryName(), e);
        }
        return false;
    }

    /**
     * 批量设置
     */
    public <T> boolean addList(String key, Collection<T> data, int expireInSeconds) {
        if (StringUtils.isEmpty(key) || CollectionUtils.isEmpty(data)) {
            log.error("SquirrelOperateService.set param error . key:{},data:{},categoryName:{}", key, data, getCategoryName());
            return false;
        }
        try {

            Long result = RetryTemplateUtil.simpleWithFixedRetry(MccConfigUtils.getSquirrelOperateRetryCount(), MccConfigUtils.getSquirrelOperateRetryPeriod())
                    .execute((RetryCallback<Long, Exception>) retryContext -> {
                        StoreKey storeKey = new StoreKey(getCategoryName(), key);
                        Object[] dataArray = data.toArray();
                        Long rpush = getRedisClient().rpush(storeKey, dataArray);
                        //设置过期时间
                        Boolean expire = getRedisClient().expire(storeKey, expireInSeconds);
                        return rpush;
                    });
            if (result == null || result <= 0) {
                log.error("SquirrelOperateService.rpush error . key:{},data:{},categoryName:{}", key, data, getCategoryName());
                return false;
            }
        } catch (Exception e) {
            log.error("SquirrelOperateService.rpush error . key:{},data:{},categoryName:{}", key, data, getCategoryName(), e);
            return false;
        }
        return true;
    }

    /**
     * 获取列表并删除
     */
    public <T> List<T> getAllListAndRemove(String key, Class<T> clazz) {
        if (StringUtils.isEmpty(key)) {
            log.error("SquirrelOperateService.getAllListAndRemove param error . key:{},categoryName:{}", key, getCategoryName());
            return Collections.emptyList();
        }
        try {

            List<T> result = RetryTemplateUtil.simpleWithFixedRetry(MccConfigUtils.getSquirrelOperateRetryCount(), MccConfigUtils.getSquirrelOperateRetryPeriod())
                    .execute((RetryCallback<List<T>, Exception>) retryContext -> {
                        StoreKey storeKey = new StoreKey(getCategoryName(), key);
                        RedisStoreClient redisClient = getRedisClient();
                        //使用 Long llen(StoreKey key); 和lpop;
                        Long size = redisClient.llen(storeKey);
                        if (Objects.isNull(size) || size <= 0) {
                            return Collections.emptyList();
                        }
                        List<T> data = new ArrayList<>();
                        for (Long i = 0L; i < size; i++) {
                            //弹出数据
                            T item = redisClient.lpop(storeKey);
                            if (Objects.nonNull(item)) {
                                data.add(item);
                            }
                        }
                        return data;
                    });
            if (result != null) {
                return result;
            }
        } catch (Exception e) {
            log.error("SquirrelOperateService.getAllListAndRemove error . key:{},categoryName:{}", key, getCategoryName(), e);
            return Collections.emptyList();
        }
        return Collections.emptyList();
    }
}
