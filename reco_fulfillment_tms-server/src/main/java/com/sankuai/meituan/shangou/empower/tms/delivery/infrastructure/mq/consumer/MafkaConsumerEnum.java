package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.consumer;

import com.meituan.reco.pickselect.common.mq.consumer.ConsumerConfig;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.producer.MafkaTopicEnum;

/**
 * <AUTHOR>
 * email <EMAIL>
 * date 2018/5/10
 */
public enum MafkaConsumerEnum implements ConsumerConfig {

    /**
     * 抖音平台配送状态变更消费组
     */
    DY_DELIVERY_ORDER_CHANGE_CONSUMER("shangou_empower_dy_delivery_order_change_consumer", "waimai", "shangou_empower_dy_delivery_order_change_topic", "com.sankuai.sgfulfillment.tms"),

    /**
     * tms内部监听配送状态变更消费组
     *
     */
    DELIVERY_CHANGE_NOTIFY_TMS_CONSUMER("shangou_empower_delivery_change_notify_tms_consumer", "waimai", "shangou_empower_delivery_change_topic", "com.sankuai.sgfulfillment.tms"),

    /**
     * 淘鲜达平台配送状态变更消费组
     */
    TXD_DELIVERY_ORDER_CHANGE_CONSUMER("shangou_empower_txd_delivery_order_change_consumer", "waimai", "shangou_empower_txd_delivery_order_change_topic", "com.sankuai.sgfulfillment.tms"),

    /**
     * tms监听订单信息修改消费组
     */
    ORDER_INFO_CHANGE_CONSUMER("shangou_empower_order_info_change_tms_consumer", "waimai", "shangou_empower_delivery_update_topic", "com.sankuai.sgfulfillment.tms"),
    ;

    private final String consumerGroupName;

    private final String bgName;

    private final String topic;

    private final String consumerClientAppKey;

    MafkaConsumerEnum(String consumerGroupName, String bgName, String topic, String consumerClientAppKey) {
        this.consumerGroupName = consumerGroupName;
        this.bgName = bgName;
        this.topic = topic;
        this.consumerClientAppKey = consumerClientAppKey;
    }

    MafkaConsumerEnum(String topic, String consumerGroupName) {
        this(consumerGroupName, "waimai", topic, "com.sankuai.waimai.sc.pickselectservice");
    }

    MafkaConsumerEnum(MafkaTopicEnum topic, String consumerGroupName) {
        this(topic.getTopic(), consumerGroupName);
    }

    @Override
    public String getConsumerGroupName() {
        return consumerGroupName;
    }

    @Override
    public String getBgName() {
        return bgName;
    }

    @Override
    public String getTopic() {
        return topic;
    }

    @Override
    public String getAppKey() {
        return consumerClientAppKey;
    }

    @SuppressWarnings("unused")
    public String getConsumerClientAppKey() {
        return consumerClientAppKey;
    }
}
