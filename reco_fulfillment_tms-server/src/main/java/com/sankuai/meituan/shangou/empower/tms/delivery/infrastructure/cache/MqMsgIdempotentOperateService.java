package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.cache;

import com.alibaba.fastjson.JSON;
import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.consumer.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.utils.RetryTemplateUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.retry.RetryCallback;
import org.springframework.retry.RetryContext;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;


/**
 * mq消息操作幂等服务
 */
@SuppressWarnings("all")
@Slf4j
@Service
public class MqMsgIdempotentOperateService extends SquirrelOperateService {


    @Resource(name = "redisSgNewSupplyOfc")
    protected RedisStoreClient redisNewSupplyClient;

    @Override
    protected RedisStoreClient getRedisClient() {
        return redisNewSupplyClient;
    }

    public static final String CATEGORY_NAME = "mq_msg_idempotent";

    @Override
    public String getCategoryName() {
        return CATEGORY_NAME;
    }

    /**
     * 构建查询key
     */
    private StoreKey buildStoreKey(String params) {
        return new StoreKey(getCategoryName(), params);
    }

    /**
     * 尝试加锁
     */
    public boolean tryLock(String msgId, MsgIdempotentBusinessEnum businessEnum) {
        try {
            StoreKey storeKey = businessEnum.buildKey(msgId);
            String data = businessEnum.buildValue(msgId);
            Boolean result = RetryTemplateUtil.simpleWithFixedRetry(MccConfigUtils.getSquirrelOperateRetryCount(), MccConfigUtils.getSquirrelOperateRetryPeriod())
                    .execute(new RetryCallback<Boolean, Exception>() {

                        @Override
                        public Boolean doWithRetry(RetryContext retryContext) throws Exception {
                            return getRedisClient().setnx(storeKey, data);
                        }
                    });
            return result;
        } catch (Exception e) {
            log.error("MqMsgIdempotentOperateService.tryLock error. msgId:{},businessEnum:{}", msgId, businessEnum, e);
            return false;
        }
    }

    /**
     * 解锁
     */
    public void unLock(String msgId, MsgIdempotentBusinessEnum businessEnum) {
        try {
            StoreKey storeKey = businessEnum.buildKey(msgId);
            Boolean result = RetryTemplateUtil.simpleWithFixedRetry(MccConfigUtils.getSquirrelOperateRetryCount(), MccConfigUtils.getSquirrelOperateRetryPeriod())
                    .execute(new RetryCallback<Boolean, Exception>() {

                        @Override
                        public Boolean doWithRetry(RetryContext retryContext) throws Exception {
                            return getRedisClient().delete(storeKey);
                        }
                    });
        } catch (Exception e) {
            log.error("MqMsgIdempotentOperateService.unLock error. msgId:{},businessEnum:{}", msgId, businessEnum, e);
        }
    }


    /**
     * 消息幂能业务枚举
     */
    @AllArgsConstructor
    @Getter
    public static enum MsgIdempotentBusinessEnum {
        DRUNK_HORSE_RIDER_POSITION_SYNC("0001", DrunkHorseRiderPositionSyncMessageListener.class, "到家骑手点位同步"),
        RIDER_POSITION_SYNC("0002", RiderPositionSyncMessageListener.class, "骑手点位同步"),
        RIDER_POINT_BATCH_SYNC("0003", RiderPointBatchSyncMessageListener.class, "骑手点位批量同步"),
        RIDER_ALL_POINT_SYNC("0004", RiderAllPointSyncMessageListener.class, "骑手所有点位批量同步"),
        DELIVERY_COMPLETE_AND_CANCEL_POINT_SYNC("0005", DeliveryChangeSyncPointMessageListener.class, "配送完吃喝取消点位同步"),
        ;

        private final String businessCode;

        private final Class<?> clazz;

        private final String desc;

        /**
         * 构建key
         */
        public StoreKey buildKey(String msgId) {
            String params = String.format("%s:%s", businessCode, msgId);
            return new StoreKey(CATEGORY_NAME, params);
        }

        /**
         * 构建 value
         */
        public String buildValue(String msgId) {
            Map<String, String> map = new HashMap<>();
            map.put("msgId", msgId);
            map.put("businessName", clazz.getName());
            map.put("desc", desc);
            map.put("lockTime", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            return JSON.toJSONString(map);
        }
    }


}
