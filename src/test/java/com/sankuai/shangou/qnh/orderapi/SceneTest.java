package com.sankuai.shangou.qnh.orderapi;

import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OCMSOrderVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.app.OrderVO;
import com.sankuai.shangou.qnh.orderapi.service.app.OrderService;
import org.junit.Assert;
import org.junit.Test;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

public class SceneTest {

    @Test
    public void testBuild() {
        OrderService orderService = new OrderService();
        List<String> list1 = Arrays.asList("美食");
//        System.out.println(orderService.buildScene(list1));
//        List<String> list2 = Arrays.asList("道路");
//        System.out.println(orderService.buildScene(list2));
//        List<String> list3 = Arrays.asList("金融","办公");
//        System.out.println(orderService.buildScene(list3));

    }

    @Test
    public void testFixShow() {
        OrderService orderService = new OrderService();
        OrderVO orderVO = new OrderVO();
        orderVO.setCreateTime(1722950025000l);
        orderVO.setIsFranchiseeOrder(true);
        OCMSOrderVO ocmsOrderVO = new OCMSOrderVO();
        ocmsOrderVO.setMerchantAmount(1000);
        orderService.fixHistoryFeeShow(orderVO, ocmsOrderVO);
        Assert.assertEquals(orderVO.getIsFranchiseeOrder(), false);
        Assert.assertEquals(Objects.equals(orderVO.getBizReceiveAmt(), 1000), true);

    }
}
