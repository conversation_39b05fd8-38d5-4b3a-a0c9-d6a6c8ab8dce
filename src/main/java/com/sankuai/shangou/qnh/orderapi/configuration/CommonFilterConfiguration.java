package com.sankuai.shangou.qnh.orderapi.configuration;

import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.DataSecurityPrepareFilter;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.filter.CharacterEncodingFilter;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by su<PERSON><PERSON><PERSON> on 2023/11/9 18:43
 */
@Configuration
public class CommonFilterConfiguration {

    private static final int CHARACTER_ENCODING_FILTER_ORDER = 5;
    // 数据鉴权准备 filter，这里放最后面
    private static final int DATA_SECURITY_PREPARE_FILTER_ORDER = 100;


    @Bean
    public FilterRegistrationBean characterEncodingFilter() {
        CharacterEncodingFilter filter = new CharacterEncodingFilter();
        FilterRegistrationBean registration = new FilterRegistrationBean(filter);
        registration.addUrlPatterns("/api/v1/orderfuse/*");
        registration.addUrlPatterns("/api/v1/channelOrder/*");
        registration.addUrlPatterns("/api/v1/channelComment/*");
        registration.addUrlPatterns("/pieapi/order/*");
        registration.addUrlPatterns("/pieapi/pda/order/*");
        registration.addUrlPatterns("/pieapi/miniapp/order/*");
        registration.addUrlPatterns("/storemanagement/ocms/order/*");
        registration.addUrlPatterns("/storemanagement/ocms/channelComment/*");
        registration.setName("SetCharacterEncoding");
        Map<String, String> initParameters = new HashMap<>();
        initParameters.put("encoding", "UTF-8");
        initParameters.put("forceEncoding", "true");
        registration.setInitParameters(initParameters);
        registration.setOrder(CHARACTER_ENCODING_FILTER_ORDER);
        return registration;
    }

    @Bean
    public FilterRegistrationBean dataSecurityPrepareFilter() {
        DataSecurityPrepareFilter filter = new DataSecurityPrepareFilter();
        FilterRegistrationBean registration = new FilterRegistrationBean(filter);
        registration.addUrlPatterns("/api/v1/orderfuse/*");
        registration.addUrlPatterns("/api/v1/channelOrder/*");
        registration.addUrlPatterns("/api/v1/channelComment/*");
        registration.addUrlPatterns("/storemanagement/ocms/order/*");
        registration.addUrlPatterns("/storemanagement/ocms/channelComment/*");
        registration.addUrlPatterns("/pieapi/order/*");
        registration.addUrlPatterns("/pieapi/pda/order/*");
        registration.addUrlPatterns("/pieapi/miniapp/order/*");
        registration.setName("data-security-prepare-filter");
        registration.setOrder(DATA_SECURITY_PREPARE_FILTER_ORDER);
        return registration;
    }
}
