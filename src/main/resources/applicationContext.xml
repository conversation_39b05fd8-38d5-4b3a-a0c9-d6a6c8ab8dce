<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:util="http://www.springframework.org/schema/util"
       xmlns:mvc="http://www.springframework.org/schema/mvc"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns:lion="http://code.dianping.com/schema/lion"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd
        http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util.xsd
        http://www.springframework.org/schema/mvc http://www.springframework.org/schema/mvc/spring-mvc.xsd
        http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd
        http://code.dianping.com/schema/lion http://code.dianping.com/schema/lion/lion-1.0.xsd">

    <context:component-scan base-package="com.sankuai.shangou.qnh.orderapi"/>
    <context:component-scan base-package="com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity"/>

    <context:annotation-config/>
    <aop:aspectj-autoproxy/>

<!--    <lion:config/>-->

    <bean id="yamlProperties" class="org.springframework.beans.factory.config.YamlPropertiesFactoryBean">
        <property name="resources" value="classpath:/application.yml"/>
    </bean>

    <context:property-placeholder properties-ref="yamlProperties"/>

    <bean class="com.meituan.reco.pickselect.common.methodlog.MethodLogAspect"/>

    <bean id="ssoSecret" class="com.meituan.service.inf.kms.value.KMSStringValue">
        <property name="appKey" value="${app.name}" />
        <property name="name" value="sso.secret" />
        <property name="retryCount" value="10" />
    </bean>

    <bean id="erpSktInstance" class="com.meituan.service.inf.kms.value.KMSStringValue">
        <property name="appKey" value="${app.name}" />
        <property name="name" value="ERP_SKT" />
        <property name="retryCount" value="10" />
    </bean>

    <mvc:interceptors>
        <!-- 加入TraceHandlerInterceptor -->
        <mvc:interceptor>
            <mvc:mapping path="/**"/>
            <!-- 想加入mtrace的url -->
            <bean class="com.meituan.mtrace.http.TraceMethodInterceptor">
            </bean>
        </mvc:interceptor>
    </mvc:interceptors>

    <bean id="mts3SecretKey" class="com.meituan.service.inf.kms.value.KMSStringValue">
        <property name="appKey" value="${app.name}" />
        <property name="name" value="mts3.secretKey" />
        <property name="retryCount" value="10" />
    </bean>

    <bean id="mts3AccessKey" class="com.meituan.service.inf.kms.value.KMSStringValue">
        <property name="appKey" value="${app.name}" />
        <property name="name" value="mts3.accessKey" />
        <property name="retryCount" value="10" />
    </bean>

    <bean id="s3StorageService" class="com.sankuai.meituan.shangou.saas.common.storage.mss.MtCloudS3StorageService"
          init-method="init">
        <property name="accessKey" ref="mts3AccessKey"/>
        <property name="secretKey" ref="mts3SecretKey"/>
        <property name="bucketName" value="${mts3.bucketName}"/>
        <property name="url" value="${mts3.url}"/>
        <property name="expireSeconds" value="${mts3.expireSeconds}"/>
    </bean>

    <bean id="thriftInvokerCatEventFilter" class="com.sankuai.meituan.reco.store.management.thrift.filter.ThriftInvokerCatEventFilter"/>

    <import resource="classpath:waimai_service_order_clientassembly_center.xml"/>

    <bean id="wmOrderServiceClientAssembly" class="com.sankuai.meituan.waimai.service.order.WmOrderServiceClientAssembly">
        <property name="requiredList">
            <set>
                <util:constant static-field= "com.sankuai.meituan.waimai.service.order.common.constants.WmOrderServiceCons.WM_ORDER_QUERY_BY_USERID_THRIFT_SERVICE" />
            </set>
        </property>
    </bean>

    <import resource="classpath*:common-context.xml"/>
    <import resource="applicationContext-thrift-client.xml"/>
    <import resource="applicationContext-cache.xml"/>

    <import resource="classpath*:saas-shangou-tenant-thrift-client.xml"/>
    <import resource="classpath*:saas-shangou-tenant-high-level-client.xml"/>

</beans>