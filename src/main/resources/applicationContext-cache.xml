<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xmlns:context="http://www.springframework.org/schema/context"
  xsi:schemaLocation="http://www.springframework.org/schema/beans
      http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
	    http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.0.xsd">

  <bean id="redisStoreClient" class="com.dianping.squirrel.client.impl.redis.spring.RedisClientBeanFactory">
    <!-- 集群名称,必填 -->
    <property name="clusterName" value="${storeCache.clusterName}"/>
    <!--读取超时时间,缓存业务建议改成100，存储业务建议改成1000，默认值为1000。选填-->
    <property name="readTimeout" value="${storeCache.readTimeout}"/>
    <!--路由策略,默认值是master-only表示只从主节点读取。slave-only表示只读从节点,master-slave表示主从都可以读。选填-->
    <property name="routerType" value="${storeCache.routerType}"/>
    <!--连接redis节点的连接池配置，选填-->
    <property name="poolMaxIdle" value="${storeCache.poolMaxIdle}"/>
    <property name="poolMaxTotal" value="${storeCache.poolMaxTotal}"/>
    <property name="poolWaitMillis" value="${storeCache.poolWaitMilles}"/>
    <!--异步操作线程池配置，选填-->
    <property name="asyncCoreSize" value="32"/>
    <property name="asyncMaxSize" value="64"/>
    <property name="asyncQueueSize" value="1000"/>
  </bean>

  <bean id="drunkHorseRedisClient" class="com.dianping.squirrel.client.impl.redis.spring.RedisClientBeanFactory" autowire="byName">
    <!-- 集群名称,必填 -->
    <property name="clusterName" value="${squirrel.drunkHorseClusterName}"></property>
    <!-- 序列化方式 -->
    <property name="serializeType" value="hessian"></property>
    <!--读写的超时时间,缓存业务建议改成100，存储业务建议改成1000，默认值为1000。选填-->
    <property name="readTimeout" value="${squirrel.readTimeout}"></property>
    <!--路由策略,默认值是master-only表示只从主节点读取。slave-only表示只读从节点,master-slave表示主从都可以读。选填-->
    <property name="routerType" value="${squirrel.routerType}"></property>
    <!--连接redis节点的连接池配置，选填，只在使用了异步化接口，multi操作或者pipeline之类的操作时使用-->
    <property name="poolMaxIdle" value="${squirrel.poolMaxIdle}"></property>
    <property name="poolMaxTotal" value="${squirrel.poolMaxTotal}"></property>
    <property name="poolWaitMillis" value="${squirrel.poolWaitMillis}"></property>
    <property name="poolMinIdle" value="${squirrel.poolMinIdle}"></property>
  </bean>

</beans>
