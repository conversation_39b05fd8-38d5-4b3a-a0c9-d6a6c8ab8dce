package com.sankuai.meituan.shangou.empower.rider.client.common;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/10/22
 */
@TypeDoc(
		description = "通用请求执行状态",
		authors = {
				"hedong07"
		}
)
@Data
@AllArgsConstructor
@NoArgsConstructor
@ThriftStruct
public class Status {

	//成功
	public static final Status SUCCESS = new Status(ResponseCodeEnum.SUCCESS.getValue(), StringUtils.EMPTY);

	//参数校验不通过
	public static final Status INVALID_PARAM = new Status(200, "参数校验不通过");

	@FieldDoc(
			description = "状态码",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(1)
	public int code;

	@FieldDoc(
			description = "失败详情",
			requiredness = Requiredness.REQUIRED
	)
	@ThriftField(2)
	public String msg;
}
