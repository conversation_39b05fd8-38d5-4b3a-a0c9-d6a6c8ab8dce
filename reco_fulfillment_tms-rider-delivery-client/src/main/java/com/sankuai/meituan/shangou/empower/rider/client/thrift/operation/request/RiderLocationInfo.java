package com.sankuai.meituan.shangou.empower.rider.client.thrift.operation.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@TypeDoc(description = "客户端骑手坐标批量上传点位信息")
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
public class RiderLocationInfo {

    @FieldDoc(description = "经度", requiredness = Requiredness.OPTIONAL)
    @ThriftField(1)
    public String longitude;

    @FieldDoc(description = "纬度", requiredness = Requiredness.OPTIONAL)
    @ThriftField(2)
    public String latitude;

    @FieldDoc(description = "定位结果来源", requiredness = Requiredness.OPTIONAL)
    @ThriftField(3)
    public String provider;

    @FieldDoc(description = "定位结果精确度", requiredness = Requiredness.OPTIONAL)
    @ThriftField(4)
    public String accuracy;

    @FieldDoc(description = "方向信息", requiredness = Requiredness.OPTIONAL)
    @ThriftField(5)
    public String bearing;

    @FieldDoc(description = "速度信息", requiredness = Requiredness.OPTIONAL)
    @ThriftField(6)
    public String speed;

    @FieldDoc(description = "时间信息", requiredness = Requiredness.REQUIRED)
    @ThriftField(7)
    public String time;

}