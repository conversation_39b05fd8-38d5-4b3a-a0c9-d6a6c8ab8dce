package com.sankuai.meituan.shangou.empower.rider.client.thrift.operation.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * <AUTHOR>
 * @since 2025/7/29
 */
@Slf4j
@TypeDoc(description = "客户端骑手坐标批量上传功能")
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
public class RiderLocationBatchReportThriftRequest {

    @FieldDoc(description = "租户id", requiredness = Requiredness.REQUIRED)
    @ThriftField(1)
    public Long tenantId;

    @FieldDoc(description = "门店id", requiredness = Requiredness.REQUIRED)
    @ThriftField(2)
    public Long storeId;

    @FieldDoc(description = "骑手id", requiredness = Requiredness.REQUIRED)
    @ThriftField(3)
    public Long riderAccountId;

    @FieldDoc(description = "骑手名字", requiredness = Requiredness.REQUIRED)
    @ThriftField(4)
    public String riderName;

    @FieldDoc(description = "骑手电话", requiredness = Requiredness.REQUIRED)
    @ThriftField(5)
    public String riderPhone;

    @FieldDoc(description = "拓展信息 包含订单信息")
    @ThriftField(6)
    public Map<String, String> extraParams;

    @FieldDoc(description = "骑手定位信息", requiredness = Requiredness.REQUIRED)
    @ThriftField(7)
    public List<RiderLocationInfo> locationInfoList;

    @FieldDoc(description = "操作系统", requiredness = Requiredness.OPTIONAL)
    @ThriftField(8)
    public String os;

    @FieldDoc(description = "牵牛花app版本", requiredness = Requiredness.OPTIONAL)
    @ThriftField(9)
    public String appVersion;

    @FieldDoc(description = "骑手设备id", requiredness = Requiredness.OPTIONAL)
    @ThriftField(10)
    public String uuid;

    /**
     * 参数校验
     */
    public boolean paramsValid() {
        try {
            AtomicBoolean flag = new AtomicBoolean(true);
            if (tenantId == null || storeId == null || riderAccountId == null || riderName == null || riderPhone == null) {
                flag.set(false);
                log.error("参数校验失败, tenantId: {}, storeId: {}, riderAccountId: {}, riderName: {}, riderPhone: {}", tenantId, storeId, riderAccountId, riderName, riderPhone);
            }
            if (CollectionUtils.isEmpty(locationInfoList)) {
                flag.set(false);
                log.error("参数校验失败, locationInfoList: {}", locationInfoList);
            }
            locationInfoList.forEach(locationInfo -> {
                if (locationInfo.getLongitude() == null || locationInfo.getLatitude() == null || locationInfo.getTime() == null) {
                    flag.set(false);
                    log.error("参数校验失败, locationInfo: {}", locationInfo);
                }
            });
            return flag.get();
        } catch (Exception e) {
            log.error("参数校验出现异常", e);
            return false;
        }
    }

}
