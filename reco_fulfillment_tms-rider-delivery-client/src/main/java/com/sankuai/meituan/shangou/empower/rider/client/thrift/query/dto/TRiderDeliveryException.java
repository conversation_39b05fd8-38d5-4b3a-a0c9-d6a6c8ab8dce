package com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import lombok.Data;

import java.util.List;

@ThriftStruct
@Data
public class TRiderDeliveryException {
    @FieldDoc(
            description = "租户id"
    )
    @ThriftField(1)
    public Long tenantId;

    @FieldDoc(
            description = "门店id"
    )
    @ThriftField(2)
    public Long storeId;

    @FieldDoc(
            description = "运单id"
    )
    @ThriftField(3)
    public Long deliveryOrderId;

    @FieldDoc(
            description = "渠道订单id"
    )
    @ThriftField(4)

    public Long channelOrderId;

    @FieldDoc(
            description = "订单业务类型"
    )
    @ThriftField(5)
    public Integer orderBizType;

    @FieldDoc(
            description = "日流水号"
    )
    @ThriftField(6)
    public Integer daySeq;

    @FieldDoc(
            description = "创建时间"
    )
    @ThriftField(7)
    public Long createTime;


    @FieldDoc(
            description = "更新时间"
    )
    @ThriftField(8)
    public Long updateTime;

    @FieldDoc(
            description = "骑手姓名"
    )
    @ThriftField(9)
    public String riderAccountName;

    @FieldDoc(
            description = "骑手账号id"
    )
    @ThriftField(10)
    public Long riderAccountId;

    @FieldDoc(
            description = "异常类型"
    )
    @ThriftField(11)
    public Integer exceptionType;

    @FieldDoc(
            description = "二级异常类型"
    )
    @ThriftField(12)
    public Integer exceptionSubType;

    @FieldDoc(
            description = "异常描述"
    )
    @ThriftField(13)
    public String exceptionTypeDesc;

    @FieldDoc(
            description = "二级异常描述"
    )
    @ThriftField(14)
    public String exceptionSubTypeDesc;

    @FieldDoc(
            description = "支付时间"
    )
    @ThriftField(15)
    public Long payTime;

    @FieldDoc(
            description = "用户真实地址"
    )
    @ThriftField(16)
    public String userRealAddress;

    @FieldDoc(
            description = "备注"
    )
    @ThriftField(17)
    public String comment;

    @FieldDoc(
            description = "照片Urls"
    )
    @ThriftField(18)
    public List<String> picUrls;

    @FieldDoc(
            description = "用户真实地址"
    )
    @ThriftField(19)
    private String modifiedAddress;

    @FieldDoc(
            description = "渠道id列表适配，替换Long型渠道id",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(20)
    private String channelOrderIdSeq;

}
