package com.sankuai.shangou.logistics.delivery.poi;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.logistics.delivery.poi.dto.SelfDeliveryPoiConfigDTO;
import com.sankuai.shangou.logistics.delivery.poi.dto.TVoid;

/**
 * <AUTHOR>
 * @date 2023-08-28
 * @email <EMAIL>
 */
@InterfaceDoc(
        displayName = "运单统计数据查询接口",
        type = "octo.thrift.annotation",
        scenarios = "运单统计数据查询接口",
        description = "运单统计数据查询接口",
        authors = {
                "jianglilin02"
        }
)
@ThriftService
public interface SelfDeliveryPoiConfigThriftService {


    @MethodDoc(
            displayName = "查询自配门店配置",
            description = "查询自配门店配置",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询自配门店配置",
                            type = Long.class,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            returnValueDescription = "查询骑手维度运单统计信息返回",
            restExampleResponseData = "{}",
            example = "暂无"
    )
    @ThriftMethod
    TResult<SelfDeliveryPoiConfigDTO> querySelfDeliveryConfig(@ThriftField(1) Long tenantId, @ThriftField(2) Long poiId);

    @MethodDoc(
            displayName = "初始化自配门店配置",
            description = "初始化自配门店配置",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "初始化自配门店配置",
                            type = Long.class,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            returnValueDescription = "初始化自配门店配置返回",
            restExampleResponseData = "{}",
            example = "暂无"
    )
    @ThriftMethod
    TResult<TVoid> initSelfDeliveryConfig(@ThriftField(1) Long tenantId, @ThriftField(2) Long poiId);

}
