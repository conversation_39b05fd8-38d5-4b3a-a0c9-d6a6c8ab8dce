package com.sankuai.shangou.logistics.delivery.config;

import com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy;
import com.meituan.shangou.goodscenter.thrift.DepotGoodsThriftService;
import com.meituan.shangou.goodscenter.thrift.GoodsSkuRelationThriftService;
import com.meituan.shangou.goodscenter.thrift.TenantGoodsThriftService;
import com.meituan.shangou.saas.order.management.client.service.online.OCMSQueryThriftService;
import com.meituan.shangou.saas.order.management.client.service.online.OcmsOrderSearchService;
import com.meituan.shangou.saas.tenant.thrift.*;
import com.meituan.shangou.sac.thrift.authenticate.AuthenticateService;
import com.sankuai.drunkhorsemgmt.labor.thrift.ScheduleThriftService;
import com.sankuai.dxenterprise.open.gateway.service.dx.api.DxService;
import com.sankuai.meituan.shangou.empower.auth.thrift.service.AccountThriftService;
import com.sankuai.meituan.shangou.empower.auth.thrift.service.AuthThriftService;
import com.sankuai.meituan.shangou.empower.auth.thrift.service.LoginThriftService;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.RiderQueryThriftService;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.open.OpenAggDeliveryThriftService;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.DeliveryOperationThriftService;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.QueryDeliveryInfoThriftService;
import com.sankuai.meituan.shangou.xsupply.product.client.service.ChannelStoreSpuThriftService;
import com.sankuai.meituan.shangou.xsupply.product.client.service.TagThriftService;
import com.sankuai.meituan.shangou.xsupply.product.client.service.api.TagThriftApi;
import com.sankuai.shangou.bizmng.labor.api.training.TOnboardTrainingService;
import com.sankuai.shangou.commons.thrift.augment.RichTypeThriftClientProxy;
import com.sankuai.shangou.infra.osw.api.poi.TPoiService;
import com.sankuai.shangou.infra.osw.api.poi.warehouse.TWarehouseService;
import com.sankuai.shangou.logistics.sdms.sdk.limit.LimitAcceptOrderThriftService;
import com.sankuai.shangou.logistics.warehouse.PromotionMaterialOutboundService;
import com.sankuai.shangou.qnh.ofc.ebase.service.FulfillmentStoreConfigThriftService;
import com.sankuai.shangou.waima.support.api.service.punish.TPunishService;
import com.sankuai.xm.openplatform.api.service.open.OpenCardServiceI;
import com.sankuai.xm.openplatform.api.service.open.XmOpenMessageServiceI;
import com.sankuai.xm.openplatform.auth.service.XmAuthServiceI;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2023-06-30
 * @email <EMAIL>
 */
@Configuration
public class ExternalServiceBeanConfig {

    @Bean("poiThriftService")
    public PoiThriftService poiThriftServiceBean() throws Exception {
        ThriftClientProxy proxy = new ThriftClientProxy();
        proxy.setRemoteAppkey("com.sankuai.shangou.empower.tenant");
        proxy.setServiceInterface(Class.forName("com.meituan.shangou.saas.tenant.thrift.PoiThriftService"));
        proxy.setNettyIO(true);
        proxy.setTimeout(3000);
        proxy.setRemoteServerPort(8849);
        proxy.afterPropertiesSet();  //初始化实例

        return (PoiThriftService) proxy.getObject();
    }

    @Bean("riderQueryThriftService")
    public RiderQueryThriftService riderQueryThriftServiceBean() throws Exception {
        ThriftClientProxy proxy = new ThriftClientProxy();
        proxy.setRemoteAppkey("com.sankuai.sgfulfillment.tms");
        proxy.setServiceInterface(Class.forName("com.sankuai.meituan.shangou.empower.rider.client.thrift.query.RiderQueryThriftService"));
        proxy.setNettyIO(true);
        proxy.setTimeout(3000);
        proxy.setFilterByServiceName(true);
        proxy.afterPropertiesSet();  //初始化实例

        return (RiderQueryThriftService) proxy.getObject();
    }

    @Bean("scheduleThriftService")
    public ScheduleThriftService scheduleThriftServiceBean() throws Exception {
        ThriftClientProxy proxy = new ThriftClientProxy();
        proxy.setRemoteAppkey("com.sankuai.drunkhorsemgmt.labor.mng");
        proxy.setServiceInterface(Class.forName("com.sankuai.drunkhorsemgmt.labor.thrift.ScheduleThriftService"));
        proxy.setNettyIO(true);
        proxy.setTimeout(3000);
        proxy.setFilterByServiceName(true);
        proxy.afterPropertiesSet();  //初始化实例

        return (ScheduleThriftService) proxy.getObject();
    }

    @Bean("userThriftService")
    public UserThriftService userThriftServiceBean() throws Exception {
        ThriftClientProxy proxy = new ThriftClientProxy();
        proxy.setRemoteAppkey("com.sankuai.shangou.empower.tenant");
        proxy.setServiceInterface(Class.forName("com.meituan.shangou.saas.tenant.thrift.UserThriftService"));
        proxy.setNettyIO(true);
        proxy.setTimeout(3000);
        proxy.setFilterByServiceName(true);
        proxy.afterPropertiesSet();  //初始化实例

        return (UserThriftService) proxy.getObject();
    }

    @Bean("loginThriftService")
    public LoginThriftService.Iface loginThriftServiceBean() throws Exception {
        ThriftClientProxy proxy = new ThriftClientProxy();
        proxy.setRemoteAppkey("com.sankuai.shangou.empower.saasauth");
        proxy.setServiceInterface(Class.forName("com.sankuai.meituan.shangou.empower.auth.thrift.service.LoginThriftService"));
        proxy.setNettyIO(true);
        proxy.setTimeout(3000);
        proxy.setFilterByServiceName(true);
        proxy.afterPropertiesSet();  //初始化实例

        return (LoginThriftService.Iface) proxy.getObject();
    }

    @Bean("authAccountThriftService")
    public AccountThriftService.Iface authAccountThriftServiceBean() throws Exception {
        ThriftClientProxy proxy = new ThriftClientProxy();
        proxy.setRemoteAppkey("com.sankuai.shangou.empower.saasauth");
        proxy.setServiceInterface(Class.forName("com.sankuai.meituan.shangou.empower.auth.thrift.service.AccountThriftService"));
        proxy.setNettyIO(true);
        proxy.setTimeout(3000);
        proxy.setFilterByServiceName(true);
        proxy.afterPropertiesSet();  //初始化实例

        return (AccountThriftService.Iface) proxy.getObject();
    }

    @Bean("channelPoiManageThriftServiceClient")
    public ChannelPoiManageThriftService channelPoiManageThriftService() throws Exception {
        ThriftClientProxy proxy = new ThriftClientProxy();
        proxy.setRemoteAppkey("com.sankuai.shangou.empower.tenant");
        proxy.setServiceInterface(Class.forName("com.meituan.shangou.saas.tenant.thrift.ChannelPoiManageThriftService"));
        proxy.setNettyIO(true);
        proxy.setTimeout(3000);
        proxy.setFilterByServiceName(true);
        proxy.afterPropertiesSet();  //初始化实例

        return (ChannelPoiManageThriftService) proxy.getObject();
    }

    @Bean("ocmsOrderSearchService")
    public OcmsOrderSearchService ocmsOrderSearchServiceBean() throws Exception {
        ThriftClientProxy proxy = new ThriftClientProxy();
        proxy.setRemoteAppkey("com.sankuai.shangou.empower.ordermng");
        proxy.setServiceInterface(Class.forName("com.meituan.shangou.saas.order.management.client.service.online.OcmsOrderSearchService"));
        proxy.setNettyIO(true);
        proxy.setTimeout(5000);
        proxy.setFilterByServiceName(true);
        proxy.afterPropertiesSet();  //初始化实例

        return (OcmsOrderSearchService) proxy.getObject();
    }

    @Bean("channelManageThriftService")
    public ChannelManageThriftService channelManageThriftService() throws Exception {
        ThriftClientProxy proxy = new ThriftClientProxy();
        proxy.setRemoteAppkey("com.sankuai.shangou.empower.tenant");
        proxy.setServiceInterface(Class.forName("com.meituan.shangou.saas.tenant.thrift.ChannelManageThriftService"));
        proxy.setNettyIO(true);
        proxy.setTimeout(5000);
        proxy.setFilterByServiceName(true);
        proxy.afterPropertiesSet();  //初始化实例

        return (ChannelManageThriftService) proxy.getObject();
    }

    @Bean("openAggDeliveryThriftService")
    public OpenAggDeliveryThriftService openAggDeliveryThriftServiceBean() throws Exception {
        ThriftClientProxy proxy = new ThriftClientProxy();
        proxy.setRemoteAppkey("com.sankuai.sgfulfillment.tms");
        proxy.setServiceInterface(Class.forName("com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.open.OpenAggDeliveryThriftService"));
        proxy.setNettyIO(true);
        proxy.setTimeout(5000);
        proxy.setFilterByServiceName(true);
        proxy.afterPropertiesSet();  //初始化实例

        return (OpenAggDeliveryThriftService) proxy.getObject();
    }

    @Bean("deliveryOperationThriftService")
    public DeliveryOperationThriftService deliveryOperationThriftServiceBean() throws Exception {
        ThriftClientProxy proxy = new ThriftClientProxy();
        proxy.setRemoteAppkey("com.sankuai.sgfulfillment.tms");
        proxy.setServiceInterface(Class.forName("com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.DeliveryOperationThriftService"));
        proxy.setNettyIO(true);
        proxy.setTimeout(5000);
        proxy.setFilterByServiceName(true);
        proxy.afterPropertiesSet();  //初始化实例

        return (DeliveryOperationThriftService) proxy.getObject();
    }

    @Bean
    public QueryDeliveryInfoThriftService queryDeliveryInfoThriftService() throws Exception {
        ThriftClientProxy proxy = new ThriftClientProxy();
        proxy.setRemoteAppkey("com.sankuai.sgfulfillment.tms");
        proxy.setServiceInterface(QueryDeliveryInfoThriftService.class);
        proxy.setNettyIO(true);
        proxy.setTimeout(5000);
        proxy.setFilterByServiceName(true);
        proxy.afterPropertiesSet();  //初始化实例
        return (QueryDeliveryInfoThriftService) proxy.getObject();
    }

    @Bean("poiManageThriftService")
    public PoiManageThriftService poiManageThriftServiceBean() throws Exception {
        ThriftClientProxy proxy = new ThriftClientProxy();
        proxy.setRemoteAppkey("com.sankuai.shangou.empower.tenant");
        proxy.setServiceInterface(Class.forName("com.meituan.shangou.saas.tenant.thrift.PoiManageThriftService"));
        proxy.setNettyIO(true);
        proxy.setTimeout(5000);
        proxy.setFilterByServiceName(true);
        proxy.afterPropertiesSet();  //初始化实例

        return (PoiManageThriftService) proxy.getObject();
    }

    @Bean("authThriftService")
    public AuthThriftService.Iface authThriftServiceBean() throws Exception {
        ThriftClientProxy proxy = new ThriftClientProxy();
        proxy.setRemoteAppkey("com.sankuai.shangou.empower.saasauth");
        proxy.setServiceInterface(Class.forName("com.sankuai.meituan.shangou.empower.auth.thrift.service.AuthThriftService"));
        proxy.setNettyIO(true);
        proxy.setTimeout(5000);
        proxy.setFilterByServiceName(true);
        proxy.afterPropertiesSet();  //初始化实例

        return (AuthThriftService.Iface) proxy.getObject();
    }

    @Bean("departmentV2ThriftService")
    public DepartmentV2ThriftService departmentV2ThriftServiceBean() throws Exception {
        ThriftClientProxy proxy = new ThriftClientProxy();
        proxy.setRemoteAppkey("com.sankuai.shangou.empower.tenant");
        proxy.setServiceInterface(Class.forName("com.meituan.shangou.saas.tenant.thrift.DepartmentV2ThriftService"));
        proxy.setNettyIO(true);
        proxy.setTimeout(5000);
        proxy.setFilterByServiceName(true);
        proxy.afterPropertiesSet();  //初始化实例

        return (DepartmentV2ThriftService) proxy.getObject();
    }


    @Bean("ocmsQueryThriftService")
    public OCMSQueryThriftService ocmsQueryThriftServiceBean() throws Exception {
        ThriftClientProxy proxy = new ThriftClientProxy();
        proxy.setRemoteAppkey("com.sankuai.shangou.empower.ordermng");
        proxy.setServiceInterface(Class.forName("com.meituan.shangou.saas.order.management.client.service.online.OCMSQueryThriftService"));
        proxy.setNettyIO(true);
        proxy.setTimeout(5000);
        proxy.setFilterByServiceName(true);
        proxy.afterPropertiesSet();  //初始化实例

        return (OCMSQueryThriftService) proxy.getObject();
    }

    @Bean("channelStoreSpuThriftService")
    public ChannelStoreSpuThriftService channelStoreSpuThriftService() throws Exception {
        ThriftClientProxy proxy = new ThriftClientProxy();
        proxy.setRemoteAppkey("com.sankuai.sgxsupply.product.management");
        proxy.setServiceInterface(Class.forName("com.sankuai.meituan.shangou.xsupply.product.client.service.ChannelStoreSpuThriftService"));
        proxy.setNettyIO(true);
        proxy.setTimeout(5000);
        proxy.setFilterByServiceName(true);
        proxy.afterPropertiesSet();  //初始化实例

        return (ChannelStoreSpuThriftService) proxy.getObject();
    }

    @Bean("tenantGoodsThriftService")
    public TenantGoodsThriftService tenantGoodsThriftService() throws Exception {
        ThriftClientProxy proxy = new ThriftClientProxy();
        proxy.setRemoteAppkey("com.sankuai.sgshopmgmt.goodscenter");
        proxy.setServiceInterface(Class.forName("com.meituan.shangou.goodscenter.thrift.TenantGoodsThriftService"));
        proxy.setNettyIO(true);
        proxy.setTimeout(5000);
        proxy.setFilterByServiceName(true);
        proxy.afterPropertiesSet();  //初始化实例

        return (TenantGoodsThriftService) proxy.getObject();
    }

    @Bean("tagThriftApi")
    public TagThriftApi tagThriftApi() throws Exception {
        ThriftClientProxy proxy = new ThriftClientProxy();
        proxy.setRemoteAppkey("com.sankuai.sgxsupply.product.management");
        proxy.setServiceInterface(Class.forName("com.sankuai.meituan.shangou.xsupply.product.client.service.api.TagThriftApi"));
        proxy.setNettyIO(true);
        proxy.setTimeout(5000);
        proxy.setFilterByServiceName(true);
        proxy.afterPropertiesSet();  //初始化实例

        return (TagThriftApi) proxy.getObject();
    }

    @Bean("tagThriftService")
    public TagThriftService tagThriftService() throws Exception {
        ThriftClientProxy proxy = new ThriftClientProxy();
        proxy.setRemoteAppkey("com.sankuai.sgxsupply.product.management");
        proxy.setServiceInterface(Class.forName("com.sankuai.meituan.shangou.xsupply.product.client.service.TagThriftService"));
        proxy.setNettyIO(true);
        proxy.setTimeout(5000);
        proxy.setFilterByServiceName(true);
        proxy.afterPropertiesSet();  //初始化实例

        return (TagThriftService) proxy.getObject();
    }

    @Bean("goodsSkuRelationThriftService")
    public GoodsSkuRelationThriftService goodsSkuRelationThriftService() throws Exception {
        ThriftClientProxy proxy = new ThriftClientProxy();
        proxy.setRemoteAppkey("com.sankuai.sgshopmgmt.goodscenter");
        proxy.setServiceInterface(Class.forName("com.meituan.shangou.goodscenter.thrift.GoodsSkuRelationThriftService"));
        proxy.setNettyIO(true);
        proxy.setTimeout(5000);
        proxy.setFilterByServiceName(true);
        proxy.afterPropertiesSet();  //初始化实例

        return (GoodsSkuRelationThriftService) proxy.getObject();
    }

    @Bean("depotGoodsThriftService")
    public DepotGoodsThriftService depotGoodsThriftService() throws Exception {
        ThriftClientProxy proxy = new ThriftClientProxy();
        proxy.setRemoteAppkey("com.sankuai.sgshopmgmt.goodscenter");
        proxy.setServiceInterface(Class.forName("com.meituan.shangou.goodscenter.thrift.DepotGoodsThriftService"));
        proxy.setNettyIO(true);
        proxy.setTimeout(5000);
        proxy.setFilterByServiceName(true);
        proxy.afterPropertiesSet();  //初始化实例

        return (DepotGoodsThriftService) proxy.getObject();
    }

    @Bean("tPoiService")
    public TPoiService tPoiService() throws Exception {
        ThriftClientProxy proxy = new ThriftClientProxy();
        proxy.setRemoteAppkey("com.sankuai.shangou.infra.osw");
        proxy.setServiceInterface(Class.forName("com.sankuai.shangou.infra.osw.api.poi.TPoiService"));
        proxy.setNettyIO(true);
        proxy.setTimeout(5000);
        proxy.setFilterByServiceName(true);
        proxy.afterPropertiesSet();  //初始化实例

        return (TPoiService) proxy.getObject();
    }


    @Bean("tWarehouseService")
    public TWarehouseService tWarehouseService() throws Exception {
        ThriftClientProxy proxy = new ThriftClientProxy();
        proxy.setRemoteAppkey("com.sankuai.shangou.infra.osw");
        proxy.setServiceInterface(Class.forName("com.sankuai.shangou.infra.osw.api.poi.warehouse.TWarehouseService"));
        proxy.setNettyIO(true);
        proxy.setTimeout(5000);
        proxy.setFilterByServiceName(true);
        proxy.afterPropertiesSet();  //初始化实例

        return (TWarehouseService) proxy.getObject();
    }

    @Bean("authenticateService")
    public AuthenticateService authenticateService() throws Exception {
        ThriftClientProxy proxy = new ThriftClientProxy();
        proxy.setRemoteAppkey("com.sankuai.shangou.empower.saasauth");
        proxy.setServiceInterface(Class.forName("com.meituan.shangou.sac.thrift.authenticate.AuthenticateService"));
        proxy.setNettyIO(true);
        proxy.setTimeout(5000);
        proxy.setFilterByServiceName(true);
        proxy.afterPropertiesSet();  //初始化实例

        return (AuthenticateService) proxy.getObject();
    }

    @Bean("tPunishService")
    public TPunishService tPunishService() throws Exception {
        RichTypeThriftClientProxy proxy = new RichTypeThriftClientProxy();
        proxy.setRemoteAppkey("com.sankuai.shangou.waima.support");
        proxy.setServiceInterface(Class.forName("com.sankuai.shangou.waima.support.api.service.punish.TPunishService"));
        proxy.setNettyIO(true);
        proxy.setTimeout(5000);
        proxy.setFilterByServiceName(true);
        proxy.afterPropertiesSet();  //初始化实例

        return (TPunishService) proxy.getObject();
    }

    @Bean("tOnboardTrainingService")
    public TOnboardTrainingService tOnboardTrainingService() throws Exception {
        RichTypeThriftClientProxy proxy = new RichTypeThriftClientProxy();
        proxy.setRemoteAppkey("com.sankuai.shangou.bizmng.labor");
        proxy.setServiceInterface(Class.forName("com.sankuai.shangou.bizmng.labor.api.training.TOnboardTrainingService"));
        proxy.setNettyIO(true);
        proxy.setTimeout(5000);
        proxy.setFilterByServiceName(true);
        proxy.afterPropertiesSet();  //初始化实例

        return (TOnboardTrainingService) proxy.getObject();
    }

    @Bean("limitAcceptOrderThriftService")
    public LimitAcceptOrderThriftService limitAcceptOrderThriftService() throws Exception {
        RichTypeThriftClientProxy proxy = new RichTypeThriftClientProxy();
        proxy.setRemoteAppkey("com.sankuai.shangou.logistics.sdms");
        proxy.setServiceInterface(Class.forName("com.sankuai.shangou.logistics.sdms.sdk.limit.LimitAcceptOrderThriftService"));
        proxy.setNettyIO(true);
        proxy.setTimeout(5000);
        proxy.setFilterByServiceName(true);
        proxy.afterPropertiesSet();  //初始化实例

        return (LimitAcceptOrderThriftService) proxy.getObject();
    }

    @Bean("promotionMaterialOutboundService")
    public PromotionMaterialOutboundService promotionMaterialOutboundService() throws Exception {
        RichTypeThriftClientProxy proxy = new RichTypeThriftClientProxy();
        proxy.setRemoteAppkey("com.sankuai.shangou.logistics.oio");
        proxy.setServiceInterface(Class.forName("com.sankuai.shangou.logistics.warehouse.PromotionMaterialOutboundService"));
        proxy.setNettyIO(true);
        proxy.setTimeout(5000);
        proxy.setFilterByServiceName(true);
        proxy.afterPropertiesSet();  //初始化实例

        return (PromotionMaterialOutboundService) proxy.getObject();
    }

    //大象开放平台鉴权服务
    @Bean
    public ThriftClientProxy xmAuthService() throws Exception {
        ThriftClientProxy proxy = new ThriftClientProxy();
        proxy.setRemoteAppkey("com.sankuai.dxenterprise.open.gateway");
        proxy.setServiceInterface(XmAuthServiceI.class);
        proxy.setNettyIO(true);
        proxy.setTimeout(3000);
        proxy.setFilterByServiceName(true);
        proxy.afterPropertiesSet();  //初始化实例
        return proxy;
    }

    //大象开放平台消息服务
    @Bean
    public ThriftClientProxy xmOpenMsgService() throws Exception {
        ThriftClientProxy proxy = new ThriftClientProxy();
        proxy.setRemoteAppkey("com.sankuai.dxenterprise.open.gateway");
        proxy.setServiceInterface(XmOpenMessageServiceI.class);
        proxy.setNettyIO(true);
        proxy.setTimeout(3000);
        proxy.setFilterByServiceName(true);
        proxy.afterPropertiesSet();  //初始化实例
        return proxy;
    }

    // 大象卡片消息服务
    @Bean
    public ThriftClientProxy openCardService() throws Exception {
        ThriftClientProxy proxy = new ThriftClientProxy();
        proxy.setRemoteAppkey("com.sankuai.dxenterprise.open.gateway");
        proxy.setServiceInterface(OpenCardServiceI.class);
        proxy.setNettyIO(true);
        proxy.setTimeout(3000);
        proxy.setFilterByServiceName(true);
        proxy.afterPropertiesSet();  //初始化实例
        return proxy;
    }

    @Bean
    public DxService dxService() {
        return new DxService();
    }


    @Bean("tenantThriftService")
    public TenantThriftService tenantThriftService() throws Exception {
        RichTypeThriftClientProxy proxy = new RichTypeThriftClientProxy();
        proxy.setRemoteAppkey("com.sankuai.shangou.empower.tenant");
        proxy.setServiceInterface(Class.forName("com.meituan.shangou.saas.tenant.thrift.TenantThriftService"));
        proxy.setNettyIO(true);
        proxy.setTimeout(5000);
        proxy.setFilterByServiceName(true);
        proxy.afterPropertiesSet();  //初始化实例

        return (TenantThriftService) proxy.getObject();
    }

    @Bean("qnhTenantThriftService")
    public com.sankuai.sgfnqnh.poi.api.client.thrift.TenantThriftService qnhTenantThriftService() throws Exception {
        RichTypeThriftClientProxy proxy = new RichTypeThriftClientProxy();
        proxy.setRemoteAppkey("com.sankuai.sgfnqnh.poi.api");
        proxy.setServiceInterface(Class.forName("com.sankuai.sgfnqnh.poi.api.client.thrift.TenantThriftService"));
        proxy.setNettyIO(true);
        proxy.setTimeout(5000);
        proxy.setFilterByServiceName(true);
        proxy.afterPropertiesSet();  //初始化实例

        return (com.sankuai.sgfnqnh.poi.api.client.thrift.TenantThriftService) proxy.getObject();
    }

    @Bean("fulfillmentStoreConfigThriftService")
    public FulfillmentStoreConfigThriftService fulfillmentStoreConfigThriftService() throws Exception {
        RichTypeThriftClientProxy proxy = new RichTypeThriftClientProxy();
        proxy.setRemoteAppkey("com.sankuai.qnh.ofc.ebase");
        proxy.setServiceInterface(Class.forName("com.sankuai.shangou.qnh.ofc.ebase.service.FulfillmentStoreConfigThriftService"));
        proxy.setNettyIO(true);
        proxy.setTimeout(5000);
        proxy.setFilterByServiceName(true);
        proxy.afterPropertiesSet();  //初始化实例

        return (FulfillmentStoreConfigThriftService) proxy.getObject();
    }

    @Bean("poiRelationThriftService")
    public PoiRelationThriftService poiRelationThriftService() throws Exception {
        RichTypeThriftClientProxy proxy = new RichTypeThriftClientProxy();
        proxy.setRemoteAppkey("com.sankuai.shangou.empower.tenant");
        proxy.setServiceInterface(Class.forName("com.meituan.shangou.saas.tenant.thrift.PoiRelationThriftService"));
        proxy.setNettyIO(true);
        proxy.setTimeout(5000);
        proxy.setFilterByServiceName(true);
        proxy.afterPropertiesSet();  //初始化实例

        return (PoiRelationThriftService) proxy.getObject();
    }

}
