package com.sankuai.shangou.logistics.delivery.configure.service.converter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryLaunchTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum;
import com.sankuai.shangou.logistics.delivery.configure.DeliveryConfigDetailVO;
import com.sankuai.shangou.logistics.delivery.configure.enums.CompletedSortModeEnum;
import com.sankuai.shangou.logistics.delivery.configure.enums.DeliveryConfigTypeEnum;
import com.sankuai.shangou.logistics.delivery.configure.enums.InternalNavigationModeEnum;
import com.sankuai.shangou.logistics.delivery.configure.enums.TimeConfigTypeEnum;
import com.sankuai.shangou.logistics.delivery.configure.model.DeliveryDimensionPoi;
import com.sankuai.shangou.logistics.delivery.configure.model.DeliveryPoi;
import com.sankuai.shangou.logistics.delivery.configure.value.AssessTimeConfig;
import com.sankuai.shangou.logistics.delivery.configure.model.value.AssessTimeModel;
import com.sankuai.shangou.logistics.delivery.configure.pojo.model.BatchTaskConfigContent;
import com.sankuai.shangou.logistics.delivery.configure.repository.DeliveryPoiRepository;
import com.sankuai.shangou.logistics.delivery.configure.service.DeliveryConfigService;
import com.sankuai.shangou.logistics.delivery.configure.value.*;
import com.sankuai.shangou.logistics.delivery.configure.value.launchpoint.BookingOrderDeliveryLaunchPointEnum;
import com.sankuai.shangou.logistics.delivery.configure.value.launchpoint.ImmediateOrderDeliveryLaunchPointEnum;
import com.sankuai.shangou.logistics.delivery.poi.client.PoiQueryClient;
import com.sankuai.shangou.logistics.delivery.poi.client.dto.PoiBaseInfo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-08-26
 * @email <EMAIL>
 */
@Service
public class DeliveryConfigConverter {

    @Resource
    private PoiQueryClient poiQueryClient;
    @Resource
    private DeliveryPoiRepository deliveryPoiRepository;


    private static final int DEFAULT_IMMEDIATE_ORDER_DELAY_MINUTES = 0;
    private static final int DEFAULT_EMPOWER_BOOKING_ORDER_DELAY_MINUTES = 60;

    @Resource
    private DeliveryConfigService deliveryConfigService;

    public DeliveryConfigDetailVO.SelfDeliveryConfigVO covertSelfDeliveryConfigVO(List<BatchTaskConfigContent> configContents) {
        DeliveryConfigDetailVO.SelfDeliveryConfigVO selfDeliveryConfig = new DeliveryConfigDetailVO.SelfDeliveryConfigVO();
        for (BatchTaskConfigContent configContent : configContents) {
            Map<String, Object> content = configContent.getContent();
            if (MapUtils.isEmpty(content)) {
                continue;
            }
            switch (configContent.getType()) {
                case DISPLAY_TIME_REMAINING:
                    AssessTimeModel assessTimeModel = (AssessTimeModel) JSON.toJavaObject(new JSONObject(content), configContent.getType().getCls());
                    List<DeliveryConfigDetailVO.AssertTimeVO> assertTimeVOS = deliveryConfigService.buildAssertTimeConfig(assessTimeModel.getAssessTimeConfigs());
                    selfDeliveryConfig.setAssertTime(assertTimeVOS);
                    break;
                case DISPLAY_CONFIRM_ACTION:
                    DeliveryCompleteMode deliveryCompleteMode = (DeliveryCompleteMode) JSON.toJavaObject(new JSONObject(content), configContent.getType().getCls());
                    DeliveryConfigDetailVO.DeliveryCompleteModeVO deliveryCompleteModeVO = deliveryConfigService.buildDeliveryCompleteModeConfig(deliveryCompleteMode);
                    selfDeliveryConfig.setDeliveryCompleteMode(deliveryCompleteModeVO);
                    break;
                case DISPLAY_RIDER_TRANS:
                    RiderTransConfig riderTransConfig = (RiderTransConfig) JSON.toJavaObject(new JSONObject(content), configContent.getType().getCls());
                    selfDeliveryConfig.setRiderTransRoles(riderTransConfig.getRiderTransRoles());
                    break;
                case DISPLAY_NAVIGATION:
                    NavigationConfig navigationConfig = (NavigationConfig) JSON.toJavaObject(new JSONObject(content), configContent.getType().getCls());
                    selfDeliveryConfig.setInternalNavigationMode(navigationConfig.getInternalNavigationMode());
                    break;
                case REMIND:
                    DeliveryRemindConfig remindConfig = (DeliveryRemindConfig) JSON.toJavaObject(new JSONObject(content), configContent.getType().getCls());
                    DeliveryConfigDetailVO.DeliveryRemindConfigVO deliveryRemindConfigVO = deliveryConfigService.buildDeliveryRemindConfig(remindConfig);
                    selfDeliveryConfig.setDeliveryRemindConfig(deliveryRemindConfigVO);
                    break;
                default:
                    break;
            }
        }
        return selfDeliveryConfig;
    }

    public Pair<List<DeliveryConfigDetailVO.DeliveryPlatformConfigVO>, Integer/*pickDeliveryWorkMode*/> covertDeliveryPlatformConfigVOs(List<BatchTaskConfigContent> configContents) {
        Integer pickDeliveryWorkMode = null;
        List<BatchTaskConfigContent> contents = configContents.stream()
                .filter(item -> DeliveryConfigTypeEnum.CHANNEL_POI_DIMENSION.contains(item.getType()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(contents)) {
            return Pair.of(Collections.emptyList(), null);
        }
        List<Integer> channelTypes = contents.stream()
                .map(item -> item.getContent().keySet())
                .flatMap(Collection::stream)
                .filter(StringUtils::isNumeric)
                .distinct()
                .map(Integer::valueOf)
                .sorted()
                .collect(Collectors.toList());

        List<DeliveryConfigDetailVO.DeliveryPlatformConfigVO> deliveryPlatformConfigVos = new ArrayList<>();
        for (Integer channelType : channelTypes) {
            DeliveryConfigDetailVO.DeliveryPlatformConfigVO platformConfig = new DeliveryConfigDetailVO.DeliveryPlatformConfigVO();
            deliveryPlatformConfigVos.add(platformConfig);
            platformConfig.setChannelType(channelType);
            for (BatchTaskConfigContent configContent : contents) {
                Map<String, Object> contentMap = configContent.getContent();
                if (MapUtils.isEmpty(contentMap)) {
                    continue;
                }
                Object content = contentMap.get(String.valueOf(channelType));
                if (Objects.isNull(content)) {
                    continue;
                }
                switch (configContent.getType()) {
                    case RULES:
                        DeliveryPlatformConfig deliveryPlatformConfig = (DeliveryPlatformConfig) JSON.parseObject(content.toString(), configContent.getType().getCls());
                        platformConfig.setStatus(deliveryPlatformConfig.getStatus());
                        if (deliveryPlatformConfig.getStatus() == 0) {
                            continue;
                        }
                        // 配送平台
                        platformConfig.setPlatformCode(deliveryPlatformConfig.getPlatformCode());
                        // 拣配模式上牵牛花门店纬度
                        pickDeliveryWorkMode = deliveryPlatformConfig.getPickDeliveryWorkMode();
                        // 配送发起时间点配置
                        if (deliveryPlatformConfig.getDeliveryLaunchPoint() != null) {
                            deliveryConfigService.fillPlatformLaunchPointConfig(platformConfig, deliveryPlatformConfig.getDeliveryLaunchPoint());
                        }
                        // 自送预约配送规则
                        if (deliveryPlatformConfig.getBookingOrderLaunchTimeConfig() != null) {
                            deliveryConfigService.buildSelfAssessDeliveryConfigOfExist(deliveryPlatformConfig.getBookingOrderLaunchTimeConfig(), platformConfig);
                        }
                        break;
                    case RULES_HELPER_WAY:
                        if (Objects.isNull(platformConfig.getSecondDeliveryConfig())) {
                            // 初始化，预防NPE
                            platformConfig.setSecondDeliveryConfig(new DeliveryConfigDetailVO.SecondDeliveryConfigVO());
                        }
                        SecondDeliveryWayConfig deliveryRulesConfig = (SecondDeliveryWayConfig) JSON.parseObject(content.toString(), configContent.getType().getCls());
                        platformConfig.getSecondDeliveryConfig().setSecondPlatformCode(deliveryRulesConfig.getSecondPlatformCodeCodes());
                        break;
                    case RULES_HELPER_RULES:
                        if (Objects.isNull(platformConfig.getSecondDeliveryConfig())) {
                            // 初始化，预防NPE
                            platformConfig.setSecondDeliveryConfig(new DeliveryConfigDetailVO.SecondDeliveryConfigVO());
                        }
                        SecondDeliveryForbiddenRuleConfig secondDeliveryForbiddenRuleConfig = (SecondDeliveryForbiddenRuleConfig) JSON.parseObject(content.toString(), configContent.getType().getCls());
                        DeliveryConfigDetailVO.ForbiddenConditionVO forbiddenCondition = new DeliveryConfigDetailVO.ForbiddenConditionVO();
                        forbiddenCondition.setOrderTags(secondDeliveryForbiddenRuleConfig.getOrderTags());
                        forbiddenCondition.setOrderActualPayment(secondDeliveryForbiddenRuleConfig.getOrderActualPayment());
                        platformConfig.getSecondDeliveryConfig().setForbiddenCondition(forbiddenCondition);
                        break;
                }
            }
        }
        return Pair.of(deliveryPlatformConfigVos, pickDeliveryWorkMode);
    }


    /**
     * 从请求更新DeliveryDimensionPoi
     */
    public DeliveryDimensionPoi updateDeliveryDimensionPoiFromRequest(DeliveryDimensionPoi existingPoi,
                                                                      DeliveryConfigDetailVO.SelfDeliveryConfigVO selfDeliveryConfig) {
        return new DeliveryDimensionPoi(
                existingPoi.getId(),
                existingPoi.getTenantId(),
                existingPoi.getStoreId(),
                InternalNavigationModeEnum.getByMode(selfDeliveryConfig.getInternalNavigationMode()),
                buildAssessTimeConfigFromRequest(selfDeliveryConfig.getAssertTime()),
                buildDeliveryCompleteModeFromRequest(selfDeliveryConfig.getDeliveryCompleteMode()),
                selfDeliveryConfig.getRiderTransRoles(),
                CompletedSortModeEnum.getByMode(selfDeliveryConfig.getCompletedSortMode()),
                buildDeliveryRemindConfigFromRequest(selfDeliveryConfig.getDeliveryRemindConfig()), LocalDateTime.now(), LocalDateTime.now()
        );
    }

    /**
     * 从请求更新DeliveryDimensionPoi
     */
    public DeliveryDimensionPoi batchUpdateDeliveryDimensionPoiFromRequest(DeliveryDimensionPoi existingPoi, List<DeliveryConfigTypeEnum> typeEnums,
                                                                           DeliveryConfigDetailVO.SelfDeliveryConfigVO selfDeliveryConfig) {
        InternalNavigationModeEnum internalNavigationMode = existingPoi.getInternalNavigationMode();
        List<AssessTimeConfig> assessTimeConfigs = existingPoi.getAssessTimeConfigs();
        DeliveryCompleteMode deliveryCompleteMode = existingPoi.getDeliveryCompleteMode();
        List<Long> riderTransRoles = existingPoi.getRiderTransRoles();
        CompletedSortModeEnum completedSortMode = existingPoi.getCompletedSortMode();
        DeliveryRemindConfig deliveryRemindConfig = existingPoi.getDeliveryRemindConfig();

        if (typeEnums.contains(DeliveryConfigTypeEnum.DISPLAY_TIME_REMAINING)) {
            assessTimeConfigs = buildAssessTimeConfigFromRequest(selfDeliveryConfig.getAssertTime());
        }
        if (typeEnums.contains(DeliveryConfigTypeEnum.DISPLAY_CONFIRM_ACTION)) {
            deliveryCompleteMode = buildDeliveryCompleteModeFromRequest(selfDeliveryConfig.getDeliveryCompleteMode());
        }
        if (typeEnums.contains(DeliveryConfigTypeEnum.DISPLAY_RIDER_TRANS)) {
            riderTransRoles = selfDeliveryConfig.getRiderTransRoles();
        }
        if (typeEnums.contains(DeliveryConfigTypeEnum.DISPLAY_NAVIGATION)) {
            internalNavigationMode = InternalNavigationModeEnum.getByMode(selfDeliveryConfig.getInternalNavigationMode());
        }
        if (typeEnums.contains(DeliveryConfigTypeEnum.REMIND)) {
            deliveryRemindConfig = buildDeliveryRemindConfigFromRequest(selfDeliveryConfig.getDeliveryRemindConfig());
        }
        if (Objects.nonNull(selfDeliveryConfig.getCompletedSortMode())) {
            completedSortMode = CompletedSortModeEnum.getByMode(selfDeliveryConfig.getCompletedSortMode());
        }

        return new DeliveryDimensionPoi(
                existingPoi.getId(),
                existingPoi.getTenantId(),
                existingPoi.getStoreId(),
                internalNavigationMode,
                assessTimeConfigs,
                deliveryCompleteMode,
                riderTransRoles,
                completedSortMode,
                deliveryRemindConfig,
                LocalDateTime.now(),
                LocalDateTime.now()
        );
    }

    /**
     * 从请求构建AssessTimeConfig
     */
    public List<AssessTimeConfig> buildAssessTimeConfigFromRequest(List<DeliveryConfigDetailVO.AssertTimeVO> assertTimeList) {
        if (org.apache.commons.collections.CollectionUtils.isEmpty(assertTimeList)) {
            return Lists.newArrayList();
        }

        List<AssessTimeConfig> result = Lists.newArrayList();

        for (DeliveryConfigDetailVO.AssertTimeVO assertTimeVO : assertTimeList) {
            AssessTimeConfig assessTimeConfig = new AssessTimeConfig();
            assessTimeConfig.setType(assertTimeVO.getType());
            assessTimeConfig.setHintType(assertTimeVO.getHintType()); // 根据实际需求设置hint字段
            assessTimeConfig.setTimeConfigType(assertTimeVO.getTimeConfigType());
            // 转换VO为ExpressionNode
            List<DeliveryConfigDetailVO.AssertTimeVO> singleVOList = Lists.newArrayList(assertTimeVO);
            List<ExpressionNode> expressionNodes =
                    BookingPushDownTimeConfigConverter.convertAssertTimeToExpressionNodes(TimeConfigTypeEnum.valueOfType(assertTimeVO.getTimeConfigType()), singleVOList);

            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(expressionNodes)) {
                assessTimeConfig.setExpressionNode(expressionNodes.get(0));
            }

            result.add(assessTimeConfig);
        }

        return result;
    }

    /**
     * 从请求构建DeliveryCompleteMode
     */
    public DeliveryCompleteMode buildDeliveryCompleteModeFromRequest(
            DeliveryConfigDetailVO.DeliveryCompleteModeVO deliveryCompleteModeVO) {
        if (deliveryCompleteModeVO == null) {
            return null;
        }

        DeliveryCompleteMode deliveryCompleteMode = new DeliveryCompleteMode();
        deliveryCompleteMode.setDistanceReminder(deliveryCompleteModeVO.getDistanceReminder());

        if (deliveryCompleteModeVO.getDeliveryCompleteConfig() != null) {
            DeliveryCompleteMode.DeliveryCompleteConfig config = buildDeliveryCompleteConfigFromRequest(
                    deliveryCompleteModeVO.getDeliveryCompleteConfig());
            deliveryCompleteMode.setDeliveryCompleteConfig(config);
        }

        return deliveryCompleteMode;
    }

    /**
     * 从请求构建DeliveryCompleteConfig
     */
    private DeliveryCompleteMode.DeliveryCompleteConfig buildDeliveryCompleteConfigFromRequest(
            DeliveryConfigDetailVO.DeliveryCompleteConfigVO configVO) {
        if (configVO == null) {
            return null;
        }

        DeliveryCompleteMode.DeliveryCompleteConfig config = new DeliveryCompleteMode.DeliveryCompleteConfig();

        // 构建示例图片信息列表
        if (!org.apache.commons.collections.CollectionUtils.isEmpty(configVO.getAllExamplePicInfoList())) {
            List<DeliveryCompleteMode.ExamplePicInfo> examplePicInfoList = new ArrayList<>();
            for (DeliveryConfigDetailVO.ExamplePicInfoVO picVO : configVO.getAllExamplePicInfoList()) {
                DeliveryCompleteMode.ExamplePicInfo picInfo = new DeliveryCompleteMode.ExamplePicInfo();
                picInfo.setType(picVO.getType());
                picInfo.setName(picVO.getName());
                picInfo.setPicUrl(picVO.getPicUrl());
                picInfo.setOrder(picVO.getOrder());
                examplePicInfoList.add(picInfo);
            }
        }

        // 构建特殊商品上传图片配置
        if (!org.apache.commons.collections.CollectionUtils.isEmpty(configVO.getSpecialProductUploadPicConfig())) {
            List<DeliveryCompleteMode.SpecialProductUploadPicConfig> specialProductConfigList = new ArrayList<>();
            for (DeliveryConfigDetailVO.SpecialProductUploadPicConfigVO specialVO : configVO.getSpecialProductUploadPicConfig()) {
                DeliveryCompleteMode.SpecialProductUploadPicConfig specialConfig = new DeliveryCompleteMode.SpecialProductUploadPicConfig();
                specialConfig.setProductType(specialVO.getProductType());
                specialConfig.setPicTypeList(specialVO.getPicTypeList());
                specialConfig.setIsForceUploadPic(specialVO.getIsForceUploadPic());
                specialConfig.setNeedUploadPicCount(specialVO.getNeedUploadPicCount());
                specialProductConfigList.add(specialConfig);
            }
            config.setSpecialProductUploadPicConfig(specialProductConfigList);
        }

        return config;
    }

    /**
     * 从请求构建DeliveryRemindConfig
     */
    public DeliveryRemindConfig buildDeliveryRemindConfigFromRequest(
            DeliveryConfigDetailVO.DeliveryRemindConfigVO remindConfigVO) {
        if (remindConfigVO == null) {
            return null;
        }

        DeliveryRemindConfig deliveryRemindConfig = new DeliveryRemindConfig();
        deliveryRemindConfig.setReceiveTimeOutMins(remindConfigVO.getReceiveTimeOutMins());
        deliveryRemindConfig.setSoonDeliveryTimeoutMinsBeforeEta(remindConfigVO.getSoonDeliveryTimeoutMinsBeforeEta());
        return deliveryRemindConfig;
    }


    /**
     * 从请求更新DeliveryPoi
     */
    public DeliveryPoi updateDeliveryPoiFromRequest(long tenantId, long storeId, @Nullable DeliveryPoi existingPoi,
                                                    DeliveryConfigDetailVO.DeliveryPlatformConfigVO platformConfig) {
        if (Objects.isNull(existingPoi)) {
            //理论上不会走到这里
            PoiBaseInfo poiBaseInfo = poiQueryClient.querySinglePoiBaseInfo(tenantId, storeId);
            existingPoi = deliveryPoiRepository.createDeliveryPoiForMissingChannel(tenantId, storeId, platformConfig.getChannelType(), null, poiBaseInfo, platformConfig);
        }

        // 构建配送发起时间点配置
        DeliveryLaunchPoint launchPoint = buildDeliveryLaunchPointFromRequest(platformConfig);

        // 获取配送平台枚举
        DeliveryPlatformEnum deliveryPlatform = DeliveryPlatformEnum.enumOf(platformConfig.getPlatformCode());
        if (deliveryPlatform == null || platformConfig.getStatus() == 0) {
            deliveryPlatform = DeliveryPlatformEnum.SELF_BUILT_DELIVERY_PLATFORM;
        }

        // 构建二级配送配置
        SecondDeliveryConfig secondDeliveryConfig = buildSecondDeliveryConfigFromRequest(platformConfig.getSecondDeliveryConfig());

        // 构建自送预约配送规则配置
        BookingOrderLaunchTimeConfig selfAssessDeliveryConfig = buildBookingPushDownTimeConfigFromRequest(platformConfig.getSelfDeliveryBookingDeliveryRule());

        // 创建新的DeliveryPoi对象，充分使用platformConfig中的配置

        return new DeliveryPoi(
                existingPoi.getId(),
                existingPoi.getTenantId(),
                existingPoi.getStoreId(),
                existingPoi.getCityCode(),
                existingPoi.getContactPhone(),
                deliveryPlatform,
                launchPoint,
                DeliveryLaunchTypeEnum.AUTO_LAUNCH_DELIVERY,
                platformConfig.getChannelType(),
                existingPoi.getDeliveryPlatform(),
                existingPoi.getDeliveryIsShowItemNumberEnum(),
                existingPoi.getStoreAddress(),
                selfAssessDeliveryConfig != null ? selfAssessDeliveryConfig : existingPoi.getSelfAssessDeliveryConfig(),
                secondDeliveryConfig
        );
    }

    /**
     * 批量操作从请求模块更新DeliveryPoi
     */
    public DeliveryPoi batchUpdateDeliveryPoiFromRequest(DeliveryPoi existingPoi, DeliveryConfigDetailVO.DeliveryPlatformConfigVO platformConfig,
                                                         List<DeliveryConfigTypeEnum> typeEnums) {
        DeliveryPlatformEnum deliveryPlatform = existingPoi.getDeliveryPlatform();
        DeliveryPlatformEnum lastDeliveryPlatform = existingPoi.getLastDeliveryPlatform();
        DeliveryLaunchPoint launchPoint = existingPoi.getDeliveryLaunchPoint();
        BookingOrderLaunchTimeConfig selfAssessDeliveryConfig = existingPoi.getSelfAssessDeliveryConfig();
        SecondDeliveryConfig secondDeliveryUpdate = existingPoi.getSecondDeliveryPlatform();
        // 配送规则更新
        if (typeEnums.contains(DeliveryConfigTypeEnum.RULES)) {
            if (platformConfig.getStatus() == 0) {
                if (deliveryPlatform != DeliveryPlatformEnum.SELF_BUILT_DELIVERY_PLATFORM) {
                    // 关闭牵牛花管理配送只更新开关
                    deliveryPlatform = DeliveryPlatformEnum.SELF_BUILT_DELIVERY_PLATFORM;
                    lastDeliveryPlatform = existingPoi.getDeliveryPlatform();
                }
            } else {
                // 构建配送发起时间点配置
                launchPoint = buildDeliveryLaunchPointFromRequest(platformConfig);
                // 获取配送平台枚举
                deliveryPlatform = DeliveryPlatformEnum.enumOf(platformConfig.getPlatformCode());
                if (deliveryPlatform == null) {
                    deliveryPlatform = DeliveryPlatformEnum.SELF_BUILT_DELIVERY_PLATFORM;
                }
                lastDeliveryPlatform = existingPoi.getDeliveryPlatform();
                // 构建自送预约配送规则配置
                selfAssessDeliveryConfig = buildBookingPushDownTimeConfigFromRequest(platformConfig.getSelfDeliveryBookingDeliveryRule());
            }
        }
        // 构建二级配送配置
        SecondDeliveryConfig secondDeliveryConfig = buildSecondDeliveryConfigFromRequest(platformConfig.getSecondDeliveryConfig());
        if (typeEnums.contains(DeliveryConfigTypeEnum.RULES_HELPER_WAY)) {
            secondDeliveryUpdate.setSecondPlatformCodeCode(secondDeliveryConfig.getSecondPlatformCodeCode());
        }

        if (typeEnums.contains(DeliveryConfigTypeEnum.RULES_HELPER_RULES)) {
            secondDeliveryUpdate.setForbiddenCondition(secondDeliveryConfig.getForbiddenCondition());
        }
        return new DeliveryPoi(
                existingPoi.getId(),
                existingPoi.getTenantId(),
                existingPoi.getStoreId(),
                existingPoi.getCityCode(),
                existingPoi.getContactPhone(),
                deliveryPlatform,
                launchPoint,
                existingPoi.getDeliveryLaunchType(),
                existingPoi.getChannelType(),
                lastDeliveryPlatform,
                existingPoi.getDeliveryIsShowItemNumberEnum(),
                existingPoi.getStoreAddress(),
                selfAssessDeliveryConfig,
                secondDeliveryUpdate
        );
    }


    /**
     * 从请求构建DeliveryLaunchPoint
     */
    public DeliveryLaunchPoint buildDeliveryLaunchPointFromRequest(
            DeliveryConfigDetailVO.DeliveryPlatformConfigVO platformConfig) {

        // 立即单配送发起时间点配置
        ImmediateOrderDeliveryLaunchPointEnum immediatePoint =
                ImmediateOrderDeliveryLaunchPointEnum.enumOf(platformConfig.getDeliveryLaunchPoint());
        if (immediatePoint == null) {
            immediatePoint = ImmediateOrderDeliveryLaunchPointEnum.MERCHANT_ACCEPT;
        }

        DeliveryLaunchPoint.ImmediateOrderDeliveryLaunchPointConfig immediateConfig =
                new DeliveryLaunchPoint.ImmediateOrderDeliveryLaunchPointConfig(
                        immediatePoint,
                        platformConfig.getDeliveryLaunchDelayMinutes() != null ?
                                platformConfig.getDeliveryLaunchDelayMinutes() : 0
                );

        // 预约单配送发起时间点配置
        DeliveryLaunchPoint.BookingOrderDeliveryLaunchPointConfig bookingConfig =
                new DeliveryLaunchPoint.BookingOrderDeliveryLaunchPointConfig(
                        BookingOrderDeliveryLaunchPointEnum.ORDER_PAID, // 默认商家接单
                        platformConfig.getBookingOrderDeliveryLaunchMinutes() != null ?
                                platformConfig.getBookingOrderDeliveryLaunchMinutes() : 0
                );

        //抖音渠道，且非平台配送，则修改自动呼叫时间为默认值
        if (platformConfig.getChannelType().equals(DynamicChannelType.DOU_YIN.getChannelId()) && !Objects.equals(platformConfig.getPlatformCode(), DeliveryPlatformEnum.ORDER_CHANNEL_DELIVERY_PLATFORM.getCode())) {
            immediateConfig.setDelayMinutes(DEFAULT_IMMEDIATE_ORDER_DELAY_MINUTES);
            bookingConfig.setConfigMinutes(DEFAULT_EMPOWER_BOOKING_ORDER_DELAY_MINUTES);
        }

        return new DeliveryLaunchPoint(immediateConfig, bookingConfig);
    }

    /**
     * 从请求构建SecondDeliveryConfig
     */
    public SecondDeliveryConfig buildSecondDeliveryConfigFromRequest(
            DeliveryConfigDetailVO.SecondDeliveryConfigVO secondDeliveryConfigVO) {
        if (secondDeliveryConfigVO == null) {
            return null;
        }

        SecondDeliveryConfig secondDeliveryConfig = new SecondDeliveryConfig();
        secondDeliveryConfig.setSecondPlatformCodeCode(secondDeliveryConfigVO.getSecondPlatformCode());

        // 构建禁用条件
        if (secondDeliveryConfigVO.getForbiddenCondition() != null) {
            SecondDeliveryForbiddenCondition forbiddenCondition = new SecondDeliveryForbiddenCondition();
            forbiddenCondition.setOrderTags(secondDeliveryConfigVO.getForbiddenCondition().getOrderTags());
            forbiddenCondition.setOrderActualPayment(secondDeliveryConfigVO.getForbiddenCondition().getOrderActualPayment());
            secondDeliveryConfig.setForbiddenCondition(forbiddenCondition);
        }

        return secondDeliveryConfig;
    }

    /**
     * 从请求构建BookingPushDownTimeConfig
     */
    public BookingOrderLaunchTimeConfig buildBookingPushDownTimeConfigFromRequest(
            DeliveryConfigDetailVO.SelfDeliveryBookingDeliveryRuleVO selfDeliveryBookingDeliveryRuleVO) {
        if (selfDeliveryBookingDeliveryRuleVO == null) {
            return null;
        }

        BookingOrderLaunchTimeConfig bookingOrderLaunchTimeConfig = new BookingOrderLaunchTimeConfig();

        // 设置是否需要营业时间下推
        bookingOrderLaunchTimeConfig.setNeedBusinessHoursPushdown(selfDeliveryBookingDeliveryRuleVO.getNeedBusinessHoursPushdown());

        // 处理预约下推时间配置列表
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(selfDeliveryBookingDeliveryRuleVO.getBookingPushDownTimeConfig())) {
            List<ExpressionNode> expressionNodes =
                    BookingPushDownTimeConfigConverter.convertToExpressionNodes(selfDeliveryBookingDeliveryRuleVO.getBookingPushDownTimeConfig());
            bookingOrderLaunchTimeConfig.setSubs(expressionNodes);
        }


        return bookingOrderLaunchTimeConfig;
    }


}
