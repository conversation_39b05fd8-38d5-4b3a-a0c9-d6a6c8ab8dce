package com.sankuai.shangou.logistics.delivery.configure.service.wrapper;

import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.sankuai.drunkhorsemgmt.labor.exception.SystemException;
import com.sankuai.shangou.commons.utils.log.MethodLog;
import com.sankuai.shangou.qnh.ofc.ebase.consts.OfcEbaseResultCodeEnum;
import com.sankuai.shangou.qnh.ofc.ebase.consts.OrderTypeEnum;
import com.sankuai.shangou.qnh.ofc.ebase.dto.FulfillConfigDTO;
import com.sankuai.shangou.qnh.ofc.ebase.request.BatchSaveTemplateFulfillConfigRequest;
import com.sankuai.shangou.qnh.ofc.ebase.request.QueryFulfillConfigRequest;
import com.sankuai.shangou.qnh.ofc.ebase.request.SaveFulfillConfigRequest;
import com.sankuai.shangou.qnh.ofc.ebase.response.QueryFulfillConfigResponse;
import com.sankuai.shangou.qnh.ofc.ebase.response.SaveFulfillConfigResponse;
import com.sankuai.shangou.qnh.ofc.ebase.service.FulfillmentStoreConfigThriftService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025-08-01
 * @email <EMAIL>
 */
@Slf4j
@Rhino
public class FulfillConfigServiceWrapper {
    @Resource
    private FulfillmentStoreConfigThriftService fulfillmentStoreConfigThriftService;

    @MethodLog(logRequest = false, logResponse = true)
    @Degrade(rhinoKey = "FulfillConfigServiceWrapper.queryFulfillConfig", fallBackMethod = "queryFulfillConfigFallback", timeoutInMilliseconds = 2000)
    public FulfillConfigDTO queryFulfillConfig(long tenantId, long storeId){
        QueryFulfillConfigRequest request = new QueryFulfillConfigRequest();
        request.setTenantId(tenantId);
        request.setWarehouseId(storeId);
        request.setOrderType(OrderTypeEnum.SALE_TYPE.getCode());
        QueryFulfillConfigResponse response = fulfillmentStoreConfigThriftService.queryFulfillConfig(request);
        if (!Objects.equals(response.getCode(), OfcEbaseResultCodeEnum.SUCCESS.getCode())) {
            throw new SystemException("查询履约配置失败");
        }
        return response.getFulfillConfig();
    }

    public FulfillConfigDTO queryFulfillConfigFallback(long tenantId, long storeId){
        log.warn("FulfillConfigServiceWrapper.queryFulfillConfig, tenantId={}, storeId={}", tenantId, storeId);
        return null;
    }

    @MethodLog(logRequest = false, logResponse = true)
    @Degrade(rhinoKey = "FulfillConfigServiceWrapper.saveFulfillConfig", fallBackMethod = "saveFulfillConfigFallback", timeoutInMilliseconds = 2000)
    public void saveFulfillConfig(long tenantId, long storeId, FulfillConfigDTO fulfillConfigDTO){
        SaveFulfillConfigRequest saveFulfillConfigRequest = new SaveFulfillConfigRequest();
        saveFulfillConfigRequest.setTenantId(tenantId);
        saveFulfillConfigRequest.setWarehouseId(storeId);
        saveFulfillConfigRequest.setOrderType(OrderTypeEnum.SALE_TYPE.getCode());
        saveFulfillConfigRequest.setFulfillConfigDTO(fulfillConfigDTO);
        SaveFulfillConfigResponse response = fulfillmentStoreConfigThriftService.saveFulfillConfig(saveFulfillConfigRequest);
        if (!Objects.equals(response.getCode(), OfcEbaseResultCodeEnum.SUCCESS.getCode())) {
            throw new SystemException("保存履约配置失败");
        }
    }

    public void saveFulfillConfigFallback(long tenantId, long storeId, FulfillConfigDTO fulfillConfigDTO){
        log.warn("FulfillConfigServiceWrapper.saveFulfillConfig, tenantId={}, storeId={}", tenantId, storeId);
    }

    @MethodLog(logRequest = false, logResponse = true)
    @Degrade(rhinoKey = "FulfillConfigServiceWrapper.batchSaveTemplateFulfillConfig", fallBackMethod = "batchSaveTemplateFulfillConfigFallback", timeoutInMilliseconds = 5000)
    public void batchSaveTemplateFulfillConfig(long tenantId, List<Long> storeIds, FulfillConfigDTO fulfillConfigDTO){
        if (CollectionUtils.isEmpty(storeIds)){
            return;
        }
        BatchSaveTemplateFulfillConfigRequest batchSaveTemplateFulfillConfigRequest = new BatchSaveTemplateFulfillConfigRequest();
        batchSaveTemplateFulfillConfigRequest.setTenantId(tenantId);
        batchSaveTemplateFulfillConfigRequest.setWarehouseIds(storeIds);
        batchSaveTemplateFulfillConfigRequest.setOrderType(OrderTypeEnum.SALE_TYPE.getCode());
        batchSaveTemplateFulfillConfigRequest.setFulfillConfigDTO(fulfillConfigDTO);
        SaveFulfillConfigResponse response = fulfillmentStoreConfigThriftService.batchSaveTemplateFulfillConfig(batchSaveTemplateFulfillConfigRequest);
        if (!Objects.equals(response.getCode(), OfcEbaseResultCodeEnum.SUCCESS.getCode())) {
            throw new SystemException("保存履约配置失败");
        }
    }

    public void batchSaveTemplateFulfillConfigFallback(long tenantId,List<Long> storeIds, FulfillConfigDTO fulfillConfigDTO){
        log.warn("FulfillConfigServiceWrapper.batchSaveTemplateFulfillConfig, tenantId={}, storeIds={}", tenantId, storeIds);
    }

}
