package com.sankuai.meituan.shangou.dms.base.model.enums;

/**
 * <AUTHOR>
 * @date 2025-04-28
 * @email <EMAIL>
 */
public enum DeliveryAuthEnum {

    //订单-待处理下
    ORDER_SUB_TAB("工作台-订单-待处理"),
        //新任务
        NEW_TASK_ONLY_PICK_TAB("工作台-自配送任务-新任务-仅拣货"),
        NEW_TASK_ONLY_DELIVERY_TAB("工作台-自配送任务-新任务-仅配送"),
        NEW_TASK_PICK_DELIVERY_TAB("工作台-自配送任务-新任务-拣配任务"),
        //取货
        WAIT_TAKE_ONLY_PICK_TAB("工作台-自配送任务-待取货-仅拣货"),
        WAIT_TAKE_DELIVERY_TASK_TAB("工作台-自配送任务-待取货-配送任务"),
        //配送中
        SELF_RIDER_IN_DELIVERY_MENU("工作台-自配送任务-配送中"),
        //已完成
        PICKER_DONE_TAB("工作台-自配送任务-已完成-已拣货"),
        RIDER_DONE_TAB("工作台-自配送任务-已完成-已送达"),



    //工作台-自配送任务
    SELF_DELIVERY_TASK_MENU("工作台-自配送任务入口"),
        SELF_DELIVERY_TASK_NEW_TASK("工作台-自配送任务-新任务"),
        SELF_DELIVERY_TASK_WAIT("工作台-自配送任务-待取货"),
        SELF_DELIVERY_TASK_DONGING("工作台-自配送任务-配送中"),
        SELF_DELIVERY_TASK_DONE("工作台-自配送任务-已完成"),
    ;

    private final String desc;

    DeliveryAuthEnum(String desc) {
        this.desc = desc;
    }


    public String getDesc() {
        return desc;
    }
}
