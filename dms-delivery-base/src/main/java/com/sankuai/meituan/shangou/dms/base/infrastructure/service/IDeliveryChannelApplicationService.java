package com.sankuai.meituan.shangou.dms.base.infrastructure.service;

import com.sankuai.meituan.shangou.dms.base.model.value.DeliveryChannel;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @description
 * @date 2025-06-18
 */
public interface IDeliveryChannelApplicationService {
    DeliveryChannel queryDeliveryChannelByCarrierCode(Integer carrierCode);

    List<DeliveryChannel> batchQueryDeliveryChannelByCarrierCodeSet(Set<Integer> carrierCodeSet);

}
