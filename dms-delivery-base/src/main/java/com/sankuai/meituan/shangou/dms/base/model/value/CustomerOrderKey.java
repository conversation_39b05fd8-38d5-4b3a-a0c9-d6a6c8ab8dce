package com.sankuai.meituan.shangou.dms.base.model.value;

import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryChannelEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.PlatformSourceEnum;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * 订单唯一标识值对象
 * <AUTHOR>
 * @date 2024/4/3
 */
@Getter
@ToString
@EqualsAndHashCode
@AllArgsConstructor
public class CustomerOrderKey {

    /**
     * 订单主键id
     */
    @Setter
    private Long orderId;

    /**
     * 渠道订单id
     */
    private String channelOrderId;

    /**
     * 订单来源
     *
     */
    private Integer orderBizType;

    /**
     * 订单日流水号
     */
    private Integer daySeq;

    /**
     * 是否为预约单
     */
    private Boolean reserved;

    /**
     * 履约订单号
     */
    @Setter
    private Long fulfillOrderId;

    /**
     * 履约订单号
     */
    private Integer orderSource;

    public CustomerOrderKey(Long orderId, String channelOrderId, Integer orderBizType, Integer daySeq, Boolean reserved, Long fulfillOrderId, Integer orderSource) {
        this.orderId = orderId;
        this.channelOrderId = channelOrderId;
        this.orderBizType = orderBizType;
        this.daySeq = daySeq;
        this.reserved = reserved;
        this.fulfillOrderId = fulfillOrderId;
        this.orderSource = orderSource;
    }

    /**
     * 订单日流水号(主要是适配私域流水号含有非数字)
     */
    private String daySeqNum;

    /**
     * 配送平台
     */
    @Setter
    private PlatformSourceEnum platformSourceEnum = PlatformSourceEnum.OMS;
} 