package com.sankuai.meituan.shangou.dms.base.model.factory;

import com.sankuai.meituan.shangou.dms.base.model.enums.SyncLocationStrategyType;
import com.sankuai.meituan.shangou.dms.base.model.strategy.SyncLocationStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.EnumMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 同步位置策略工厂
 * <p>
 * 使用 Spring 自动注入所有 SyncLocationStrategy 类型的 Bean，
 * 并根据策略定义的类型进行注册，方便按类型获取。
 *
 * <AUTHOR> coder
 * @date 2024/07/26
 */
@Slf4j
@Component
public class SyncLocationStrategyFactory {

    private final Map<SyncLocationStrategyType, SyncLocationStrategy> strategyMap = new EnumMap<>(SyncLocationStrategyType.class);

    private final List<SyncLocationStrategy> strategies;

    /**
     * 构造函数注入所有 SyncLocationStrategy 实例。
     *
     * @param strategies Spring 自动注入的策略列表
     */
    @Autowired
    public SyncLocationStrategyFactory(List<SyncLocationStrategy> strategies) {
        this.strategies = strategies;
    }

    /**
     * Spring Bean 初始化后执行，将策略注册到 Map 中。
     */
    @PostConstruct
    public void initStrategies() {
        if (strategies == null || strategies.isEmpty()) {
            log.warn("No SyncLocationStrategy beans found to register in the factory.");
            return;
        }
        log.info("Initializing SyncLocationStrategyFactory with {} strategies.", strategies.size());
        strategies.forEach(strategy -> {
            SyncLocationStrategyType type = strategy.getStrategyType();
            if (type != null) {
                SyncLocationStrategy existing = strategyMap.put(type, strategy);
                if (existing != null) {
                    log.warn("Duplicate strategy registration for type {}. Overwriting {} with {}.",
                             type, existing.getClass().getSimpleName(), strategy.getClass().getSimpleName());
                }
            } else {
                log.warn("Strategy {} does not provide a valid type via getStrategyType() and will be ignored.",
                         strategy.getClass().getSimpleName());
            }
        });
        log.info("SyncLocationStrategyFactory initialization complete. Registered types: {}", strategyMap.keySet());
    }

    /**
     * 根据策略类型获取对应的策略实例。
     *
     * @param type 策略类型
     * @return 对应的策略实例 Optional，如果未找到则为空
     */
    public Optional<SyncLocationStrategy> getStrategy(SyncLocationStrategyType type) {
        SyncLocationStrategy strategy = strategyMap.get(type);
        if (strategy == null) {
            log.warn("No SyncLocationStrategy found for type: {}", type);
        }
        return Optional.ofNullable(strategy);
    }

    /**
     * 根据策略类型获取对应的策略实例，如果找不到则抛出异常。
     *
     * @param type 策略类型
     * @return 对应的策略实例
     * @throws IllegalArgumentException 如果找不到指定类型的策略
     */
    public SyncLocationStrategy getRequiredStrategy(SyncLocationStrategyType type) {
        return getStrategy(type)
                .orElseThrow(() -> {
                    log.error("Required SyncLocationStrategy not found for type: {}", type);
                    return new IllegalArgumentException("Unsupported SyncLocationStrategyType: " + type);
                });
    }
} 