package com.sankuai.meituan.shangou.dms.base.model.enums;

import com.dianping.cat.util.StringUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryChannelEnum;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021/4/14
 * @email jianglilin02@meituan
 */
public enum FarmDeliveryChannelEnum {

    MEITUAN("mtps", "美团"),
    FENGNIAO("fengniao", "蜂鸟跑腿"),
    FENGKA("fengka", "蜂鸟配送"),
    DADA("dada", "达达"),
    SHUNFENG("shunfeng", "顺丰"),
    SHANSONG("bingex", "闪送"),
    UU("uupt", "uu跑腿"),
    PAOTUI("shuguopai","美团跑腿"),
    SELF("self", "自己送"),
    SANLIUWU("sanliuwu","365"),
    GUOXIAODI("guoxiaodi","裹小递"),
    CAOSONG("caosong","曹操送"),
    KELOOP("keloop","快跑者"),
    BANGLA("bangla","帮啦跑腿"),
    CAOCAO("caocao","曹操跑腿"),
    FUWU("fuwu","快服务"),
    IPAOTUI("ipaotui","爱跑腿"),
    WUKONG("wukong","悟空快跑"),
    HAOJI("haoji","好急"),
    MTBS("mtbs","美团跑腿帮送"),
    SHUNFENGC("shunfengc","顺丰C"),
    LAIDA("laida", "来答配送"),
    SONGDONGXI("songdongxi", "送个东西"),
    ZHONGLIBAN("zlb", "中里办"),
    ;

    private String channelMark;

    private String msg;

    FarmDeliveryChannelEnum(String channelMark, String msg) {
        this.channelMark = channelMark;
        this.msg = msg;
    }


    public String getChannelMark() {
        return channelMark;
    }

    public String getMsg() {
        return msg;
    }

    public static FarmDeliveryChannelEnum enumOf(String channelMark) {
        for (FarmDeliveryChannelEnum each : values()) {
            if (each.getChannelMark().equals(channelMark)) {
                return each;
            }
        }
        return null;
    }

    public static DeliveryChannelEnum mapToDeliveryChannel(String channelMark) {
        if(StringUtils.isEmpty(channelMark)){
            return DeliveryChannelEnum.MALT_FARM;
        }

        FarmDeliveryChannelEnum farmDeliveryChannelEnum = enumOf(channelMark);
        if (Objects.isNull(farmDeliveryChannelEnum)) {
            return DeliveryChannelEnum.FARM_DELIVERY_UNKNOW;
        }
        return deliveryChannelMapping(farmDeliveryChannelEnum);
    }

    private static DeliveryChannelEnum deliveryChannelMapping(FarmDeliveryChannelEnum farmDeliveryChannelEnum) {
        switch (farmDeliveryChannelEnum) {
            case MEITUAN:
                return DeliveryChannelEnum.FARM_DELIVERY_MEITUAN;
            case UU:
                return DeliveryChannelEnum.FARM_DELIVERY_UU;
            case LAIDA:
                return DeliveryChannelEnum.FARM_DELIVERY_LAIDA;
            case DADA:
                return DeliveryChannelEnum.FARM_DELIVERY_DADA;
            case FENGNIAO:
                return DeliveryChannelEnum.FARM_DELIVERY_FENGNIAO;
            case SHUNFENG:
                return DeliveryChannelEnum.FARM_DELIVERY_SHUNFENG;
            case SHANSONG:
                return DeliveryChannelEnum.FARM_DELIVERY_SHANSONG;
            case FENGKA:
                return DeliveryChannelEnum.FARM_DELIVERY_FENGNIAO_KA;
            case PAOTUI:
                return DeliveryChannelEnum.FARM_DELIVERY_PAO_TUI;
            case SELF:
                return DeliveryChannelEnum.FARM_DELIVERY_MERCHANT;
            case SANLIUWU:
                return DeliveryChannelEnum.FARM_DELIVERY_365;
            case GUOXIAODI:
                return DeliveryChannelEnum.FARM_DELIVERY_GUOXIAODI;
            case CAOSONG:
                return DeliveryChannelEnum.FARM_DELIVERY_CAOSONG;
            case KELOOP:
                return DeliveryChannelEnum.FARM_DELIVERY_KELOOP;
            case BANGLA:
                return DeliveryChannelEnum.FARM_DELIVERY_BANGLA;
            case CAOCAO:
                return DeliveryChannelEnum.FARM_DELIVERY_CAOCAO;
            case FUWU:
                return DeliveryChannelEnum.FARM_DELIVERY_FUWU;
            case IPAOTUI:
                return DeliveryChannelEnum.FARM_DELIVERY_IPAOTUI;
            case WUKONG:
                return DeliveryChannelEnum.FARM_DELIVERY_WUKONG;
            case HAOJI:
                return DeliveryChannelEnum.FARM_DELIVERY_HAOJI;
            case SHUNFENGC:
                return DeliveryChannelEnum.FARM_DELIVERY_SHUNFENGC;
            case MTBS:
                return DeliveryChannelEnum.FARM_DELIVERY_MTBS;
            case SONGDONGXI:
                return DeliveryChannelEnum.FARM_DELIVERY_SONGDONGXI;
            case ZHONGLIBAN:
                return DeliveryChannelEnum.FARM_DELIVERY_ZHONGLIBAN;
            default:
                return DeliveryChannelEnum.FARM_DELIVERY_UNKNOW;
        }
    }
}
