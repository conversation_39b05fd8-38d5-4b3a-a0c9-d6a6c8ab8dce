package com.sankuai.meituan.shangou.dms.base.model.strategy;

import com.sankuai.meituan.shangou.dms.base.model.enums.SyncLocationStrategyType;
import com.sankuai.meituan.shangou.dms.base.model.enums.SyncType;
import com.sankuai.meituan.shangou.dms.base.model.value.CoordinatePoint;

import java.time.Duration;

/**
 * 同步位置策略接口
 *
 * <AUTHOR>
 * @date 2025-04-03
 * @email <EMAIL>
 */
public interface SyncLocationStrategy extends Strategy {

    /**
     * 同步位置信息。策略实现内部会根据需要（当frontendLocation为null时）
     * 使用orderId和riderId获取门店、收货人或骑手缓存位置。
     *
     * @param syncType         同步类型 (接单/取货 或 送达)
     * @param frontendLocation 前端传递的位置 (优先使用, 可能为null)
     * @param orderId          订单ID，用于获取门店或收货人位置
     * @param riderId          骑手ID，用于获取骑手缓存位置
     * @return 最终选择的位置，如果都无法获取则返回null
     */
    CoordinatePoint sync(SyncType syncType, CoordinatePoint frontendLocation, long orderId, long riderId);

    /**
     * 获取此策略对应的类型。
     *
     * @return 策略类型枚举
     */
    SyncLocationStrategyType getStrategyType();

    /**
     * 获取此策略用于周期性同步的延迟时间。
     *
     * @return 延迟时间
     */
    Duration getPeriodicSyncDelay();

    /**
     * 执行周期性同步逻辑。
     * 通常由处理延迟MQ消息的消费者调用。
     * 实现应包含获取必要数据（如缓存位置）并执行同步操作。
     *
     * @param riderId 骑手ID
     */
    void performPeriodicSync(long riderId);

    /**
     * 安排初始的周期性同步任务。
     * 通常在需要开始周期同步时（例如，骑手接单后）调用一次，
     * 它会发送第一个延迟MQ消息。
     *
     * @param riderId 骑手ID
     */
    void scheduleInitialPeriodicSync(long riderId);
}
