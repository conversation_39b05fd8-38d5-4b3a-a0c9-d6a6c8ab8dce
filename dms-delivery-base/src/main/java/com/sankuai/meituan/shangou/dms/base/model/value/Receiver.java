package com.sankuai.meituan.shangou.dms.base.model.value;

import com.google.common.base.Preconditions;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

/**
 * 收货人信息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/3
 */
@Getter
@ToString
public class Receiver {

	/**
	 * 收货人姓名
	 */
	@Setter
	private String receiverName;

	/**
	 * 收货人电话号码
	 */
	@Setter
	private String receiverPhone;

	/**
	 * 收货人隐私号码
	 */
	private final String receiverPrivacyPhone;

	/**
	 * 收货人地址信息
	 */
	private final Address receiverAddress;

	public Receiver(String receiverName, String receiverPhone, String receiverPrivacyPhone, Address receiverAddress) {
		Preconditions.checkArgument(StringUtils.isNotEmpty(receiverName), "receiverName is empty");
		Preconditions.checkArgument(StringUtils.isNotEmpty(receiverPhone), "receiverPhone is empty");
		Preconditions.checkNotNull(receiverAddress, "receiverAddress is null");
		this.receiverName = receiverName;
		this.receiverPhone = receiverPhone;
		this.receiverPrivacyPhone = receiverPrivacyPhone;
		this.receiverAddress = receiverAddress;
	}

	/**
	 * 收货人信息是否满足配送需求
	 */
	public boolean isDeliverable() {
		return getReceiverAddress() != null && getReceiverAddress().isValid();
	}
}
