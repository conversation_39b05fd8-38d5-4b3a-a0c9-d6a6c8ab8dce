package com.sankuai.shangou.supplychain.tof.controller.vo.delivery;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.supplychain.tof.controller.vo.order.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.beans.BeanUtils;

import java.util.List;

/**
 * 返回给前端的骑手配送单VO
 *
 * <AUTHOR>
 * @since 2021/6/11 15:37
 */
@TypeDoc(
        description = "返回给前端的骑手配送单VO"
)
@Data
public abstract class AggregateOrderVO {


}
