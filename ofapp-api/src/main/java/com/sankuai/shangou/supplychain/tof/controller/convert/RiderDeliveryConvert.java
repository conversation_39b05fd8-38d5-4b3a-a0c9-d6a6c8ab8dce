package com.sankuai.shangou.supplychain.tof.controller.convert;

import com.google.common.collect.Lists;
import com.meituan.shangou.goodscenter.dto.DepotGoodsDetailDto;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OCMSOrderVO;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.meituan.shangou.saas.order.platform.enums.OrderStatusEnum;
import com.sankuai.meituan.reco.pickselect.dh.enums.TemperaturePropertyEnum;
import com.sankuai.meituan.reco.pickselect.thrift.picking.rider.dto.TConsumableMaterialInfo;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.operation.request.RiderLocationBatchReportThriftRequest;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.operation.request.RiderLocationInfo;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto.TDeliveryRiskControlOrder;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.deliverychannel.dto.DeliveryChannelDto;
import com.sankuai.shangou.commons.auth.login.context.AppLoginContext;
import com.sankuai.shangou.commons.auth.login.context.LoginUser;
import com.sankuai.shangou.commons.auth.login.context.holder.AppLoginContextHolder;
import com.sankuai.shangou.commons.auth.login.utils.LoginContextUtils;
import com.sankuai.shangou.commons.utils.collection.IListUtils;
import com.sankuai.shangou.commons.utils.time.TimeUtils;
import com.sankuai.shangou.logistics.delivery.questionnaire.dto.DeliveryQuestionnaireDTO;
import com.sankuai.shangou.logistics.warehouse.dto.MaterialTransInfoDto;
import com.sankuai.shangou.logistics.warehouse.dto.PickConsumableItemDTO;
import com.sankuai.shangou.logistics.warehouse.dto.TradeShippingOrderItemDTO;
import com.sankuai.shangou.logistics.warehouse.enums.ConsumableGoodsType;
import com.sankuai.shangou.logistics.warehouse.enums.PromotionMaterialProcessCodeEnum;
import com.sankuai.shangou.logistics.warehouse.enums.TradeShippingOrderStatus;
import com.sankuai.shangou.supplychain.tof.component.TradeOrderInfoComponent;
import com.sankuai.shangou.supplychain.tof.config.LionConfigUtils;
import com.sankuai.shangou.supplychain.tof.controller.vo.delivery.*;
import com.sankuai.shangou.supplychain.tof.controller.vo.delivery.request.RiderLocationBatchReportRequest;
import com.sankuai.shangou.supplychain.tof.controller.vo.order.RevenueDetailVo;
import com.sankuai.shangou.supplychain.tof.controller.vo.order.TradeOrderVO;
import com.sankuai.shangou.supplychain.tof.controller.vo.picking.PickingOrderVO;
import com.sankuai.shangou.supplychain.tof.enums.AggDeliveryPlatformEnum;
import com.sankuai.shangou.supplychain.tof.enums.CouldOperateItemEnum;
import com.sankuai.shangou.supplychain.tof.enums.DeliveryOperateItemEnum;
import com.sankuai.shangou.supplychain.tof.enums.ShowTagEnum;
import com.sankuai.shangou.supplychain.tof.utils.MccConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.BeanUtils;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.sankuai.shangou.supplychain.tof.controller.convert.RiderPickingConvert.parseConsumableMaterialInfo;

/**
 * <AUTHOR>
 * @since 2024/3/19 20:02
 **/
@Slf4j
public class RiderDeliveryConvert {

    //用来标记非自己的运单/拣货单
    public static final int NOT_VALID_STATUS = -1;

    public static List<RiderDeliveryOrderVO> buildRiderDeliveryOrderVOList(Map<TradeOrderInfoComponent.TradeOrderKey, PickingOrderVO> pickingOrderVOMap,
                                                                           Map<TradeOrderInfoComponent.TradeOrderKey, DeliveryOrderVO> deliveryOrderVOMap,
                                                                           Map<TradeOrderInfoComponent.TradeOrderKey, TradeOrderVO> tradeOrderVOMap,
                                                                           Map<TradeOrderInfoComponent.TradeOrderKey, DeliveryExceptionSummaryVO> exceptionSummaryVOMap,
                                                                           Map<TradeOrderInfoComponent.TradeOrderKey, RevenueDetailVo> revenueDetailVoMap,
                                                                           Map<Long, List<DeliveryQuestionnaireDTO>> questionnaireMap,
                                                                           Map<TradeOrderInfoComponent.TradeOrderKey, TDeliveryRiskControlOrder> deliveryRiskControlOrderMap,
                                                                           Map<TradeOrderInfoComponent.TradeOrderKey, Boolean> couldTurnDapDeliveryMap,
                                                                           Map<TradeOrderInfoComponent.TradeOrderKey, Boolean> lackStockMap,
                                                                           Map<TradeOrderInfoComponent.TradeOrderKey, Boolean> highPriceTagMap,
                                                                           Map<TradeOrderInfoComponent.TradeOrderKey, Boolean> sealDeliveryTag,
                                                                           Map<TradeOrderInfoComponent.TradeOrderKey, OCMSOrderVO> ocmsOrderMap,
                                                                           Map<String, Pair<MaterialTransInfoDto, DepotGoodsDetailDto>> tradeOrderKeyAndMaterialSkuInfoMap,
                                                                           Map<Long,List<Integer>> operatorItemMap) {
        return deliveryOrderVOMap.entrySet().stream().map(entry -> {
            TradeOrderInfoComponent.TradeOrderKey tradeOrderKey = entry.getKey();
            DeliveryOrderVO deliveryOrderVO = entry.getValue();
            PickingOrderVO pickingOrderVO = pickingOrderVOMap.get(tradeOrderKey);
            Pair<MaterialTransInfoDto, DepotGoodsDetailDto> materialTransInfoDtoDepotGoodsDetailDtoPair = tradeOrderKeyAndMaterialSkuInfoMap.get(tradeOrderKey.getChannelOrderId());
            OCMSOrderVO ocmsOrderVO = ocmsOrderMap.get(tradeOrderKey);
            List<Integer> operatorItemList = operatorItemMap.get(ocmsOrderVO.getOrderId());
            return buildRiderDeliveryOrderVO(pickingOrderVO, deliveryOrderVO, tradeOrderVOMap.get(tradeOrderKey),
                    exceptionSummaryVOMap.get(tradeOrderKey), revenueDetailVoMap.get(tradeOrderKey),
                    questionnaireMap.get(deliveryOrderVO.getDeliveryOrderId()),
                    deliveryRiskControlOrderMap.get(tradeOrderKey),
                    couldTurnDapDeliveryMap.getOrDefault(tradeOrderKey, false),
                    lackStockMap.getOrDefault(tradeOrderKey, false),
                    highPriceTagMap.getOrDefault(tradeOrderKey, false),
                    sealDeliveryTag.getOrDefault(tradeOrderKey, false),
                    ocmsOrderVO,
                    materialTransInfoDtoDepotGoodsDetailDtoPair,operatorItemList);
        }).collect(Collectors.toList());
    }

    public static List<RiderDeliveryOrderVO> buildBaseRiderDeliveryOrderVOList(Map<TradeOrderInfoComponent.TradeOrderKey, PickingOrderVO> pickingOrderVOMap,
                                                                               Map<TradeOrderInfoComponent.TradeOrderKey, DeliveryOrderVO> deliveryOrderVOMap,
                                                                               Map<TradeOrderInfoComponent.TradeOrderKey, TradeOrderVO> tradeOrderVOMap,
                                                                               Map<TradeOrderInfoComponent.TradeOrderKey, TDeliveryRiskControlOrder> deliveryRiskControlOrderMap,
                                                                               Map<Long,List<Integer>> itemList) {
        return deliveryOrderVOMap.entrySet().stream().map(entry -> {
            TradeOrderInfoComponent.TradeOrderKey tradeOrderKey = entry.getKey();
            DeliveryOrderVO deliveryOrderVO = entry.getValue();
            PickingOrderVO pickingOrderVO = pickingOrderVOMap.get(tradeOrderKey);
            TradeOrderVO tradeOrderVO = tradeOrderVOMap.get(tradeOrderKey);
            List<Integer> operateItemList = itemList.get(tradeOrderVO.getOrderId());
            return buildBaseRiderDeliveryOrderVO(pickingOrderVO, deliveryOrderVO, tradeOrderVO, deliveryRiskControlOrderMap.get(tradeOrderKey),operateItemList);
        }).collect(Collectors.toList());
    }

    public static RiderDeliveryOrderVO buildRiderDeliveryOrderVO(PickingOrderVO pickingOrderVO,
                                                                 DeliveryOrderVO deliveryOrderVO,
                                                                 TradeOrderVO tradeOrderVO,
                                                                 DeliveryExceptionSummaryVO deliveryExceptionSummaryVO,
                                                                 RevenueDetailVo revenueDetailVo,
                                                                 List<DeliveryQuestionnaireDTO> questionnaires,
                                                                 TDeliveryRiskControlOrder tDeliveryRiskControlOrder,
                                                                 Boolean couldTurnDapDelivery,
                                                                 Boolean isLackStock,
                                                                 Boolean isHighPriceOrder,
                                                                 Boolean isSealDelivery,
                                                                 OCMSOrderVO ocmsOrderVO,
                                                                 Pair<MaterialTransInfoDto, DepotGoodsDetailDto> materialTransInfoDtoDepotGoodsDetailDtoPair,
                                                                 List<Integer> operatorItemList) {
        RiderDeliveryOrderVO riderDeliveryOrderVO = new RiderDeliveryOrderVO();

        //运单模块
        riderDeliveryOrderVO.setDeliveryOrderId(deliveryOrderVO.getDeliveryOrderId());
        riderDeliveryOrderVO.setDeliveryStatus(deliveryOrderVO.getDeliveryStatus());
        riderDeliveryOrderVO.setEstimateArriveTimeStart(deliveryOrderVO.getEstimateArriveTimeStart());
        riderDeliveryOrderVO.setEstimateArriveTimeEnd(deliveryOrderVO.getEstimateArriveTimeEnd());
        riderDeliveryOrderVO.setFromRiderName(deliveryOrderVO.getFromRiderName());
        riderDeliveryOrderVO.setReceiverName(deliveryOrderVO.getReceiverName());
        riderDeliveryOrderVO.setReceiverPhone(deliveryOrderVO.getReceiverPhone());
        riderDeliveryOrderVO.setReceiverAddress(deliveryOrderVO.getReceiverAddress());
        riderDeliveryOrderVO.setReceiverLongitude(deliveryOrderVO.getReceiverLongitude());
        riderDeliveryOrderVO.setReceiverLatitude(deliveryOrderVO.getReceiverLatitude());
        riderDeliveryOrderVO.setDeliveryStatusLocked(deliveryOrderVO.getDeliveryStatusLocked());
        riderDeliveryOrderVO.setCanStatusBeLocked(deliveryOrderVO.getCanStatusBeLocked());
        riderDeliveryOrderVO.setEvaluateArriveDeadline(deliveryOrderVO.getEvaluateArriveDeadline());
        riderDeliveryOrderVO.setEvaluateArriveLeftTime(deliveryOrderVO.getEvaluateArriveLeftTime());
        riderDeliveryOrderVO.setEvaluateArriveTimeout(deliveryOrderVO.getEvaluateArriveTimeout());
        riderDeliveryOrderVO.setDeliveryDistance(deliveryOrderVO.getDeliveryDistance());
        riderDeliveryOrderVO.setDeliveryDoneTime(deliveryOrderVO.getDeliveryDoneTime());

        //一元单标签
        riderDeliveryOrderVO.setIsOneYuanOrder(deliveryOrderVO.getIsOneYuanOrder());

        //代收点
        riderDeliveryOrderVO.setSigningPoint(deliveryOrderVO.getSigningPosition());

        //定价路线信息
        riderDeliveryOrderVO.setRouteInfo(deliveryOrderVO.getRouteInfoVO());

        //拣配分离标签
        riderDeliveryOrderVO.setIsPickDeliverySplit(deliveryOrderVO.getIsPickDeliverySplit());
        riderDeliveryOrderVO.setAssessRewardShowType(deliveryOrderVO.getAssessRewardShowType());
        //订单模块
        if (Objects.nonNull(tradeOrderVO)) {
            riderDeliveryOrderVO.setItemCount(tradeOrderVO.getItemCount());
            riderDeliveryOrderVO.setComments(tradeOrderVO.getComments());
            //商品信息 start
            riderDeliveryOrderVO.setProductList(tradeOrderVO.getProductList());
            riderDeliveryOrderVO.setTotalOfflinePrice(tradeOrderVO.getTotalOfflinePrice());
            riderDeliveryOrderVO.setGiftVOList(tradeOrderVO.getGiftVOList());
            riderDeliveryOrderVO.setGiftCount(tradeOrderVO.getGiftCount());
            //礼袋信息
            riderDeliveryOrderVO.setGiftBagList(tradeOrderVO.getGiftBagList());

            //商品信息 end

            riderDeliveryOrderVO.setPayTime(tradeOrderVO.getPayTime());
            riderDeliveryOrderVO.setOrderUserType(tradeOrderVO.getOrderUserType());
            riderDeliveryOrderVO.setCreateTime(tradeOrderVO.getCreateTime());
            riderDeliveryOrderVO.setTenantId(deliveryOrderVO.getTenantId());
            riderDeliveryOrderVO.setChannelId(tradeOrderVO.getChannelId());
            riderDeliveryOrderVO.setChannelName(tradeOrderVO.getChannelName());
            riderDeliveryOrderVO.setStoreId(tradeOrderVO.getStoreId());
            riderDeliveryOrderVO.setStoreName(tradeOrderVO.getStoreName());
            riderDeliveryOrderVO.setChannelOrderId(tradeOrderVO.getChannelOrderId());
            riderDeliveryOrderVO.setSerialNo(tradeOrderVO.getSerialNo());
            riderDeliveryOrderVO.setDeliveryOrderType(tradeOrderVO.getDeliveryOrderType());
            riderDeliveryOrderVO.setDeliveryOrderTypeName(tradeOrderVO.getDeliveryOrderTypeName());
            riderDeliveryOrderVO.setDeliveryMethod(tradeOrderVO.getDeliveryMethod());
            riderDeliveryOrderVO.setDeliveryMethodDesc(tradeOrderVO.getDeliveryMethodDesc());
            riderDeliveryOrderVO.setUserId(tradeOrderVO.getUserId());
            // 展示用户标签
            riderDeliveryOrderVO.setUserTags(tradeOrderVO.getUserTags());
            riderDeliveryOrderVO.setSortedTagList(tradeOrderVO.getSortedTagList());
            riderDeliveryOrderVO.setOrderUserType(tradeOrderVO.getOrderUserType());
            riderDeliveryOrderVO.setEmpowerOrderId(tradeOrderVO.getOrderId());

            //餐馆标签
            riderDeliveryOrderVO.setScene(tradeOrderVO.getScene());

            //展示标签汇集
            List<ShowTagVO> showTags = new ArrayList<>();
            if (Objects.nonNull(tradeOrderVO.getIsWiderShippingArea()) && tradeOrderVO.getIsWiderShippingArea()) {
                ShowTagVO widerTag = new ShowTagVO();
                widerTag.setTagDesc(LionConfigUtils.getTagHint(ShowTagEnum.WIDER_SHIPPING_AREA.getLionKey(), ShowTagEnum.WIDER_SHIPPING_AREA.getDefaultValue()));
                widerTag.setTagCode(ShowTagEnum.WIDER_SHIPPING_AREA.getCode());
                showTags.add(widerTag);
            }
            riderDeliveryOrderVO.setShowTags(showTags);

            //用户尾号
            riderDeliveryOrderVO.setReceiverTailPhoneNumber(tradeOrderVO.getReceiverTailPhoneNumber());

            //只有餐馆 && 实时单场景才展示奖励
            if (StringUtils.isNotBlank(tradeOrderVO.getScene())
                    && LionConfigUtils.getDhRestaurantSceneList().contains(tradeOrderVO.getScene())
                    && Objects.equals(tradeOrderVO.getDeliveryOrderType(), 1)
            ) {
                riderDeliveryOrderVO.setShowRewardHint(deliveryOrderVO.getShowRewardHint());
            }

            riderDeliveryOrderVO.setDeliveryPosition(tradeOrderVO.getDeliveryPosition());

            // 发财酒标识
            riderDeliveryOrderVO.setIsFacaiWine(tradeOrderVO.getIsFacaiWine());
        }

        //营收模块
        if (Objects.nonNull(revenueDetailVo)) {
            riderDeliveryOrderVO.setRevenueDetail(revenueDetailVo);
        }

        //配送异常模块
        riderDeliveryOrderVO.setDeliveryExceptionSummaryVOS(deliveryExceptionSummaryVO);

        //问卷调查模块
        if (CollectionUtils.isNotEmpty(questionnaires)) {
            //有没填完的问卷
            riderDeliveryOrderVO.setIsNeedAnswerQuestionnaire(questionnaires.stream()
                    .anyMatch(questionnaire -> StringUtils.isBlank(questionnaire.getAnswer())));
        }

        //风控单标签
        riderDeliveryOrderVO.setIsRiskControlOrder(Objects.nonNull(tDeliveryRiskControlOrder));

        //转三方按钮
        if (Objects.equals(couldTurnDapDelivery, true)) {
            riderDeliveryOrderVO.setCouldOperateItemList(Collections.singletonList(CouldOperateItemEnum.TURN_AGG_DELIVERY.getValue()));
        }

        riderDeliveryOrderVO.setDeliveryOperateItems(DeliveryOperateItemEnum.tmsItemListToOperateItemIntegerList(operatorItemList));

        //是否缺货
        riderDeliveryOrderVO.setHasLackGoods(isLackStock);

        //封签交付标签
        riderDeliveryOrderVO.setIsSealDelivery(Objects.equals(isSealDelivery, true));

        //高价值标签
        riderDeliveryOrderVO.setIsContainsHighWaxGoods(Objects.equals(isHighPriceOrder, true));

        //出库信息
        if (Objects.nonNull(pickingOrderVO)) {
            riderDeliveryOrderVO.setPickFinished(Objects.equals(pickingOrderVO.getStatus(), TradeShippingOrderStatus.FINISH.getCode()));
            riderDeliveryOrderVO.setIsPickDeliverySplit(pickingOrderVO.getIsPickDeliverySplit());
            riderDeliveryOrderVO.setPickDoneTime(TimeUtils.toMilliSeconds(pickingOrderVO.getShipTime()));
            //展示逻辑：展示拣货人，出库后展示封签码（如果有）
            TakenGoodsInfo takenGoodsInfo = new TakenGoodsInfo();
            if (StringUtils.isNotBlank(pickingOrderVO.getOperatorName())) {
                takenGoodsInfo.setOperatorName(pickingOrderVO.getOperatorName());
            }
            if (!Objects.equals(pickingOrderVO.getStatus(), TradeShippingOrderStatus.RECEIVE.getCode())) {
                takenGoodsInfo.setStockOutPics(pickingOrderVO.getPickingCheckPictureUrlList());
                takenGoodsInfo.setSealCodes(
                        IListUtils.nullSafeStream(pickingOrderVO.getPickConsumableItems())
                                .filter(pickConsumableItemDTO -> Objects.equals(pickConsumableItemDTO.getSkuId(), MccConfigUtil.getConsumableByType(pickingOrderVO.getWarehouseId(), ConsumableGoodsType.SEAL_TAG).get(0).getSkuId()))
                                .flatMap(pickConsumableItemDTO -> IListUtils.nullSafeStream(pickConsumableItemDTO.getEnteringTypeInfos()))
                                .map(PickConsumableItemDTO.EnteringTypeInfoDTO::getCode)
                                .collect(Collectors.toList())
                );
            }
            riderDeliveryOrderVO.setTakenGoodsInfo(takenGoodsInfo);
        }

        //货品项信息
        if (Objects.nonNull(pickingOrderVO) && LionConfigUtils.isShowGoodsItemListGrayStore(pickingOrderVO.getWarehouseId())) {
            riderDeliveryOrderVO.setGoodsItemList(transfer2GoodsItemVO(pickingOrderVO.getItems()));
        }

        //耗材信息
        if (Objects.nonNull(ocmsOrderVO) && LionConfigUtils.isShowGoodsItemListGrayStore(ocmsOrderVO.getShopId())) {
            riderDeliveryOrderVO.setNeedWineBottleOpener(
                    parseConsumableMaterialInfo(ocmsOrderVO)
                            .stream()
                            .anyMatch(consumable ->  {
                                return LionConfigUtils.getWineBottleOpenerSkuIds().contains(consumable.getSkuId()) && Objects.nonNull(consumable.getCount()) && consumable.getCount() > 0;
                            }));
        }
        if (Objects.nonNull(ocmsOrderVO)) {
            // 名酒馆标识
            riderDeliveryOrderVO.setIsMtFamousTavern(ocmsOrderVO.getIsMtFamousTavern());
        }

        //已送达的发财酒订单 展示地推二维码的倒计时
        countFacaiQrCodeLeftTime(deliveryOrderVO, riderDeliveryOrderVO);

        //额外商品信息（发财酒）
        if (Objects.nonNull(materialTransInfoDtoDepotGoodsDetailDtoPair)) {
            //拣配分离就直接展示,非拣配分离就要发生过转单
            if (deliveryOrderVO.getIsPickDeliverySplit() || StringUtils.isNotBlank(deliveryOrderVO.getFromRiderName())) {
                ExternalProductVO externalProductVO = buildExternalProductVO(materialTransInfoDtoDepotGoodsDetailDtoPair);
                externalProductVO.setCount(materialTransInfoDtoDepotGoodsDetailDtoPair.getKey().getOperateCount());
                riderDeliveryOrderVO.setExternalProductList(Lists.newArrayList(externalProductVO));
                if (Objects.equals(materialTransInfoDtoDepotGoodsDetailDtoPair.getKey().getProcessCode(), PromotionMaterialProcessCodeEnum.RIDER_CHANGE.getCode())) {
                    riderDeliveryOrderVO.setIsAfterRiderTakenTransfer(true);
                }
            }
        }

        return riderDeliveryOrderVO;
    }

    private static ExternalProductVO buildExternalProductVO(Pair<MaterialTransInfoDto, DepotGoodsDetailDto> materialTransInfoDtoDepotGoodsDetailDtoPair) {
        ExternalProductVO externalProductVO = new ExternalProductVO();
        externalProductVO.setProductName(materialTransInfoDtoDepotGoodsDetailDtoPair.getValue().getGoodsName());
        externalProductVO.setSpecification(materialTransInfoDtoDepotGoodsDetailDtoPair.getValue().getSpecName());
        if (Objects.nonNull(materialTransInfoDtoDepotGoodsDetailDtoPair.getValue().getGoodsPic()) && CollectionUtils.isNotEmpty(materialTransInfoDtoDepotGoodsDetailDtoPair.getValue().getGoodsPic().getRealPicUrlList())) {
            externalProductVO.setPicUrl(materialTransInfoDtoDepotGoodsDetailDtoPair.getValue().getGoodsPic().getRealPicUrlList().get(0));
        }
        return externalProductVO;
    }

    private static void countFacaiQrCodeLeftTime(DeliveryOrderVO deliveryOrderVO, RiderDeliveryOrderVO riderDeliveryOrderVO) {
        try {
            if (Objects.nonNull(deliveryOrderVO.getDeliveryDoneTime()) && riderDeliveryOrderVO.getIsFacaiWine()) {
                long qrEndTime = deliveryOrderVO.getDeliveryDoneTime() + LionConfigUtils.getFacaiQrCodeValidMin() * 60 * 1000;
                long currentTime = System.currentTimeMillis();
                riderDeliveryOrderVO.setFacaiQrLeftTime(qrEndTime - currentTime);
            }
        } catch (Exception e) {
            log.error("buildBaseRiderDeliveryOrderVO 计算发财酒二维码倒计时异常", e);
        }
    }

    private static RiderDeliveryOrderVO buildBaseRiderDeliveryOrderVO(PickingOrderVO pickingOrderVO,
                                                                      DeliveryOrderVO deliveryOrderVO,
                                                                      TradeOrderVO tradeOrderVO,
                                                                      TDeliveryRiskControlOrder tDeliveryRiskControlOrder,
                                                                      List<Integer> operateItemList) {
        RiderDeliveryOrderVO riderDeliveryOrderVO = new RiderDeliveryOrderVO();

        //运单模块
        riderDeliveryOrderVO.setDeliveryOrderId(deliveryOrderVO.getDeliveryOrderId());
        riderDeliveryOrderVO.setDeliveryStatus(deliveryOrderVO.getOriginalDeliveryStatus());
        riderDeliveryOrderVO.setEstimateArriveTimeStart(deliveryOrderVO.getEstimateArriveTimeStart());
        riderDeliveryOrderVO.setEstimateArriveTimeEnd(deliveryOrderVO.getEstimateArriveTimeEnd());
        riderDeliveryOrderVO.setFromRiderName(deliveryOrderVO.getFromRiderName());
        riderDeliveryOrderVO.setReceiverName(deliveryOrderVO.getReceiverName());
        riderDeliveryOrderVO.setReceiverPhone(deliveryOrderVO.getReceiverPhone());
        riderDeliveryOrderVO.setReceiverAddress(deliveryOrderVO.getReceiverAddress());
        riderDeliveryOrderVO.setReceiverLongitude(deliveryOrderVO.getReceiverLongitude());
        riderDeliveryOrderVO.setReceiverLatitude(deliveryOrderVO.getReceiverLatitude());
        riderDeliveryOrderVO.setDeliveryStatusLocked(deliveryOrderVO.getDeliveryStatusLocked());
        riderDeliveryOrderVO.setCanStatusBeLocked(deliveryOrderVO.getCanStatusBeLocked());
        riderDeliveryOrderVO.setEvaluateArriveDeadline(deliveryOrderVO.getEvaluateArriveDeadline());
        riderDeliveryOrderVO.setEvaluateArriveLeftTime(deliveryOrderVO.getEvaluateArriveLeftTime());
        riderDeliveryOrderVO.setEvaluateArriveTimeout(deliveryOrderVO.getEvaluateArriveTimeout());
        riderDeliveryOrderVO.setDeliveryDistance(deliveryOrderVO.getDeliveryDistance());
        riderDeliveryOrderVO.setDeliveryDoneTime(deliveryOrderVO.getDeliveryDoneTime());

        //名酒馆标记
        riderDeliveryOrderVO.setIsMtFamousTavern(deliveryOrderVO.getIsMtFamousTavern());

        //发财酒标记
        riderDeliveryOrderVO.setIsFacaiWine(tradeOrderVO.getIsFacaiWine());

        //一元单标签
        riderDeliveryOrderVO.setIsOneYuanOrder(deliveryOrderVO.getIsOneYuanOrder());

        //代收点
        riderDeliveryOrderVO.setSigningPoint(deliveryOrderVO.getSigningPosition());

        //定价路线信息
        riderDeliveryOrderVO.setRouteInfo(deliveryOrderVO.getRouteInfoVO());

        //拣配分离标签
        riderDeliveryOrderVO.setIsPickDeliverySplit(deliveryOrderVO.getIsPickDeliverySplit());

        //三方相关
        riderDeliveryOrderVO.setIsThirdDelivery(deliveryOrderVO.getIsThirdDelivery());

        //订单取消 或 订单完成但运单取消 都不展示
        if(!Objects.equals(tradeOrderVO.getOrderStatus(), OrderStatusEnum.CANCELED.getValue())
                && !(Objects.equals(deliveryOrderVO.getDeliveryStatus(), DeliveryStatusEnum.DELIVERY_CANCELLED.getCode()) && Objects.equals(tradeOrderVO.getOrderStatus(), OrderStatusEnum.COMPLETED.getValue()))) {
            riderDeliveryOrderVO.setIsThirdException(deliveryOrderVO.getIsThirdException());
        } else {
            riderDeliveryOrderVO.setIsThirdException(false);
        }

        riderDeliveryOrderVO.setDeliveryChannelId(deliveryOrderVO.getDeliveryChannelId());
        riderDeliveryOrderVO.setDeliveryPlatformCode(deliveryOrderVO.getDeliveryPlatformCode());
        riderDeliveryOrderVO.setDeliveryPlatformDesc(deliveryOrderVO.getDeliveryPlatformDesc());
        riderDeliveryOrderVO.setDistributeStatusDesc(DeliveryStatusEnum.valueOf(riderDeliveryOrderVO.getDeliveryStatus()).getDesc());
        //用运单创建时间标识拣货单下发时间
        riderDeliveryOrderVO.setPickPushDownTime(deliveryOrderVO.getCreateTime());
        riderDeliveryOrderVO.setAssessRewardShowType(deliveryOrderVO.getAssessRewardShowType());

        //订单模块
        if (Objects.nonNull(tradeOrderVO)) {
            riderDeliveryOrderVO.setItemCount(tradeOrderVO.getItemCount());
            riderDeliveryOrderVO.setComments(tradeOrderVO.getComments());
            //商品信息 start
            riderDeliveryOrderVO.setProductList(tradeOrderVO.getProductList());
            riderDeliveryOrderVO.setTotalOfflinePrice(tradeOrderVO.getTotalOfflinePrice());
            riderDeliveryOrderVO.setGiftVOList(tradeOrderVO.getGiftVOList());
            riderDeliveryOrderVO.setGiftCount(tradeOrderVO.getGiftCount());
            //礼袋信息
            riderDeliveryOrderVO.setGiftBagList(tradeOrderVO.getGiftBagList());

            //商品信息 end

            riderDeliveryOrderVO.setPayTime(tradeOrderVO.getPayTime());
            riderDeliveryOrderVO.setOrderUserType(tradeOrderVO.getOrderUserType());
            riderDeliveryOrderVO.setCreateTime(tradeOrderVO.getCreateTime());
            riderDeliveryOrderVO.setTenantId(deliveryOrderVO.getTenantId());
            riderDeliveryOrderVO.setChannelId(tradeOrderVO.getChannelId());
            riderDeliveryOrderVO.setChannelName(tradeOrderVO.getChannelName());
            riderDeliveryOrderVO.setStoreId(tradeOrderVO.getStoreId());
            riderDeliveryOrderVO.setStoreName(tradeOrderVO.getStoreName());
            riderDeliveryOrderVO.setChannelOrderId(tradeOrderVO.getChannelOrderId());
            riderDeliveryOrderVO.setSerialNo(tradeOrderVO.getSerialNo());
            riderDeliveryOrderVO.setDeliveryOrderType(tradeOrderVO.getDeliveryOrderType());
            riderDeliveryOrderVO.setDeliveryOrderTypeName(tradeOrderVO.getDeliveryOrderTypeName());
            riderDeliveryOrderVO.setDeliveryMethod(tradeOrderVO.getDeliveryMethod());
            riderDeliveryOrderVO.setDeliveryMethodDesc(tradeOrderVO.getDeliveryMethodDesc());
            riderDeliveryOrderVO.setUserId(tradeOrderVO.getUserId());
            // 展示用户标签
            riderDeliveryOrderVO.setUserTags(tradeOrderVO.getUserTags());
            //
            riderDeliveryOrderVO.setSortedTagList(tradeOrderVO.getSortedTagList());
            riderDeliveryOrderVO.setOrderUserType(tradeOrderVO.getOrderUserType());

            //餐馆标签
            riderDeliveryOrderVO.setScene(tradeOrderVO.getScene());

            //展示标签汇集
            List<ShowTagVO> showTags = new ArrayList<>();
            if (Objects.nonNull(tradeOrderVO.getIsWiderShippingArea()) && tradeOrderVO.getIsWiderShippingArea()) {
                ShowTagVO widerTag = new ShowTagVO();
                widerTag.setTagDesc(LionConfigUtils.getTagHint(ShowTagEnum.WIDER_SHIPPING_AREA.getLionKey(), ShowTagEnum.WIDER_SHIPPING_AREA.getDefaultValue()));
                widerTag.setTagCode(ShowTagEnum.WIDER_SHIPPING_AREA.getCode());
                showTags.add(widerTag);
            }
            riderDeliveryOrderVO.setShowTags(showTags);

            //用户尾号
            riderDeliveryOrderVO.setReceiverTailPhoneNumber(tradeOrderVO.getReceiverTailPhoneNumber());

            //只有餐馆 && 实时单场景才展示奖励
            if (StringUtils.isNotBlank(tradeOrderVO.getScene())
                    && LionConfigUtils.getDhRestaurantSceneList().contains(tradeOrderVO.getScene())
                    && Objects.equals(tradeOrderVO.getDeliveryOrderType(), 1)
            ) {
                riderDeliveryOrderVO.setShowRewardHint(deliveryOrderVO.getShowRewardHint());
            }
            riderDeliveryOrderVO.setEmpowerOrderId(tradeOrderVO.getOrderId());

            riderDeliveryOrderVO.setDeliveryPosition(tradeOrderVO.getDeliveryPosition());
        }


        if (Objects.nonNull(pickingOrderVO)) {
            riderDeliveryOrderVO.setPickFinished(Objects.equals(pickingOrderVO.getStatus(), TradeShippingOrderStatus.FINISH.getCode()));
            riderDeliveryOrderVO.setIsPickDeliverySplit(pickingOrderVO.getIsPickDeliverySplit());
            riderDeliveryOrderVO.setPickDoneTime(TimeUtils.toMilliSeconds(pickingOrderVO.getShipTime()));
            //展示逻辑：展示拣货人，出库后展示封签码（如果有）
            TakenGoodsInfo takenGoodsInfo = new TakenGoodsInfo();
            //todo: 待拣货融合
            takenGoodsInfo.setOutboundStatus(pickingOrderVO.getStatus());
            if (StringUtils.isNotBlank(pickingOrderVO.getOperatorName())) {
                takenGoodsInfo.setOperatorName(pickingOrderVO.getOperatorName());
            }
            if (!Objects.equals(pickingOrderVO.getStatus(), TradeShippingOrderStatus.RECEIVE.getCode())) {
                takenGoodsInfo.setStockOutPics(pickingOrderVO.getPickingCheckPictureUrlList());
                takenGoodsInfo.setSealCodes(
                        IListUtils.nullSafeStream(pickingOrderVO.getPickConsumableItems())
                                .filter(pickConsumableItemDTO -> Objects.equals(pickConsumableItemDTO.getSkuId(), MccConfigUtil.getConsumableByType(pickingOrderVO.getWarehouseId(), ConsumableGoodsType.SEAL_TAG).get(0).getSkuId()))
                                .flatMap(pickConsumableItemDTO -> IListUtils.nullSafeStream(pickConsumableItemDTO.getEnteringTypeInfos()))
                                .map(PickConsumableItemDTO.EnteringTypeInfoDTO::getCode)
                                .collect(Collectors.toList())
                );
            }
            riderDeliveryOrderVO.setTakenGoodsInfo(takenGoodsInfo);

            //只有当前用户id相等时才返回
            if (Objects.isNull(pickingOrderVO.getOperatorId()) || pickingOrderVO.getOperatorId() <= 0L || Objects.equals(pickingOrderVO.getOperatorId(), LoginContextUtils.getAppLoginAccountId())) {
                riderDeliveryOrderVO.setPickStatus(pickingOrderVO.getStatus());
            }
        }

        //货品项信息
        if (Objects.nonNull(pickingOrderVO) && LionConfigUtils.isShowGoodsItemListGrayStore(pickingOrderVO.getWarehouseId())) {
            riderDeliveryOrderVO.setGoodsItemList(transfer2GoodsItemVO(pickingOrderVO.getItems()));
        }

        //耗材信息
        if (LionConfigUtils.isShowGoodsItemListGrayStore(tradeOrderVO.getStoreId())) {
            //解析耗材
            List<TConsumableMaterialInfo> materialInfos= parseConsumableMaterialInfo(tradeOrderVO);

            //判断是否包含红酒开瓶器
            boolean needWineBottleOpener = materialInfos.stream()
                    .anyMatch(consumable -> {
                        return LionConfigUtils.getWineBottleOpenerSkuIds().contains(consumable.getSkuId()) && Objects.nonNull(consumable.getCount()) && consumable.getCount() > 0;
                    });
            riderDeliveryOrderVO.setNeedWineBottleOpener(needWineBottleOpener);

        }

        riderDeliveryOrderVO.setDeliveryOperateItems(DeliveryOperateItemEnum.tmsItemListToOperateItemIntegerList(operateItemList));

        return riderDeliveryOrderVO;
    }

    public static List<RiderPickingOrderVO> buildBaseRiderPickingOrderVOList(Map<TradeOrderInfoComponent.TradeOrderKey, PickingOrderVO> pickingOrderVOMap,
                                                                             Map<TradeOrderInfoComponent.TradeOrderKey, DeliveryOrderVO> deliveryOrderVOMap,
                                                                             Map<TradeOrderInfoComponent.TradeOrderKey, TradeOrderVO> tradeOrderVOMap,
                                                                             Map<TradeOrderInfoComponent.TradeOrderKey, TDeliveryRiskControlOrder> deliveryRiskControlOrderMap) {

        return pickingOrderVOMap.entrySet().stream()
                .filter(entry -> deliveryOrderVOMap.containsKey(entry.getKey()))
                .map(entry -> {
                    TradeOrderInfoComponent.TradeOrderKey tradeOrderKey = entry.getKey();
                    PickingOrderVO pickingOrderVO = entry.getValue();
                    DeliveryOrderVO deliveryOrderVO = deliveryOrderVOMap.get(tradeOrderKey);
                    return buildBaseRiderPickingOrderVO(pickingOrderVO, deliveryOrderVO, tradeOrderVOMap.get(tradeOrderKey),
                            deliveryRiskControlOrderMap.get(tradeOrderKey)
                    );
                }).collect(Collectors.toList());
    }

    public static List<GoodsItemVO> transfer2GoodsItemVO(List<TradeShippingOrderItemDTO> tradeShippingOrderItemDTOList) {
        try {
            return Optional.ofNullable(tradeShippingOrderItemDTOList)
                    .orElse(Collections.emptyList())
                    .stream()
                    .collect(Collectors.groupingBy(TradeShippingOrderItemDTO::getSkuId))
                    .values()
                    .stream()
                    .map(tradeShippingOrderItemDTOS -> {
                                Map<String, Integer> temperature2CountMap = tradeShippingOrderItemDTOS
                                        .stream()
                                        .collect(Collectors.toMap(item -> Objects.isNull(item.getTemperatureZoneCode()) ? StringUtils.EMPTY : item.getTemperatureZoneCode(),
                                                item -> item.getActualQty().intValue(), Integer::sum));
                                TradeShippingOrderItemDTO orderItemDTO = tradeShippingOrderItemDTOS.get(0);
                                GoodsItemVO goodsItemVO = new GoodsItemVO();
                                goodsItemVO.setGoodsId(orderItemDTO.getSkuId());
                                goodsItemVO.setGoodsName(orderItemDTO.getSkuName());
                                goodsItemVO.setRealPicUrlList(orderItemDTO.getImgUrls());
                                goodsItemVO.setSpecification(orderItemDTO.getSpecification());
                                goodsItemVO.setIceCount(temperature2CountMap.getOrDefault(TemperaturePropertyEnum.ICE_TEMPERATURE.getDesc(), 0));
                                goodsItemVO.setNormalCount(temperature2CountMap.getOrDefault(TemperaturePropertyEnum.NORMAL_TEMPERATURE.getDesc(), 0));
                                goodsItemVO.setNoTemperatureCount(temperature2CountMap.getOrDefault(StringUtils.EMPTY, 0));
                                goodsItemVO.setDependOnRemarkCount(temperature2CountMap.getOrDefault(TemperaturePropertyEnum.USER_DEFINED.getDesc(), 0));
                                return goodsItemVO;
                            }
                    )
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("transfer2GoodsItemVO error", e);
            return Collections.emptyList();
        }

    }

    private static RiderPickingOrderVO buildBaseRiderPickingOrderVO(PickingOrderVO pickingOrderVO,
                                                                    DeliveryOrderVO deliveryOrderVO,
                                                                    TradeOrderVO tradeOrderVO,
                                                                    TDeliveryRiskControlOrder tDeliveryRiskControlOrder) {
        RiderDeliveryOrderVO riderDeliveryOrderVO = buildBaseRiderDeliveryOrderVO(pickingOrderVO, deliveryOrderVO, tradeOrderVO, tDeliveryRiskControlOrder,null);

        //operator id一样才返回
        if (Objects.nonNull(deliveryOrderVO.getRiderAccountId()) && deliveryOrderVO.getRiderAccountId() > 0L && !Objects.equals(deliveryOrderVO.getRiderAccountId(), LoginContextUtils.getAppLoginAccountId())) {
            riderDeliveryOrderVO.setDeliveryStatus(NOT_VALID_STATUS);
        }

        //修改拣货单不同的地方
        long evaluateArriveDeadline = deliveryOrderVO.getCreateTime() + LionConfigUtils.getPickTimoutDurationMills();
        riderDeliveryOrderVO.setEvaluateArriveDeadlineForPick(evaluateArriveDeadline);

        if (Objects.equals(TradeShippingOrderStatus.FINISH.getCode(), pickingOrderVO.getStatus())) {
            riderDeliveryOrderVO.setEvaluateArriveLeftTimeForPick(0L);
            if (pickingOrderVO.getShipTime() != null
                    && TimeUtils.toMilliSeconds(pickingOrderVO.getShipTime()) > evaluateArriveDeadline) {
                // 已拣货完成，已超时
                riderDeliveryOrderVO.setEvaluateArriveTimeoutForPick(TimeUtils.toMilliSeconds(pickingOrderVO.getShipTime()) - evaluateArriveDeadline);

            } else {
                // 已拣货完成，未超时
                riderDeliveryOrderVO.setEvaluateArriveTimeoutForPick(0L);
            }
        } else if (Objects.equals(TradeShippingOrderStatus.WAITED.getCode(), pickingOrderVO.getStatus())
                || Objects.equals(TradeShippingOrderStatus.RECEIVE.getCode(), pickingOrderVO.getStatus())) {
            long tsNow = System.currentTimeMillis();
            if (tsNow > evaluateArriveDeadline) {
                // 配送中，已经超时
                riderDeliveryOrderVO.setEvaluateArriveLeftTimeForPick(0L);
                riderDeliveryOrderVO.setEvaluateArriveTimeoutForPick(tsNow - evaluateArriveDeadline);
            } else {
                // 配送中，还未超时
                riderDeliveryOrderVO.setEvaluateArriveLeftTimeForPick(evaluateArriveDeadline - tsNow);
                riderDeliveryOrderVO.setEvaluateArriveTimeoutForPick(0L);
            }
        } else {
            // 其他状态暂时无需处理
        }

        RiderPickingOrderVO riderPickingOrderVO = new RiderPickingOrderVO();
        BeanUtils.copyProperties(riderDeliveryOrderVO, riderPickingOrderVO);
        return riderPickingOrderVO;
    }


    public static List<AggregateOrderVO> updateRiderPickingOrderVOList(
            List<AggregateOrderVO> aggregateOrderVOS,
            Map<TradeOrderInfoComponent.TradeOrderKey, DeliveryExceptionSummaryVO> exceptionSummaryVOMap,
            Map<TradeOrderInfoComponent.TradeOrderKey, RevenueDetailVo> revenueDetailVoMap,
            Map<Long, List<DeliveryQuestionnaireDTO>> questionnaireMap,
            Map<TradeOrderInfoComponent.TradeOrderKey, Boolean> couldTurnDapDeliveryMap,
            Map<TradeOrderInfoComponent.TradeOrderKey, Boolean> lackStockMap,
            Map<TradeOrderInfoComponent.TradeOrderKey, Boolean> highPriceTagMap,
            Map<TradeOrderInfoComponent.TradeOrderKey, Boolean> sealDeliveryTagMap,
            Map<Integer, DeliveryChannelDto> deliveryChannelDtoMap,
            Map<String, String> urlMap,
            Map<TradeOrderInfoComponent.TradeOrderKey, List<Integer>> couldOperateMap,
            Map<String, Pair<MaterialTransInfoDto, DepotGoodsDetailDto>> tradeOrderNoAndMaterialSkuInfoMap) {

        return aggregateOrderVOS.stream().map(aggregateOrderVO -> {
            if (aggregateOrderVO instanceof RiderDeliveryOrderVO) {
                RiderDeliveryOrderVO riderDeliveryOrderVO = (RiderDeliveryOrderVO) aggregateOrderVO;
                TradeOrderInfoComponent.TradeOrderKey key = new TradeOrderInfoComponent.TradeOrderKey(DynamicOrderBizType.channelId2OrderBizType(riderDeliveryOrderVO.getChannelId()).getValue(), riderDeliveryOrderVO.getChannelOrderId());
                riderDeliveryOrderVO.setDeliveryExceptionSummaryVOS(exceptionSummaryVOMap.get(key));

                //营收模块
                if (Objects.nonNull(revenueDetailVoMap.get(key))) {
                    riderDeliveryOrderVO.setRevenueDetail(revenueDetailVoMap.get(key));
                }
                //问卷调查模块
                if (CollectionUtils.isNotEmpty(questionnaireMap.get(riderDeliveryOrderVO.getDeliveryOrderId()))) {
                    //有没填完的问卷
                    riderDeliveryOrderVO.setIsNeedAnswerQuestionnaire(questionnaireMap.get(riderDeliveryOrderVO.getDeliveryOrderId()).stream()
                            .anyMatch(questionnaire -> StringUtils.isBlank(questionnaire.getAnswer())));
                }
                //可操作按钮
                if (Objects.equals(couldTurnDapDeliveryMap.get(key), true)) {
                    riderDeliveryOrderVO.setCouldOperateItemList(Collections.singletonList(CouldOperateItemEnum.TURN_AGG_DELIVERY.getValue()));
                }

                riderDeliveryOrderVO.setHasLackGoods(lackStockMap.getOrDefault(key, false));
                riderDeliveryOrderVO.setIsContainsHighWaxGoods(highPriceTagMap.getOrDefault(key, false));
                riderDeliveryOrderVO.setIsSealDelivery(sealDeliveryTagMap.getOrDefault(key, false));

                //三方按钮+跳转
                AggDeliveryPlatformEnum aggDeliveryPlatformEnum = AggDeliveryPlatformEnum.channelCodeValueOf(riderDeliveryOrderVO.getDeliveryChannelId());
                if (Objects.nonNull(aggDeliveryPlatformEnum) && aggDeliveryPlatformEnum.checkChannel(riderDeliveryOrderVO.getDeliveryChannelId())) {
                    riderDeliveryOrderVO.setDeliveryPlatformCode(AggDeliveryPlatformEnum.DAP_DELIVERY.getCode());
                    //配送拒单，也不展示链接
                    if (riderDeliveryOrderVO.getDeliveryStatus() == null || riderDeliveryOrderVO.getDeliveryStatus() <= DeliveryStatusEnum.INIT.getCode() ||
                            riderDeliveryOrderVO.getDeliveryStatus() == DeliveryStatusEnum.DELIVERY_REJECTED.getCode()) {
                        DeliveryRedirectModuleVo deliveryRedirectModuleVo = new DeliveryRedirectModuleVo();
                        deliveryRedirectModuleVo.setTitle("暂无配送状态");
                        deliveryRedirectModuleVo.setShowButton(false);
                        riderDeliveryOrderVO.setDeliveryRedirectModule(deliveryRedirectModuleVo);

                    } else {
                        String url = urlMap.get(riderDeliveryOrderVO.getEmpowerOrderId().toString());
                        DeliveryRedirectModuleVo deliveryRedirectModuleVo = AggDeliveryPlatformEnum.DAP_DELIVERY.fillDeliveryRedirectModule(url, Optional.ofNullable(riderDeliveryOrderVO.getIsThirdException()).orElse(false));
                        riderDeliveryOrderVO.setDeliveryRedirectModule(deliveryRedirectModuleVo);
                    }
                }

                List<Integer> finalCouldOperateItems = Lists.newArrayList();
                finalCouldOperateItems.addAll(Optional.ofNullable(riderDeliveryOrderVO.getOrderCouldOperateItems()).orElse(Lists.newArrayList()));
                finalCouldOperateItems.addAll(couldOperateMap.getOrDefault(key, Lists.newArrayList()));
                riderDeliveryOrderVO.setOrderCouldOperateItems(finalCouldOperateItems);

                //发财酒信息
                if (tradeOrderNoAndMaterialSkuInfoMap.containsKey(key.getChannelOrderId())) {
                    //拣配分离就直接展示,非拣配分离就要发生过转单
                    if (riderDeliveryOrderVO.getIsPickDeliverySplit() || StringUtils.isNotBlank(riderDeliveryOrderVO.getFromRiderName())) {
                        Pair<MaterialTransInfoDto, DepotGoodsDetailDto> materialPair = tradeOrderNoAndMaterialSkuInfoMap.get(key.getChannelOrderId());
                        ExternalProductVO externalProductVO = buildExternalProductVO(materialPair);
                        externalProductVO.setCount(materialPair.getKey().getOperateCount());
                        riderDeliveryOrderVO.setExternalProductList(Lists.newArrayList(externalProductVO));
                        if (Objects.equals(materialPair.getKey().getProcessCode(), PromotionMaterialProcessCodeEnum.RIDER_CHANGE.getCode())) {
                            riderDeliveryOrderVO.setIsAfterRiderTakenTransfer(true);
                        }
                    }
                }

                //TEMP: 融合一阶段，为赋能租户填充拣货信息
                if (!LionConfigUtils.getDrunkHorseTenantIds().contains(riderDeliveryOrderVO.getTenantId())) {
                    riderDeliveryOrderVO.setPickFinished(true);
                    riderDeliveryOrderVO.setIsThirdDelivery(false);
                    riderDeliveryOrderVO.setIsPickDeliverySplit(true);
                }
                return riderDeliveryOrderVO;
            }
            return null;
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }


    /**
     * 骑手点位上报转换tms请求对象
     */
    public static RiderLocationBatchReportThriftRequest riderLocationConvertThriftRequest(RiderLocationBatchReportRequest request) {
        RiderLocationBatchReportThriftRequest thriftRequest = new RiderLocationBatchReportThriftRequest();
        AppLoginContext appLoginContext = AppLoginContextHolder.getAppLoginContext();
        LoginUser loginUser = appLoginContext.getLoginUser();
        thriftRequest.setTenantId(loginUser.getTenantId());
        thriftRequest.setStoreId(request.getStoreId());
        thriftRequest.setRiderAccountId(loginUser.getAccountId());
        thriftRequest.setRiderName(loginUser.getEmployeeName());
        thriftRequest.setRiderPhone(loginUser.getEmployeePhone());
        thriftRequest.setExtraParams(request.getExtraParams());
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        thriftRequest.setLocationInfoList(request.getLocationInfoList()
                .stream()
                .map(locationInfo -> {
                    RiderLocationInfo riderLocationInfo = new RiderLocationInfo();
                    riderLocationInfo.setLongitude(locationInfo.getLongitude());
                    riderLocationInfo.setLatitude(locationInfo.getLatitude());
                    riderLocationInfo.setProvider(locationInfo.getProvider());
                    riderLocationInfo.setAccuracy(locationInfo.getAccuracy());
                    riderLocationInfo.setBearing(locationInfo.getBearing());
                    riderLocationInfo.setSpeed(locationInfo.getSpeed());
                    String time;
                    try {
                        time = simpleDateFormat.parse(locationInfo.getTime()).getTime() / 1000 + "";
                    } catch (Exception e) {
                        return null;
                    }
                    riderLocationInfo.setTime(time);
                    return riderLocationInfo;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList()));
        thriftRequest.setOs(appLoginContext.getOs());
        thriftRequest.setAppVersion(appLoginContext.getAppVersion());
        thriftRequest.setUuid(appLoginContext.getUuid());
        return thriftRequest;
    }


}
