package com.sankuai.shangou.supplychain.tof.service;

import com.dianping.cat.Cat;
import com.meituan.shangou.saas.dto.StatusCodeEnum;
import com.meituan.shangou.saas.order.management.client.dto.request.online.OCMSQueryOrderQuantityRequest;
import com.meituan.shangou.saas.order.management.client.dto.response.online.OCMSQueryOrderQuantityResponse;
import com.meituan.shangou.saas.order.management.client.enums.QueryOrderTypeQuantityEnum;
import com.meituan.shangou.saas.order.management.client.service.online.OCMSQueryThriftService;
import com.meituan.shangou.saas.tenant.thrift.common.enums.PoiEntityTypeEnum;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.response.QueryActivateDeliveryOrderCntDetailResponse;
import com.sankuai.qnh.ofc.ofw.client.thrift.common.consts.OfcResultCodeEnum;
import com.sankuai.shangou.commons.auth.login.context.LoginUser;
import com.sankuai.shangou.commons.auth.login.utils.LoginContextUtils;
import com.sankuai.shangou.commons.exception.common.exceptions.SystemException;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.commons.utils.log.MethodLog;
import com.sankuai.shangou.logistics.delivery.poi.dto.SelfDeliveryPoiConfigDTO;
import com.sankuai.shangou.logistics.warehouse.dto.TradeShippingOrderDTO;
import com.sankuai.shangou.logistics.warehouse.enums.TradeShippingOrderStatus;
import com.sankuai.shangou.qnh.ofc.ebase.consts.OrderTypeEnum;
import com.sankuai.shangou.qnh.ofc.ebase.consts.PickDeliveryWorkModeEnum;
import com.sankuai.shangou.qnh.ofc.ebase.dto.FulfillConfigDTO;
import com.sankuai.shangou.qnh.ofc.ebase.request.QueryFulfillConfigRequest;
import com.sankuai.shangou.qnh.ofc.ebase.response.QueryFulfillConfigResponse;
import com.sankuai.shangou.qnh.ofc.ebase.service.FulfillmentStoreConfigThriftService;
import com.sankuai.shangou.supplychain.tof.config.LionConfigUtils;
import com.sankuai.shangou.supplychain.tof.controller.vo.config.AuthCodeEnum;
import com.sankuai.shangou.supplychain.tof.controller.vo.config.IntegerBooleanConstants;
import com.sankuai.shangou.supplychain.tof.controller.vo.config.OrderHomePageModuleVO;
import com.sankuai.shangou.supplychain.tof.controller.vo.config.OrderPendingTaskVO;
import com.sankuai.shangou.supplychain.tof.controller.vo.config.request.DeliveryTabRequest;
import com.sankuai.shangou.supplychain.tof.controller.vo.config.response.DeliveryTabResponse;
import com.sankuai.shangou.supplychain.tof.enums.TmsDeliveryStatusDescEnum;
import com.sankuai.shangou.supplychain.tof.wrapper.AuthThriftWrapper;
import com.sankuai.shangou.supplychain.tof.wrapper.OutboundServiceWrapper;
import com.sankuai.shangou.supplychain.tof.wrapper.RiderDeliveryServiceWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.sankuai.shangou.supplychain.tof.controller.vo.config.IntegerBooleanConstants.BOOLEAN_FALSE;
import static com.sankuai.shangou.supplychain.tof.controller.vo.config.IntegerBooleanConstants.BOOLEAN_TRUE;
import static com.sankuai.shangou.supplychain.tof.enums.TmsDeliveryStatusDescEnum.WAITING_TO_ASSIGN_RIDER;

/**
 * 配送配置服务
 *
 * <AUTHOR>
 * @since 2025/1/15
 */
@Slf4j
@Service
public class DeliveryConfigService {

    @Resource
    private AuthThriftWrapper authThriftWrapper;

    @Resource
    private OCMSQueryThriftService ocmsQueryThriftService;

    @Resource
    private RiderDeliveryServiceWrapper riderDeliveryServiceWrapper;
    @Resource
    private FulfillmentStoreConfigThriftService fulfillmentStoreConfigThriftService;


    private final static List<String> SELF_RIDER_AUTH_CODE_LIST = com.google.common.collect.Lists.newArrayList(
            AuthCodeEnum.ORDER_SUB_TAB.getAuthCode(),
            AuthCodeEnum.SELF_RIDER_WAIT_TO_GET.getAuthCode(), AuthCodeEnum.SELF_RIDER_IN_DELIVERY.getAuthCode(), AuthCodeEnum.SELF_RIDER_WAIT_TO_TAKEGOODS.getAuthCode(),
            AuthCodeEnum.NEW_TASK_ONLY_DELIVERY.getAuthCode(), AuthCodeEnum.WAIT_TAKE_DELIVERY_TASK.getAuthCode(), AuthCodeEnum.RIDER_DONE_DELIVERY.getAuthCode(),
            AuthCodeEnum.WORKBENCH_SELF_RIDER_IN_DELIVERY.getAuthCode(), AuthCodeEnum.WORKBENCH_NEW_TASK_ONLY_DELIVERY.getAuthCode(), AuthCodeEnum.WORKBENCH_WAIT_TAKE_DELIVERY_TASK.getAuthCode(), AuthCodeEnum.WORKBENCH_RIDER_DONE_DELIVERY.getAuthCode(),
            AuthCodeEnum.NEW_TASK_PICK_DELIVERY.getAuthCode(), AuthCodeEnum.WORKBENCH_DELIVERY_SELF_NEW_PICK_DELIVERY.getAuthCode()
    );
    @Autowired
    private OutboundServiceWrapper outboundServiceWrapper;

    /**
     * 查询配送Tab信息
     *
     * @param request 请求参数
     * @return 配送Tab响应
     */
    @MethodLog(logResponse = true, logRequest = true)
    public DeliveryTabResponse queryDeliveryTabs(DeliveryTabRequest request) {
        try {
            // 获取用户身份信息
            LoginUser loginUser = LoginContextUtils.getAppLoginUser();
            Long tenantId = loginUser.getTenantId();
            Long storeId = LoginContextUtils.getAppLoginStoreId();

            log.info("queryDeliveryTabs start, tenantId: {}, storeId: {}, request: {}", tenantId, storeId, request);

            // 初始化返回对象
            DeliveryTabResponse response = new DeliveryTabResponse();
            OrderHomePageModuleVO orderHomePageModuleVO = OrderHomePageModuleVO.appHomePageInit();
            OrderPendingTaskVO orderPendingTaskVO = OrderPendingTaskVO.homePageInit();

            // 获取权限码列表
            List<String> permissionCodes = getOrderPermissionCodes(tenantId, storeId);
            log.info("queryDeliveryTabs permissionCodes: {}", permissionCodes);

            // 填充模块权限信息
            fillModulePermission(permissionCodes, orderHomePageModuleVO, request.isIgnoreOrderSubTabAuth());
            processPickDeliveryIntegrationSwitch(tenantId, storeId, orderHomePageModuleVO);
            // 填充模块数量统计信息
            fillModuleCount(orderHomePageModuleVO, orderPendingTaskVO);

            // 组装返回参数
            response.setOrderHomePageModuleVO(orderHomePageModuleVO);
            response.setOrderPendingTaskVO(orderPendingTaskVO);

            log.info("queryDeliveryTabs end, response: {}", response);
            return response;

        } catch (Exception e) {
            log.error("queryDeliveryTabs error", e);
            // 返回默认值，避免接口异常
            DeliveryTabResponse response = new DeliveryTabResponse();
            response.setOrderHomePageModuleVO(OrderHomePageModuleVO.appHomePageInit());
            response.setOrderPendingTaskVO(OrderPendingTaskVO.homePageInit());
            return response;
        }
    }

    /**
     * 获取订单权限码列表
     */
    private List<String> getOrderPermissionCodes(Long tenantId, Long storeId) {
        try {
            return authThriftWrapper.queryAuthorizedCodes(SELF_RIDER_AUTH_CODE_LIST);
        } catch (Exception e) {
            log.error("getOrderPermissionCodes error, tenantId: {}, storeId: {}", tenantId, storeId, e);
            return Collections.emptyList();
        }
    }

    /**
     * 填充模块权限信息
     */
    private void fillModulePermission(List<String> permissionCodes, OrderHomePageModuleVO orderHomePageModuleVO, boolean ignoreOrderSubTabAuth) {
        fillPermissionForMultiBindAuthCode(permissionCodes, orderHomePageModuleVO, ignoreOrderSubTabAuth);
    }

    private void fillPermissionForMultiBindAuthCode(List<String> permissionCodes, OrderHomePageModuleVO orderHomePageModuleVO, boolean ignoreOrderSubTabAuth) {

        //暂时用ignoreOrderSubTabAuth判断是不是从工作台进来的
        if (ignoreOrderSubTabAuth) {
            boolean anySelfDeliveryAuth = false;
            for (String permissionCode : permissionCodes) {
                if (StringUtils.equals(permissionCode, AuthCodeEnum.ORDER_SUB_TAB.getAuthCode())) {
                    // 订单顶部的订单tab页权限
                    orderHomePageModuleVO.setShowOrderTab(BOOLEAN_TRUE);
                }
                if (StringUtils.equals(permissionCode, AuthCodeEnum.WORKBENCH_NEW_TASK_ONLY_DELIVERY.getAuthCode())) {
                    orderHomePageModuleVO.setShowNewJustDeliveryTaskTab(BOOLEAN_TRUE);
                    //二级tab
                    orderHomePageModuleVO.setShowRiderGetOrderTab(BOOLEAN_TRUE);
                    anySelfDeliveryAuth = true;
                } else if (StringUtils.equals(permissionCode, AuthCodeEnum.WORKBENCH_WAIT_TAKE_DELIVERY_TASK.getAuthCode())) {
                    orderHomePageModuleVO.setShowWaitTakeDeliveryTaskTab(BOOLEAN_TRUE);
                    //二级tab
                    orderHomePageModuleVO.setShowRiderTakeGoodsTab(BOOLEAN_TRUE);
                    anySelfDeliveryAuth = true;
                } else if (StringUtils.equals(permissionCode, AuthCodeEnum.WORKBENCH_RIDER_DONE_DELIVERY.getAuthCode())) {
                    orderHomePageModuleVO.setShowCompletedDeliveryTaskTab(BOOLEAN_TRUE);
                    //二级tab
                    orderHomePageModuleVO.setShowRiderCompletedTab(BOOLEAN_TRUE);
                    anySelfDeliveryAuth = true;
                } else if (StringUtils.equals(permissionCode, AuthCodeEnum.WORKBENCH_SELF_RIDER_IN_DELIVERY.getAuthCode())) {
                    orderHomePageModuleVO.setShowRiderInDeliveryTab(BOOLEAN_TRUE);
                    anySelfDeliveryAuth = true;
                } else if (StringUtils.equals(permissionCode, AuthCodeEnum.WORKBENCH_DELIVERY_SELF_NEW_PICK_DELIVERY.getAuthCode())) {
                    orderHomePageModuleVO.setShowNewPickDeliveryTaskTab(BOOLEAN_TRUE);
                    //二级tab
                    orderHomePageModuleVO.setShowRiderGetOrderTab(BOOLEAN_TRUE);
                    anySelfDeliveryAuth = true;
                }
            }
            //一级tab
            if (anySelfDeliveryAuth) {
                orderHomePageModuleVO.setShowOrderTab(BOOLEAN_TRUE);
            }
        } else {
            for (String permissionCode : permissionCodes) {
                if (StringUtils.equals(permissionCode, AuthCodeEnum.ORDER_SUB_TAB.getAuthCode())) {
                    // 订单顶部的订单tab页权限
                    orderHomePageModuleVO.setShowOrderTab(BOOLEAN_TRUE);
                }
                if (StringUtils.equals(permissionCode, AuthCodeEnum.NEW_TASK_ONLY_DELIVERY.getAuthCode())) {
                    orderHomePageModuleVO.setShowNewJustDeliveryTaskTab(BOOLEAN_TRUE);
                    //二级tab
                    orderHomePageModuleVO.setShowRiderGetOrderTab(BOOLEAN_TRUE);
                } else if (StringUtils.equals(permissionCode, AuthCodeEnum.WAIT_TAKE_DELIVERY_TASK.getAuthCode())) {
                    orderHomePageModuleVO.setShowWaitTakeDeliveryTaskTab(BOOLEAN_TRUE);
                    //二级tab
                    orderHomePageModuleVO.setShowRiderTakeGoodsTab(BOOLEAN_TRUE);
                } else if (StringUtils.equals(permissionCode, AuthCodeEnum.RIDER_DONE_DELIVERY.getAuthCode())) {
                    orderHomePageModuleVO.setShowCompletedDeliveryTaskTab(BOOLEAN_TRUE);
                    //二级tab
                    orderHomePageModuleVO.setShowRiderCompletedTab(BOOLEAN_TRUE);
                } else if (StringUtils.equals(permissionCode, AuthCodeEnum.SELF_RIDER_IN_DELIVERY.getAuthCode())) {
                    orderHomePageModuleVO.setShowRiderInDeliveryTab(BOOLEAN_TRUE);
                } else if (StringUtils.equals(permissionCode, AuthCodeEnum.NEW_TASK_PICK_DELIVERY.getAuthCode())) {
                    orderHomePageModuleVO.setShowNewPickDeliveryTaskTab(BOOLEAN_TRUE);
                    //二级tab
                    orderHomePageModuleVO.setShowRiderGetOrderTab(BOOLEAN_TRUE);
                }
            }
        }
    }

    /**
     * 基础权限填充 - 简化版本，直接根据权限码设置对应字段
     */
    private void fillPermission(List<String> permissionCodes, OrderHomePageModuleVO orderHomePageModuleVO) {
        // 订单TAB权限
        if (permissionCodes.contains("ORDER_TAB")) {
            orderHomePageModuleVO.setShowOrderTab(BOOLEAN_TRUE);
        }

        // 骑手相关权限
        if (permissionCodes.contains("SELF_RIDER_WAIT_TO_GET")) {
            orderHomePageModuleVO.setShowRiderGetOrderTab(BOOLEAN_TRUE);
        }
        if (permissionCodes.contains("SELF_RIDER_WAIT_TO_TAKEGOODS")) {
            orderHomePageModuleVO.setShowRiderTakeGoodsTab(BOOLEAN_TRUE);
        }
        if (permissionCodes.contains("SELF_RIDER_IN_DELIVERY")) {
            orderHomePageModuleVO.setShowRiderInDeliveryTab(BOOLEAN_TRUE);
        }
        if (permissionCodes.contains("SELF_RIDER_COMPLETED")) {
            orderHomePageModuleVO.setShowRiderCompletedTab(BOOLEAN_TRUE);
        }
    }


    /**
     * 填充模块数量统计信息
     */
    private void fillModuleCount(OrderHomePageModuleVO orderHomePageModuleVO, OrderPendingTaskVO orderPendingTaskVO) {
        try {
            Set<QueryActivateDeliveryOrderCntDetailResponse.ChannelOrderKey> waitGetOrderKeySet = new HashSet<>();
            Set<QueryActivateDeliveryOrderCntDetailResponse.ChannelOrderKey> waitTakeOrderKeySet = new HashSet<>();

            Optional<QueryActivateDeliveryOrderCntDetailResponse> cntDetailResponseOpt = riderDeliveryServiceWrapper.queryActivateDeliveryOrderCntDetail();
            if (cntDetailResponseOpt.isPresent()) {
                QueryActivateDeliveryOrderCntDetailResponse cntDetailResponse = cntDetailResponseOpt.get();

                if (BOOLEAN_TRUE.equals(orderHomePageModuleVO.getShowNewJustDeliveryTaskTab())) {
                    orderPendingTaskVO.setNewJustDeliveryTaskCount(cntDetailResponse.getNewJustDeliveryOrderCnt());
                    waitGetOrderKeySet.addAll(cntDetailResponse.getNewJustDeliveryOrderKeyList());
                }

                if (BOOLEAN_TRUE.equals(orderHomePageModuleVO.getShowNewPickDeliveryTaskTab())) {
                    orderPendingTaskVO.setNewPickDeliveryTaskCount(cntDetailResponse.getNewPickDeliveryOrderCnt());
                    waitGetOrderKeySet.addAll(cntDetailResponse.getNewPickDeliveryOrderKeyList());
                }

                if (BOOLEAN_TRUE.equals(orderHomePageModuleVO.getShowWaitTakeDeliveryTaskTab())) {
                    orderPendingTaskVO.setWaitTakeDeliveryTaskCount(cntDetailResponse.getWaitTakeDeliveryOrderCount());
                    waitTakeOrderKeySet.addAll(cntDetailResponse.getWaitTakeDeliveryOrderKeyList());
                }

                if (BOOLEAN_TRUE.equals(orderHomePageModuleVO.getShowRiderInDeliveryTab())) {
                    orderPendingTaskVO.setRiderInDeliveryCount(cntDetailResponse.getInDeliveryOrderCount());
                }
            }

            if (BOOLEAN_TRUE.equals(orderHomePageModuleVO.getShowNewJustPickTaskTab())) {
                List<QueryActivateDeliveryOrderCntDetailResponse.ChannelOrderKey> newJustPickTaskOrderKeyList =
                        outboundServiceWrapper.getRecentListByOperatorIdAndStatusList(LoginContextUtils.getAppLoginStoreId(), null, Collections.singletonList(TradeShippingOrderStatus.WAITED.getCode()))
                                .stream()
                                .filter(tradeShippingOrderDTO -> Objects.isNull(tradeShippingOrderDTO.getIsThirdDeliveryPick()) || !tradeShippingOrderDTO.getIsThirdDeliveryPick())
                                .map(tradeShippingOrderDTO -> new QueryActivateDeliveryOrderCntDetailResponse.ChannelOrderKey(tradeShippingOrderDTO.getTradeOrderNo(), tradeShippingOrderDTO.getTradeChannelType()))
                                .distinct()
                                .collect(Collectors.toList());
                orderPendingTaskVO.setNewJustPickTaskCount(newJustPickTaskOrderKeyList.size());
                waitGetOrderKeySet.addAll(newJustPickTaskOrderKeyList);
            }

            if(BOOLEAN_TRUE.equals(orderHomePageModuleVO.getShowWaitTakePickTaskTab())) {
                List<TradeShippingOrderDTO> waitPickTradeShippingOrderList = outboundServiceWrapper.getRecentListByOperatorIdAndStatusList(LoginContextUtils.getAppLoginStoreId(),
                        LoginContextUtils.getAppLoginAccountId(), Collections.singletonList(TradeShippingOrderStatus.RECEIVE.getCode()));
                List<QueryActivateDeliveryOrderCntDetailResponse.ChannelOrderKey> waitTakePickTaskOrderKeyList = waitPickTradeShippingOrderList.stream()
                        .filter(tradeShippingOrderDTO -> Objects.equals(tradeShippingOrderDTO.getIsPickDeliverySplit(), true))
                        .map(tradeShippingOrderDTO -> new QueryActivateDeliveryOrderCntDetailResponse.ChannelOrderKey(tradeShippingOrderDTO.getTradeOrderNo(), tradeShippingOrderDTO.getTradeChannelType()))
                        .distinct()
                        .collect(Collectors.toList());
                orderPendingTaskVO.setWaitTakePickTaskCount(waitTakePickTaskOrderKeyList.size());
                waitTakeOrderKeySet.addAll(waitTakePickTaskOrderKeyList);
            }


            orderPendingTaskVO.setRiderWaitToGetOrderCount(waitGetOrderKeySet.size());
            orderPendingTaskVO.setRiderWaitToTakeGoodsCount(waitTakeOrderKeySet.size());
        } catch (Exception e) {
            log.error("query drunk horse tab red count fail", e);
            Cat.logEvent("HOME_PAGE", "QUERY_TAB_RED_COUNT_FAIL");
        }
    }

    private void processPickDeliveryIntegrationSwitch(long tenantId, Long storeId, OrderHomePageModuleVO orderHomePageModuleVO) {
        try {
            if(!LionConfigUtils.needFilterPickDeliveryWorkMode()) {
                return;
            }
            QueryFulfillConfigRequest request = new QueryFulfillConfigRequest();
            request.setTenantId(tenantId);
            request.setWarehouseId(storeId);
            request.setOrderType(OrderTypeEnum.SALE_TYPE.getCode());
            QueryFulfillConfigResponse response = fulfillmentStoreConfigThriftService.queryFulfillConfig(request);
            if (!Objects.equals(response.getCode(), OfcResultCodeEnum.SUCCESS.getCode()) || Objects.isNull(response.getFulfillConfig())) {
                throw new SystemException("query error");
            }
            FulfillConfigDTO fulfillConfig = response.getFulfillConfig();

            boolean isPickDeliveryIntegration = Objects.equals(fulfillConfig.getPickDeliveryWorkMode(), PickDeliveryWorkModeEnum.PICK_DELIVERY_INTEGRATION.getCode());
            boolean notShowNewPickDeliveryTaskTab = !Objects.equals(orderHomePageModuleVO.getShowNewPickDeliveryTaskTab(), BOOLEAN_TRUE);
            boolean hasSplitTab = Objects.equals(orderHomePageModuleVO.getShowNewJustPickTaskTab(), BOOLEAN_TRUE) || Objects.equals(orderHomePageModuleVO.getShowNewJustDeliveryTaskTab(), BOOLEAN_TRUE);
            //错误页面条件：拣配一体 && 没有待处理-新任务下的拣配任务 &&  (有仅拣货 || 仅配送)
            if(isPickDeliveryIntegration && notShowNewPickDeliveryTaskTab && hasSplitTab) {
                orderHomePageModuleVO.setShowNewTaskSplitErrorHint(BOOLEAN_TRUE);
            }
            //屏蔽仅拣货&&仅配送条件: 拣配一体  && 有待处理-新任务下的拣配任务  && (有仅拣货 || 仅配送)
            if (isPickDeliveryIntegration && !notShowNewPickDeliveryTaskTab && hasSplitTab) {
                orderHomePageModuleVO.setShowNewJustPickTaskTab(BOOLEAN_FALSE);
                orderHomePageModuleVO.setShowNewJustDeliveryTaskTab(BOOLEAN_FALSE);
            }

        } catch (Exception e) {
            log.error("processPickDeliveryIntegrationSwitch error", e);
        }
    }

}
