package com.sankuai.shangou.supplychain.tof.utils;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Splitter;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import com.sankuai.shangou.logistics.warehouse.dto.ConsumableItemInfo;
import com.sankuai.shangou.logistics.warehouse.enums.ConsumableGoodsType;
import com.sankuai.shangou.supplychain.tof.controller.vo.delivery.DeliveryCompleteConfigVO;
import com.sankuai.shangou.supplychain.tof.controller.vo.picking.PickConfigVO;
import com.sankuai.shangou.supplychain.tof.controller.vo.picking.SealDegradeReasonVO;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import javax.annotation.Nullable;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.dianping.lion.client.Lion.getConfigRepository;

/**
 * <AUTHOR>
 * @since 2024/3/27 17:52
 **/
@SuppressWarnings("all")
@Slf4j
public class MccConfigUtil {

    private static final String ORDER_API_APPKEY = "com.sankuai.shangou.qnh.orderapi";

    private static final long ONE_DAY_SECONDS = 24 * 60 * 60L;

    /**
     * 配送系统appKey
     */
    public static final String SG_FULFILLMENT_TMS_APP_KEY = "com.sankuai.sgfulfillment.tms";

    public static Integer getMaxActualPayAmtForThirdDelivery() {
        return Lion.getConfigRepository().getIntValue("max.actual.pay.third.delivery", 150 * 100);
    }

    public static HighPriceTagConfig getHighPriceTagConfig() {
        String jsonStr = getConfigRepository().get("high.price.tag.pair");
        return JSON.parseObject(jsonStr, HighPriceTagConfig.class);
    }

    public static List<DeliveryCompleteConfigVO> getDeliveryCompleteConfig() {
        String deliveryCompleteConfigStr = Lion.getConfigRepository().get("drunk.horse.delivery.complete.config");
        return JsonUtil.fromJson(deliveryCompleteConfigStr, new TypeReference<List<DeliveryCompleteConfigVO>>() {});
    }

    public static DeliveryCompleteConfigVO getDeliveryCompleteConfigVO(long tenantId, @Nullable Integer operationMode) {
        String defaultJson = getConfigRepository().get("drunk.horse.delivery.complete.config");
        Map<String, String> map = getConfigRepository().getMap("delivery.complete.example.config.map", String.class, ImmutableMap.of("1000395", defaultJson));
        if (MapUtils.isEmpty(map) || StringUtils.isBlank(map.getOrDefault(String.valueOf(tenantId), StringUtils.EMPTY))) {
            return null;
        }
        List<DeliveryCompleteConfigVO> deliveryCompleteConfigVOS = JsonUtil.fromJson(map.get(String.valueOf(tenantId)), new TypeReference<List<DeliveryCompleteConfigVO>>() {});
        if (CollectionUtils.isEmpty(deliveryCompleteConfigVOS)) {
            return null;
        }
        if (Objects.isNull(operationMode)) {
            return deliveryCompleteConfigVOS.get(0);
        }
        return deliveryCompleteConfigVOS.stream().filter(deliveryCompleteConfigVO ->  Objects.equals(deliveryCompleteConfigVO.getOperationMode(),operationMode)).findFirst().orElse(null);
    }

    public static Integer getShortExpirationThreshold() {
        return Lion.getConfigRepository().getIntValue("short.expiration.threshold", 90);
    }

    /**
     * sn弱校验门店名单
     */
    public static boolean checkIsSnWeekCheckStore(Long storeId) {
        try {
            if (Objects.isNull(storeId)) {
                return false;
            }

            List<Long> storeIds = Lion.getConfigRepository("com.sankuai.waimai.sc.pickselectservice").getList("sn.weak.check.store.ids", Long.class);

            if (CollectionUtils.isEmpty(storeIds)) {
                return false;
            }

            if (storeIds.size() == 1 && Long.valueOf(-1L).equals(storeIds.get(0))) {
                return true;
            }

            return storeIds.contains(storeId);
        } catch (Exception e) {
            log.error("checkIsSnWeekCheckStore error", e);
            Cat.logEvent("SN_WEAK_CHECK", "GET_SN_WEAK_CHECK_SWITCH_ERROR");
            return true;
        }

    }

    public static List<String> getDhTurnAggLimitSceneList() {
        try {
            return getConfigRepository(ORDER_API_APPKEY).getList("dh.turn.agg.limit.scene", String.class, Lists.newArrayList("餐馆"));
        } catch (Exception e) {
            log.error("isDhScenePoi error", e);
            return Lists.newArrayList();
        }
    }

    public static PickConfigVO getPickingConfig() {
        String pickConfigJsonStr = Lion.getConfigRepository().get("drunk.horse.pick.config");
        return JsonUtil.fromJson(pickConfigJsonStr, PickConfigVO.class);
    }

    public static List<SealDegradeReasonVO> getSealDegradeReasonList() {
        String reasonListStr = Lion.getConfigRepository().get("seal.degrade.reason.list");
        return JsonUtil.fromJson(reasonListStr, new TypeReference<List<SealDegradeReasonVO>>() {
        });
    }

    /**
     *
     * 隐私商品脱敏开关
     */
    public static Boolean getPrivacyGoodsDesensitizationSwitch() {
        return Lion.getConfigRepository().getBooleanValue("privacy.goods.desensitization.switch", true);
    }

    @Data
    public static class HighPriceTagConfig {
        private String tagCode;

        private String tagValueCode;
    }

    public static boolean getQuestionnaireSwitch(){
        return Lion.getConfigRepository().getBooleanValue("questionnaire.switch", false);
    }

    public static Long getAddressHideDuration() {
        return Lion.getConfigRepository().getLongValue("receiver.address.hide.duration", 4 * 60 * 60 * 1000L);
    }

    public static boolean getAssignVerifyTaskSwitch() {
        return Lion.getConfigRepository().getBooleanValue("assign.verify.task.switch", true);
    }

    public static boolean getPostOperationLocationSwitch() {
        return Lion.getConfigRepository().getBooleanValue("post.operation.location.switch", true);
    }

    public static boolean getReturnFixSwitch() {
        return Lion.getConfigRepository().getBooleanValue("return.fix.switch", true);
    }

    public static boolean getGiftBagSwitch() {
        return Lion.getConfigRepository().getBooleanValue("gift.bag.switch", true);
    }

    /**
     * 歪马临时骑手的角色id
     */
    public static List<String> tempRiderRoleIds() {
        String roleIds = Lion.getConfigRepository("com.sankuai.sgshopmgmt.empower.pieapi").get("temp.rider.role.ids","");
        if (StringUtils.isBlank(roleIds)) {
            return Collections.emptyList();
        }
        return Splitter.on(",").splitToList(roleIds);
    }

    public static Boolean isTrainingGrayStore(Long storeId) {
        if (storeId == null) {
            return false;
        }

        List<Long> grayStoreList = getConfigRepository("com.sankuai.shangou.bizmng.labor")
                .getList("training.gray.store.ids", Long.class, Collections.emptyList());

        if (grayStoreList.size() == 1 && Objects.equals(grayStoreList.get(0), -1L)) {
            return true;
        }

        return grayStoreList.contains(storeId);
    }

    public static Boolean acceptPickOrderSwitch(Long storeId) {
        List<Long> grayStoreIds = Lion.getConfigRepository("com.sankuai.sgshopmgmt.empower.pieapi")
                .getList("accept.pick.order.gray.store.list", Long.class, Collections.emptyList());
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(grayStoreIds)) {
            return false;
        }

        if (grayStoreIds.size() == 1 && Objects.equals(-1L, grayStoreIds.get(0))) {
            return true;
        }

        return grayStoreIds.contains(storeId);
    }

    public static Boolean getDefaultOpenScannerSwitch() {
        return Lion.getConfigRepository().getBooleanValue("default.open.scanner.switch", false);
    }

    public static boolean checkIsDrunkHorseTenant(Long tenantId) {
        if (tenantId == null) {
            return false;
        }

        List<Long> dhTenantIds = getConfigRepository().getList("drunk.horse.tenant.ids", Long.class, Collections.singletonList(1000395L));

        return dhTenantIds.contains(tenantId);
    }

    public static List<Long> needCheckPickFinishTenants() {
        return Lion.getConfigRepository().getList("need.check.pick.finish.tenants", Long.class, Lists.newArrayList(1000395L));
    }

    public static List<Long> needDeliveryStatsTenants() {
        return Lion.getConfigRepository().getList("need.delivery.stats.tenants", Long.class, Lists.newArrayList(1000395L));
    }

    public static boolean useRiderChangeByNameMethodNew(){
        return Lion.getConfigRepository().getBooleanValue("use.rider.change.by.name.method.new",true);
    }

    /**
     * 青云配送订单详情无授权链接开关
     */
    public static boolean dapOrderLinkNoAuthSwitch() {
        return Lion.getConfigRepository().getBooleanValue("dap.order.link.no.auth.switch", true);
    }

    public static List<ConsumableItemInfo> getAllTypeConsumableList(long warehouseId) {
        List<ConsumableItemInfo> consumableItemVOS;
        List<Long> greyStoreIds = Lion.getList("com.sankuai.shangou.logistics.oio", "consumable.grey.store.list", Long.class, Collections.emptyList());
        if (greyStoreIds.contains(warehouseId)) {
            consumableItemVOS = Lion.getList("com.sankuai.shangou.logistics.oio", "consumable.grey.sku.list", ConsumableItemInfo.class, Collections.emptyList());
        } else {
            consumableItemVOS = Lion.getList("com.sankuai.shangou.logistics.oio", "consumable.sku.list", ConsumableItemInfo.class, Collections.emptyList());
        }

        return consumableItemVOS;
    }

    public static List<ConsumableItemInfo> getNormalConsumableList(long warehouseId) {
        List<ConsumableItemInfo> consumableItemInfoList = getAllTypeConsumableList(warehouseId);

        return consumableItemInfoList.stream()
                .filter(consumableItemInfo -> Objects.equals(consumableItemInfo.getType(), ConsumableGoodsType.NORMAL.getCode()))
                .collect(Collectors.toList());

    }

    /**
     * 未来封签容具不作为耗材了，这里老方法还继续包含封签容具，一方面是本期容具还没完全下线，另一方面是兼容前端老版本
     * @param warehouseId
     * @return
     */
    @Deprecated
    public static List<ConsumableItemInfo> getSealConsumableList(long warehouseId) {
        List<ConsumableItemInfo> consumableItemInfoList = getAllTypeConsumableList(warehouseId);

        return consumableItemInfoList.stream().filter(consumableItemInfo ->
                        Objects.equals(consumableItemInfo.getType(), ConsumableGoodsType.SEAL_TAG.getCode()) ||
                        Objects.equals(consumableItemInfo.getType(), ConsumableGoodsType.SEAL_CONTAINER.getCode()) ||
                        Objects.equals(consumableItemInfo.getType(), ConsumableGoodsType.SAFE_PACKAGE.getCode()))
                .collect(Collectors.toList());
    }

    @Deprecated   // 未来封签容具不作为耗材了，这里老方法还继续包含封签容具，一方面是本期容具还没完全下线，另一方面是兼容前端老版本
    public static Map<String, ConsumableItemInfo> getSealConsumableMap(long warehouseId) {
        return getSealConsumableList(warehouseId).stream().collect(Collectors.toMap(ConsumableItemInfo::getSkuId, Function.identity(), (o1,o2) -> o1));
    }

    public static List<ConsumableItemInfo> getConsumableByType(long warehouseId, ConsumableGoodsType consumableGoodsType) {
        List<ConsumableItemInfo> consumableItemInfoList = getAllTypeConsumableList(warehouseId);

        return consumableItemInfoList.stream()
                .filter(consumableItemInfo -> Objects.equals(consumableItemInfo.getType(), consumableGoodsType.getCode()))
                .collect(Collectors.toList());

    }

    public static List<ConsumableItemInfo> getNeedManageStockConsumableList(long warehouseId) {
        List<ConsumableItemInfo> consumableItemInfoList = getAllTypeConsumableList(warehouseId);

        return consumableItemInfoList.stream()
                .filter(consumableItemInfo -> Objects.equals(consumableItemInfo.getNeedCheckStockQuantity(), true))
                .collect(Collectors.toList());

    }

    public static boolean getShowRewardHintSwitch() {
        try {
            return Lion.getConfigRepository().getBooleanValue("show.reward.hint.switch", false);
        } catch (Exception e) {
            log.error("getShowRewardHintSwitch error", e);
            return false;
        }
    }

    public static boolean getFusionGaryV1(Long tenantId,Long storeId){
        List<Long> grayTenantIdList = Lion.getConfigRepository().getList("fusion.gary.v1.tenant.list",Long.class,Collections.emptyList());
        if(tenantId!=null && CollectionUtils.isNotEmpty(grayTenantIdList)){
            if(grayTenantIdList.contains(-1L) || grayTenantIdList.contains(tenantId)){
                return true;
            }
        }
        List<Long> grayStoreIdList = Lion.getConfigRepository().getList("fusion.gary.v1.store.list",Long.class,Collections.emptyList());
        if(storeId!=null && CollectionUtils.isNotEmpty(grayStoreIdList)){
            return grayStoreIdList.contains(-1L) || grayStoreIdList.contains(storeId);
        }
        return false;
    }

    /**
     * 脱敏收货人信息的时长配置
     * @return  脱敏时长  单位秒
     */
    public static Long getDesensitizeReceiverInfoTime() {
        return getConfigRepository(ORDER_API_APPKEY).getLongValue("desensitize_receiver_info_time", ONE_DAY_SECONDS);
    }

    /**
     * 货品查询接口返回信息取值，是否切换到最新的字段
     * @param warehouseId  仓ID
     * @return             boolean
     */
    public static boolean goodsQuerySwitch2NewFields(Long warehouseId) {
        if (Objects.isNull(warehouseId)) {
            return false;
        }
        List<Long> grayStoreIds = Lion.getConfigRepository("com.sankuai.shangou.logistics.oio")
                                      .getList("goods.query.switch.new.fields.stores", Long.class, Collections.emptyList());
        // -1 表示全量
        if (grayStoreIds.size() == 1 && Objects.equals(-1L, grayStoreIds.get(0))) {
            return true;
        }
        return grayStoreIds.contains(warehouseId);
    }
    /**
     * 旧渠道名称配置开关
     */
    public static boolean getOldChannelNameSwitch() {
        return Lion.getConfigRepository().getBooleanValue("old.channel.name.switch", false);
    }


    /**
     * 骑手轨迹批量上传功能
     */
    public static boolean checkOpenRiderSyncV2(Long storeId, Long tenantId) {
        try {
            if (Objects.isNull(storeId) && Objects.isNull(tenantId)) {
                return false;
            }

            //门店纬度
            if (Objects.nonNull(storeId)) {
                List<Long> openStoreIds = Lion.getConfigRepository(SG_FULFILLMENT_TMS_APP_KEY)
                        .getList("OPEN_CHANNEL_RIDER_POINT_BATCH_SYNC", Long.class, Collections.emptyList());
                // -1 表示全量
                if (openStoreIds.contains(-1L)) {
                    return true;
                }
                if (openStoreIds.contains(storeId)) {
                    return true;
                }
            }

            if (Objects.nonNull(tenantId)) {
                List<Long> opeTenantIds = Lion.getConfigRepository(SG_FULFILLMENT_TMS_APP_KEY)
                        .getList("OPEN_CHANNEL_RIDER_POINT_BATCH_SYNC_FOR_TENANT", Long.class, Collections.emptyList());
                // -1 表示全量
                if (opeTenantIds.contains(-1L)) {
                    return true;
                }
                if (opeTenantIds.contains(tenantId)) {
                    return true;
                }
            }

            return false;
        } catch (Exception e) {
            log.error("checkOpenRiderSyncV2 error storeId:{} tenantId:{}", storeId, tenantId, e);
            return false;
        }
    }

    public static boolean getFusionGaryV2(Long tenantId,Long storeId){
        List<Long> grayTenantIdList = Lion.getConfigRepository().getList("fusion.gary.v2.tenant.list",Long.class,Collections.emptyList());
        if(tenantId!=null && CollectionUtils.isNotEmpty(grayTenantIdList)){
            if(grayTenantIdList.contains(-1L) || grayTenantIdList.contains(tenantId)){
                return true;
            }
        }
        List<Long> grayStoreIdList = Lion.getConfigRepository().getList("fusion.gary.v2.store.list",Long.class,Collections.emptyList());
        if(storeId!=null && CollectionUtils.isNotEmpty(grayStoreIdList)){
            return grayStoreIdList.contains(-1L) || grayStoreIdList.contains(storeId);
        }
        return false;
    }
}
