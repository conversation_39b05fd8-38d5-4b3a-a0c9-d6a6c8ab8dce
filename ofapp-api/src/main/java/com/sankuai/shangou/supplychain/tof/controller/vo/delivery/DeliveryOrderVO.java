package com.sankuai.shangou.supplychain.tof.controller.vo.delivery;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023-12-19
 * @email <EMAIL>
 */
@Data
public class DeliveryOrderVO {

    @FieldDoc(
            description = "运单ID", requiredness = Requiredness.REQUIRED
    )
    private Long deliveryOrderId;

    @FieldDoc(
            description = "租户ID", requiredness = Requiredness.REQUIRED
    )
    private Long tenantId;

    @FieldDoc(
            description = "配送状态 30-待领取 40-骑手已接单 50-骑手已取货", requiredness = Requiredness.REQUIRED
    )
    private Integer deliveryStatus;


    @FieldDoc(
            description = "配送状态,兼容三方", requiredness = Requiredness.REQUIRED
    )
    private Integer originalDeliveryStatus;

    @FieldDoc(
            description = "预计送达时间开始时间", requiredness = Requiredness.REQUIRED
    )
    private Long estimateArriveTimeStart;

    @FieldDoc(
            description = "预计送达时间截止时间", requiredness = Requiredness.REQUIRED
    )
    private Long estimateArriveTimeEnd;

    @FieldDoc(
            description = "配送订单类型名称", requiredness = Requiredness.REQUIRED
    )
    private String deliveryOrderTypeName;

    @FieldDoc(
            description = "考核送达截止时间", requiredness = Requiredness.OPTIONAL
    )
    private Long evaluateArriveDeadline;

    @FieldDoc(
            description = "考核送达剩余时间", requiredness = Requiredness.OPTIONAL
    )
    private Long evaluateArriveLeftTime;

    @FieldDoc(
            description = "考核送达超时时间", requiredness = Requiredness.OPTIONAL
    )
    private Long evaluateArriveTimeout;

    @FieldDoc(
            description = "配送距离，单位米", requiredness = Requiredness.OPTIONAL
    )
    private Long deliveryDistance;


    @FieldDoc(
            description = "改派前的骑手姓名", requiredness = Requiredness.OPTIONAL
    )
    private String fromRiderName;


    @FieldDoc(
            description = "收货人姓名", requiredness = Requiredness.REQUIRED
    )
    private String receiverName;

    @FieldDoc(
            description = "收货人电话号码", requiredness = Requiredness.REQUIRED
    )
    private String receiverPhone;

    @FieldDoc(
            description = "收货人地址", requiredness = Requiredness.REQUIRED
    )
    private String receiverAddress;

    @FieldDoc(
            description = "收货人定位经度，精确到小数点后六位", requiredness = Requiredness.REQUIRED
    )
    private String receiverLongitude;

    @FieldDoc(
            description = "收货人定位纬度，精确到小数点后六位", requiredness = Requiredness.REQUIRED
    )
    private String receiverLatitude;


    @FieldDoc(
            description = "运单状态是否被锁定：0-未锁定；1-锁定",
            example = {}
    )
    public Integer deliveryStatusLocked;

    @FieldDoc(
            description = "运单状态是否可以被锁定：0-不可以；1-可以",
            example = {}
    )
    public Integer canStatusBeLocked;

    @FieldDoc(
            description = "配送完成时间",
            example = {}
    )
    public Long deliveryDoneTime;

    @FieldDoc(
            description = "是否是一元单",
            example = {}
    )
    public Boolean isOneYuanOrder;

    @FieldDoc(
            description = "代收点",
            example = {}
    )
    public String signingPosition;

    @FieldDoc(
            description = "定价路线信息",
            example = {}
    )
    public RouteInfoVO routeInfoVO;

    @FieldDoc(
            description = "订单是否需要展示奖励文案",
            example = {}
    )
    public Boolean showRewardHint = false;

    @FieldDoc(
            description = "运单创建时间",
            example = {}
    )
    public Long createTime;

    @FieldDoc(
            description = "是否是拣配分离订单",
            example = {}
    )
    public Boolean isPickDeliverySplit;


    @FieldDoc(
            description = "是否三方配送单",
            example = {}
    )
    private Boolean isThirdDelivery = false;

    @FieldDoc(
            description = "是否三方异常单",
            example = {}
    )
    private Boolean isThirdException = false;

    @FieldDoc(
            description = "骑手id",
            example = {}
    )
    private Long riderAccountId ;

    @FieldDoc(
            description = "渠道ID", requiredness = Requiredness.REQUIRED
    )
    private Integer deliveryChannelId;

    @FieldDoc(
            description = "聚合运力平台code", requiredness = Requiredness.OPTIONAL
    )
    private Integer deliveryPlatformCode;

    @FieldDoc(
            description = "聚合运力平台描述", requiredness = Requiredness.OPTIONAL
    )
    private String deliveryPlatformDesc;


    @FieldDoc(
            description = "是否为美团名酒馆订单，true：是"
    )
    private Boolean isMtFamousTavern;

    @FieldDoc(
            description = "是否为美团名酒馆订单，true：是"
    )
    private Integer assessRewardShowType;
}
