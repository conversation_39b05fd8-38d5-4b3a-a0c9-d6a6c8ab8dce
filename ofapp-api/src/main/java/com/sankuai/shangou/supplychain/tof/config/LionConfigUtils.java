package com.sankuai.shangou.supplychain.tof.config;

import com.dianping.lion.client.Lion;
import com.google.common.collect.Lists;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import javax.validation.constraints.NotBlank;
import java.lang.reflect.Array;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.dianping.lion.client.Lion.getConfigRepository;

/**
 * <AUTHOR>
 * @date 2024-03-14
 * @email <EMAIL>
 */
@Slf4j
public class LionConfigUtils {

    private static final String ORDER_API_APP_KEY = "com.sankuai.shangou.qnh.orderapi";

    private LionConfigUtils() {

    }

    public static List<Long> getDrunkHorseTenantIds() {
        return Lion.getConfigRepository().getList("dh.tenant.ids", Long.class, Lists.newArrayList(1000395L));
    }

    /**
     * AXB 隐私号有效时长 -- 默认三小时:单位秒
     *
     * @return .
     */
    public static Integer axbPrivacyPhoneValidTime() {
        return Lion.getConfigRepository(ORDER_API_APP_KEY).getIntValue("axb.privacy.phone.validTime", 10800);
    }

    /**
     * 骑手端联系用户入口有效时长 -- 默认7天: 单位秒
     *
     * @return .
     */
    public static Long contactUserDuration() {
        return Lion.getConfigRepository(ORDER_API_APP_KEY).getLongValue("contact.user.duration", 7 * 24 * 60 * 60L);
    }

    /**
     * 骑手端(短时间)联系用户入口有效时长 -- 默认3h: 单位秒
     *
     * @return .
     */
    public static Long newContactUserDuration() {
        return Lion.getConfigRepository(ORDER_API_APP_KEY).getLongValue("short.contact.user.duration", 3 * 60 * 60L);
    }

    /**
     * 获取缩短隐私号时效的岗位类别id列表
     * @return
     */
    public static List<Long> getMinusPrivatePhonePositionIds() {
        return getMinusPrivatePhonePositionIdsByName(Lion.getConfigRepository(ORDER_API_APP_KEY).getList("minus.private.position.list", String.class,
                Lists.newArrayList("前置仓-临时骑手", "前置仓-地推人员", "前置仓-店员", "前置仓-短班店员")));
    }

    /**
     * 根据岗位中文名查岗位id列表，岗位id线上线下不同所以使用lion
     * @param positionNames 岗位中文名已配到lion上
     * @return 岗位id列表
     */
    private static List<Long> getMinusPrivatePhonePositionIdsByName(List<String> positionNames) {
        if (org.apache.commons.collections.CollectionUtils.isEmpty(positionNames)) {
            return Lists.newArrayList();
        }
        Map<String, Long> positionMap = Lion.getConfigRepository(ORDER_API_APP_KEY).getMap("minus.private.position.id.map", Long.class);
        if (MapUtils.isEmpty(positionMap)) {
            return Lists.newArrayList();
        }
        return positionNames.stream()
                .map(positionMap::get)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    public static int getDrunkhorsePrivatePhoneShowHour() {
        return Lion.getConfigRepository().getIntValue("drunkhorse.private.phone.show.hour", 24);
    }

    public static Boolean isDhScenePoi(Long storeId) {
        try {
            List<Long> stores = Lion.getConfigRepository(ORDER_API_APP_KEY).getList("dh_scene_poi_switch", Long.class, org.assertj.core.util.Lists.newArrayList());
            //全量逻辑
            if (stores.size() == 1 && stores.get(0).equals(-1L)) {
                return true;
            }
            return stores.contains(storeId);
        } catch (Exception e) {
            log.error("isDhScenePoi error", e);
            return false;
        }
    }

    public static String getDhSceneListJson() {
        try {
            return getConfigRepository(ORDER_API_APP_KEY).get("dh_scene_list", StringUtils.EMPTY);
        } catch (Exception e) {
            log.error("isDhScenePoi error", e);
            return StringUtils.EMPTY;
        }
    }

    public static String getTagHint(String lionKey, String defaultValue) {
        return getConfigRepository().get(lionKey, defaultValue);
    }

    public static String getDeliveryPositionPrefix() {
        return getConfigRepository("com.sankuai.shangou.qnh.orderapi").get("deliveryPosition.show.prefix");
    }

    public static boolean isGrayWiderShippingAreaStores(long storeId) {
        List<Long> grayStores = getConfigRepository("com.sankuai.shangou.supplychain.ofapp").getList("wider.shipping.area.stores", Long.class);
        if (CollectionUtils.isNotEmpty(grayStores) && grayStores.size() == 1 && grayStores.get(0).equals(-1L)) {
            return true;
        }
        return grayStores.contains(storeId);
    }

    public static int getSealGoodsQtyExceedLimit() {
        return Lion.getConfigRepository().getIntValue("seal.exceed.limit", 5);

    }

    public static int getSealContainerCodeLength() {
        return Lion.getConfigRepository().getIntValue("seal.container.code.length", 9);
    }

    public static boolean isNewDeliveryListGrayStore(long storeId) {
        List<Long> grayStoreIds = getConfigRepository().getList("new.delivery.list.stores", Long.class, Lists.newArrayList());
        if (CollectionUtils.isEmpty(grayStoreIds)) {
            return false;
        }
        if (grayStoreIds.size() == 1 && grayStoreIds.get(0).equals(-1L)) {
            return true;
        }

        return grayStoreIds.contains(storeId);
    }

    public static List<String> getDhRestaurantSceneList() {
        try {
            return getConfigRepository("com.sankuai.sgfulfillment.tms").getList("dh.restaurant.scene", String.class, Lists.newArrayList("餐馆"));
        } catch (Exception e) {
            log.error("isDhScenePoi error", e);
            return Lists.newArrayList();
        }
    }

    public static Long preOrderAssessTimePlusMills() {
        return Lion.getConfigRepository().getLongValue("pre.order.plus.mills", 0L);
    }

    public static boolean isNewPreOrderAssessGrayStore(long storeId) {
        List<Long> grayStoreIds = getConfigRepository().getList("new.pre.order.stores", Long.class, Lists.newArrayList());
        if (CollectionUtils.isEmpty(grayStoreIds)) {
            return false;
        }
        if (grayStoreIds.size() == 1 && grayStoreIds.get(0).equals(-1L)) {
            return true;
        }

        return grayStoreIds.contains(storeId);
    }

    public static boolean getNeedEstTimeWithoutDeliveryOrder() {
        return Lion.getConfigRepository().getBooleanValue("need.est.without.delivery", true);
    }

    public static boolean isSwitchBatchStockGrayStore(Long storeId) {
        List<Long> grayStoreIds = Lion.getConfigRepository("com.sankuai.shangou.logistics.oio").getList("switch.batch.stock.gray.store.list", Long.class, Collections.emptyList());
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(grayStoreIds)) {
            return false;
        }

        if (grayStoreIds.size() == 1 && Objects.equals(-1L, grayStoreIds.get(0))) {
            return true;
        }

        return grayStoreIds.contains(storeId);
    }

    public static int getPickTimoutDurationMills() {
        return Lion.getConfigRepository().getIntValue("pick.timeout.duration", 5*60*1000);
    }

    public static boolean getNeedGiftBagHint() {
        return Lion.getConfigRepository().getBooleanValue("need.gift.bag.hint", true);
    }

    public static String getGiftBagReplaceHint() {
        return Lion.getConfigRepository().get("gift.bag.replace.hint", "如门店有未贴条码的品牌礼袋，优先出存量礼袋。此单品牌礼袋库存不足用歪马礼袋补充，建议联系顾客说明情况");
    }

    public static String getGiftBagEnoughHint() {
        return Lion.getConfigRepository().get("gift.bag.enough.hint", "如门店有未贴条码的品牌礼袋，优先出存量品牌礼袋");
    }

    public static int getPartitionSize() {
        return Lion.getConfigRepository().getIntValue("ofapp.partition.size", 50);
    }

    public static int getMaxLoopNum() {
        return Lion.getConfigRepository().getIntValue("ofapp.max.loop", 100);
    }

    public static boolean newAggregateQueryFallback() {
        return Lion.getConfigRepository().getBooleanValue("aggregate.query.fallback", false);
    }


    public static boolean isShowGoodsItemListGrayStore(Long storeId) {
        List<Long> grayStoreIds = Lion.getConfigRepository().getList("show.goods.item.list.gray.store.ids", Long.class, Collections.emptyList());
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(grayStoreIds)) {
            return false;
        }

        if (grayStoreIds.size() == 1 && Objects.equals(-1L, grayStoreIds.get(0))) {
            return true;
        }

        return grayStoreIds.contains(storeId);
    }

    public static List<String> getWineBottleOpenerSkuIds() {
        return Lion.getConfigRepository().getList("wine.bottle.opener.sku.ids", String.class, Collections.singletonList("70013100302"));
    }

    public static Integer getFacaiQrCodeValidMin() {
        return Lion.getConfigRepository("com.sankuai.sgxsupply.wxmall.play").getIntValue("rich.wine.activity.available.minutes", 10);
    }

    /**
     * 获取查询接口的单次查询数量
     *
     * @return 单次查询数量
     */
    public static Integer getSearchSize() {
        return Lion.getConfigRepository("com.sankuai.shangou.supplychain.ofapp").getIntValue("search.size", 1000);
    }

    /**
     * 滚动查询的最大查询次数
     *
     * @return 滚动查询的最大查询次数
     */
    public static Integer getMaxSearchTimes() {
        return Lion.getConfigRepository("com.sankuai.shangou.supplychain.ofapp").getIntValue("max.search.times", 10);
    }

    /**
     * 判读当前租户是否使用新接口，-1为全量
     *
     * @return 当前租户是否使用新接口
     */
    public static boolean isNewAuthApiGrayTenant(long tenantId) {
        List<Long> tenantIdList = getConfigRepository("com.sankuai.shangou.supplychain.ofapp").getList("new.auth.api.gray.tenant", Long.class, Collections.emptyList());
        if (CollectionUtils.isEmpty(tenantIdList)) {
            return false;
        }

        if (tenantIdList.size() == 1 && Objects.equals(-1L, tenantIdList.get(0))) {
            return true;
        }

        return tenantIdList.contains(tenantId);
    }

    public static boolean isQueryPickInfo(Long tenantId){
        List<Long> tenantIdList = Lion.getConfigRepository().getList("is.query.pick.info", Long.class, Lists.newArrayList());
        return tenantIdList.contains(tenantId);
    }

    public static boolean needFilterPickDeliveryWorkMode() {
        return Lion.getConfigRepository().getBooleanValue("filter.pick.delivery.work.mode", true);
    }
}
