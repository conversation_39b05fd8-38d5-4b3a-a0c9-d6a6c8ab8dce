/*
 * Copyright (c) 2019 Meituan.com. All Rights Reserved.
 */
package com.sankuai.shangou.supplychain.tof.enums;

import lombok.Getter;

/**
 * 权限code
 */
@Getter
public enum AuthCodeEnum {

    //TAB权限
    SHOW_SALE_PRICE("SHOW_SALE_PRICE", "订单零售价&营收数据"),

    //按钮权限
    DH_EXCEPTION_REFUND_BTN("WAIMA_DH_EXCEPTION_REFUND_BTN" ,"歪马异常单页面退款按钮权限"),
    DH_EXCEPTION_CANCEL_BTN("WAIMA_DH_EXCEPTION_CANCEL_BTN", "歪马异常单页面取消按钮权限"),

    TURN_AGG_DELIVERY("ORDER_CHANGE_TO_THIRD_DELIVERY_BUTTON", "歪马转三方配送"),
    TURN_SELF_DELIVERY("WAIMA_ORDER_CHANGE_TO_SELF_DELIVERY_BUTTON", "歪马转自配送"),

    NEW_TASK_ONLY_PICK("NEW_TASK_ONLY_PICK", "歪马新任务-仅拣货TAB"),
    NEW_TASK_ONLY_DELIVERY("NEW_TASK_ONLY_DELIVERY", "歪马新任务-仅配送TAB"),
    NEW_TASK_PICK_DELIVERY("NEW_TASK_PICK_DELIVERY", "歪马新任务-拣配任务TAB"),
    WAIT_TAKE_ONLY_PICK("WAIT_TAKE_ONLY_PICK", "歪马待取货-拣货任务TAB"),
    WAIT_TAKE_DELIVERY_TASK("WAIT_TAKE_DELIVERY_TASK", "歪马待取货-配送任务TAB"),
    RIDER_DONE_PICKED("RIDER_DONE_PICKED", "歪马已完成-拣货任务TAB"),
    RIDER_DONE_DELIVERY("RIDER_DONE_DELIVERY", "歪马已完成-配送任务TAB"),


    WORKBENCH_DELIVERY_SELF_NEW_PICK_DELIVERY("DELIVERY_SELF_NEW_PICK_DELIVERY", "自送工作台-拣配任务配送TAB"),
    WORKBENCH_NEW_TASK_ONLY_DELIVERY("DELIVERY_SELF_NEW_ONLY_DELIVERY", "自送工作台-仅配送TAB"),
    WORKBENCH_WAIT_TAKE_DELIVERY_TASK("DELIVERY_SELF_WAIT_PICK_DELIVERY", "自送工作台-待取货-配送任务TAB"),
    WORKBENCH_SELF_RIDER_IN_DELIVERY("DELIVERY_SELF_DELIVERY_IN", "自送工作台-配送中"),
    WORKBENCH_RIDER_DONE_DELIVERY("DELIVERY_SELF_DELIVERY_DONE", "自送工作台-已完成"),
    ;

    private final String authCode;
    private final String desc;

    AuthCodeEnum(String auth, String desc) {
        this.authCode = auth;
        this.desc = desc;
    }

    public static AuthCodeEnum authOf(String authCode) {
        for (AuthCodeEnum moduleEnum : AuthCodeEnum.values()) {
            if (moduleEnum.authCode.equals(authCode)) {
                return moduleEnum;
            }
        }
        return null;
    }
}
