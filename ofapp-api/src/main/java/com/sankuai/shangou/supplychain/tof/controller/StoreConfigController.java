package com.sankuai.shangou.supplychain.tof.controller;

import com.meituan.linz.boot.exception.BusinessException;
import com.meituan.servicecatalog.api.annotations.*;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.DataSecurity;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamDataType;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamSource;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.SecurityParam;
import com.sankuai.shangou.commons.auth.login.utils.LoginContextUtils;
import com.sankuai.shangou.commons.utils.log.MethodLog;
import com.sankuai.shangou.supplychain.tof.controller.vo.store.request.StoreConfigQueryRequest;
import com.sankuai.shangou.supplychain.tof.controller.vo.store.request.StoreGrayRequest;
import com.sankuai.shangou.supplychain.tof.controller.vo.store.response.StoreConfigQueryResponse;
import com.sankuai.shangou.supplychain.tof.controller.vo.store.response.StoreGrayResponse;
import com.sankuai.shangou.supplychain.tof.enums.StoreConfigKeyEnum;
import com.sankuai.shangou.supplychain.tof.service.StoreConfigService;
import com.sankuai.shangou.supplychain.tof.utils.MccConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@InterfaceDoc(
        type = "restful",
        displayName = "门店配置相关接口",
        description = "门店配置相关接口",
        scenarios = "门店配置接口"
)
@RestController
@RequestMapping("/api/orderfulfill/app/store/config")
public class StoreConfigController {

    @Autowired
    private StoreConfigService storeConfigService;


    @MethodDoc(
            displayName = "灰度查询",
            description = "灰度查询",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "灰度查询",
                            type = StoreGrayRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/api/orderfulfill/store/config/gray",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/gray", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public StoreGrayResponse queryStoreGrayConfig(@Valid @RequestBody StoreGrayRequest request) {
        Long tenantId = LoginContextUtils.getAppLoginTenant();
        Long storeId = LoginContextUtils.getAppLoginStoreId();
        StoreGrayResponse response = new StoreGrayResponse();
        if (request.getGrayConfig() == null) {
            return response;
        }
        if (request.getGrayConfig() == 1) {
            response.setGrayValue(MccConfigUtil.getFusionGaryV1(tenantId, storeId));
        }
        return response;
    }

    @MethodDoc(
            displayName = "门店配置查询",
            description = "门店配置查询",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "门店配置查询",
                            type = StoreConfigQueryRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/api/orderfulfill/store/config/query",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/query", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public StoreConfigQueryResponse queryStoreConfig(@Valid @RequestBody StoreConfigQueryRequest request) {

        Long tenantId = LoginContextUtils.getAppLoginTenant();
        Long storeId = LoginContextUtils.getAppLoginStoreId();
        StoreConfigQueryResponse response = new StoreConfigQueryResponse();
        if (CollectionUtils.isEmpty(request.getConfigKeyList())) {
            throw new BusinessException("参数异常，请检查参数后重试");
        }
        Map<String, Object> configValueMap = new HashMap<>();
        for (Integer key : request.getConfigKeyList()) {
            StoreConfigKeyEnum keyEnum = StoreConfigKeyEnum.keyToEnum(key);
            if (keyEnum == null) {
                log.error("枚举转换失败 key:{}", key);
                continue;
            }
            switch (keyEnum) {
                case SORT_MODE:
                    configValueMap.put(key + "", storeConfigService.getSortMode(tenantId, storeId));
                    break;
                case OPERATION_MODE:
                    configValueMap.put(key + "", storeConfigService.getOperationMode(tenantId, storeId));
                    break;
                case INTERNALLY_NAVIGATION:
                    configValueMap.put(key + "", storeConfigService.getInternallyNavigation(tenantId, storeId));
                    break;
                case DELIVERY_COMPLETE_MODE:
                    configValueMap.put(key + "", storeConfigService.getDeliveryCompleteMode(tenantId, storeId));
                    break;
                case M_TERMINAL_MODE:
                    configValueMap.put(key + "", storeConfigService.getManagerTerminalMode(tenantId, storeId));
                    break;
                case ORDER_STATISTIC:
                    configValueMap.put(key + "", storeConfigService.getOrderStatisticConfig(tenantId, storeId));
                    break;
                default:
                    log.info("未适配枚举 keyEnum:{}", keyEnum);
            }
        }
        response.setConfigValueMap(configValueMap);
        return response;
    }


}
