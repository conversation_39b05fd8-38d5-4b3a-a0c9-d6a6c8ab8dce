package com.sankuai.shangou.supplychain.tof.controller.vo.config.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 5/6/22
 **/
@TypeDoc(
        description = "订单tab页首页查询接口参数"
)
@ApiModel("订单tab页首页查询接口参数")
@Data
public class DeliveryTabRequest {

    @FieldDoc(
            description = "门店类型 3-门店 5-中心仓 6-前置仓", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "门店类型")
    private Integer entityType;

    @FieldDoc(
            description = "忽略'订单-待处理'权限", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "门店类型")
    private boolean ignoreOrderSubTabAuth = false;

}
