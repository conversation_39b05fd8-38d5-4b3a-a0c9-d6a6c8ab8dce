package com.sankuai.shangou.supplychain.tof.assembler;


import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.mafka.client.producer.IProducerProcessor;
import com.meituan.shangou.goodscenter.dto.DepotGoodsDetailDto;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OCMSOrderVO;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.meituan.xframe.boot.mafka.autoconfigure.annotation.MafkaProducer;
import com.sankuai.meituan.shangou.empower.rider.client.enums.RiderDeliveryStatusEnum;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.RiderQueryThriftService;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto.TDeliveryRiskControlOrder;

import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto.TRiderDeliveryOrder;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.request.QueryActivateDeliveryOrderCntDetailRequest;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.response.QueryActivateDeliveryOrderCntDetailResponse;
import com.sankuai.shangou.common.Page;
import com.sankuai.shangou.commons.auth.login.context.holder.AppLoginContextHolder;
import com.sankuai.shangou.commons.auth.login.utils.LoginContextUtils;
import com.sankuai.shangou.commons.auth.login.utils.LoginContextUtils;
import com.sankuai.shangou.commons.exception.common.exceptions.BizException;
import com.sankuai.shangou.commons.exception.common.exceptions.SystemException;
import com.sankuai.shangou.commons.utils.collection.IListUtils;
import com.sankuai.shangou.commons.utils.time.TimeUtils;
import com.sankuai.shangou.infra.osw.api.poi.dto.response.BusinessPoiDTO;
import com.sankuai.shangou.infra.osw.api.poi.warehouse.dto.response.WarehouseDTO;
import com.sankuai.shangou.logistics.delivery.gray.enums.GrayKeyEnum;
import com.sankuai.shangou.logistics.delivery.gray.utils.GrayConfigUtils;
import com.sankuai.shangou.logistics.delivery.questionnaire.dto.DeliveryQuestionnaireDTO;
import com.sankuai.shangou.logistics.warehouse.TradeShippingOrderService;
import com.sankuai.shangou.logistics.warehouse.dto.MaterialTransInfoDto;
import com.sankuai.shangou.logistics.warehouse.dto.TradeOrderKey;
import com.sankuai.shangou.logistics.warehouse.dto.TradeShippingOrderDTO;
import com.sankuai.shangou.logistics.warehouse.enums.CompensateType;
import com.sankuai.shangou.logistics.warehouse.enums.TradeShippingOrderStatus;
import com.sankuai.shangou.logistics.warehouse.message.CompensateFulfillmentOrderMessage;
import com.sankuai.shangou.supplychain.tof.component.*;
import com.sankuai.shangou.supplychain.tof.config.LionConfigUtils;
import com.sankuai.shangou.supplychain.tof.controller.convert.RiderDeliveryConvert;
import com.sankuai.shangou.supplychain.tof.controller.vo.PageVO;
import com.sankuai.shangou.supplychain.tof.controller.vo.delivery.*;
import com.sankuai.shangou.supplychain.tof.controller.vo.delivery.request.*;
import com.sankuai.shangou.supplychain.tof.controller.vo.order.RevenueDetailVo;
import com.sankuai.shangou.supplychain.tof.controller.vo.order.TradeOrderVO;
import com.sankuai.shangou.supplychain.tof.controller.vo.picking.PickingOrderVO;
import com.sankuai.shangou.supplychain.tof.enums.AuthCodeEnum;
import com.sankuai.shangou.supplychain.tof.enums.ComponentTypeEnum;
import com.sankuai.shangou.supplychain.tof.enums.FilterSpilitTypeEnum;
import com.sankuai.shangou.supplychain.tof.utils.MccConfigUtil;
import com.sankuai.shangou.supplychain.tof.utils.MemPageUtils;
import com.sankuai.shangou.supplychain.tof.wrapper.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static com.sankuai.shangou.supplychain.tof.enums.ComponentTypeEnum.*;

/**
 * <AUTHOR>
 * @since 2024/4/24 20:43
 **/
@Service
@Slf4j
public class SelfDeliveryOrderAssembler {

    @Resource
    private DeliveryOrderComponent deliveryOrderComponent;

    @Resource
    private TradeOrderInfoComponent tradeOrderInfoComponent;

    @Resource
    private RevenueComponent revenueComponent;

    @Resource
    private DeliveryExceptionComponent deliveryExceptionComponent;

    @Resource
    private DeliveryStatisticComponent deliveryStatisticComponent;

    @Resource
    private QuestionnaireComponent questionnaireComponent;

    @Resource
    private DeliveryRiskControlComponent deliveryRiskControlComponent;

    @Resource
    private ProductHighPriceTagComponent productHighPriceTagComponent;

    @Resource
    private AbnormalOrderComponent abnormalOrderComponent;

    @Resource
    private TurnDeliveryButtonComponent turnDeliveryButtonComponent;

    @Resource
    private OSWServiceWrapper oswServiceWrapper;

    @Resource
    private SealDeliveryTagComponent sealDeliveryTagComponent;

    @Resource
    private PickingOrderComponent pickingOrderComponent;

    @Resource
    private AuthThriftWrapper authThriftWrapper;

    @Resource
    private RiderQueryThriftService riderQueryThriftService;

    @Resource
    private TradeShippingOrderService tradeShippingOrderService;
    @Resource
    private OutboundServiceWrapper outboundServiceWrapper;
    @Resource
    private DepotGoodsWrapper depotGoodsWrapper;

    @Resource
    private DeliveryServiceWrapper deliveryServiceWrapper;

    @MafkaProducer(namespace = "com.sankuai.mafka.castle.daojiacommon", appkey = "com.sankuai.shangou.supplychain.ofapp", topic = "compensate_fulfillment_order_message")
    private IProducerProcessor<Object, String> compensateOrderProducer;


    public Map<String, Object> queryRiderOrderTemplate(
            Supplier<Pair<PageVO, LinkedHashMap<TradeOrderInfoComponent.TradeOrderKey, TRiderDeliveryOrder>>> queryDeliveryOrderSupplier,
            List<ComponentTypeEnum> extInfoEnums) {

        Map<String, Object> resMap = new HashMap<>();

        Long tenantId = LoginContextUtils.getAppLoginTenant();
        Long storeId = LoginContextUtils.getAppLoginStoreId();
        //查运单数据
        Pair<PageVO, LinkedHashMap<TradeOrderInfoComponent.TradeOrderKey, TRiderDeliveryOrder>> deliveryOrderPair = queryDeliveryOrderSupplier.get();
        LinkedHashMap<TradeOrderInfoComponent.TradeOrderKey, TRiderDeliveryOrder> tRiderDeliveryOrderMap = deliveryOrderPair.getRight();
        PageVO pageVO = deliveryOrderPair.getLeft();

        //查订单数据
        Map<TradeOrderInfoComponent.TradeOrderKey, OCMSOrderVO> ocmsOrderMap = tradeOrderInfoComponent.queryTradeOrderInfoList(new ArrayList<>(tRiderDeliveryOrderMap.keySet()));

        //build运单模块
        LinkedHashMap<TradeOrderInfoComponent.TradeOrderKey, DeliveryOrderVO> deliveryOrderVOMap = new LinkedHashMap<>();
        //是否使用考核时间
        boolean useAssessTime = Optional.ofNullable(extInfoEnums).orElse(Lists.newArrayList()).contains(USE_ASSESS_TIME);
        tRiderDeliveryOrderMap.forEach((key, value) -> {
            deliveryOrderVOMap.putIfAbsent(key, deliveryOrderComponent.buildDeliveryOrderVO(value, ocmsOrderMap.get(key), useAssessTime && LionConfigUtils.isNewDeliveryListGrayStore(value.getStoreId())));
        });

        //build订单模块
        Map<TradeOrderInfoComponent.TradeOrderKey, TradeOrderVO> tradeOrderVOMap = ocmsOrderMap.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> tradeOrderInfoComponent.buildTradeOrderVO(entry.getValue()),
                        (older, newer) -> newer
                ));

        //查拣货单数据
        Map<TradeOrderInfoComponent.TradeOrderKey, PickingOrderVO> pickingOrderVOMap = new HashMap<>();
        if (LionConfigUtils.getDrunkHorseTenantIds().contains(tenantId)) {
            List<TradeShippingOrderDTO> tradeShippingOrderDTOS = pickingOrderComponent.queryByTradeOrderIds(storeId, Lists.newArrayList(tRiderDeliveryOrderMap.keySet()));
            pickingOrderVOMap = IListUtils.nullSafeAndOverrideCollectToMap(
                    tradeShippingOrderDTOS,
                    tradeShippingOrderDTO -> new TradeOrderInfoComponent.TradeOrderKey(tradeShippingOrderDTO.getTradeChannelType(), tradeShippingOrderDTO.getTradeOrderNo()), tradeShippingOrderDTO -> pickingOrderComponent.buildPickingOrderVO(tradeShippingOrderDTO)
            );
        }

        //补偿逻辑
        if (extInfoEnums.contains(CHECK_PICK_AND_DELIVERY_ORDER_STATUS) && LionConfigUtils.getDrunkHorseTenantIds().contains(tenantId)) {
            for (TRiderDeliveryOrder tRiderDeliveryOrder : tRiderDeliveryOrderMap.values()) {
                PickingOrderVO pickingOrderVO = pickingOrderVOMap.get(new TradeOrderInfoComponent.TradeOrderKey(tRiderDeliveryOrder.getOrderBizTypeCode(), tRiderDeliveryOrder.getChannelOrderId()));
                if(Objects.isNull(pickingOrderVO)) {
                    Cat.logEvent("PICK_DELIVERY_MISMATCH", "PICK_MISS");
                    sendCompensateForDeliveryMessage(tRiderDeliveryOrder, CompensateType.COMPENSATE_FOR_NEW_SHIPPING);
                }
                //有运单了，拣货单还没下发。补下发消息
                if (Objects.equals(pickingOrderVO.getStatus(), TradeShippingOrderStatus.INIT.getCode())) {
                    Cat.logEvent("PICK_DELIVERY_MISMATCH", "PICK_NOT_START");
                    sendCompensateForDeliveryMessage(tRiderDeliveryOrder, CompensateType.COMPENSATE_FOR_START_SHIPPING);
                }
            }
        }


        //配送风控单标签 失败不影响流程
        Map<TradeOrderInfoComponent.TradeOrderKey, TDeliveryRiskControlOrder> deliveryRiskControlOrderMap = new HashMap<>();
        try {
            deliveryRiskControlOrderMap = deliveryRiskControlComponent.queryRiskControlOrderMap(new ArrayList<>(deliveryOrderVOMap.keySet()));
        } catch (Exception e) {
            log.error("查风控单标签失败", e);
        }

        //配送异常模块 失败不影响流程
        Map<TradeOrderInfoComponent.TradeOrderKey, DeliveryExceptionSummaryVO> exceptionSummaryVOMap = new HashMap<>();
        if (extInfoEnums.contains(DELIVERY_EXCEPTION_INFO)) {
            try {
                exceptionSummaryVOMap = deliveryExceptionComponent.queryRiderReportException(new ArrayList<>(deliveryOrderVOMap.keySet()));
            } catch (Exception e) {
                log.error("查询配送异常失败", e);
            }
        }

        //营收模块 失败不影响流程
        Map<TradeOrderInfoComponent.TradeOrderKey, RevenueDetailVo> revenueDetailVoMap = new HashMap<>();
        if (extInfoEnums.contains(ORDER_REVENUE_INFO)) {
            try {
                revenueDetailVoMap = revenueComponent.revenueComponent(tenantId, new ArrayList<>(deliveryOrderVOMap.keySet()));
            } catch (Exception e) {
                log.error("查询营收数据失败", e);
            }
        }

        //配送问卷模块 失败不影响流程
        Map<Long, List<DeliveryQuestionnaireDTO>> questionnaireMap = new HashMap<>();
        if (extInfoEnums.contains(QUESTIONNAIRE) && MccConfigUtil.getQuestionnaireSwitch()) {
            try {
                questionnaireMap = questionnaireComponent.queryDeliveryQuestionnaireMap(deliveryOrderVOMap.values().stream().map(DeliveryOrderVO::getDeliveryOrderId).collect(Collectors.toList()));
            } catch (Exception e) {
                log.error("查询配送问卷失败", e);
            }
        }

        //查高价值标签 失败不影响流程
        Map<TradeOrderInfoComponent.TradeOrderKey, Boolean> highPriceTagMap = Collections.emptyMap();
        if (extInfoEnums.contains(HIGH_PRICE_TAG)) {
            try {
                highPriceTagMap = productHighPriceTagComponent.batchGetProductHighPriceTag(new ArrayList<>(deliveryOrderVOMap.keySet()));
            } catch (Exception e) {
                log.error("查高价值标签失败", e);
            }
        }

        //查缺货标签 失败不影响流程
        Map<TradeOrderInfoComponent.TradeOrderKey, Boolean> lackStockMap = Collections.emptyMap();
        try {
            if (extInfoEnums.contains(LACK_STOCK) && LionConfigUtils.getDrunkHorseTenantIds().contains(tenantId)) {
                lackStockMap = abnormalOrderComponent.getLackStockTag(new ArrayList<>(deliveryOrderVOMap.keySet()));
            }
        } catch (Exception e) {
            log.error("查是否缺货失败", e);
        }

        //查询封签交付标签
        Map<TradeOrderInfoComponent.TradeOrderKey, Boolean> sealDeliveryTagMap = Collections.emptyMap();
        try {
            if(extInfoEnums.contains(SEAL_DELIVER) && GrayConfigUtils.judgeIsGrayStore(tenantId, storeId, GrayKeyEnum.SEAL_DELIVERY.getGrayKey(), false)) {
                sealDeliveryTagMap = sealDeliveryTagComponent.batchGetSealDeliveryTag(new ArrayList<>(deliveryOrderVOMap.keySet()));
            }
        } catch (Exception e) {
            log.error("查询封签交付标签失败", e);
        }

        //查是否展示转青云按钮 失败不影响流程
        Map<TradeOrderInfoComponent.TradeOrderKey, Boolean> couldTurnDapDeliveryMap = Collections.emptyMap();
        try {
            //查询是否可以转配送，这里是自营，目前是直接转青云
            if (extInfoEnums.contains(TURN_DELIVERY_BUTTON_INFO) && LionConfigUtils.getDrunkHorseTenantIds().contains(tenantId)) {
                //如果之前没有查封签信息，这里补查一次
                if (GrayConfigUtils.judgeIsGrayStore(tenantId, storeId, GrayKeyEnum.SEAL_DELIVERY.getGrayKey(), false) && MapUtils.isEmpty(sealDeliveryTagMap)){
                    couldTurnDapDeliveryMap = turnDeliveryButtonComponent.queryTurnDeliveryButton(tradeOrderVOMap, sealDeliveryTagComponent.batchGetSealDeliveryTag(new ArrayList<>(deliveryOrderVOMap.keySet())));
                } else {
                    couldTurnDapDeliveryMap = turnDeliveryButtonComponent.queryTurnDeliveryButton(tradeOrderVOMap, sealDeliveryTagMap);
                }

            }
        } catch (Exception e) {
            log.error("查询是否可以转青云失败", e);
        }

        //查询额外商品信息（本期是发财酒）
        Map<String, Pair<MaterialTransInfoDto, DepotGoodsDetailDto>> tradeOrderKeyAndMaterialSkuInfoMap =  Maps.newHashMap();
        if (extInfoEnums.contains(EXTERNAL_PRODUCT_INFO)) {
            try {
                List<OCMSOrderVO> hasFacaiWineOrders = ocmsOrderMap.values().stream()
                        .filter(ocmsOrderVO -> Optional.ofNullable(ocmsOrderVO.getIsFacaiWine()).orElse(false))
                        .collect(Collectors.toList());
                Map<String, Pair<MaterialTransInfoDto, DepotGoodsDetailDto>> tradeOrderNoAndMaterialSkuInfoMap = Maps.newHashMap();
                if (CollectionUtils.isNotEmpty(hasFacaiWineOrders)) {
                    //查询发财酒情况
                    Map<String, MaterialTransInfoDto> tradeOrderNoAndTransInfoMap = outboundServiceWrapper.queryTransMaterialInfo(
                            LoginContextUtils.getAppLoginAccountId(),
                            IListUtils.mapTo(hasFacaiWineOrders, ocmsOrderVO -> {
                                TradeOrderKey tradeOrderKey = new TradeOrderKey();
                                tradeOrderKey.setTradeOrderNo(ocmsOrderVO.getViewOrderId());
                                tradeOrderKey.setTradeChannelType(ocmsOrderVO.getOrderBizType());
                                return tradeOrderKey;
                            })
                    );
                    //查询发财酒货品信息
                    if (MapUtils.isNotEmpty(tradeOrderNoAndTransInfoMap)) {
                        List<String> facaiWineSkuIds = IListUtils.mapTo(tradeOrderNoAndTransInfoMap.values(), MaterialTransInfoDto::getMaterialSkuId);
                        Map<String, DepotGoodsDetailDto> skuIdAndDepotGoodsMap = depotGoodsWrapper.queryBySkuId(tenantId, storeId, facaiWineSkuIds);
                        tradeOrderNoAndMaterialSkuInfoMap = IListUtils.nullSafeStream(tradeOrderNoAndTransInfoMap.entrySet())
                                .filter(entry -> skuIdAndDepotGoodsMap.containsKey(entry.getValue().getMaterialSkuId()))
                                .collect(Collectors.toMap(Map.Entry::getKey, entry -> Pair.of(entry.getValue(), skuIdAndDepotGoodsMap.get(entry.getValue().getMaterialSkuId()))));
                    }
                }
                tradeOrderKeyAndMaterialSkuInfoMap = tradeOrderNoAndMaterialSkuInfoMap;
            } catch (Exception e) {
                log.error("getTradeOrderNoAndMaterialSkuInfoMap error", e);
            }
        }

        List<Long> orderIdList = ocmsOrderMap.values().stream().map(OCMSOrderVO::getOrderId).collect(Collectors.toList());

        Map<Long,List<Integer>> itemList = deliveryServiceWrapper.queryDeliveryOperatorItemList(tenantId,storeId,orderIdList);


        List<RiderDeliveryOrderVO> riderDeliveryOrderVOS =
                RiderDeliveryConvert.buildRiderDeliveryOrderVOList(pickingOrderVOMap,
                        deliveryOrderVOMap,
                        tradeOrderVOMap,
                        exceptionSummaryVOMap,
                        revenueDetailVoMap,
                        questionnaireMap,
                        deliveryRiskControlOrderMap,
                        couldTurnDapDeliveryMap,
                        lackStockMap,
                        highPriceTagMap,
                        sealDeliveryTagMap,
                        ocmsOrderMap,
                        tradeOrderKeyAndMaterialSkuInfoMap,itemList);

        resMap.put("pageInfo", pageVO);
        resMap.put("orderList", riderDeliveryOrderVOS);

        return resMap;
    }

    public List<RiderDeliveryOrderVO> queryBaseRiderOrderTemplate(
            Supplier<LinkedHashMap<TradeOrderInfoComponent.TradeOrderKey, TRiderDeliveryOrder>> queryDeliveryOrderSupplier,
            AggregateOrderAssembler.AggregateContext aggregateContext
    ) {
        Long tenantId = LoginContextUtils.getAppLoginTenant();
        Long storeId = LoginContextUtils.getAppLoginStoreId();
        //查运单数据
        LinkedHashMap<TradeOrderInfoComponent.TradeOrderKey, TRiderDeliveryOrder> tRiderDeliveryOrderMap = queryDeliveryOrderSupplier.get();

        //查订单数据
        Map<TradeOrderInfoComponent.TradeOrderKey, OCMSOrderVO> ocmsOrderMap = tradeOrderInfoComponent.queryTradeOrderInfoList(new ArrayList<>(tRiderDeliveryOrderMap.keySet()));

        //build运单模块
        LinkedHashMap<TradeOrderInfoComponent.TradeOrderKey, DeliveryOrderVO> deliveryOrderVOMap = new LinkedHashMap<>();
        tRiderDeliveryOrderMap.forEach((key, value) -> {
            deliveryOrderVOMap.putIfAbsent(key, deliveryOrderComponent.buildDeliveryOrderVO(value, ocmsOrderMap.get(key), true));
        });

        //build订单模块
        Map<TradeOrderInfoComponent.TradeOrderKey, TradeOrderVO> tradeOrderVOMap = ocmsOrderMap.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> tradeOrderInfoComponent.buildTradeOrderVO(entry.getValue()),
                        (older, newer) -> newer
                ));
        aggregateContext.appendTradeOrderVOMap(tradeOrderVOMap);

        //查拣货单数据
        Map<TradeOrderInfoComponent.TradeOrderKey, PickingOrderVO> pickingOrderVOMap = Maps.newHashMap();
        if (LionConfigUtils.getDrunkHorseTenantIds().contains(tenantId)) {
            List<TradeShippingOrderDTO> tradeShippingOrderDTOS = pickingOrderComponent.queryByTradeOrderIds(storeId, Lists.newArrayList(tRiderDeliveryOrderMap.keySet()));
            pickingOrderVOMap = IListUtils.nullSafeAndOverrideCollectToMap(
                    tradeShippingOrderDTOS,
                    tradeShippingOrderDTO -> new TradeOrderInfoComponent.TradeOrderKey(tradeShippingOrderDTO.getTradeChannelType(), tradeShippingOrderDTO.getTradeOrderNo()), tradeShippingOrderDTO -> pickingOrderComponent.buildPickingOrderVO(tradeShippingOrderDTO)
            );
        }

        //配送风控单标签 失败不影响流程
        Map<TradeOrderInfoComponent.TradeOrderKey, TDeliveryRiskControlOrder> deliveryRiskControlOrderMap = new HashMap<>();
        try {
            deliveryRiskControlOrderMap = deliveryRiskControlComponent.queryRiskControlOrderMap(new ArrayList<>(deliveryOrderVOMap.keySet()));
        } catch (Exception e) {
            log.error("查风控单标签失败", e);
        }


        List<Long> orderIdList = ocmsOrderMap.values().stream().map(OCMSOrderVO::getOrderId).collect(Collectors.toList());

        Map<Long,List<Integer>> itemList = deliveryServiceWrapper.queryDeliveryOperatorItemList(tenantId,storeId,orderIdList);


        List<RiderDeliveryOrderVO> riderDeliveryOrderVOS =
                RiderDeliveryConvert.buildBaseRiderDeliveryOrderVOList(pickingOrderVOMap,
                        deliveryOrderVOMap,
                        tradeOrderVOMap,
                        deliveryRiskControlOrderMap,itemList);

        return riderDeliveryOrderVOS;
    }



    public Map<String, Object> queryCompleteDeliveryOrder(QueryRiderCompletedOrderRequest request) {
        List<ComponentTypeEnum> extComponentTypeEnums = Arrays.asList(
                ComponentTypeEnum.ORDER_REVENUE_INFO,
                ComponentTypeEnum.DELIVERY_EXCEPTION_INFO,
                ComponentTypeEnum.QUESTIONNAIRE,
                ComponentTypeEnum.SEAL_DELIVER,
                ComponentTypeEnum.USE_ASSESS_TIME,
                ComponentTypeEnum.EXTERNAL_PRODUCT_INFO);
        Map<String, Object> resultMap = queryRiderOrderTemplate(
                () -> deliveryOrderComponent.completedDeliveryOrder(request, FilterSpilitTypeEnum.NO_FILTER),
                extComponentTypeEnums);

        if (Objects.equals(request.getNeedReturnStatisticData(), true)) {
            try {
                CompletedDeliveryOrderStatisticVO orderStatisticVO =
                        deliveryStatisticComponent.queryDeliveryStatisticComponent(LocalDate.parse(request.getDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                resultMap.put("statisticsInfo", orderStatisticVO);
            } catch (Exception e) {
                log.error("查询配送统计信息失败", e);
            }

        }

        return resultMap;
    }

    public Map<String, Object> queryIncrementCompleteDeliveryOrder(QueryIncrementRiderCompletedOrderRequest request) {
        List<ComponentTypeEnum> extComponentTypeEnums = Arrays.asList(
                ComponentTypeEnum.ORDER_REVENUE_INFO,
                ComponentTypeEnum.DELIVERY_EXCEPTION_INFO,
                ComponentTypeEnum.QUESTIONNAIRE,
                ComponentTypeEnum.SEAL_DELIVER,
                ComponentTypeEnum.USE_ASSESS_TIME,
                ComponentTypeEnum.EXTERNAL_PRODUCT_INFO);
        Map<String, Object> resultMap = queryRiderOrderTemplate(
                () -> deliveryOrderComponent.completedDeliveryOrder(request, FilterSpilitTypeEnum.NO_FILTER),
                extComponentTypeEnums);

        if (Objects.equals(request.getNeedReturnStatisticData(), true)) {
            try {
                CompletedDeliveryOrderStatisticVO orderStatisticVO =
                        deliveryStatisticComponent.queryDeliveryStatisticComponent(TimeUtils.fromMilliSeconds(request.getLastUpdateTime()).toLocalDate());
                resultMap.put("statisticsInfo", orderStatisticVO);
            } catch (Exception e) {
                log.error("查询配送统计信息失败", e);
            }

        }

        return resultMap;
    }

    public Map<String, Object> queryDeliveryOrderWithRouteInfo(QueryRouteRequest request) {
        Integer deliveryStatus = request.getDeliveryStatus();
        Long deliveryOrderId = request.getDeliveryOrderId();

        List<ComponentTypeEnum> extComponentTypeEnums = Arrays.asList(
                ComponentTypeEnum.DELIVERY_EXCEPTION_INFO,
                ComponentTypeEnum.LACK_STOCK,
                ComponentTypeEnum.HIGH_PRICE_TAG,
                ComponentTypeEnum.SEAL_DELIVER,
                ComponentTypeEnum.USE_ASSESS_TIME
        );

        //没指定运单,直接查进行中的所有运单
        if (Objects.isNull(deliveryOrderId)) {
            QueryRiderInProgressOrderRequest req = new QueryRiderInProgressOrderRequest();
            req.setStoreId(LoginContextUtils.getAppLoginStoreId().toString());
            req.setPage(1);
            req.setSize(100);
            return queryRiderOrderTemplate(() -> deliveryOrderComponent.getRiderInProgressDeliveryOrders(req, true),
                    extComponentTypeEnums);
        }

        //如果指定了运单 但是没指定状态 先去查一下运单状态
        if (Objects.isNull(deliveryStatus)) {
            TRiderDeliveryOrder tRiderDeliveryOrder = deliveryOrderComponent.queryDeliveryOrderById(LoginContextUtils.getAppLoginTenant(), request.getDeliveryOrderId(),LoginContextUtils.getAppLoginStoreId());
            deliveryStatus = tRiderDeliveryOrder.getDeliveryStatus();
        }

        RiderDeliveryStatusEnum deliveryStatusEnum = RiderDeliveryStatusEnum.enumOf(deliveryStatus);
        switch (deliveryStatusEnum) {
            case WAITING_TO_ASSIGN_RIDER:
            case DELIVERY_DONE:
                return queryRiderOrderTemplate(() -> deliveryOrderComponent.queryDeliveryOrderByIdAndCheckStatusAndRider(
                                LoginContextUtils.getAppLoginTenant(), request.getDeliveryOrderId(),
                                deliveryStatusEnum.getCode(), LoginContextUtils.getAppLoginAccountId(),LoginContextUtils.getAppLoginStoreId()),
                        extComponentTypeEnums);
            case RIDER_ASSIGNED:
            case RIDER_TAKEN_GOODS:
                return queryRiderOrderTemplate(() -> deliveryOrderComponent.getRiderInProgressDeliveryOrdersAndCheckStatus(request.getDeliveryOrderId(), deliveryStatusEnum.getCode()),
                        extComponentTypeEnums);
            case DELIVERY_CANCELLED:
                throw new BizException("配送已取消,请刷新页面");
            default:
                throw new SystemException("运单状态不合法");
        }
    }

    /**
     * 查询送达配置
     * @return DeliveryCompleteConfigVO
     */
    public DeliveryCompleteConfigVO getDeliveryCompleteConfig() {
        Long storeId = LoginContextUtils.getAppLoginStoreId();

        //获取送达配置并map化
        List<DeliveryCompleteConfigVO> deliveryCompleteConfigVO = MccConfigUtil.getDeliveryCompleteConfig();
        Map<Integer, DeliveryCompleteConfigVO> configMap = deliveryCompleteConfigVO.stream()
                .collect(Collectors.toMap(DeliveryCompleteConfigVO::getOperationMode, Function.identity(), (k1, k2) -> k2));

        long tenantId = LoginContextUtils.getAppLoginTenant();
        long accountId = LoginContextUtils.getAppLoginAccountId();

        //查询是加盟店还是直营店
        BusinessPoiDTO poiDTO = oswServiceWrapper.queryOperatePoiByPoiId(tenantId, storeId, accountId);

        if (!configMap.containsKey(poiDTO.getOperationMode())) {
            log.error("未获取到对应经营模式下的送达配置, poiDto: {}", poiDTO);
            return null;
            //throw new RuntimeException("未获取到对应经营模式下的送达配置");
        }
        return configMap.get(poiDTO.getOperationMode());
    }


    public TabCountVO queryTabCount(TabCountRequest request) {
        List<String> permissionCodes = authThriftWrapper.queryAuthorizedCodes(Arrays.asList(AuthCodeEnum.NEW_TASK_ONLY_PICK.getAuthCode(),
                AuthCodeEnum.NEW_TASK_ONLY_DELIVERY.getAuthCode(),
                AuthCodeEnum.NEW_TASK_PICK_DELIVERY.getAuthCode(),
                AuthCodeEnum.WAIT_TAKE_ONLY_PICK.getAuthCode(),
                AuthCodeEnum.WAIT_TAKE_DELIVERY_TASK.getAuthCode(),
                AuthCodeEnum.RIDER_DONE_PICKED.getAuthCode(),
                AuthCodeEnum.RIDER_DONE_DELIVERY.getAuthCode(),
                AuthCodeEnum.WORKBENCH_NEW_TASK_ONLY_DELIVERY.getAuthCode(),
                AuthCodeEnum.WORKBENCH_DELIVERY_SELF_NEW_PICK_DELIVERY.getAuthCode(),
                AuthCodeEnum.WORKBENCH_WAIT_TAKE_DELIVERY_TASK.getAuthCode())
                );
        List<String> hasPermissionCodes = authThriftWrapper.queryAuthorizedCodes(permissionCodes);

        Set<QueryActivateDeliveryOrderCntDetailResponse.ChannelOrderKey> waitGetOrderKeySet = new HashSet<>();
        Set<QueryActivateDeliveryOrderCntDetailResponse.ChannelOrderKey> waitTakeOrderKeySet = new HashSet<>();

        QueryActivateDeliveryOrderCntDetailRequest cntDetailRequest = new QueryActivateDeliveryOrderCntDetailRequest();
        cntDetailRequest.setTenantId(LoginContextUtils.getAppLoginTenant());
        cntDetailRequest.setStoreId(LoginContextUtils.getAppLoginStoreId());
        cntDetailRequest.setRiderAccountId(LoginContextUtils.getAppLoginAccountId());
        QueryActivateDeliveryOrderCntDetailResponse cntDetailResponse = riderQueryThriftService.queryActivateDeliveryOrderCntDetail(cntDetailRequest);
        log.info("end invoke riderQueryThriftService.queryActivateDeliveryOrderCntDetail, request: {}, response: {}", cntDetailRequest, cntDetailResponse);

        if (cntDetailResponse.getStatus().getCode() != 0) {
            throw new BizException(cntDetailResponse.getStatus().getMsg());
        }

        TabCountVO tabCountVO = new TabCountVO();

        if (hasPermissionCodes.contains(AuthCodeEnum.NEW_TASK_ONLY_DELIVERY.getAuthCode()) || hasPermissionCodes.contains(AuthCodeEnum.WORKBENCH_NEW_TASK_ONLY_DELIVERY.getAuthCode())) {
            waitGetOrderKeySet.addAll(cntDetailResponse.getNewJustDeliveryOrderKeyList());
            tabCountVO.setNewJustDeliveryTaskCount(cntDetailResponse.getNewJustDeliveryOrderCnt());
        }

        if (hasPermissionCodes.contains(AuthCodeEnum.NEW_TASK_PICK_DELIVERY.getAuthCode()) || hasPermissionCodes.contains(AuthCodeEnum.WORKBENCH_DELIVERY_SELF_NEW_PICK_DELIVERY.getAuthCode())) {
            waitGetOrderKeySet.addAll(cntDetailResponse.getNewPickDeliveryOrderKeyList());
            tabCountVO.setNewPickDeliveryTaskCount(cntDetailResponse.getNewPickDeliveryOrderCnt());
        }

        if (hasPermissionCodes.contains(AuthCodeEnum.WAIT_TAKE_DELIVERY_TASK.getAuthCode()) || hasPermissionCodes.contains(AuthCodeEnum.WORKBENCH_WAIT_TAKE_DELIVERY_TASK.getAuthCode())) {
            waitTakeOrderKeySet.addAll(cntDetailResponse.getWaitTakeDeliveryOrderKeyList());
            tabCountVO.setWaitTakeDeliveryTaskCount(cntDetailResponse.getWaitTakeDeliveryOrderCount());
        }

        boolean ignorePickCount = false;
        if(request!=null && request.getIgnorePickCount()!=null){
            ignorePickCount = request.getIgnorePickCount();
        }

        if (hasPermissionCodes.contains(AuthCodeEnum.NEW_TASK_ONLY_PICK.getAuthCode()) && !ignorePickCount) {
            List<QueryActivateDeliveryOrderCntDetailResponse.ChannelOrderKey> newJustPickTaskOrderKeyList =
                    tradeShippingOrderService.getRecentListByOperatorIdAndStatusList(LoginContextUtils.getAppLoginStoreId(), null, Collections.singletonList(TradeShippingOrderStatus.WAITED.getCode()))
                            .getData()
                            .stream()
                            .filter(tradeShippingOrderDTO -> Objects.isNull(tradeShippingOrderDTO.getIsThirdDeliveryPick()) || !tradeShippingOrderDTO.getIsThirdDeliveryPick())
                            .map(tradeShippingOrderDTO -> new QueryActivateDeliveryOrderCntDetailResponse.ChannelOrderKey(tradeShippingOrderDTO.getTradeOrderNo(), tradeShippingOrderDTO.getTradeChannelType()))
                            .distinct()
                            .collect(Collectors.toList());
            tabCountVO.setNewJustPickTaskCount(newJustPickTaskOrderKeyList.size());
            waitGetOrderKeySet.addAll(newJustPickTaskOrderKeyList);
        }

        if (hasPermissionCodes.contains(AuthCodeEnum.WAIT_TAKE_ONLY_PICK.getAuthCode()) && !ignorePickCount) {
            List<TradeShippingOrderDTO> waitPickTradeShippingOrderList = tradeShippingOrderService.getRecentListByOperatorIdAndStatusList(LoginContextUtils.getAppLoginStoreId(),
                    LoginContextUtils.getAppLoginAccountId(), Collections.singletonList(TradeShippingOrderStatus.RECEIVE.getCode())).getData();
            List<QueryActivateDeliveryOrderCntDetailResponse.ChannelOrderKey> waitTakePickTaskOrderKeyList = waitPickTradeShippingOrderList.stream()
                    .filter(tradeShippingOrderDTO -> Objects.equals(tradeShippingOrderDTO.getIsPickDeliverySplit(), true))
                    .map(tradeShippingOrderDTO -> new QueryActivateDeliveryOrderCntDetailResponse.ChannelOrderKey(tradeShippingOrderDTO.getTradeOrderNo(), tradeShippingOrderDTO.getTradeChannelType()))
                    .distinct()
                    .collect(Collectors.toList());
            tabCountVO.setWaitTakePickTaskCount(waitTakePickTaskOrderKeyList.size());
            waitTakeOrderKeySet.addAll(waitTakePickTaskOrderKeyList);
        }


        tabCountVO.setRiderWaitToGetOrderCount(waitGetOrderKeySet.size());
        tabCountVO.setRiderWaitToTakeGoodsCount(waitTakeOrderKeySet.size());
        tabCountVO.setInDeliveryDeliveryTaskCount(Optional.ofNullable(cntDetailResponse.getInDeliveryOrderKeyList()).orElse(Collections.emptyList()).size());
        return tabCountVO;
    }

    private void sendCompensateForDeliveryMessage(TRiderDeliveryOrder tRiderDeliveryOrder, CompensateType compensateType) {
        try {
            compensateOrderProducer.sendMessage(JSON.toJSONString(buildCompensateMessageFromShippingOrder(tRiderDeliveryOrder, compensateType)));
        } catch (Exception e) {
            log.error("compensateOrderProducer.sendMessage error", e);
        }
    }

    private CompensateFulfillmentOrderMessage buildCompensateMessageFromShippingOrder(TRiderDeliveryOrder tRiderDeliveryOrder, CompensateType compensateType) {
        CompensateFulfillmentOrderMessage message = new CompensateFulfillmentOrderMessage();
        message.setMerchantId(tRiderDeliveryOrder.getTenantId());
        message.setWarehouseId(tRiderDeliveryOrder.getStoreId());
        message.setTradeOrderNo(tRiderDeliveryOrder.getChannelOrderId());
        message.setTradeOrderBizType(tRiderDeliveryOrder.getOrderBizTypeCode());
        message.setCompensateForPickType(compensateType.getCode());
        return message;
    }
}
