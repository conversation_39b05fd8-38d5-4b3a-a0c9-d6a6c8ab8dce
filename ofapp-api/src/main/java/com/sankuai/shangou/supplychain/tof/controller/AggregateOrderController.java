package com.sankuai.shangou.supplychain.tof.controller;

import com.google.common.collect.Maps;
import com.meituan.servicecatalog.api.annotations.*;
import com.sankuai.shangou.commons.utils.log.MethodLog;
import com.sankuai.shangou.supplychain.tof.assembler.AggregateOrderAssembler;
import com.sankuai.shangou.supplychain.tof.controller.vo.delivery.AggregateOrderVO;
import com.sankuai.shangou.supplychain.tof.controller.vo.delivery.request.AggregateQueryReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2024-11-27
 * @email <EMAIL>
 */

@Slf4j
@InterfaceDoc(
        type = "restful",
        displayName = "订单履约聚合接口",
        description = "提供订单履约聚合相关接口的查询/操作功能",
        scenarios = "主要应用于用户信息管理的场景"
)
@RestController
@RequestMapping("/api/orderfulfill/app/fulfillment/")
public class AggregateOrderController {

    @Resource
    private AggregateOrderAssembler aggregateOrderAssembler;


    @MethodDoc(
            displayName = "查询进行中聚合列表",
            description = "查询进行中聚合列表",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询进行中聚合列表请求",
                            type = Void.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/api/app/orderfulfill/abnormal/list",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/aggregateInProcessQuery", method = {RequestMethod.POST})
    @ResponseBody
    public Map<String, Object> aggregateInProcessQuery(@RequestBody AggregateQueryReq request) {
        Map<String, Object> resultMap = Maps.newHashMap();
        List<AggregateOrderVO> aggregateOrderVOS = aggregateOrderAssembler.aggregateOrderComponent(request);
        resultMap.put("orderList", aggregateOrderVOS);
        return resultMap;
    }



}
