<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.sankuai.shangou.supplychain</groupId>
        <artifactId>ofapp</artifactId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>ofapp-api</artifactId>
    <version>1.0.0</version>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>com.meituan.xframe</groupId>
            <artifactId>web-xframe-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

        <dependency>
            <groupId>com.meituan.xframe</groupId>
            <artifactId>xframe-boot-starter</artifactId>
        </dependency>

        <!-- 异常处理 -->
        <dependency>
            <groupId>com.sankuai.shangou</groupId>
            <artifactId>shangou-exception-collector</artifactId>
            <version>2.6.1</version>
        </dependency>

        <!-- 公共utils包 -->
        <dependency>
            <groupId>com.sankuai.shangou</groupId>
            <artifactId>shangou-common-utils</artifactId>
            <version>2.8.14</version>
        </dependency>

        <!-- ServiceCatalog api doc -->
        <dependency>
            <groupId>com.meituan.servicecatalog</groupId>
            <artifactId>api-annotations</artifactId>
        </dependency>

        <!-- 货品中心 -->
        <dependency>
            <groupId>com.meituan.shangou.goodscenter</groupId>
            <artifactId>goods_center_client</artifactId>
            <version>1.1.16</version>
        </dependency>

        <!--client-->
        <dependency>
            <groupId>com.sankuai.meituan.reco.pickselect</groupId>
            <artifactId>pick-select-service-idl</artifactId>
            <version>6.1.29</version>
            <exclusions>
                <exclusion>
                    <groupId>com.sankuai.meituan.reco.pickselect</groupId>
                    <artifactId>fulfill-constants</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.reco.store.management</groupId>
            <artifactId>dh-wms-stock-operate-center-idl</artifactId>
            <version>8.5.0</version>
            <exclusions>
                <exclusion>
                    <artifactId>pick-select-common</artifactId>
                    <groupId>com.meituan.reco.pickselect</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.sankuai.shangou.logistics</groupId>
            <artifactId>oio-api</artifactId>
            <version>1.1.4</version>
        </dependency>

        <dependency>
            <groupId>com.meituan.shangou.saas</groupId>
            <artifactId>reco_store_saas_order_management_client</artifactId>
            <version>1.6.82</version>
            <exclusions>
                <exclusion>
                    <groupId>com.sankuai.meituan.shangou</groupId>
                    <artifactId>store-saas-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.meituan.shangou.saas</groupId>
                    <artifactId>reco_store_saas_order_platform_common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.sankuai.meituan.shangou.empower.ocms</groupId>
                    <artifactId>reco_shopmgmt_ocms_service-client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.meituan.shangou.saas</groupId>
            <artifactId>reco_store_saas_order_platform_common</artifactId>
            <version>1.9.44</version>
            <exclusions>
                <exclusion>
                    <groupId>com.sankuai.meituan.shangou</groupId>
                    <artifactId>store-saas-common</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.reco.pickselect</groupId>
            <artifactId>fulfill-constants</artifactId>
            <version>6.1.43</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.shangou.empower.ocms</groupId>
            <artifactId>reco_shopmgmt_ocms_service-client</artifactId>
            <version>2.18.38</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan.reco.store.management</groupId>
            <artifactId>stock-biz-commons</artifactId>
            <version>4.8.1</version>
            <exclusions>
                <exclusion>
                    <groupId>com.sankuai.meituan</groupId>
                    <artifactId>poros-high-level-client</artifactId>
                </exclusion>
            </exclusions>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.sankuai.shangou</groupId>
            <artifactId>shangou-auth</artifactId>
            <version>1.2.0</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan.reco.sgfulfillment</groupId>
            <artifactId>reco_fulfillment_tms-rider-delivery-client</artifactId>
            <version>2.2.83-fusion-v2-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan.reco.sgfulfillment</groupId>
            <artifactId>reco_fulfillment_tms-delivery-client</artifactId>
            <version>2.2.83-fusion-v2-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.meituan.shangou.saas</groupId>
            <artifactId>reco_store_saas_order_biz_client</artifactId>
            <version>2.3.27</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.shangou</groupId>
            <artifactId>shangou-thrift-augment</artifactId>
            <version>2.6.1</version>
        </dependency>
        <dependency>
            <groupId>com.meituan.service.mobile</groupId>
            <artifactId>mtthrift</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.shangou</groupId>
            <artifactId>reco_store_saas_message_management_client</artifactId>
            <version>2.0.50</version>
        </dependency>

        <!--OFC-->
        <dependency>
            <groupId>com.sankuai.shangou.qnh.ofc</groupId>
            <artifactId>qnh_ofc_ofw-client</artifactId>
            <version>1.0.3${env-suffix}</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.shangou.qnh.ofc</groupId>
            <artifactId>qnh_ofc_ofw-common</artifactId>
            <version>1.0.3</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.shangou.qnh.ofc</groupId>
            <artifactId>qnh_ofc_ebase-client</artifactId>
            <version>1.0.2-delivery-fusion-SNAPSHOT</version>
        </dependency>

    <!--dmp-->
        <dependency>
            <groupId>com.sankuai.shangou</groupId>
            <artifactId>dmp-sdk</artifactId>
            <version>1.0.15-fusion-v2-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>thrift-xframe-boot-starter</artifactId>
                    <groupId>com.meituan.xframe</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>zebra-xframe-boot-starter</artifactId>
                    <groupId>com.meituan.xframe</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.sankuai.meituan.reco.sgfulfillment</groupId>
                    <artifactId>reco_fulfillment_tms-delivery-client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.shangou.xsupply</groupId>
            <artifactId>product_management-client</artifactId>
            <version>2.0.0</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.shangou.infra</groupId>
            <artifactId>osw-api</artifactId>
            <version>1.0.50</version>
        </dependency>

        <!-- 异常处理,Controller模块依赖的公共异常码与collector包一致 -->
<!--        <dependency>-->
<!--            <artifactId>shangou-controller</artifactId>-->
<!--            <groupId>com.sankuai.shangou</groupId>-->
<!--            <version>2.7.4</version>-->
<!--        </dependency>-->

        <dependency>
            <groupId>com.sankuai.meituan.reco.pickselect</groupId>
            <artifactId>pick-select-service-dh-client</artifactId>
            <version>1.0.5</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan.reco.store.management</groupId>
            <artifactId>stock-biz-base-idl</artifactId>
            <version>4.8.1</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.shangou</groupId>
            <artifactId>shangou-exception-common</artifactId>
            <version>2.7.4</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.shangou.empower</groupId>
            <artifactId>reco_store_saas_auth_client</artifactId>
            <version>1.6.17</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.shangou.logistics</groupId>
            <artifactId>handle-unit-api</artifactId>
            <version>1.0.2</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.shangou.logistics</groupId>
            <artifactId>sdms-sdk</artifactId>
            <version>1.0.9</version>
        </dependency>

        <dependency>
            <groupId>com.meituan.xframe</groupId>
            <artifactId>mafka-xframe-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.shangou</groupId>
            <artifactId>shangou-supplychain-api</artifactId>
            <version>2.17.10</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.shangou.bizmng</groupId>
            <artifactId>labor-api</artifactId>
            <version>1.0.0</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.shangou.sgxsupply.wxmall</groupId>
            <artifactId>bizmanagement-client</artifactId>
            <version>2.18.7</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.shangou.empower</groupId>
            <artifactId>reco_store_sac_client</artifactId>
            <version>2.3.9</version>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>


            <plugin>
                <groupId>com.sankuai.inf</groupId>
                <artifactId>xmdlog-maven-plugin</artifactId>
                <version>1.1.6</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>check</goal>
                        </goals>
                        <phase>compile</phase>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
