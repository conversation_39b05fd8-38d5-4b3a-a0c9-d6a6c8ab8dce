package com.sankuai.meituan.shangou.empower.tms.delivery.access.domain;


import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.log.changeinfo.DeliveryChangeInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryEventEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.TransDeliveryTypeOperationTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.service.RiderDeliveryChangeNotifyService;

import java.time.LocalDateTime;

/**
 * 向外或对内通知配送信息变更
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/7
 */
public interface DeliveryChangeNotifyService extends RiderDeliveryChangeNotifyService {

	/**
	 * 通知配送变更流水，用于异步记录运单流水
	 */
	void notifyDeliveryChangeLog(Long deliveryId, DeliveryEventEnum deliveryEvent, DeliveryChangeInfo changeInfo, LocalDateTime changeTime);

	/**
	 * 通知骑手分配超时检查触发
	 */
	void notifyRiderAssignTimeOutCheckTriggered(DeliveryOrder deliveryOrder, int checkMinutes);

	/**
	 * 骑手位置同步
	 */
	void notifySyncRiderPosition(Long deliveryId, DeliveryPlatformEnum platformEnum,Long tenantId,Long storeId);

	/**
	 * 骑手位置同步 v2
	 */
	void notifySyncRiderPositionV2(DeliveryPlatformEnum platformEnum, Long deliveryId, Long tenantId, Long storeId);

	/**
	 * 新供给骑手位置同步
	 */
	void notifySyncRiderPosition(Long deliveryId, DeliveryPlatformEnum platformEnum, Integer orderBizType,Long tenantId,Long storeId);

	/**
	 * 骑手位置同步
	 */
	void notifySyncDrunkHorseRiderPosition(Long deliveryId, DeliveryPlatformEnum platformEnum, Long tenantId, Long storeId, boolean feDirectSyncRiderPosition);

	/**
	 * 通知配送取消
	 */
	void notifyDeliveryCancel(Long tenantId,Long storeId ,Long deliveryId);

	/**
	 * 通知配送超时检查触发
	 */
	void notifyDeliveryTimeOutCheckTriggered(Long orderId, Long tenantId, Long storeId, LocalDateTime estimatedDeliveryTime, Long checkMinutes);

	/**
	 * 通知歪马运单配送转方式
	 * 1-转自配 2-转三方
	 */
	void notifyDrunkHorseTransDeliveryType(String viewOrderId, Integer orderBizType, TransDeliveryTypeOperationTypeEnum transType, Long newRiderAccountId, String newRiderName);

}
