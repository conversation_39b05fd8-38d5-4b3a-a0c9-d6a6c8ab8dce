package com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.push;


import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrder;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.RiderDeliveryOrder;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.client.sync.DeliveryChangeSyncOutMessage;

/**
 * 面向用户的消息推送客户端
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/11
 */
public interface PushClient {

	/**
	 * 推送配送异常
	 */
	void pushDeliveryException(DeliveryOrder deliveryOrder);

	/**
	 * 三方聚合运力配送异常
	 *
	 * @param deliveryOrder
	 * @param url
	 */
	void pushAggDeliveryException(DeliveryOrder deliveryOrder, String url);

	/**
	 * 推送商家自配送运单取消通知
	 */
	void pushMerchantSelfDeliveryOrderCancelled(DeliveryOrder deliveryOrder, DeliveryStatusEnum beforeStatus);

	/**
	 * 推送自配送门店的骑手，有新的配送任务
	 *
	 * @param deliveryOrder 运单
	 */
	void pushMerchantSelfDeliveryNewDeliveryTask(DeliveryOrder deliveryOrder, OrderInfo orderInfo);

	/**
	 * 推送自配送门店的骑手，有配送任务领取超时
	 */
	void pushMerchantSelfRiderAssignTimeout(RiderDeliveryOrder riderDeliveryOrder);

	/**
	 * 推送自配送门店的骑手，有配送任务取货超时
	 */
	void pushMerchantSelfRiderTakenGoodsTimeout(RiderDeliveryOrder riderDeliveryOrder);

	/**
	 * 推送自配送门店的骑手，有配送任务配送即将超时
	 */
	void pushMerchantSelfRiderDeliveryTimeoutWarning(RiderDeliveryOrder deliveryOrder);

	/**
	 * 通知自配送门店的店长/副店长，有配送单暂停配送
	 */
	void pushMerchantSelfManagerDeliveryPauseWarning(DeliveryChangeSyncOutMessage syncOutMessage);

	/**
	 * 聚合配送发生状态回退异常通知
	 */
	void pushDeliveryStatusRollbackException(DeliveryOrder deliveryOrder, String orderSerialNumber);

	void pushFastOrderDeliveryTimeoutException(DeliveryOrder deliveryOrder);

	void pushSealContainerReturnTimeout(Long tenantId, Long storeId, Long riderAccountId);
}
