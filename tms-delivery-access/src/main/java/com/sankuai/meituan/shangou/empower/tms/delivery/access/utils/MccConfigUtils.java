package com.sankuai.meituan.shangou.empower.tms.delivery.access.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.dianping.lion.client.Lion;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.FourWheelBaseInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryChannelEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.dianping.lion.client.Lion.getConfigRepository;

/**
 * <AUTHOR>
 * @date 2021/3/8
 * @email jianglilin02@meituan
 */
@SuppressWarnings("all")
@Slf4j
public class MccConfigUtils {
	private static final Splitter DOT_SPLITTER = Splitter.on(",").omitEmptyStrings().trimResults();
	private static final List<Integer> CANCEL_DELIVERY_STATUS_LIST =
			Lists.newArrayList(
					DeliveryStatusEnum.DELIVERY_LAUNCHED.getCode(),
					DeliveryStatusEnum.WAITING_TO_ASSIGN_RIDER.getCode(),
					DeliveryStatusEnum.RIDER_ASSIGNED.getCode(),
					DeliveryStatusEnum.RIDER_ARRIVED_SHOP.getCode()
			);

	private static final String ORDER_API_APPKEY = "com.sankuai.shangou.qnh.orderapi";
	private static final String OCMS_CHANNEL_APPKEY = "com.sankuai.shangou.empower.ocmschannel";
	// 全量标识
	private static final String ALL = "all";

	/**
	 * 白名单-全量
	*/
	public static final String WHITE_LIST_ALL = "all";

	/**
	 * 白名单-不配置名单
	 */
	public static final String WHITE_LIST_EMPTY = StringUtils.EMPTY;

	private static final List<Integer> EMPLOY_DELIVERY_STATUS = Lists.newArrayList(1, 30, 40, 45, 50, 51, 60);

	public static List<Integer> getCancelDeliveryStatusList() {
		try {
			String cancelDeliveryStatusStr = ConfigUtilAdapter.getString("delivery.cancel.status.list", StringUtils.EMPTY);
			if (StringUtils.isBlank(cancelDeliveryStatusStr)) {
				return CANCEL_DELIVERY_STATUS_LIST;
			}
			return Arrays.stream(cancelDeliveryStatusStr.split(",")).map(Integer::valueOf).collect(Collectors.toList());
		} catch (Exception e) {
			log.error("getCancelDeliveryStatusList error", e);
			return CANCEL_DELIVERY_STATUS_LIST;
		}


	}

	public static String getAggDeliveryPlatformAppConfig() {
		try {
			return Lion.getConfigRepository().get("agg.delivery.platform.app.config", StringUtils.EMPTY);
		} catch (Exception e) {
			log.error("getAggDeliveryPlatformAppConfig error", e);
			return StringUtils.EMPTY;
		}
	}

	public static String getAggDeliveryPlatformAppNewConfig() {
		try {
			return Lion.getConfigRepository().get("agg.delivery.platform.app.new.config", StringUtils.EMPTY);
		} catch (Exception e) {
			log.error("getAggDeliveryPlatformAppNewConfig error", e);
			return StringUtils.EMPTY;
		}
	}

	/**
	 * toB: 实时订单配送超时时长配置，单位：分钟。从超过预计送达时间开始计时
	 */
	public static int getInTimeOrderDeliveryTimeOutDuration2B() {
		return Lion.getConfigRepository().getIntValue("delivery.timeout.duration-in-minutes.inTime.toB", 0);
	}

	/**
	 * toC: 实时订单配送超时时长配置，单位：分钟。从超过预计送达时间开始计时
	 */
	public static int getInTimeOrderDeliveryTimeOutDuration2C() {
		return Lion.getConfigRepository().getIntValue("delivery.timeout.duration-in-minutes.inTime.toC", 0);
	}

	/**
	 * toB: 预约订单配送超时时长配置，单位：分钟。从超过预计送达时间开始计时
	 */
	public static int getBookingOrderDeliveryTimeOutDuration2B() {
		return Lion.getConfigRepository().getIntValue("delivery.timeout.duration-in-minutes.booking.toB", 5);
	}

	/**
	 * toC: 预约订单配送超时时长配置，单位：分钟。从超过预计送达时间开始计时
	 */
	public static int getBookingOrderDeliveryTimeOutDuration2C() {
		return Lion.getConfigRepository().getIntValue("delivery.timeout.duration-in-minutes.booking.toC", 0);
	}

	/**
	 * 是否发送配送超时检查消息（检查是否骑手已接单） 说明：上线完成3分钟后配置为true，为了平滑上线
	 */
	public static boolean publishDeliveryTimeOutCheckRiderAssignedMsg() {
		return Lion.getConfigRepository().getBooleanValue("publish.delivery.time.out.check.rider.assigned.msg", false);
	}


	/**
	 * 歪马骑手接单超时时长配置，单位：分钟。从配送任务下发后开始计时
	 */
	public static int getDHRiderAssignTimeoutMinutes() {
		return Lion.getConfigRepository().getIntValue("drunk.horse.rider.assigned.timeout.minutes", 1);
	}

	/**
	 * 歪马骑手取货超时时长配置，单位：分钟。从配送任务下发后开始计时
	 */
	public static int getDHRiderTakenGoodsTimeoutMinutes() {
		return Lion.getConfigRepository().getIntValue("drunk.horse.rider.taken.goods.timeout.minutes", 4);
	}

	/**
	 * 骑手接单超时时长配置，单位：分钟。从配送任务下发后开始计时
	 */
	public static int getRiderAssignTimeoutMinutes() {
		return Lion.getConfigRepository().getIntValue("new.supply.rider.assigned.timeout.minutes", 1);
	}

	/**
	 * 骑手取货超时时长配置，单位：分钟。从配送任务下发后开始计时
	 */
	public static int getRiderTakenGoodsTimeoutMinutes() {
		return Lion.getConfigRepository().getIntValue("new.supply.rider.taken.goods.timeout.minutes", 4);
	}

	/**
	 * 获取歪马租户 ID
	 */
	@Deprecated
	public static Long getWaiMaTenantId() {
		return Lion.getConfigRepository().getLongValue("waima.tenant.id", 1000395L);
	}

	/**
	 * 歪马送酒租户id
	 */
	@SuppressWarnings({"UnstableApiUsage"})
	public static List<String> getDHTenantIdList(){
		String tenantIdStr = Lion.getConfigRepository().get("drunk.horse.tenant.id.list", "1000395");
		if(StringUtils.isBlank(tenantIdStr)){
			return Collections.emptyList();
		}
		return Splitter.on(",").splitToList(tenantIdStr);
	}


	/**
	 * 是否是歪马租户
	 */
	public static Boolean checkIsDHTenant(Long tenantId) {
		if (Objects.isNull(tenantId)) {
			return false;
		}
		List<String> dhTenantIdList = getDHTenantIdList();
		return dhTenantIdList.contains(tenantId.toString());
	}

	public static Boolean checkPickLackTenant(Long tenantId){
		if (Objects.isNull(tenantId)) {
			return false;
		}

		String tenantIdStr = Lion.getConfigRepository().get("check.pick.lack.order.tenant.list", "1000395");
		if(StringUtils.isBlank(tenantIdStr)){
			return false;
		}
		List<String> tenantIdList = Splitter.on(",").splitToList(tenantIdStr);
		if(tenantIdList.contains(tenantId.toString())){
			return  true;
		}
		return false;
	}

	/**
	 * 预计送达时间前x分钟,给骑手推送超时语音提醒
	 */
	public static Integer getPushDeliveryTimeoutWarningTime() {
		return Lion.getConfigRepository().getIntValue("drunk.horse.push.delivery.timeout.warning", 3);
	}


	/**
	 * 获取压测租户id
	 * @return
	 */
	public static List<Long> getPressureTestTenantIds() {
		String tenantIdStr = Lion.getConfigRepository().get("pressure.test.tenant.ids", "1000087");
		if (StringUtils.isBlank(tenantIdStr)) {
			return Collections.emptyList();
		}

		return Splitter.on(",").splitToList(tenantIdStr).stream().map(Long::valueOf).collect(Collectors.toList());
	}

	public static long getEstimatedTimeOutMinimumSeconds() {
		return Lion.getConfigRepository().getIntValue("delivery.estimated.time.out.minimum.seconds", 300);
	}

	public static boolean getEstimatedTimeOutCheckSwitch() {
		return Lion.getConfigRepository().getBooleanValue("delivery.estimated.time.out.check.switch", true);
	}


	/**
	 * 开关-查询订单信息强制走主库
	 * @return
	 */
	public static Boolean queryOrderInfoFromMasterSwitch() {
		return Lion.getConfigRepository().getBooleanValue("query.order.info.from.master", true);
	}


	/**
	 * 向渠道同步配送信息时是否通过发MQ的方式
	 * @param tenantId
	 * @return
	 */
	public static Boolean isSyncByMQTenant(Long tenantId) {
		List<Long> tenantIds = Lion.getConfigRepository().getList("sync.rider.location.by.MQ.tenantIds", Long.class, Collections.singletonList(1000395L));
		if (CollectionUtils.isEmpty(tenantIds)) {
			return false;
		}

		if (tenantIds.size() == 1 && tenantIds.contains(-1L)) {
			return true;
		}

		return tenantIds.contains(tenantId);
	}

	/**
	 * 运单小于X分钟 美团不能取消配送
	 */
	public static int getMtCancelDeliveryLimitTime() {
		return Lion.getConfigRepository().getIntValue("mt.cancel.delivery.limit.time", 15);
	}

	/*
	 * 转单处理开关
	 */
	public static Boolean getDeliveryTransferConfigSwitch(){
		return Lion.getConfigRepository().getBooleanValue("delivery.transfer.config.switch", true);
	}

	public static Boolean switchToNewSquirrel() {
		return Lion.getConfigRepository().getBooleanValue("transfer.order.switch.new.category",true);
	}

	/**
	 * 从db查询配送渠道信息，默认关闭
	 */
	public static Boolean queryDeliveryChannelFromDBSwitch() {
		return Lion.getConfigRepository().getBooleanValue("query.delivery.channel.from.db.switch", false);
	}

	/**
	 * 查询青云智信的承运商code映射关系
	 */
	public static DeliveryChannelEnum dapChannelMapping(String dapChannelCode){
		if(org.apache.commons.lang3.StringUtils.isEmpty(dapChannelCode)){
			return DeliveryChannelEnum.DAP_DELIVERY;
		}
		//格式 dapChannelCode=DeliveryChannelEnumCode|....
		String codeMappingStr = Lion.getConfigRepository().get("dap.channel.code.mapping", "");
		if(org.apache.commons.lang3.StringUtils.isEmpty(codeMappingStr)){
			log.info("dapChannelMapping not set dapChannelCode:{}",dapChannelCode);
			return DeliveryChannelEnum.DAP_DELIVERY_UNKNOW;
		}
		try {
			List<String> mappingCodeList=Splitter.on("|").splitToList(codeMappingStr);
			for (String codeStr : mappingCodeList){
				String[] code = codeStr.split("=");
				if(code[0].equals(dapChannelCode)){
					return DeliveryChannelEnum.valueOf(Integer.parseInt(code[1]));
				}
			}
		}catch (Exception e){
			log.error("dapChannelMapping error codeMappingStr:{}，dapChannelCode:{}",codeMappingStr,dapChannelCode,e);
		}
		return DeliveryChannelEnum.DAP_DELIVERY_UNKNOW;
	}

	/**
	 * 查询特殊业务场景下承运商code，目前只有跑腿场景
	 */
	public static Map<String, List<Integer>> querySpecialBusinessDeliveryChannelMap() {
		String specialBusinessDeliveryChannelStr = Lion.getConfigRepository().get("special.business.delivery.channel.mapping", StringUtils.EMPTY);
		if (StringUtils.isEmpty(specialBusinessDeliveryChannelStr)) {
			return Maps.newHashMap();
		}

		Map<String, String> sourceMap = Maps.newHashMap();
		try {
			sourceMap = JSON.parseObject(specialBusinessDeliveryChannelStr, new TypeReference<Map<String, String>>() {}.getType());
			return sourceMap.entrySet().stream()
					.collect(Collectors.toMap(Map.Entry::getKey, entry -> Arrays.stream(entry.getValue().split(","))
							.map(Integer::valueOf).collect(Collectors.toList())));
		} catch (Exception e) {
			log.error("querySpecialBusinessDeliveryChannelMap error");
			return Maps.newHashMap();
		}
	}

	/**
	 * 查询承运商信息降级开关，降级后从枚举值DeliveryChannelEnum获取，默认关闭
	 */
	public static Boolean isQueryDeliveryChannelDegrade(){
		return Lion.getConfigRepository().getBooleanValue("delivery.channel.query.degrade.switch", false);
	}

	/**
	 * 从DB查询全量承运商信息时的batchSize
	 */
	public static int getBatchSize4QueryAllDeliveryChannelFromDB() {
		return Lion.getConfigRepository().getIntValue("query.all.delivery.channel.from.db.batch", 100);
	}

    public static boolean getTenantPoiFastAuthSwitch() {
        return Lion.getConfigRepository().getBooleanValue("tenant.poi.fast.auth.switch", true);
    }

	public static List<Long> getDHOfflinePromoteGrayStores() {
		try {
			List<Long> grayStores = Lion.getConfigRepository().getList("dh.offline.promote.gray.stores", Long.class);
			return Optional.ofNullable(grayStores).orElse(Lists.newArrayList());
		} catch (Exception e) {
			log.error("getDHOfflinePromoteGrayStores error", e);
			return Lists.newArrayList();
		}
	}

	public static boolean isDHOfflinePromoteGrayStore(Long storeId) {
		try {
			List<Long> dhOfflinePromoteGrayStores = getDHOfflinePromoteGrayStores();
			//为空就全量了
			if (CollectionUtils.isEmpty(dhOfflinePromoteGrayStores)) {
				return true;
			}
			return dhOfflinePromoteGrayStores.contains(storeId);
		} catch (Exception e) {
			log.error("isDHOfflinePromoteGrayStore error", e);
			return false;
		}
	}

	public static int dhTransDeliveryChannelTimesLimit() {
		return Lion.getConfigRepository().getIntValue("dh.trans.delivery.channel.max.times", 2);
	}

	public static boolean isDHAggDeliveryGrayStore(long poiId) {
		try {
			List<Long> grayPoiList = Lion.getConfigRepository().getList("dh.agg.delivery.gray", Long.class);
			//-1则全量
			if (CollectionUtils.isNotEmpty(grayPoiList) && grayPoiList.size() == 1 && grayPoiList.get(0).equals(-1L)) {
				return true;
			}
			return Optional.ofNullable(grayPoiList).orElse(Lists.newArrayList()).contains(poiId);
		} catch (Exception e) {
			log.error("isDHAggDeliveryGrayStore error", e);
			return false;
		}

	}

	public static boolean isPreOrderGrayStore(long poiId) {
		try {
			List<Long> grayPoiList = Lion.getConfigRepository().getList("dh.pre.order.gray", Long.class, Lists.newArrayList());
			//-1则全量
			if (CollectionUtils.isNotEmpty(grayPoiList) && grayPoiList.size() == 1 && grayPoiList.get(0).equals(-1L)) {
				return true;
			}
			return Optional.ofNullable(grayPoiList).orElse(Lists.newArrayList()).contains(poiId);
		} catch (Exception e) {
			log.error("isDHAggDeliveryGrayStore error", e);
			return false;
		}

	}

	public static int getRetryPeriod() {
		return Lion.getConfigRepository().getIntValue("cancel.third.delivery.retry.period", 300);
	}

	public static List<String> getTurnToDapPushMis() {
		return Lion.getConfigRepository().getList("dh.agg.delivery.gray", String.class, Lists.newArrayList("jianglilin02"));
	}


	public static Integer getDelayNotifyDuration() {
		return Lion.getConfigRepository().getIntValue("dh.delay.notify.delivery.duration", 5000);
	}

	/**
	 * 医药无人仓（成人无人仓门店开关）
	 * @return
	 */
	public static Boolean getMedicineUnmannedWarehouseSwitch(){
		return Lion.getConfigRepository().getBooleanValue("medicine.unmanned.warehouse.switch", false);
	}

	/**
	 * 歪马配送门店信息同步开关
	 * @return
	 */
	public static Boolean getDrunkHorseDeliveryPoiSyncSwitch() {
		return Lion.getConfigRepository().getBooleanValue("drunk.horse.delivery.poi.sync.switch", Boolean.FALSE);
    }

	/**
	 * 租户门店变更同步开关
	 * @return
	 */
	public static Boolean getTenantPoiChangeSyncSwitch() {
		return Lion.getConfigRepository().getBooleanValue("tenant.poi.change.sync.switch", Boolean.TRUE);
	}

	public static Boolean filterUnequalDeliveryPlatform() {
		return Lion.getConfigRepository().getBooleanValue("filter.unequal.delivery.platform", true);
	}

	public static Boolean duplicateMessagePush() {
		return Lion.getConfigRepository().getBooleanValue("duplicate.message.push", false);
	}

	public static Boolean getOnlyUpdateRiderInfoSwitch() {
		return Lion.getConfigRepository().getBooleanValue("only.update.rider.info.switch", false);
	}

	/**
	 * 抖音平台配送默认deliveryChannelCode
	 */
	public static Integer getDyPlatformDeliveryChannelCode() {
		return Lion.getConfigRepository().getIntValue("dy.platform.delivery.channel.code", 51001);
	}

	/**
	 * 淘鲜达平台配送默认deliveryChannelCode
	 */
	public static Integer getTxdPlatformDeliveryChannelCode() {
		return Lion.getConfigRepository().getIntValue("txd.platform.delivery.channel.code", 54001);
	}

	public static Integer getStoreSplitNum() {
		return Lion.getConfigRepository().getIntValue("store.split.num", 50);
	}

	public static Integer getOrderIdSplitNum() {
		return Lion.getConfigRepository().getIntValue("orderId.split.num", 50);
	}

	/**
	 * 有赞渠道配送没有开启牵牛花管理也接收配送消息开关
	 */
	public static boolean yzPlatformDeliveryUpdateSwitch() {
		return Lion.getConfigRepository().getBooleanValue("yz.platform.delivery.update.switch",true);
	}

	/**
	 * 有赞平台配送，无具体承运商映射DeliveryType
	 */
	public static List<Integer> yzDeliveryNoCarrierTypeList() {
		return Lion.getConfigRepository().getList("yz.delivery.no.carrier.type.list", Integer.class, Collections.emptyList());
	}

	/**
	 * 第三方平台映射DistributeTypeEnum类型
	 */
	public static Map<String, Integer> orderChannelDistributeTypeMappingMap() {
		return Lion.getConfigRepository().getMap("order.channel.distribute.type.mapping.map", Integer.class);
	}

	/**
	 * 抖音平台配送状态回调mq最大重试次数
	 */
	public static Integer getDyPlatformDeliveryMqMaxRetryTimes() {
		return Lion.getConfigRepository().getIntValue("dy.platform.delivery.mq.max.retry.times", 3);
	}

	/**
	 * 抖音平台配送状态回调mq重试间隔
	 */
	public static Integer getDyPlatformDeliveryMqDelaySeconds() {
		return Lion.getConfigRepository().getIntValue("dy.platform.delivery.mq.delay.seconds", 60);
	}

	/**
	 * 淘鲜达平台配送状态回调mq最大重试次数
	 */
	public static Integer getTxdPlatformDeliveryMqMaxRetryTimes() {
		return Lion.getConfigRepository().getIntValue("txd.platform.delivery.mq.max.retry.times", 3);
	}

	/**
	 * 淘鲜达平台配送状态回调mq重试间隔
	 */
	public static Integer getTxdPlatformDeliveryMqDelaySeconds() {
		return Lion.getConfigRepository().getIntValue("txd.platform.delivery.mq.delay.seconds", 60);
	}

	/**
	 * tms内部配送状态监听mq最大重试次数
	 */
	public static Integer getDeliveryChangeNotifyMqMaxRetryTimes() {
		return Lion.getConfigRepository().getIntValue("delivery.change.notify.mq.max.retry.times", 3);
	}

	/**
	 * tms内部配送状态监听mq重试间隔
	 */
	public static Integer getDeliveryChangeNotifyMqDelaySeconds() {
		return Lion.getConfigRepository().getIntValue("delivery.change.notify.mq.delay.seconds", 30);
	}

	/**
	 * tms监听订单信息变更mq最大重试次数
	 */
	public static Integer getOrderInfoChangeMqMaxRetryTimes() {
		return Lion.getConfigRepository().getIntValue("order.info.change.mq.max.retry.times", 3);
	}

	/**
	 * tms监听订单信息变更mq重试间隔
	 */
	public static Integer getOrderInfoChangeMqDelaySeconds() {
		return Lion.getConfigRepository().getIntValue("order.info.change.mq.delay.seconds", 30);
	}

	/**
	 * 新供给延迟发配送开关
	 */
	public static Boolean getNewSupplyDeliveryLaunchCommandMessageListenerSwitch() {
		return Lion.getConfigRepository().getBooleanValue("new.supply.delivery.launch.command.message.listener.switch", true);
	}

	/**
	 * 有赞同城配送状态回调处理开关
	 */
	public static Boolean getYzDeliveryChangeMessageListenerSwitch() {
		return Lion.getConfigRepository().getBooleanValue("yz.delivery.change.message.listener.switch", true);
	}

	/**
	 * 抖音平台配送状态回调处理开关
	 */
	public static Boolean getDyDeliveryChangeMessageListenerSwitch() {
		return Lion.getConfigRepository().getBooleanValue("dy.delivery.change.message.listener.switch", true);
	}

	/**
	 * 淘鲜达平台配送状态回调处理开关
	 */
	public static Boolean getTxdDeliveryChangeMessageListenerSwitch() {
		return Lion.getConfigRepository().getBooleanValue("txd.delivery.change.message.listener.switch", true);
	}

	public static boolean transDeliveryCancelSwitch() {
		return Lion.getConfigRepository().getBooleanValue("trans.delivery.cancel.switch",true);
	}

	/**
	 * 对外提供查询骑手轨迹能力，查询骑手坐标开关
	 */
	public static boolean getOuterQueryRiderLocation4DynamicInfo() {
		return Lion.getConfigRepository().getBooleanValue("outer.query.rider.location.for.dynamic.switch", true);
	}

	/**
	 * 对外提供查询配送信息能力，查询骑手坐标开关
	 */
	public static boolean getOuterQueryRiderLocation4DeliveryInfo() {
		return Lion.getConfigRepository().getBooleanValue("outer.query.rider.location.for.delivery.switch", true);
	}

	/**
	 * tms内部配送状态监听消费逻辑开关，返回true代表打开消费逻辑
	 */
	public static Boolean getDeliveryChangeConsumeSwitch() {
		return Lion.getConfigRepository().getBooleanValue("delivery.change.consume.switch", true);
	}

	/**
	 * 对外提供查询接口，配送完成后多少小时不展示骑手坐标
	 */
	public static Integer getOuterQueryDeliveryInfoNoShowRiderPositionHours() {
		return Lion.getConfigRepository().getIntValue("outer.query.delivery.info.no.show.rider.position.hours", 8);
	}


	public static Boolean isDhScenePoi(Long storeId) {
		try {
			List<Long> stores = Lion.getConfigRepository(ORDER_API_APPKEY).getList("dh_scene_poi_switch", Long.class, org.assertj.core.util.Lists.newArrayList());
			//全量逻辑
			if (stores.size() == 1 && stores.get(0).equals(-1L)) {
				return true;
			}
			return stores.contains(storeId);
		} catch (Exception e) {
			log.error("isDhScenePoi error", e);
			return false;
		}
	}

	public static List<String> getDhRestaurantSceneList() {
		try {
			return getConfigRepository().getList("dh.restaurant.scene", String.class, Lists.newArrayList("餐馆"));
		} catch (Exception e) {
			log.error("isDhScenePoi error", e);
			return Lists.newArrayList();
		}
	}

	/**
	 * 京东渠道再次呼叫骑手功能开关
	 */
	public static boolean getJddjRecallDeliverySwitch(){
		return Lion.getConfigRepository().getBooleanValue("jddj.recall.delivery.switch",true);
	}

	/**
	 * 触发京东渠道再次呼叫骑手对应的渠道配送状态
	 * https://opendj.jd.com/staticnew/widgets/resources.html?id=3001
	 */
	public static List<Integer> getJddjRecallDeliveryStatusList() {
		try {
			return Lion.getConfigRepository().getList("jddj.recall.delivery.status.list", Integer.class);
		} catch (Exception e) {
			return Collections.emptyList();
		}
	}

	public static List<Integer> getOrderPlatformDeliveryNotCoverCodeList() {
		return Lion.getConfigRepository().getList("order.plarform.delivery.not.cover.code.list", Integer.class, Lists.newArrayList(14, 18, 19));
	}

	/**
	 * 新订单流水号开关
	 */
	public static boolean getDaySeqNumSwitch(){
		return Lion.getConfigRepository().getBooleanValue("day.seq.num.switch", true);
	}

	public static boolean getPrivateChannelId (Integer channelId) {
		List<Integer> channelIdList = getConfigRepository().getList("private.channel.id", Integer.class);
		if (CollectionUtils.isEmpty(channelIdList)) {
			return false;
		}
		return channelIdList.contains(channelId);

	}

	public static Boolean isSwitchLockEndLaunchDelivery() {
		return Lion.getConfigRepository().getBooleanValue("lock.end.launch.delivery", true);
	}

	public static Boolean getPaotuiLockOrderV2Switch() {
		return Lion.getConfigRepository().getBooleanValue("paotui.lock.order.v2.switch", false);
	}

	/**
	 * 锁单2.0  锁单开关
	 * @return Boolean
	 */
	public static Boolean getPaotuiLockSwitch() {
		return Lion.getConfigRepository().getBooleanValue("paotui.v2.lock.switch", false);
	}

	/**
	 *  解锁2.0  解锁开关
	 *  @return Boolean
	 * @return
	 */
	public static Boolean getPaotuiUnLockSwitch() {
		return Lion.getConfigRepository().getBooleanValue("paotui.v2.unlock.switch", false);
	}

	/**
	 *  判断是否为配置的聚合配送平台
	 * @param deliveryPlatFormCode 配送平台编码
	 * @return Boolean
	 */
	public static Boolean isPaotuiLockAggDeliveryPlatform(Integer deliveryPlatFormCode) {
		List<String> list = getConfigRepository().getList("paotui.lock.agg.delivery.platform", String.class);
		if (CollectionUtils.isEmpty(list)) {
			return false;
		}
		if (list.size() == 1 && ALL.equalsIgnoreCase(list.get(0)) ) {
			return true;
		}
		return list.contains(String.valueOf(deliveryPlatFormCode));
	}

	/**
	 * 是否锁单2，0灰度租户
	 * @param tenantId 租户ID
	 * @return Boolean
	 */
	public static Boolean isPaotuiLockOrderV2GrayTenant(Long tenantId) {
		List<Long> tenantIdList = getConfigRepository().getList("paotui.lock.order.v2.tenant.ids", Long.class, Collections.emptyList());
		if (CollectionUtils.isEmpty(tenantIdList)) {
			return false;
		}
		if (tenantIdList.size() == 1 && Objects.equals(tenantIdList.get(0), -1L)) {
			return true;
		}
		return tenantIdList.contains(tenantId);
	}

	public static Boolean getUnlockLaunchAggDeliveryDistributeStatus(Integer distributeStatus) {
		List<Integer> distributeStatusList = getConfigRepository().getList("unlock.launch.agg.delivery.distribute.status", Integer.class);
		if (CollectionUtils.isEmpty(distributeStatusList)) {
			return false;
		}
		return distributeStatusList.contains(distributeStatus);

	}

	public static List<String> getPaoTuiCode(){
		String codeStr=getConfigRepository().get("delivery.paotui.code","20024,40000");
		if(org.apache.commons.lang3.StringUtils.isEmpty(codeStr)){
			return Collections.emptyList();
		}
		List<String> codeList = Splitter.on(",").splitToList(codeStr);
		if(CollectionUtils.isEmpty(codeList)){
			return Collections.emptyList();
		}
		return codeList;
	}

	/**
	 * 是否是歪马租户
	 */
	public static boolean isQueryRouteGrayStore(Long storeId) {
		List<Long> grayStoreIds = getConfigRepository().getList("query.route.gray.store.list", Long.class, Collections.emptyList());

		if (CollectionUtils.isEmpty(grayStoreIds)) {
			return false;
		}

		if (grayStoreIds.size() == 1 && Objects.equals(grayStoreIds.get(0), -1L)) {
			return true;
		}

		return grayStoreIds.contains(storeId);
	}


	/**
	 * 新供给处理自配送骑手信息变更功能开关
	 */
	public static boolean getProcessSelfDeliveryRiderChangeSwitch(){
		return Lion.getConfigRepository().getBooleanValue("process.self.delivery.rider.change.switch", true);
	}

	private static final List<Integer> TMS_CANCEL_CHANNEL_DELIVERY_DEFAULT = Arrays.asList(2300, 2200, 501);

	/**
	 * tms侧直接将运单配送状态推进至「配送已取消」，无需调用渠道平台取消配送接口的场景，返回true
	 */
	public static boolean isTmsCancelDeliveryChannel(Integer orderBizType) {
		List<Integer> list = getConfigRepository().getList("tms.cancel.delivery.channel", Integer.class, TMS_CANCEL_CHANNEL_DELIVERY_DEFAULT);
		if (CollectionUtils.isEmpty(list)) {
			return false;
		}
		return list.contains(orderBizType);
	}

	/**
	 * 发聚合配送同步价格设置
	 */
	public static boolean aggDeliveryPriceTenantWhitelist(Long tenantId) {
		String whitelist = Lion.getConfigRepository(OCMS_CHANNEL_APPKEY).get("agg.delivery.price.tenant.whitelist", StringUtils.EMPTY);
		return isWhite(tenantId, whitelist);
	}

	/**
	 * 白名单通用判断
	 * whitelist格式：1234,3444554 、all
	 */
	private static boolean isWhite(Number id, String whiteStr) {
		if (StringUtils.isBlank(whiteStr)) {
			return false;
		}
		//所有租户打开，不需要灰度
		if (ALL.equals(whiteStr)) {
			return true;
		}
		String[] whiteArray = StringUtils.split(whiteStr, ",");
		return Arrays.stream(whiteArray).anyMatch(whiteId -> String.valueOf(id).equals(whiteId.trim()));
	}

	/**
	 * 返回true代表门店在适配订单信息变更白名单
	 */
	public static boolean isOrderInfoChangeStoreIdWhiteList(Long storeId) {
		String storeIdWhiteList = Lion.getConfigRepository().get("order.info.change.consume.storeId.whitelist", WHITE_LIST_EMPTY);
		if (MccConfigUtils.WHITE_LIST_EMPTY.equals(storeIdWhiteList)) {
			// 不配置任何门店进白名单
			return false;
		}
		if (MccConfigUtils.WHITE_LIST_ALL.equals(storeIdWhiteList)) {
			// 全量放开
			return true;
		}
		return Arrays.stream(storeIdWhiteList.split(",")).collect(Collectors.toList()).contains(storeId.toString());
	}

	public static Boolean getDeliveryQueryTenantSwitch(Long tenantId){
		if(tenantId == null || tenantId<=0){
			log.info("getPickSelectQueryJoinSwitch tenantId:{}",tenantId);
			return false;
		}
		return getDeliveryQueryTenantSwitch();
	}

	public static Boolean getDeliveryQueryTenantSwitch(){
		return Lion.getConfigRepository().getBooleanValue("delivery.query.tenant.switch", true);
	}

	public static Boolean getDeliveryCancelFilterSwitch() {
		return Lion.getConfigRepository().getBooleanValue("delivery.final.status.filter.switch", false);
	}

	public static List<Integer> getNeedDownType() {
		List<Integer> list = getConfigRepository().getList("order.down.type", Integer.class);

		if (CollectionUtils.isEmpty(list)) {
			return new ArrayList<>();
		}
		return list;
	}

	public static boolean switchOrderDown() {
		return Lion.getConfigRepository().getBooleanValue("switch.order.down", false);
	}

	public static boolean needReportWithTenantId(Long tenantId) {
		List<String> codeStr=Lion.getConfigRepository().getList("need.report.tenant",String.class);
		if(CollectionUtils.isEmpty(codeStr)){
			return false;
		}
		if (codeStr.contains(ALL)) {
			return true;
		}
		return codeStr.contains(String.valueOf(tenantId));
	}

	/**
	 * 非医药无人仓（成人无人仓门店开关）（替换货号）
	 * @return
	 */
	public static boolean getNotMedicineUnmannedWarehouseSwitch(Long tenantId) {
		try {
			if (tenantId == null) {
				return false;
			}

			String tenantIdList = getConfigRepository().get("all.replace.goods.code.switch", "all");

			if ("all".equals(tenantIdList)) {
				return true;
			}

			final List<String> collect = Arrays.stream(tenantIdList.split(",")).collect(Collectors.toList());

			return collect.contains(tenantId.toString());
		} catch (Exception e) {
			return false;
		}
	}

	/**
	 * 查询配送详情列表batchSize上限
	 */
	public static Integer getQueryActiveDeliveryInfoLimitSize() {
		return Lion.getConfigRepository().getIntValue("query.active.delivery.info.limit.size", 50);
	}

	/**
	 * 查询聚合配送URL batchSize上限
	 */
	public static Integer getAggRedirectConfigLimitSize() {
		return Lion.getConfigRepository().getIntValue("query.agg.redirect.config.limit.size", 50);
	}

	public static boolean openChannelDeliveryDockingThriftService() {
		return Lion.getConfigRepository().getBooleanValue("channel.delivery.docking.thrift.service", false);

	}

	/**
	 * 批量查询原始运单号数据源切换开关
	 */
	public static boolean queryOriginWaybillNoSource() {
		return Lion.getConfigRepository().getBooleanValue("query.originwaybillno.source.es.switch", true);
	}

	/**
	 * DB批量查询原始运单号批量大小
	 */
	public static int queryOriginWaybillNoSourceDbLimit() {
		return Lion.getConfigRepository().getIntValue("query.originwaybillno.source.db.limit", 100);
	}

	/**
	 * @return 有效的运单状态（进行中/完成）
	 */
	public static List<Integer> getEmployDeliveryStatus() {
		return Lion.getConfigRepository(ORDER_API_APPKEY).getList("originWaybill.view.deliveryStatus", Integer.class, EMPLOY_DELIVERY_STATUS);
	}

	public static Boolean loadTestAopOpenFlag() {
		return Lion.getConfigRepository().getBooleanValue("load.test.open.flag", false);
	}

	public static boolean isAssessTimeBySecondStore(long storeId) {
		try {
			List<Long> stores = Lion.getConfigRepository().getList("seconds.assess.time.gray", Long.class, Lists.newArrayList());
			//全量逻辑
			if (stores.size() == 1 && stores.get(0).equals(-1L)) {
				return true;
			}
			return stores.contains(storeId);
		} catch (Exception e) {
			log.error("isDhScenePoi error", e);
			return false;
		}
	}

	/**
	 * @return 是否切换至新的配送异常推送逻辑，主要是配送异常权限码
	 */
	public static boolean pushDeliveryExceptionAuthCodeSwitch() {
		return Lion.getConfigRepository().getBooleanValue("push.delivery.exception.auth.code.switch", true);
	}

	/**
	 * 自配送订单消费租户白名单
	 */
    public static boolean getSelfDeliveryOrderConsumeSwitch(Long tenantId) {
		if (Objects.isNull(tenantId)) {
			return false;
		}
		List<Long> list = getConfigRepository().getList("self.delivery.order.tenant.list", Long.class, Collections.emptyList());
		if (list.contains(-1L)) {
			// 全量
			return true;
		}
		return list.contains(tenantId);
    }

	/**
	 * 获取降级订单补偿类型code【针对牵牛花订单解密失败转自送降级】
	 * @return
	 */
	public static List<Integer> getDownOrderCompensateTypeCode() {
		List<Integer> compensateTypeCodes = Lion.getConfigRepository().getList("down.order.compensate.type.code", Integer.class, Arrays.asList(2301,16));
		return compensateTypeCodes;
	}

	public static boolean closeQueryDeliveryConfigSwitch() {
		return Lion.getConfigRepository().getBooleanValue("close.query.delivery.config.switch", true);
	}

	/**
	 * key-> 平台品类id; value-> 牵牛花主营品类codes
	 * 配送平台品类映射
	 */
	public static Map<String, List<String>> getDeliveryPlatformCategoryMapping() {
		Map<String, List<String>> map = new HashMap<>();
		Map<String, String> mappingMap = getConfigRepository().getMap("delivery.platform.category.mapping");
		mappingMap.forEach((categoryId, categoryCodes) -> map.put(categoryId, DOT_SPLITTER.splitToList(categoryCodes)));
		return map;
	}

	public static Map<String,String> getTenantCategoryMapping(){
		return getConfigRepository().getMap("tenant.category.mapping");
	}

	public static boolean deliveryStatusDefaultRiderLocationSwitch() {
		return Lion.getConfigRepository().getBooleanValue("delivery.status.default.riderLocation.switch", true);
	}

	/**
	 * 聚合配送骑手位置兜底配置
	 * Double[角度, 距离]
	 */
	public static Map<String, Double[]> deliveryStatusDefaultRiderLocationConfig() {
		try {
			Map<String, String> map = getConfigRepository().getMap("delivery.status.default.riderLocation");
			Map<String, Double[]> mapConfig = new HashMap<>();
			map.forEach((k, v) -> {
				if (StringUtils.isBlank(v)) {
					mapConfig.put(k, new Double[]{0.0, 0.0});
					return;
				}
				String[] values = v.split(",");
				if (values.length != 2) {
					mapConfig.put(k, new Double[]{0.0, 0.0});
					return;
				}
				mapConfig.put(k, new Double[]{Double.parseDouble(values[0]), Double.parseDouble(values[1])});
			});
			return mapConfig;
		} catch (Exception e) {
			log.error("get deliveryStatusDefaultRiderLocationConfig error   ", e);
			return new HashMap<>();
		}
	}

	/**
	 * 跑腿不同步配送信息到渠道的OrderBizType
	 */
	public static boolean paoTuiUploadFilterChannel(Integer orderBizType) {
		String orderBizTypes = getConfigRepository().get("paotui.upload.filter.channel", "101");
		if (StringUtils.isBlank(orderBizTypes)) {
			return false;
		}
		return Splitter.on(",").splitToList(orderBizTypes).contains(String.valueOf(orderBizType));
	}

	/**
	 * 跑腿同步配送信息到渠道的租户
	 * order_biz共用
	 */
	public static boolean paoTuiUploadTenantIds(Long tenantId) {
		if (Objects.isNull(tenantId)) {
			return false;
		}
		List<Long> list = getConfigRepository().getList("paotui.upload.tenant.list", Long.class, Collections.emptyList());
		if (list.contains(-1L)) {
			// 全量
			return true;
		}
		return list.contains(tenantId);
	}

	public static Map<String,FourWheelBaseInfo> getFourWheelBaseInfo(){
		Map<String,FourWheelBaseInfo> picInfoMap = Lion.getConfigRepository().getMap("four.wheel.delivery.base.info", FourWheelBaseInfo.class);
		if(MapUtils.isEmpty(picInfoMap)){
			return new HashMap<>();
		}
		return picInfoMap;
	}
	/*
	 * 青云H5链路鉴权开关
	 */
	public static boolean getDapLinkAuthSwitch() {
		return Lion.getConfigRepository().getBooleanValue("dap.link.auth.switch", true);
	}

	/**
	 * 青云H5链路鉴权灰度名单
	 * @return
	 */
	public static boolean dapLinkAuthDHStoreGrayList(Long storeId) {
		List<Long> list = getConfigRepository().getList("dap.link.auth.dh.store.gray.list", Long.class, Collections.emptyList());
		if (list.contains(-1L)) {
			// 全量
			return true;
		}
		return list.contains(storeId);
	}

	public static boolean openServicePhoneSwitch() {
		return getConfigRepository().getBooleanValue("open.store.service.phone", false);
	}

	public static boolean getMaltLinkAuthSwitch() {
		return Lion.getConfigRepository().getBooleanValue("malt.link.auth.switch", true);
	}

	public static boolean getAllEsSwitch() {
		return Lion.getConfigRepository().getBooleanValue("es.all.switch", true);
	}

	public static boolean getLockInDeliverySwitch() {
		return Lion.getConfigRepository().getBooleanValue("lock.in.delivery.switch", true);
	}

	/**
	 * 检查租户门店是否开启取消配送push
	 */
	public static boolean checkTenantStoreOpenCancelDeliveryPush(Long storeId) {
		try {
			List<Long> list = Lion.getConfigRepository()
					.getList("CANCEL_DELIVERY_PUSH_TENANT_STORE", Long.class, Collections.emptyList());
			// 全量逻辑
			if (list.contains(-1L)) {
				return true;
			}
			// 单店逻辑
			return list.contains(storeId);
		} catch (Exception e) {
			log.error("checkTenantOpenCancelDeliveryPush error", e);
			return false;
		}
	}

	/**
	 * 判断聚合配送开启隐私配送的门店
	 */
	public static boolean checkStoreOpenAggPrivacyDelivery(Long storeId) {
		try {
			List<Long> list = Lion.getConfigRepository()
					.getList("STORE_OPEN_AGG_PRIVACY_DELIVERY", Long.class, Collections.emptyList());
			// 全量逻辑
			if (list.contains(-1L)) {
				return true;
			}
			// 单店逻辑
			return list.contains(storeId);
		} catch (Exception e) {
			log.error("checkStoreOpenAggPrivacyDelivery error", e);
			return false;
		}
	}

	/**
	 * 隐私配送性别脱敏字眼
	 */
	public static List<String> privacyDeliveryGenderHideList() {
		return Lion.getConfigRepository().getList("privacy.delivery.gender.hide.list", String.class, Lists.newArrayList("女士", "先生"));
	}

	/**
	 * 检查门店是否开启配置，-1 代表全量
	 */
	public static boolean checkStoreOpenConfigForList(String lionName, Long storeId) {
		try {
			if (StringUtils.isBlank(lionName)) {
				return false;
			}
			if (Objects.isNull(storeId)) {
				return false;
			}

			List<Long> openStoreIds = Lion.getConfigRepository().getList(lionName, Long.class, Collections.emptyList());
			// -1 表示全量
			if (openStoreIds.contains(-1L)) {
				return true;
			}
			return openStoreIds.contains(storeId);
		} catch (Exception e) {
			log.error("checkStoreOpenConfigForList error ,lionName:{} ,storeId:{}", lionName, storeId, e);
			return false;
		}
	}

	/**
	 * 检查租户是否开启配置，-1 代表全量
	 */
	public static boolean checkTenantOpenConfigForList(String lionName, Long tenantId) {
		try {
			if (StringUtils.isBlank(lionName)) {
				return false;
			}
			if (Objects.isNull(tenantId)) {
				return false;
			}

			List<Long> openTenantIds = Lion.getConfigRepository().getList(lionName, Long.class, Collections.emptyList());
			// -1 表示全量
			if (openTenantIds.contains(-1L)) {
				return true;
			}
			return openTenantIds.contains(tenantId);
		} catch (Exception e) {
			log.error("checkTenantOpenConfigForList error ,lionName:{} ,tenantId:{}", lionName, tenantId, e);
			return false;
		}
	}

	/**
	 * 获取lion Inter类型数据配置
	 */
	public static Integer getLionIntConf(String lionName, Integer defaultValue) {
		try {
			if (StringUtils.isBlank(lionName)) {
				return defaultValue;
			}
			return Lion.getConfigRepository().getIntValue(lionName, defaultValue);
		} catch (Exception e) {
			log.error("getLionInterConf error ,lionName:{}", lionName, e);
			return defaultValue;
		}
	}

	/**
	 * 获取lion Str类型数据配置
	 */
	public static String getLionStrConf(String lionName, String defaultValue) {
		try {
			if (StringUtils.isBlank(lionName)) {
				return defaultValue;
			}
			return Lion.getConfigRepository().get(lionName, defaultValue);
		} catch (Exception e) {
			log.error("getLionStrConf error ,lionName:{}", lionName, e);
			return defaultValue;
		}
	}

	/**
	 * 获取lion List类型数据配置
	 */
	public static <T> List<T> getLionListDataConf(String lionName, Class<T> clazz, List<T> defaultValue) {
		try {
			if (StringUtils.isBlank(lionName)) {
				return defaultValue;
			}
			String data = getConfigRepository().get(lionName);
			if (StringUtils.isBlank(data)) {
				return defaultValue;
			}
			return JSON.parseArray(data, clazz);
		} catch (Exception e) {
			log.error("getLionListDataConf error ,lionName:{}", lionName, e);
			return defaultValue;
		}
	}
}
