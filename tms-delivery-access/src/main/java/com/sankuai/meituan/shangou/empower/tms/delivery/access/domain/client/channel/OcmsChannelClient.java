package com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.channel;


import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.delivery.FarmPaoTuiPreDeliveryDetailDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.delivery.FarmPaotuiCancelInnerResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poishipping.PoiShippingInfoDTO;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrder;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum;
import com.sankuai.meituan.shangou.dms.base.model.value.CoordinatePoint;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Rider;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.RiderDeliveryOrder;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.RiderLocationDetail;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.client.sync.DeliveryChangeSyncOutMessage;
import org.apache.thrift.TException;

import java.util.List;

/**
 * 渠道系统客户端
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/1/21
 */
public interface OcmsChannelClient {

	void syncRiderLocation(DeliveryOrder deliveryOrder, CoordinatePoint riderLocationPoint);

	void syncDrunkHorseRiderLocation(DeliveryOrder deliveryOrder, CoordinatePoint riderLocationPoint);

	/**
	 * 通用接口，用于同步运单变更信息给ocms-channel，不建议上层业务直接调用，而是通过
	 * com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.consumer
	 * .MQConsumerEnum#DELIVERY_CHANGE_ASYNC_OUT发送消息，并通过DeliveryChangeSyncOutMessageListener异步调用
	 * @param syncOutMessage
	 */
	void syncDeliveryChange(DeliveryChangeSyncOutMessage syncOutMessage);

	FarmPaoTuiPreDeliveryDetailDTO preDelivery(Long tenantId, Long storeId, String channelOrderId);

	List<PoiShippingInfoDTO> queryPoiShippingInfo(Long tenantId, Long storeId, Integer channelId) throws TException;

	void updateDeliveryProofPhoto(RiderDeliveryOrder deliveryOrder, CoordinatePoint coordinatePoint) throws TException;

	void syncDeliveryPlatformChange(Long tenantId, Long storeId, String channelOrderId, int orderBizType, DeliveryPlatformEnum deliveryPlatformEnum) throws TException;

	void onlySyncRiderInfo(DeliveryOrder deliveryOrder);

	FarmPaotuiCancelInnerResponse cancelDelivery(Long tenantId, Long storeId, String channelOrderId);

	/**
	 * 同步渠道骑手信息变更
	*/
	void syncRiderInfoChange(DeliveryOrder deliveryOrder, Rider rider, String channelOrderId);

	/**
	 * 同步渠道骑手批量位置信息变更
	 */
	boolean syncRiderBatchLocationToChanel(DeliveryOrder deliveryOrder, List<RiderLocationDetail> list);

	/**
	 * 同步渠道骑手所有位置信息变更
	 */
	boolean syncRiderAllLocationToChanel(DeliveryOrder deliveryOrder, List<RiderLocationDetail> list);
}
