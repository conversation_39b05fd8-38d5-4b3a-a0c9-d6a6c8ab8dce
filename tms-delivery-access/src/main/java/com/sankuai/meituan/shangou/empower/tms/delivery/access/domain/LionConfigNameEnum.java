package com.sankuai.meituan.shangou.empower.tms.delivery.access.domain;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2025/7/31
 */
@Getter
@AllArgsConstructor
public enum LionConfigNameEnum {
    OPEN_CHANNEL_RIDER_POINT_BATCH_SYNC("OPEN_CHANNEL_RIDER_POINT_BATCH_SYNC", "渠道配送骑手点位批量同步开关"),
    OPEN_CHANNEL_RIDER_POINT_BATCH_SYNC_FOR_TENANT("OPEN_CHANNEL_RIDER_POINT_BATCH_SYNC_FOR_TENANT", "渠道配送骑手点位批量同步开关(租户纬度)"),
    RIDER_POINT_BATCH_SYNC_DELAY_TIME("RIDER_POINT_BATCH_SYNC_DELAY_TIME", "骑手点位批量同步延时时间"),
    RIDER_ALL_POINT_SYNC_DELAY_TIME("RIDER_ALL_POINT_SYNC_DELAY_TIME", "骑手所有点位同步延时时间"),
    BATCH_REPORT_RIDER_LOCATION_URL("BATCH_REPORT_RIDER_LOCATION_URL", "批量上报骑手点位同步url"),
    BATCH_REPORT_RIDER_LOCATION_DISTANCE_FILTER("BATCH_REPORT_RIDER_LOCATION_DISTANCE_FILTER", "批量上报骑手点位同步距离"),
    MERCHANT_SELF_DELIVERY_APP_VERSION_CONF("MERCHANT_SELF_DELIVERY_APP_VERSION_CONF", "商家自送app版本处理"),
    RIDER_POINT_SYNC_V2_DELAY_TIME("RIDER_POINT_SYNC_V2_DELAY_TIME", "骑手点位同步v2版本延时时间-自配送"),
    RIDER_POINT_SYNC_AGG_V2_DELAY_TIME("RIDER_POINT_SYNC_AGG_V2_DELAY_TIME", "骑手点位同步v2版本延时时间-聚合配送"),
    RIDER_CURRENT_POINT_REDIS_EXPIRE_TIME("RIDER_CURRENT_POINT_REDIS_EXPIRE_TIME", "骑手当前点位过期时间"),
    RIDER_ALL_POINT_REDIS_EXPIRE_TIME("RIDER_ALL_POINT_REDIS_EXPIRE_TIME", "骑手所有点位过期时间"),
    ;

    private final String name;

    private final String desc;


}
