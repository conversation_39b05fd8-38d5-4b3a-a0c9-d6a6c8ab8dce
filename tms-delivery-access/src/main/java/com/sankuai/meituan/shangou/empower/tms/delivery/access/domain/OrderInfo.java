package com.sankuai.meituan.shangou.empower.tms.delivery.access.domain;

import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.meituan.shangou.saas.order.platform.enums.DistributeMethodEnum;
import com.meituan.shangou.saas.order.platform.enums.OrderSourceEnum;
import com.meituan.shangou.saas.order.platform.enums.OrderStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.DaySeqNumUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.TmsMccUtils;
import com.sankuai.meituan.shangou.dms.base.model.value.Receiver;
import lombok.*;

import javax.annotation.Nullable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * 订单信息模型
 * 仅包含与配送相关的信息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/3
 */
@Getter
@ToString
@AllArgsConstructor
public class OrderInfo {

	//配送订单的默认重量 1KG
	private static final int DEFAULT_DELIVERY_ORDER_WEIGHT = 1000;

	/**
	 * 订单业务key
	 */
	private final OrderKey orderKey;

	/**
	 * 渠道订单id
	 */
	private final String channelOrderId;

	/**
	 * 订单日流水号
	 */
	private final Long daySeq;

	/**
	 * 订单日流水号(新)
	 */
	private final String daySeqNum;

	/**
	 * 订单渠道
	 *
	 * @see com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType
	 */
	private final Integer orderBizType;

	/**
	 * 订单来源
	 *
	 * @see OrderSourceEnum
	 */
	private final Integer orderSource;

	/**
	 * 订单状态
	 *
	 * @see OrderStatusEnum
	 */
	private final Integer orderStatus;

	/**
	 * 是否商家配送订单(非订单平台配送)
	 */
	@Setter
	private boolean isSelfDelivery;

	/**
	 * 配送方式
	 *
	 * @see DistributeMethodEnum
	 */
	private final Integer deliveryMethod;

	/**
	 * 收货人信息
	 */
	private final Receiver receiver;

	/**
	 * 预计送达时间
	 * 默认值1970-01-01 00:00:00.000
	 */
	private final LocalDateTime estimatedDeliveryTime;

	/**
	 * 预计送达结束时间，针对预计送达时间为时间段时有效，否则等于预计送达时间
	 * 默认值1970-01-01 00:00:00.000
	 */
	private final LocalDateTime estimatedDeliveryEndTime;

	/**
	 * 是否是预订单
	 */
	private final boolean isBookingOrder;

	/**
	 * 原始配送类型
	 */
	private final Integer originalDistributeType;

	/**
	 * 订单原总金额(单位：分)
	 */
	private final Integer originalTotalAmount;

	/**
	 * 商品列表
	 */
	private final List<GoodsInfo> goodsList;

	/**
	 * 订单备注
	 */
	private final String comments;

	/**
	 * 发票抬头
	 */
	private final String invoiceTitle;

	/**
	 * 配送渠道id
	 */
	private final Integer deliveryChannelId;

	/**
	 * 渠道配送ID
	 */
	private final String channelDeliveryId;

	/**
	 * 是否正在配送中
	 */
	private final boolean inDelivery;

	/**
	 * 骑手姓名
	 */
	private final String riderName;

	/**
	 * 骑手电话
	 */
	private final String riderPhone;

	/**
	 * 用户实付金额(单位:分)
	 */
	private final Integer actualPayAmt;

	/**
	 * 订单创建时间
	 */
	private final LocalDateTime createTime;

	/**
	 * 订单支付时间
	 */
	private final LocalDateTime payTime;

	/**
	 * 商家接单时间
	 */
	private final LocalDateTime merchantConfirmOrderTime;

	@Setter
	private Long warehousePoiId;

	/**
	 * 订单优惠金额
	 */
	private Integer discountAmt;

	/**
	 * 骑手电话
	 */
	private final String riderPhoneToken;

	@Setter
	private Integer distributeStatus;

	/**
	 * 拣货完成时间
	 */
	@Setter
	private LocalDateTime pickCompleteTime;

	/**
	 * 用户id
	 */
	private Long userId;

	@Nullable
	private OrderExtInfo orderExtInfo;


	/**
	 * 中台订单赠品信息
	 */
	private final List<GiftGoodsInfo> giftGoodsList;

	@Setter
	private OrderTransInfo orderTransInfo;

	/**
	 * 代签点
	 */
	private String signPosition;

	/**
	 * 是否是一元单
	 */
	private Boolean isOneYuanOrder;

	/**
	 * description = "品类列表,如[美食,火锅]"
	 */
	private String category;

	/**
	 * 拣货状态
	 */
	private Integer pickStatus;

	/**
	 * 渠道包裹id
	 */
	private String channelPackageId;

	/**
	 * 锁单标识
	 */
	@Setter
	private Boolean orderLockLabel;

	/**
	 * 锁单状态
	 */
	@Setter
	private Boolean isLocked;

	/**
	 * 是否是闪电送订单
	 */
	private Boolean isFastOrder;

	/**
	 * 闪电送是否转自配送
	 */
	private Boolean isFastOrderToSelfDelivery;

	/**
	 * 正单地址变更费，单位元
	 */
	private String addressChangeFee;

	/**
	 * 退单地址变更费，单位元
	 */
	private String refundAddressChangeFee;
	private Integer downFlag;

	private String degradeModules;

	/**
	 * 扩展字段
	 */
	private OrderExt orderExt;

	/**
	 * 是否为美团名酒馆订单，true：是
	 */
	private Boolean isMtFamousTavern;

	/**
	 * 用户号码不打印，true：不进行打印
	 */
	private Boolean phoneNoPrint;

	/**
	 * 是否订单有隐私商品
	 */
	@Setter
	private Boolean privacyGoods;

	/**
	 *  订单类型
	 *  0 普通订单 1 拼团订单 2 团长代收 3 团购订单,4-线下订单，5-虚拟商品订单
	 *
	 *
	 * @see com.meituan.shangou.saas.order.platform.enums.OrderGroupIdEnum
	 */
	private Integer groupId;

	private List<Long> labelList;



	public Long getBrandId() {
		if (Objects.isNull(orderExt)) {
			return null;
		}
		return orderExt.getOriginBrandId();
	}


	/**
	 * 是闪电送订单且未转自配
	 */
	public boolean fastOrder() {
		if (isFastOrder == null) {
			return false;
		}
		return isFastOrder && (isFastOrderToSelfDelivery == null || !isFastOrderToSelfDelivery);
	}

	/**
	 * 是否可以发起配送
	 */
	public boolean canLaunchDelivery() {
		if (isFinished()) {
			return false;
		}

		//部分商家自提也需要发配送
		//注意自提的话，isSelfDelivery为false，所以没和下面代码一起判断
		if (isStoreDelivery() && TmsMccUtils.isLaunchStoreDeliveryTenant(this.getOrderKey().getTenantId())) {
			return true;
		}

		if (orderSource == OrderSourceEnum.OTO_ONLINE_MIDDLE.getValue() || orderSource == OrderSourceEnum.GLORY.getValue()) {
			return isSelfDelivery && isDeliveryToHome();

		} else if (orderSource == OrderSourceEnum.OTO_ONLINE.getValue()) {
			return isDeliveryToHome();
		}

		return false;
	}

	/**
	 * 是否可以发起平台配送
	 */
	public boolean canLaunchPlatformDelivery() {
		if (isFinished()) {
			return false;
		}

		//部分商家自提也需要发配送
		//注意自提的话，isSelfDelivery为false，所以没和下面代码一起判断
		if (isStoreDelivery() && TmsMccUtils.isLaunchStoreDeliveryTenant(this.getOrderKey().getTenantId())) {
			return true;
		}

		if (orderSource == OrderSourceEnum.OTO_ONLINE_MIDDLE.getValue() || orderSource == OrderSourceEnum.GLORY.getValue()) {
			return !isSelfDelivery && isDeliveryToHome();

		} else if (orderSource == OrderSourceEnum.OTO_ONLINE.getValue()) {
			return isDeliveryToHome();
		}

		return false;
	}

	/**
	 * 是否可以手动呼叫配送
	 */
	public boolean canManualLaunchDelivery() {
		if (isFinished()) {
			return false;
		}

		//部分商家自提也需要发配送
		//注意自提的话，isSelfDelivery为false，所以没和下面代码一起判断
		if (isStoreDelivery() && TmsMccUtils.isLaunchStoreDeliveryTenant(this.getOrderKey().getTenantId())) {
			return true;
		}

		return isDeliveryToHome();
	}

	public boolean canTurnToSelfDelivery() {
		if (isFinished()) {
			return false;
		}

		if (orderSource == OrderSourceEnum.OTO_ONLINE_MIDDLE.getValue() || orderSource == OrderSourceEnum.GLORY.getValue()
				|| orderSource == OrderSourceEnum.OTO_ONLINE.getValue()) {
			return isDeliveryToHome();
		}

		return false;
	}

	/**
	 * 订单是否完结(结束/取消)
	 */
	public boolean isFinished() {
		return orderStatus == OrderStatusEnum.COMPLETED.getValue() || orderStatus == OrderStatusEnum.CANCELED.getValue();
	}

	/**
	 * 是否配送到家订单(非自提等)
	 */
	public boolean isDeliveryToHome() {
		return deliveryMethod == DistributeMethodEnum.HOME_DELIVERY.getValue();
	}

	public String getShopSequence() {
		String daySeq = DaySeqNumUtil.getDaySeqNumWithoutDHTenant(this.daySeq, this.daySeqNum, this.orderKey.getTenantId());
		DynamicOrderBizType orderBizTypeEnum = DynamicOrderBizType.findOf(this.orderBizType);
		if (Objects.isNull(orderBizTypeEnum)) {
			return String.valueOf(daySeq);
		}

		if (DynamicOrderBizType.ELE_ME.equals(orderBizTypeEnum)) {
			return "饿#" + daySeq;
		}
		if (DynamicOrderBizType.MEITUAN_WAIMAI.equals(orderBizTypeEnum)) {
			return "美#" + daySeq;
		}
		if (DynamicOrderBizType.JING_DONG.equals(orderBizTypeEnum)) {
			return "京#" + daySeq;
		}
		return String.valueOf(daySeq);
	}

	public int getGoodsTotalWeight() {
		BigDecimal totalWeight = new BigDecimal(0);
		for (GoodsInfo each : this.goodsList) {
			totalWeight = totalWeight.add(BigDecimal.valueOf(each.getQuantity()).multiply(BigDecimal.valueOf(each.getSingleGoodsWeight())));
		}
		//如果计算结果小于等于10克，默认采用1000克
		if (totalWeight.compareTo(BigDecimal.valueOf(10)) <= 0) {
			return DEFAULT_DELIVERY_ORDER_WEIGHT;
		}
		return totalWeight.intValue();
	}

	/**
	 * 商品信息模型
	 */
	@Getter
	@ToString
	@AllArgsConstructor
	public static class GoodsInfo {
		/**
		 * 闪购SKU编码
		 */
		private final String skuId;

		/**
		 * 商品名称
		 */
		@Setter
		private String name;
		/**
		 * 数量
		 */
		private final int quantity;
		/**
		 * 单个商品金额(单位：分)
		 */
		private final Integer singlePrice;
		/**
		 * 销售单位(g/kg/个)
		 */
		private final String sellUnit;
		/**
		 * 单个商品重量(单位：克)
		 */
		@Setter
		private Integer singleGoodsWeight;

		/**
		 * 商品货号（目前仅支持医药无人仓 成人无人仓）
		 */
		private OrderGoodsInfoExtInfo orderGoodsInfoExtInfo;

		/**
		 * 商品名称
		 */
		@Setter
		private boolean hasReplaceGoodsCode = false;

		public GoodsInfo(String skuId, String name, int quantity, Integer singlePrice, String sellUnit, Integer singleGoodsWeight) {
			this.skuId = skuId;
			this.name = name;
			this.quantity = quantity;
			this.singlePrice = singlePrice;
			this.sellUnit = sellUnit;
			this.singleGoodsWeight = singleGoodsWeight;
		}

		public GoodsInfo(GiftGoodsInfo giftGoodsInfo) {
			this.skuId = giftGoodsInfo.getSkuId();
			this.name = giftGoodsInfo.getName();
			this.quantity = giftGoodsInfo.getQuantity();
			this.singlePrice = 0;
			this.sellUnit = "";
			this.singleGoodsWeight =0;
			this.orderGoodsInfoExtInfo=new OrderGoodsInfoExtInfo(giftGoodsInfo.getGoodsCode());
		}
	}

	@Getter
	@ToString
	@AllArgsConstructor
	public static class GiftGoodsInfo {
		/**
		 * 闪购SKU编码
		 */
		private final String skuId;

		/**
		 * 商品名称
		 */

		private final String name;
		/**
		 * 数量
		 */
		private final int quantity;

		/**
		 * 商品货号（目前仅支持医药无人仓 成人无人仓）
		 */
		private final String goodsCode;



	}


	@AllArgsConstructor
	@Data
	@NoArgsConstructor
	public static class OrderExt {
		private Long originBrandId;
	}



	public boolean isStoreDelivery() {
		return Objects.equals(this.getDeliveryMethod(),DistributeMethodEnum.STORE_DELIVERY.getValue());
	}

	//新供给自提单
	public boolean isNewSupplyStoreDelivery() {
		return Objects.equals(this.getDeliveryMethod(), DistributeMethodEnum.STORE_DELIVERY.getValue());
	}

	public boolean orderCancelled() {
		return this.orderStatus == OrderStatusEnum.CANCELED.getValue();
	}

	/**
	 * 获取门店id
	 *
	 * @return {@link #warehousePoiId}和{@link OrderKey#storeId}都不为null时，前者表示门店id，后者表示仓id；
	 * 如果{@link #warehousePoiId}为null则{@link OrderKey#storeId}表示门店id
	 */
	public long getPoiId() {
		return warehousePoiId != null ? warehousePoiId : orderKey.getStoreId();
	}

	public Long getWarehouseId(){
		if(orderTransInfo!=null && orderTransInfo.getDispatchShopId()!=null){
			return orderTransInfo.getDispatchShopId();
		}
		return orderKey.getStoreId();
	}


	public boolean isPullNewSelfPickGoodsOrder() {
		//ext里的地推人和线下推广标记都不能为空
		if (Objects.nonNull(orderExtInfo) && Objects.nonNull(orderExtInfo.getPullNewSelfPickGoodsOrder()) && Objects.nonNull(orderExtInfo.getPullNewAccountId())) {
			if (orderExtInfo.getPullNewSelfPickGoodsOrder()) {
				return true;
			}
		}
		return false;
	}


}
